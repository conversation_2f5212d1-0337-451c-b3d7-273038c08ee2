<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" fullscreen append-to-body>
    <div v-loading="modelState.loading" class="h-full">
      <el-row :gutter="10" class="h-full">
        <el-col :span="16" class="h-full">
          <PdfView title="原文" src="123" />
        </el-col>
        <el-col :span="8" class="h-full">
          <FyInfo :info="fymxInfo" />
        </el-col>
      </el-row>
    </div>

    <DragUpload v-model="uploadState.visible" :title="uploadState.title" :multiple="false" :limit="1" accept=".doc,.docx" @on-all-done="uploadAllDone" />

    <TempPicker ref="tempPickRef" @pick="tempPicked"/>

    <gz-dialog v-model="rejectState.show" :title="rejectState.title" @closed="closedRj" append-to-body>
      <el-form :model="rejectState">
        <el-form-item label="原因">
          <el-input v-model="rejectState.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end items-center">
          <el-button @click="submitRj" type="primary">确认</el-button>
          <el-button @click="cancelRj">取消</el-button>
        </div>
      </template>
    </gz-dialog>

    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="uploadFw" v-if="modelState.viewStatus === 'translation'" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">上传范文</el-button>
        <el-button @click="submitYw" v-if="modelState.viewStatus === 'translation'" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">提交译文</el-button>
        <el-button @click="toGen(fymxInfo.ywlj ? '重置译文' : '生成译文')" v-if="modelState.viewStatus === 'translation'" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">{{ fymxInfo.ywlj ? '重置译文' : '生成译文' }}</el-button>
        <el-button @click="toRejectFy" v-if="modelState.viewStatus === 'proofread'" :loading="modelState.submitting" :disabled="modelState.submitting" type="danger">驳回翻译</el-button>
        <el-button @click="submitCheck" v-if="modelState.viewStatus === 'proofread'" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">提交校对</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import PdfView from './PdfView.vue';
import FyInfo from './FyInfo.vue';
import { docOpenEdit } from '@/views/gongzheng/doc/DocEditor';
import { updateGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import TempPicker from '@/views/gongzheng/components/TempPicker/index.vue';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '翻译明细'
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const tempPickRef = ref(null);

const modelState = reactive({
  visible: false,
  title: '翻译明细',
  gzjzId: undefined,
  loading: false,
  submitting: false,

  viewStatus: '',
})

// 上传对话框相关
const uploadState = reactive({
  title: '文档上传',
  docType: '',
  docId: '',
  visible: false,
  okUploads: [],
  loading: false,
  sxRow: null,
  mbId: '',
})

// 驳回
const rejectState = reactive({
  show: false,
  title: '驳回',
  remark: ''
})

const fymxInfo = ref(null);

const uploadFw = () => {
  uploadState.visible = true;
  uploadState.title = '译文范文上传'
}

const submitYw = async () => {
  const { ywmc, ywlj } = fymxInfo.value
  if(!ywmc || !ywlj) {
    ElMessage.warning('未找到翻译译文')
    return;
  }
  await proxy?.$modal.confirm('提交后，译文无法修改，是否继续？');

  updateGzjzWjccxx({
    ...fymxInfo.value,
    fyzt: '1'
  }).then(async (res) => {
    if(res.code === 200) {
      ElMessage.success('提交成功')
      modelState.viewStatus = 'proofread'
    }
  }).catch((err: any) => {
    console.error('提交译文错误', err)
  });
}

const submitCheck = async () => {
  await proxy?.$modal.confirm('确认要提交校对吗？');
  updateGzjzWjccxx({
    ...fymxInfo.value,
    fyzt: '3'
  }).then(async (res) => {
    if(res.code === 200) {
      ElMessage.success('提交成功')
      modelState.viewStatus = 'view'
    }
  }).catch((err: any) => {
    console.error('提交校对错误', err)
  });
}

const toRejectFy = () => {
  rejectState.show = true;
}

const submitRj = async () => {
  await proxy?.$modal.confirm('是否确认驳回翻译？');
  updateGzjzWjccxx({
    ...fymxInfo.value,
    fyzt: '2',
    remark: rejectState.remark
  }).then(async (res) => {
    if(res.code === 200) {
      ElMessage.success('提交成功')
      modelState.viewStatus = 'translation'
    }
  }).catch((err: any) => {
    console.error('驳回翻译错误', err)
  });
}

const cancelRj = () => {
  rejectState.show = false;
  closedRj()
}

const closedRj = () => {
  rejectState.remark = ''
}

// 所有文件上传后的回调 res中只有上传成功的文件信息
const uploadAllDone = async (res: any[]) => {
  console.log('所有文件上传后的回调', res);
  const { fileName, ossId, path } = res[0];
  const ywmc = fileName;
  const ywlj = JSON.stringify({
    ossId,
    path,
    fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
    fileName,
    dsrId: fymxInfo.value.dsrId,
    gzsx: fymxInfo.value.gzsx,
    gzsxId: fymxInfo.value.gzjzId,
  })

  updateGzjzWjccxx({
    ...fymxInfo.value,
    ywmc,
    ywlj
  }).then(async (res) => {
    await proxy?.$modal.confirm('上传成功，是否打开？');
    docOpenEdit(path);
  }).catch((err: any) => {
    console.error('上传范文错误', err)
  });
}

const toGen = (name: string) => {
  tempPickRef.value.open({
    title: name,
    initFilterName: fymxInfo.value.gzsx || ''
  })
}

const tempPicked = (data: any) => {
  console.log('已选模版信息', data)
}

const close = () => {
  modelState.visible = false
}

const closed = () => {

}

const initData = async () => {
  try {
    modelState.loading = true;
    
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.loading = false;
  }
 }

const open = (data?: any) => {
  const { gzjzId, title, status, mx } = data;
  modelState.visible  = true;
  modelState.gzjzId = gzjzId || undefined;
  modelState.title = title || '';
  modelState.viewStatus = status || 'view'

  fymxInfo.value = mx || null;
  console.log('>>>>> ', data)

  initData();
}

const comfirmSave = async () => {
  try {
   
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.submitting = false;
  }
}

defineExpose({
  open
})

</script>
