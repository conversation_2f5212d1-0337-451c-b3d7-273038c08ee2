// 常用短语API service
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Phrase, PhraseQuery, PhraseSaveParams } from './types';

// 查询短语列表
export const listPhrases = (query: PhraseQuery): AxiosPromise<{ records: Phrase[]; total: number }> => {
  return request({
    url: '/mb/mbCydy/list',
    method: 'get',
    params: query
  });
};

// 新增短语
export const addPhrase = (data: PhraseSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbCydy',
    method: 'post',
    data
  });
};

// 编辑短语
export const updatePhrase = (data: PhraseSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbCydy',
    method: 'put',
    data
  });
};

// 删除短语
export const deletePhrase = (ids: string): AxiosPromise<any> => {
  return request({
    url: `/mb/mbCydy/${ids}`,
    method: 'delete'
  });
};
