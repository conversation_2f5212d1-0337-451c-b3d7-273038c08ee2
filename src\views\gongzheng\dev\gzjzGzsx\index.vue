<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证事项ID" prop="gzsxId">
              <el-input v-model="queryParams.gzsxId" placeholder="请输入公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证事项" prop="gzsxMc">
              <el-input v-model="queryParams.gzsxMc" placeholder="请输入公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="关系人ID" prop="gxrId">
              <el-input v-model="queryParams.gxrId" placeholder="请输入关系人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="关系人" prop="gxrMc">
              <el-input v-model="queryParams.gxrMc" placeholder="请输入关系人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书ID" prop="gzsId">
              <el-input v-model="queryParams.gzsId" placeholder="请输入公证书ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsBh">
              <el-input v-model="queryParams.gzsBh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书名称" prop="gzsMc">
              <el-input v-model="queryParams.gzsMc" placeholder="请输入公证书名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合成公证书ID" prop="hcGzsId">
              <el-input v-model="queryParams.hcGzsId" placeholder="请输入合成公证书ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="份数" prop="gzsFs">
              <el-input v-model="queryParams.gzsFs" placeholder="请输入份数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备案信息ID" prop="baxxId">
              <el-input v-model="queryParams.baxxId" placeholder="请输入备案信息ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzGzsx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzGzsx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzGzsx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzGzsx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzGzsxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证事项ID" align="center" prop="gzsxId" />
        <el-table-column label="公证事项" align="center" prop="gzsxMc" />
        <el-table-column label="关系人ID" align="center" prop="gxrId" />
        <el-table-column label="关系人" align="center" prop="gxrMc" />
        <el-table-column label="公证书ID" align="center" prop="gzsId" />
        <el-table-column label="公证书编号" align="center" prop="gzsBh" />
        <el-table-column label="公证书名称" align="center" prop="gzsMc" />
        <el-table-column label="合成公证书ID" align="center" prop="hcGzsId" />
        <el-table-column label="是否合成公证书" align="center" prop="sfHc" />
        <el-table-column label="份数" align="center" prop="gzsFs" />
        <el-table-column label="是否备案" align="center" prop="sfBa" />
        <el-table-column label="备案信息ID" align="center" prop="baxxId" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzGzsx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzGzsx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证事项v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzGzsxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证事项ID" prop="gzsxId">
          <el-input v-model="form.gzsxId" placeholder="请输入公证事项ID" />
        </el-form-item>
        <el-form-item label="公证事项" prop="gzsxMc">
          <el-input v-model="form.gzsxMc" placeholder="请输入公证事项" />
        </el-form-item>
        <el-form-item label="关系人ID" prop="gxrId">
          <el-input v-model="form.gxrId" placeholder="请输入关系人ID" />
        </el-form-item>
        <el-form-item label="关系人" prop="gxrMc">
          <el-input v-model="form.gxrMc" placeholder="请输入关系人" />
        </el-form-item>
        <el-form-item label="公证书ID" prop="gzsId">
          <el-input v-model="form.gzsId" placeholder="请输入公证书ID" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsBh">
          <el-input v-model="form.gzsBh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="公证书名称" prop="gzsMc">
          <el-input v-model="form.gzsMc" placeholder="请输入公证书名称" />
        </el-form-item>
        <el-form-item label="合成公证书ID" prop="hcGzsId">
          <el-input v-model="form.hcGzsId" placeholder="请输入合成公证书ID" />
        </el-form-item>
        <el-form-item label="份数" prop="gzsFs">
          <el-input v-model="form.gzsFs" placeholder="请输入份数" />
        </el-form-item>
        <el-form-item label="备案信息ID" prop="baxxId">
          <el-input v-model="form.baxxId" placeholder="请输入备案信息ID" />
        </el-form-item>
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzGzsx" lang="ts">
import { listGzjzGzsx, getGzjzGzsx, delGzjzGzsx, addGzjzGzsx, updateGzjzGzsx } from '@/api/gongzheng/dev/gzjzGzsx';
import { GzjzGzsxVO, GzjzGzsxQuery, GzjzGzsxForm } from '@/api/gongzheng/dev/gzjzGzsx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzGzsxList = ref<GzjzGzsxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzGzsxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzGzsxForm = {
  id: undefined,
  gzsxId: undefined,
  gzsxMc: undefined,
  gxrId: undefined,
  gxrMc: undefined,
  gzsId: undefined,
  gzsBh: undefined,
  gzsMc: undefined,
  hcGzsId: undefined,
  sfHc: undefined,
  gzsFs: undefined,
  sfBa: undefined,
  baxxId: undefined,
  gzjzId: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzGzsxForm, GzjzGzsxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzsxId: undefined,
    gzsxMc: undefined,
    gxrId: undefined,
    gxrMc: undefined,
    gzsId: undefined,
    gzsBh: undefined,
    gzsMc: undefined,
    hcGzsId: undefined,
    sfHc: undefined,
    gzsFs: undefined,
    sfBa: undefined,
    baxxId: undefined,
    gzjzId: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证事项v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzGzsx(queryParams.value);
  gzjzGzsxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzGzsxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzGzsxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证事项v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzGzsxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzGzsx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证事项v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzGzsxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzGzsx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzGzsx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzGzsxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证事项v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzGzsx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzGzsx/export', {
    ...queryParams.value
  }, `gzjzGzsx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
