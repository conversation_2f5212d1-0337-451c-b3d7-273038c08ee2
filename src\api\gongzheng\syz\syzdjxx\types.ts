export interface SyzdjxxVO {
  /**
   * $column.columnComment
   */
  id: string | number;

  /**
   * 水印纸前缀
   */
  syzQz: string;

  /**
   * 水印纸后缀
   */
  syzHz: string;

  /**
   * 首张号码
   */
  szbh: number;

  /**
   * 末张号码
   */
  mzbh: number;

  /**
   * 入库张数
   */
  rkzs: number;

  /**
   * 已使用张数
   */
  syzs: number;

  /**
   * 作废张数
   */
  zfzs: number;

  /**
   * 入库时间
   */
  rksj: string;

  /**
   * 入库操作人
   */
  rkczr: string;

}

export interface SyzdjxxForm extends BaseEntity {
  /**
   * $column.columnComment
   */
  id?: string | number;

  /**
   * 水印纸前缀
   */
  syzQz?: string;

  /**
   * 水印纸后缀
   */
  syzHz?: string;

  /**
   * 首张号码
   */
  szbh?: number;

  /**
   * 末张号码
   */
  mzbh?: number;

  /**
   * 入库张数
   */
  rkzs?: number;

  /**
   * 已使用张数
   */
  syzs?: number;

  /**
   * 作废张数
   */
  zfzs?: number;

  /**
   * 入库时间
   */
  rksj?: string;

  /**
   * 入库操作人
   */
  rkczr?: string;

}

export interface SyzdjxxQuery extends PageQuery {

  /**
   * 水印纸前缀
   */
  syzQz?: string;

  /**
   * 首张号码
   */
  szbh?: number;

  /**
   * 入库时间
   */
  rksj?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

export interface SyzLyFfForm extends BaseEntity {
  /**
   * $column.columnComment
   */
  id?: string | number;

  /**
   * 领用人ID
   */
  lyrId?: number;

  /**
   * 领用张数
   */
  lyzs?: number;

}


