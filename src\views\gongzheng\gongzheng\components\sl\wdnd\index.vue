<template>
  <div class="h-full flex flex-col gap-10px">
    <!-- 文档列表区域 -->
    <el-card class="flex-1">
      <div class="document-list-header">
        文档列表 (阅览清单: 如需在文档中生成人员信息，请勾选当事人)
      </div>

      <div class="document-table">
        <el-table :data="wdndDocTypeList" v-loading="listLoading" border stripe size="small">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column prop="typeName" label="文档类型" align="center" />
          <el-table-column label="文档名称" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex flex-wrap gap-4px">
                <el-tag v-for="item in row.docList" :key="item.id">
                  <el-button type="primary" link @click="handleOpen(item)">{{item.wbmc}}</el-button>
                  <el-button type="danger" link icon="Delete" @click="handleDelete(item)" style="margin-left: 4px;"></el-button>
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template #default="{ row }">
              <el-button type="primary" size="small" link @click="generateDoc(row)">生成</el-button>
              <el-button type="primary" size="small" link @click="handleUpload(row)">上传</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <!-- 标签页切换区域 -->
    <el-card class="flex-1">
      <SubDataMod ref="SubRef" />
    </el-card>

    <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable :modal="false" show-close destroy-on-close width="400">
      <div class="h-100px flex items-center justify-center">
        <div class="flex items-center justify-center gap-10px">
          <strong>文档模版：</strong>
          <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;">
            <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button type="primary" @click="comfirmGen" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
          <el-button @click="genClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 上传实现 -->
    <DragUpload v-model="uploadState.visible" :title="uploadState.title" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DragUpload from '@/components/FileUpload/DragUpload.vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { formatDate, formatGender, dictMapFormat } from '@/utils/ruoyi';
import SubDataMod from '@/views/gongzheng/gongzheng/components/sl/wdnd/SubDataMod.vue'
import { docGenerator, docOpenEdit } from '@/views/gongzheng/doc/DocEditor'
import { UserDocGenParams } from '@/views/gongzheng/doc/type';
import { wdndDocTableData, type DocListItem } from '../preset_data';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
import { addGzjzWjccxx, delGzjzWjccxx, listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

// 当事人
const dsrState = reactive({
  listData: [],
  loading: false
})

// 常用短语
const cydyState = reactive({
  listData: [],
  loading: false,
  search: ''
})

const gzsxState = reactive({
  listData: [],
  loading: false,
})

const SubRef = ref(null)
const genState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  typeData: [],
  mbId: '',
})

// 列表加载
const listLoading = ref(false)

// 上传对话框相关
const uploadState = reactive({
  title: '文档上传',
  docType: '',
  docId: '',
  visible: false,
  okUploads: [],
  loading: false,
  sxRow: null,
  mbId: '',
})

// 文档拟定 文档类型列表
const wdndDocTypeList = ref<DocListItem[]>(wdndDocTableData);

// 文档生成按钮点击
const generateDoc = (row: any) => {
  const subIns = SubRef.value;
  const selectedDsrList = subIns?.getSelectedDsr();
  const selectedGzsxList = subIns?.getSelectedGzsx();
  if(selectedDsrList.length === 0) {
    ElMessage.warning('至少选一个当事人');
    return;
  }
  if(selectedGzsxList.length === 0) {
    ElMessage.warning('未选取公证事项，无法生成文档');
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  queryMbFiles({ wdLb: row.typeCode }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        genState.typeData = res.data;
        genState.mbId = res.data[0].id;
        genState.sxRow = row;
        genState.visible = true;
        genState.title = `${row.typeName} 文档生成`;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

const loadMbFiles = async (lb: number) => {
  try {
    const res = await queryMbFiles({ wdLb: lb });
  } catch (err: any) {
    console.log('查询模版文件异常', err);
  }
}

const genClose = () => {
  genState.loading = false;
  genState.visible = false;
  genClosed();
}

const genClosed = () => {
  genState.sxRow = null
  genState.mbId = ''
}

// 确认生成文档
const comfirmGen = () => {
  if (!genState.mbId) {
    ElMessage.warning('未选择生成指定模版')
    return;
  }

  const subIns = SubRef.value;
  const selectedDsrList = (subIns?.getSelectedDsr() || []);
  const dsrIdArr = selectedDsrList.map((dsr: GzjzDsrVO) => dsr.dsrId)
  const dsrIds = dsrIdArr.join(',')

  let extraParams: any;

  if(genState.sxRow.singleGen || genState.sxRow.typeCode === 8) {
    extraParams = {
      dsr: selectedDsrList.map((dsr: GzjzDsrVO) => {
        return {
          dsrId: dsr.dsrId,
          dsrLx: dsr.dsrLx,
          js: dsr.js,
        }
      })
    }
  } else {
    extraParams = {
      dsrIds
    }
  }

  let params: UserDocGenParams = {
    bizId: (curGzjz.value.id || currentRecordId.value) as string,
    // type: genState.sxRow.typeCode,
    // fjlb: EnumDocFileType.QT,
    mbWdId: genState.mbId,
    extraParams,
  }

  console.log('生成文档参数', params)

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  genState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      const fileName = `${genState.sxRow.typeName}_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
      const docInfo: GzjzWjccxxForm = {
        wbmc: fileName,
        wblj: JSON.stringify({
          ossId,
          path,
          fileSuffix,
          fileName,
          typeCode: genState.sxRow.typeCode,
          typeName: genState.sxRow.typeName,
          mbWdId: genState.mbId,
          dsrIds
        }),
        lx: genState.sxRow.typeCode,
      }
      relateDoc(docInfo);
      genState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败')
  }).finally(() => {
    genState.loading = false;
    loading.close()
  })
}

// 文档文件列表查询
const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.error('文档文件列表查询失败', err)
  }
}

interface FilterRes<T> {
  matches: T[];
  noMatches: T[];
}

function nodeFilter<T>(data: Array<T>, match: (node: T) => boolean = () => false): FilterRes<T> {
  let y: Array<T> = [], n: Array<T> = [];
  data.forEach((item, index) => {
    if (match(item)) {
      y.push(item);
    } else {
      n.push(item);
    }
  });
  return {
    matches: y,
    noMatches: n
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  wdndDocTypeList.value.map(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      if(!obj) return false;
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    source = noMatches;
  })
}

// 添加生成后的文档（关联生成文档）
const relateDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    const res = await addGzjzWjccxx(params);
    if(res.code === 200) {
      ElMessage.success('生成文件添加成功')
      loadDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

// 删除关联文档
const delDoc = async (id: number | string) => {
  delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadDocList()
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
}

// 处理上传
const handleUpload = (row: any) => {
  // ElMessage.success(`准备上传文档: ${row.name}`)
  console.log('准备上传文档', row)
  // 实际上传逻辑
  uploadState.visible = true;
  uploadState.title = `上传文档 - ${row.typeName}`;
  uploadState.sxRow = row;
}

// 每一个文件上传后的回调 有成功或结束状态
const uploadEveryDone = (res: any) => {
  // console.log('每一个文件上传后的回调', res)
  if (res.status === 'success') {
    // uploadState.okUploads.push(res.result)
  } else if (res.status === 'error') {

  }
}

// 所有文件上传后的回调 res中只有上传成功的文件信息
const uploadAllDone = (res: any[]) => {
  console.log('所有文件上传后的回调', res);
  const mxList = res.map((item) => {
    const { fileName, ossId, path } = item;
    const docInfo: GzjzWjccxxForm = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      wbmc: fileName,
      wblj: JSON.stringify({
        ossId,
        path,
        fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
        fileName,
        typeCode: uploadState.sxRow.typeCode,
        typeName: uploadState.sxRow.typeName,
      }),
      lx: uploadState.sxRow.typeCode
    }

    return addGzjzWjccxx(docInfo)
  });

  // 此处需优化，如存在一个添加失败会出错
  Promise.all(mxList).then(() => {

  }).finally(() => {
    loadDocList();
  })

}

// 打开预览文档
const handleOpen = (data: any) => {
  // 格式化json字符串为对象数据
  const obj = JSON.parse(data.wblj)
  docOpenEdit(obj.path)
}

const handleDelete = (item: any) => {
  ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDoc(item.id)
  }).catch(() => {
    // ElMessage.info('已取消删除')
  })
}

onMounted(() => {
  loadDocList();
})
</script>

<style scoped>
.wdnd-container {
  /* padding: 20px; */
  height: 100%;
  /* background: #f5f7fa; */
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.document-section {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.document-list-header {
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

.document-table {
  margin-bottom: 15px;
}

.search-area {
  display: flex;
  align-items: center;
  margin: 15px 0;
}

.search-label {
  margin-right: 10px;
}

.tab-section {
  margin-top: 20px;
}

.tab-content {
  padding: 15px 0;
}

.parties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.parties-table {
  margin-bottom: 15px;
}

.notary-matters-table {
  margin-bottom: 15px;
}

.notary-content {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.phrases-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.phrases-search {
  display: flex;
  gap: 10px;
}

.phrases-table {
  margin-bottom: 15px;
}

.zmcl-tag+.zmcl-tag {
  margin-left: 8px;
}

:deep(.el-card__body) {
  height: 100%;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .search-area {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-area > * {
    margin-bottom: 10px;
  }
}
</style>
