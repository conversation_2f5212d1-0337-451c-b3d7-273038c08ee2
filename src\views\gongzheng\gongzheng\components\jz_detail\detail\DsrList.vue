<template>
  <el-card>
    <template #header>
      <strong class="text-base">当事人列表</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="js" label="角色" width="120" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_dsr_jslx, row.js) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="当事人姓名" width="120" align="center" />
      <el-table-column prop="dsrLx" label="当事人类别" width="120" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_dsr_lb, row.dsrLx) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="certificateType" label="证件类型" width="120" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_gr_zjlx, row.certificateType) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="certificateNo" label="证件号码" align="center" show-overflow-tooltip/>
      <el-table-column prop="address" label="住址" align="center" show-overflow-tooltip />
      <el-table-column prop="contactTel" label="电话号码" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { dictMapFormat } from '@/utils/ruoyi'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_dsr_jslx, gz_dsr_lb, gz_gr_zjlx } = toRefs<any>(proxy?.useDict('gz_dsr_jslx', 'gz_dsr_lb', 'gz_gr_zjlx'));

</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
