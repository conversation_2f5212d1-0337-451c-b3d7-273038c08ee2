export interface GzjzTcxLqrVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 提存项ID
   */
  tcxId: string | number;

  /**
   * 当事人ID
   */
  dsrId: string | number;
  /**
   * 当事人类型
   */
  dsrLx: string;

  /**
   * 已领取
   */
  ylq: number;

}

export interface GzjzTcxLqrForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 提存项ID
   */
  tcxId?: string | number;
  /**
   * 当事人ID
   */
  dsrId?: string | number;
  /**
   * 当事人类型
   */
  dsrLx: string;
  /**
   * 已领取
   */
  ylq?: number;

}

export interface GzjzTcxLqrQuery extends PageQuery {

  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 当事人ID
   */
  dsrId?: string | number;
  /**
   * 当事人类型
   */
  dsrLx: string;

  /**
   * 已领取
   */
  ylq?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}


export interface GzTcxLqrListVo extends GzjzTcxLqrVO{
  /**
   * 姓名
   */
  name?:string;
  /**
   * 证件类型
   */
  zjlx?:string|number;
  /**
   * 证件号码
   */
  zjhm?:string|number;
  /**
   * 地址
   */
  zz?:string;
  /**
   * 联系电话
   */
  lxdh?:string|number;
}

export interface GzTcxLqrListQuery extends PageQuery{
  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 当事人ID
   */
  dsrId?: string | number;
  /**
   * 姓名
   */
  name?:string;
  /**
   * 证件类型
   */
  zjlx?:string|number;
  /**
   * 证件号码
   */
  zjhm?:string|number;
  /**
   * 地址
   */
  zz?:string;
  /**
   * 联系电话
   */
  lxdh?:string|number;
}

