<template>
  <div class="person-form">
    <el-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名：" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别：" prop="gender">
            <el-select v-model="formData.gender" placeholder="请选择性别" >
              <el-option v-for="dict in gz_xb" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="证件类型：" prop="idType">
            <el-select v-model="formData.idType" placeholder="请选择证件类型" >
              <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号码：" prop="idNumber">
            <el-input v-model="formData.idNumber" placeholder="请输入证件号码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出生日期：" prop="birthDate">
            <el-date-picker
              v-model="formData.birthDate"
              type="date"
              placeholder="选择出生日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话：" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型：" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型" style="width: 100%">
              <el-option
                v-for="item in personTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="住址：" prop="address">
            <el-input
              v-model="formData.address"
              type="textarea"
              :rows="3"
              placeholder="请输入住址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="dialog-footer" v-if="mode !== 'view'">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
    <div class="dialog-footer" v-else>
      <el-button @click="handleCancel">关闭</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElForm, ElMessage } from 'element-plus'
import type { PartyPersonData } from '@/api/gongzheng/gongzheng/sl/sl'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gr_zjlx,gz_xb} = toRefs<any>(proxy?.useDict('gz_gr_zjlx','gz_xb'));

interface Props {
  formData: PartyPersonData
  mode: 'add' | 'edit' | 'view'
}

interface Emits {
  (e: 'save', value: PartyPersonData): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const formData = reactive<PartyPersonData>({ ...props.formData })

// 选项数据
const idTypeOptions = [
  { label: '身份证', value: '身份证' },
  { label: '护照', value: '护照' },
  { label: '港澳通行证', value: '港澳通行证' },
  { label: '台胞证', value: '台胞证' },
  { label: '军官证', value: '军官证' },
  { label: '其他', value: '其他' }
]

const personTypeOptions = [
  { label: '申请人', value: '申请人' },
  { label: '代理人', value: '代理人' },
  { label: '法定代理人', value: '法定代理人' },
  { label: '委托代理人', value: '委托代理人' },
  { label: '其他', value: '其他' }
]

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  idType: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  idNumber: [
    { required: true, message: '请输入证件号码', trigger: 'blur' },
    {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$|^[a-zA-Z0-9]{5,20}$/,
      message: '请输入正确的证件号码',
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
}

// 监听props变化
watch(
  () => props.formData,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true }
)

// 保存
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    emit('save', { ...formData })
  } catch (error) {
    ElMessage.error('请检查表单信息')
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.person-form {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-date-picker) {
  width: 100%;
}
</style>
