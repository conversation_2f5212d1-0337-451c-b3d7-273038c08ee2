export interface GzsxPzVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 档案期限
   */
  archivePeriod : string | number;

  /**
   * 案件级别
   */
  caseLevel : string | number;

  /**
   * 标的审核
   */
  subjectReview : string | number;

  /**
   * 质检员（多个逗号分隔）
   */
  qualityInspector : string;

  /**
   * 告知模板（多个逗号分隔）
   */
  notifyTemplateId : string | number;

  /**
   * 申请表（多个逗号分隔）
   */
  applicationFormId : string | number;

  /**
   * 电子公证材料类型（字典）
   */
  dzgzcllx : string | number;

  /**
   * 最低收费比例
   */
  minimumFeeRatio : number;

  /**
   * 最高收费比例
   */
  maximumFeeRatio : number;


  gzsxId : string | number;

  gzsxCode : string;

  gzlbValue : string;
}

export interface GzsxPzForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 档案期限
   */
  archivePeriod ?: string | number;

  /**
   * 案件级别
   */
  caseLevel ?: string | number;

  /**
   * 标的审核
   */
  subjectReview ?: string;

  /**
   * 质检员（多个逗号分隔）
   */
  qualityInspector ?: string;

  /**
   * 告知模板（多个逗号分隔）
   */
  notifyTemplateId ?: string | number;

  /**
   * 申请表（多个逗号分隔）
   */
  applicationFormId ?: string | number;

  /**
   * 电子公证材料类型（字典）
   */
  dzgzcllx ?: string | number;

  /**
   * 最低收费比例
   */
  minimumFeeRatio ?: number;

  /**
   * 最高收费比例
   */
  maximumFeeRatio ?: number;

  gzsxId ?: string | number;

  gzsxCode ?: string;

  gzlbValue ?: string;
}

export interface GzsxPzQuery extends PageQuery {

  /**
   * 档案期限
   */
  archivePeriod ?: string | number;

  /**
   * 案件级别
   */
  caseLevel ?: string | number;

  /**
   * 标的审核
   */
  subjectReview ?: string | number;

  /**
   * 质检员（多个逗号分隔）
   */
  qualityInspector ?: string;

  /**
   * 告知模板（多个逗号分隔）
   */
  notifyTemplateId ?: string | number;

  /**
   * 申请表（多个逗号分隔）
   */
  applicationFormId ?: string | number;

  /**
   * 电子公证材料类型（字典）
   */
  dzgzcllx ?: string | number;

  /**
   * 最低收费比例
   */
  minimumFeeRatio ?: number;

  /**
   * 最高收费比例
   */
  maximumFeeRatio ?: number;

  /**
   * 日期范围参数
   */
  params ?: any;

  gzsxId ?: string | number;

  gzsxCode ?: string;

  gzlbValue ?: string;
}
