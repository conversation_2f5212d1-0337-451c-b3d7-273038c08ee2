import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzJdVO, GzjzJdForm, GzjzJdQuery } from '@/api/gongzheng/bzfz/gzjzJd/types';

/**
 * 查询公证卷宗-借贷列表
 * @param query
 * @returns {*}
 */

export const listGzjzJd = (query?: GzjzJdQuery): AxiosPromise<GzjzJdVO[]> => {
  return request({
    url: '/gongzheng/gzjzJd/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-借贷详细
 * @param id
 */
export const getGzjzJd = (id: string | number): AxiosPromise<GzjzJdVO> => {
  return request({
    url: '/gongzheng/gzjzJd/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-借贷
 * @param data
 */
export const addGzjzJd = (data: GzjzJdForm) => {
  return request({
    url: '/gongzheng/gzjzJd',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-借贷
 * @param data
 */
export const updateGzjzJd = (data: GzjzJdForm) => {
  return request({
    url: '/gongzheng/gzjzJd',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-借贷
 * @param id
 */
export const delGzjzJd = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzJd/' + id,
    method: 'delete'
  });
};

/**
 * 根据gzjzGzsxId查询借贷信息
 * @param params 
 * @returns 
 */
export const getGzjzJdByGzjzGzsxId = (params: { gzjzGzsxId: string }): AxiosPromise<GzjzJdVO> => {
  return request({
    url: '/gongzheng/gzjzJd/getByGzjzGzsxId',
    method: 'get',
    params
  })
}
