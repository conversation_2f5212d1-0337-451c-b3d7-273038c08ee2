<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费清单号" prop="sfqdh">
              <el-input v-model="queryParams.sfqdh" placeholder="请输入收费清单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="缴费人ID" prop="jfrId">
              <el-input v-model="queryParams.jfrId" placeholder="请输入缴费人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="缴费人" prop="jfr">
              <el-input v-model="queryParams.jfr" placeholder="请输入缴费人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付方式" prop="zffs">
              <el-select v-model="queryParams.zffs" placeholder="请选择支付方式" clearable >
                <el-option v-for="dict in gz_sf_fs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="收费日期" prop="sfrq">
              <el-date-picker clearable
                v-model="queryParams.sfrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择收费日期"
              />
            </el-form-item>
            <el-form-item label="收费金额" prop="sfje">
              <el-input v-model="queryParams.sfje" placeholder="请输入收费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费人ID" prop="sfrId">
              <el-input v-model="queryParams.sfrId" placeholder="请输入收费人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费人" prop="sfr">
              <el-input v-model="queryParams.sfr" placeholder="请输入收费人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否打印收据" prop="sfdysj">
              <el-select v-model="queryParams.sfdysj" placeholder="请选择是否打印收据" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否打印收据证明" prop="sfdysjzm">
              <el-select v-model="queryParams.sfdysjzm" placeholder="请选择是否打印收据证明" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="发票抬头" prop="fptt">
              <el-input v-model="queryParams.fptt" placeholder="请输入发票抬头" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发票类型" prop="fplx">
              <el-select v-model="queryParams.fplx" placeholder="请选择发票类型" clearable >
                <el-option v-for="dict in gz_mr_zt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="发票号码" prop="fphm">
              <el-input v-model="queryParams.fphm" placeholder="请输入发票号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开票金额" prop="fpje">
              <el-input v-model="queryParams.fpje" placeholder="请输入开票金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开票日期" prop="fprq">
              <el-date-picker clearable
                v-model="queryParams.fprq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择开票日期"
              />
            </el-form-item>
            <el-form-item label="已收金额" prop="ysje">
              <el-input v-model="queryParams.ysje" placeholder="请输入已收金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退费金额" prop="tfje">
              <el-input v-model="queryParams.tfje" placeholder="请输入退费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="减免金额" prop="jmje">
              <el-input v-model="queryParams.jmje" placeholder="请输入减免金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否收费" prop="sfsf">
              <el-select v-model="queryParams.sfsf" placeholder="请选择是否收费" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="收费来源" prop="sfly">
              <el-select v-model="queryParams.sfly" placeholder="请选择收费来源" clearable >
                <el-option v-for="dict in gz_sfly" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzSfjfp:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzSfjfp:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzSfjfp:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzSfjfp:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzSfjfpList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="收费清单号" align="center" prop="sfqdh" />
        <el-table-column label="缴费人ID" align="center" prop="jfrId" />
        <el-table-column label="缴费人" align="center" prop="jfr" />
        <el-table-column label="支付方式" align="center" prop="zffs">
          <template #default="scope">
            <dict-tag :options="gz_sf_fs" :value="scope.row.zffs"/>
          </template>
        </el-table-column>
        <el-table-column label="收费日期" align="center" prop="sfrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sfrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收费金额" align="center" prop="sfje" />
        <el-table-column label="收费人ID" align="center" prop="sfrId" />
        <el-table-column label="收费人" align="center" prop="sfr" />
        <el-table-column label="是否打印收据" align="center" prop="sfdysj">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfdysj"/>
          </template>
        </el-table-column>
        <el-table-column label="是否打印收据证明" align="center" prop="sfdysjzm">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfdysjzm"/>
          </template>
        </el-table-column>
        <el-table-column label="发票抬头" align="center" prop="fptt" />
        <el-table-column label="发票类型" align="center" prop="fplx">
          <template #default="scope">
            <dict-tag :options="gz_mr_zt" :value="scope.row.fplx"/>
          </template>
        </el-table-column>
        <el-table-column label="发票号码" align="center" prop="fphm" />
        <el-table-column label="开票金额" align="center" prop="fpje" />
        <el-table-column label="开票日期" align="center" prop="fprq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.fprq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已收金额" align="center" prop="ysje" />
        <el-table-column label="退费金额" align="center" prop="tfje" />
        <el-table-column label="减免金额" align="center" prop="jmje" />
        <el-table-column label="是否收费" align="center" prop="sfsf">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfsf"/>
          </template>
        </el-table-column>
        <el-table-column label="收费来源" align="center" prop="sfly">
          <template #default="scope">
            <dict-tag :options="gz_sfly" :value="scope.row.sfly"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzSfjfp:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzSfjfp:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-收费及开票记录v1.2对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzSfjfpFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="收费清单号" prop="sfqdh">
          <el-input v-model="form.sfqdh" placeholder="请输入收费清单号" />
        </el-form-item>
        <el-form-item label="缴费人ID" prop="jfrId">
          <el-input v-model="form.jfrId" placeholder="请输入缴费人ID" />
        </el-form-item>
        <el-form-item label="缴费人" prop="jfr">
          <el-input v-model="form.jfr" placeholder="请输入缴费人" />
        </el-form-item>
        <el-form-item label="支付方式" prop="zffs">
          <el-select v-model="form.zffs" placeholder="请选择支付方式">
            <el-option
                v-for="dict in gz_sf_fs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收费日期" prop="sfrq">
          <el-date-picker clearable
            v-model="form.sfrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择收费日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="收费金额" prop="sfje">
          <el-input v-model="form.sfje" placeholder="请输入收费金额" />
        </el-form-item>
        <el-form-item label="收费人ID" prop="sfrId">
          <el-input v-model="form.sfrId" placeholder="请输入收费人ID" />
        </el-form-item>
        <el-form-item label="收费人" prop="sfr">
          <el-input v-model="form.sfr" placeholder="请输入收费人" />
        </el-form-item>
        <el-form-item label="是否打印收据" prop="sfdysj">
          <el-radio-group v-model="form.sfdysj">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否打印收据证明" prop="sfdysjzm">
          <el-radio-group v-model="form.sfdysjzm">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头" prop="fptt">
          <el-input v-model="form.fptt" placeholder="请输入发票抬头" />
        </el-form-item>
        <el-form-item label="发票类型" prop="fplx">
          <el-select v-model="form.fplx" placeholder="请选择发票类型">
            <el-option
                v-for="dict in gz_mr_zt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发票号码" prop="fphm">
          <el-input v-model="form.fphm" placeholder="请输入发票号码" />
        </el-form-item>
        <el-form-item label="开票金额" prop="fpje">
          <el-input v-model="form.fpje" placeholder="请输入开票金额" />
        </el-form-item>
        <el-form-item label="开票日期" prop="fprq">
          <el-date-picker clearable
            v-model="form.fprq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择开票日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="已收金额" prop="ysje">
          <el-input v-model="form.ysje" placeholder="请输入已收金额" />
        </el-form-item>
        <el-form-item label="退费金额" prop="tfje">
          <el-input v-model="form.tfje" placeholder="请输入退费金额" />
        </el-form-item>
        <el-form-item label="减免金额" prop="jmje">
          <el-input v-model="form.jmje" placeholder="请输入减免金额" />
        </el-form-item>
        <el-form-item label="是否收费" prop="sfsf">
          <el-radio-group v-model="form.sfsf">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收费来源" prop="sfly">
          <el-select v-model="form.sfly" placeholder="请选择收费来源">
            <el-option
                v-for="dict in gz_sfly"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzSfjfp" lang="ts">
import { listGzjzSfjfp, getGzjzSfjfp, delGzjzSfjfp, addGzjzSfjfp, updateGzjzSfjfp } from '@/api/gongzheng/dev/gzjzSfjfp';
import { GzjzSfjfpVO, GzjzSfjfpQuery, GzjzSfjfpForm } from '@/api/gongzheng/dev/gzjzSfjfp/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_mr_zt, gz_yes_or_no, gz_sf_fs, gz_sfly } = toRefs<any>(proxy?.useDict('gz_mr_zt', 'gz_yes_or_no', 'gz_sf_fs', 'gz_sfly'));

const gzjzSfjfpList = ref<GzjzSfjfpVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzSfjfpFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzSfjfpForm = {
  id: undefined,
  gzjzId: undefined,
  sfqdh: undefined,
  jfrId: undefined,
  jfr: undefined,
  zffs: undefined,
  sfrq: undefined,
  sfje: undefined,
  sfrId: undefined,
  sfr: undefined,
  sfdysj: undefined,
  sfdysjzm: undefined,
  fptt: undefined,
  fplx: undefined,
  fphm: undefined,
  fpje: undefined,
  fprq: undefined,
  ysje: undefined,
  tfje: undefined,
  jmje: undefined,
  sfsf: undefined,
  sfly: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzSfjfpForm, GzjzSfjfpQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    sfqdh: undefined,
    jfrId: undefined,
    jfr: undefined,
    zffs: undefined,
    sfrq: undefined,
    sfje: undefined,
    sfrId: undefined,
    sfr: undefined,
    sfdysj: undefined,
    sfdysjzm: undefined,
    fptt: undefined,
    fplx: undefined,
    fphm: undefined,
    fpje: undefined,
    fprq: undefined,
    ysje: undefined,
    tfje: undefined,
    jmje: undefined,
    sfsf: undefined,
    sfly: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-收费及开票记录v1.2列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzSfjfp(queryParams.value);
  gzjzSfjfpList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzSfjfpFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzSfjfpVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-收费及开票记录v1.2";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzSfjfpVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzSfjfp(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-收费及开票记录v1.2";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzSfjfpFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzSfjfp(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzSfjfp(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzSfjfpVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-收费及开票记录v1.2编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzSfjfp(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzSfjfp/export', {
    ...queryParams.value
  }, `gzjzSfjfp_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
