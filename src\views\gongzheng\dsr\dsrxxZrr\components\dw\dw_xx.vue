<template>
  <div>
    <el-form ref="dsrxxZrrFormRef" :model="form" :rules="rules" label-width="150px" :disabled="!props.dialigEdit">
      <el-row>
        <el-col :span="8">
          <el-form-item label="会员号码" prop="hyhm">
            <el-input v-model="form.hyhm" clearable disabled readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位名称" prop="dwmc">
            <el-input v-model="form.dwmc" :placeholder="!props.dialigEdit ? '' : '请输入单位名称'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="英文名称" prop="ywmc">
            <el-input v-model="form.ywmc" :placeholder="!props.dialigEdit ? '' : '请输入英文名称'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="企业证件类型" prop="zjlx">
            <el-select v-model="form.zjlx" :placeholder="!props.dialigEdit ? '' : '请选择证件类型'">
              <el-option v-for="dict in gz_jg_zjlx" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="证件号码" prop="zjhm">
            <el-input v-model="form.zjhm" :placeholder="!props.dialigEdit ? '' : '请输入证件号码'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址" prop="dwszd">
            <el-input v-model="form.dwszd" :placeholder="!props.dialigEdit ? '' : '请输入地址'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="联系电话" prop="lxdh">
            <el-input v-model="form.lxdh" :placeholder="!props.dialigEdit ? '' : '请输入联系电话'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人名字" prop="fddbr">
            <el-input v-model="form.fddbr" :placeholder="!props.dialigEdit ? '' : '请输入法定代表人名字'" />
            <!-- <el-button @click.stop="() => toCard(1)" type="primary" size="small" v-if="props.dialigEdit">刷卡</el-button> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人证件类型" prop="fddbrzjlx">
            <el-select v-model="form.fddbrzjlx" :placeholder="!props.dialigEdit ? '' : '请选择证件类型'">
              <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="法定代表人证件编号" prop="fddbrzjhm">
            <el-input v-model="form.fddbrzjhm" :placeholder="!props.dialigEdit ? '' : '请输入法定代表人证件编号'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人性别" prop="fddbrxb">
            <el-select v-model="form.fddbrxb" :placeholder="!props.dialigEdit ? '' : '请选择性别'">
              <el-option v-for="dict in gz_xb" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人出生日期" prop="fddbrcsrq">
            <el-date-picker clearable v-model="form.fddbrcsrq" type="date" value-format="YYYY-MM-DD"
              :placeholder="!props.dialigEdit ? '' : '请选择法定代表人出生日期'">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="法定代表人职务" prop="fddbrzw">
            <el-input v-model="form.fddbrzw" :placeholder="!props.dialigEdit ? '' : '请输入法定代表人职务'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人住址" prop="fddbrzz">
            <el-input v-model="form.fddbrzz" :placeholder="!props.dialigEdit ? '' : '请输入法定代表人住址'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="代理人" prop="dlrxm">
            <el-input v-model="form.dlrxm" :placeholder="!props.dialigEdit ? '' : '请输入代理人'" disabled />
            <el-button @click.stop="toSelectDsr" type="primary" size="small" v-if="props.dialigEdit" link>引用</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="代理人证件类型" prop="dlrxmzjlx">
            <el-select v-model="form.dlrxmzjlx" :placeholder="!props.dialigEdit ? '' : '请选择证件类型'" disabled>
              <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="代理人证件号码" prop="dlrzjhm">
            <el-input v-model="form.dlrzjhm" :placeholder="!props.dialigEdit ? '' : '请输入负责人证件号码'" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人姓名" prop="fzrxm">
            <el-input v-model="form.fzrxm" :placeholder="!props.dialigEdit ? '' : '请输入负责人姓名'" />
            <el-button @click.stop="() => toCard(2)" type="primary" size="small" v-if="props.dialigEdit" link>刷卡</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="负责人证件类型" prop="fzrzjlx">
            <el-select v-model="form.fzrzjlx" :placeholder="!props.dialigEdit ? '' : '请选择负责人证件类型'">
              <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人证件号码" prop="fzrzjhm">
            <el-input v-model="form.fzrzjhm" :placeholder="!props.dialigEdit ? '' : '请输入负责人证件号码'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人性别" prop="fzrxb">
            <el-select v-model="form.fzrxb" :placeholder="!props.dialigEdit ? '' : '请选择性别'">
              <el-option v-for="dict in gz_xb" :key="dict.value" :label="dict.label" :value="dict.value"
               ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="负责人出生日期" prop="fzrcsrq">
            <el-date-picker clearable v-model="form.fzrcsrq" value-format="YYYY-MM-DD"
              :placeholder="!props.dialigEdit ? '' : '请选择负责人出生日期'">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人微信号" prop="fzrwxh">
            <el-input v-model="form.fzrwxh" :placeholder="!props.dialigEdit ? '' : '请输入负责人微信号'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人电子邮件" prop="fzrdzyj">
            <el-input v-model="form.fzrdzyj" :placeholder="!props.dialigEdit ? '' : '请输入负责人电子邮件'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="开户行名称" prop="khhmc">
            <el-input v-model="form.khh" :placeholder="!props.dialigEdit ? '' : '请输入开户行名称'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开户行账号" prop="khhzh">
            <el-input v-model="form.khzh" :placeholder="!props.dialigEdit ? '' : '请输入开户行账号'" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 引用当事人-->
    <el-dialog :title="dialog4.title" v-model="dialog4.visible" append-to-body>
      <Yydsr v-if="dialog4.visible" ref="yydsrRef" :editDlrlb="disabledDlrlb" @update-select="handledUpdateDsrSelectId">
      </Yydsr>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading2" type="primary" @click="submitForm2">确认引用</el-button>
          <el-button @click="cancel2">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <IdCardReader v-if="cardReaderShate.show" @close="() => {cardReaderShate.show = false}" ref="cardReader" @success="idCardReaderSuccess"/>

  </div>
</template>

<script setup name="Grxx" lang="ts">
  import { listDsrxxFrhzz, getDsrxxFrhzz, delDsrxxFrhzz, addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue'
  import IdCardReader from '@/views/components/popIdCardReader.vue'
import { getGzjzDsr } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { getDsrxxDlrxx } from '@/api/gongzheng/dsr/dsrxxDlrxx';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_lb, gz_gr_zjlx, gz_xb, gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_xb', 'gz_jg_zjlx'));
  const yydsrRef = ref<InstanceType<typeof Yydsr> | null>(null);
  const dsrxxFrhzzList = ref<DsrxxFrhzzVO[]>([]);
  const buttonLoading = ref(false);
  const buttonLoading2 = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const disabledDlrlb = ref(true);
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxFrhzzFormRef = ref<ElFormInstance>();
  const dlrInfo = ref(null);
  interface Props {
    vo : DsrxxFrhzzVO;
    dialigEdit : boolean;
    formPro?: any;
  }
  const props = defineProps<Props>();
  const dialog3 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  const data = reactive<PageData<DsrxxFrhzzForm, DsrxxFrhzzQuery>>({
    form: { ...initFormData, fddbrxb: '1', fzrxb: '1' },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      dwmc: [
        { required: true, message: "单位名称不能为空", trigger: "blur" }
      ],
      zjlx: [
        { required: true, message: "证件类型名称不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
    }
  });

  const dsrxxZrrFormRef = ref<ElFormInstance>(null)

  const { queryParams, form, rules } = toRefs(data);

  const cardReader = ref(null);
  const cardReaderShate = reactive({
    show: false,
    readType: undefined,  // 1代表人 2负责人
  })

  const toCard = (type: 1 | 2) => {
    cardReaderShate.show = true;
    cardReaderShate.readType = type;
    nextTick(() => {
      cardReader.value.open({})
    })
  }

  const idCardReaderSuccess = (data: any) => {
    if(!data) return;
    const {
      address,
      birth, // "1990年1月14日"
      errorMessage,
      gender,
      idNum,
      moreInfo,
      name,
      nation,
      photo, // 无前缀的base64
      signOrg, // 发证地点
      valid,  // "1990年1月14日"
    } = data;

    const d = birth.replace('日', '').replace(/年|月/g, '-')
    const ds = d.split('-');
    ds[1] = String(ds[1]).padStart(2, '0');
    ds[2] = String(ds[2]).padStart(2, '0');

    if(cardReaderShate.readType === 1) {
      form.value.fddbr = name;
      form.value.fddbrcsrq = ds.join('-');
      form.value.fddbrzjhm = idNum;
      form.value.fddbrxb = gender === '男' ? '1' : '2';
      // form.value.fddbrzjlx = '1'
    } else if(cardReaderShate.readType === 2) {
      form.value.fzrxm = name;
      form.value.fzrcsrq = ds.join('-');
      form.value.fzrzjhm = idNum;
      form.value.fzrxb = gender === '男' ? '1' : '2';
      form.value.fzrzjlx = '1'
    }
  }


  const toSelectDsr = () => {
    dialog4.title = "引用当事人";
    dialog4.visible = true;
  }

  const cancel = () => {
    dialog3.title = "";
    dialog3.visible = false;
  }
  const submitForm = () => {

  }
  // 确认选择 代理人
  const submitForm2 = () => {
    console.log( dlrInfo.value)
    if (dlrInfo.value == null) {
      proxy?.$modal.msgError("请选择当事人");
    } else {
      if (dlrInfo.value.dsrlb === '1') {
        form.value.dlrxm = dlrInfo.value.xm;
        form.value.dlrxmzjlx = dlrInfo.value.zjlx;
        form.value.dlrzjhm = dlrInfo.value.zjhm;
      } else if (dlrInfo.value.dsrlb === '2') {
        form.value.dlrxm = dlrInfo.value.dwmc;
        form.value.dlrxmzjlx = dlrInfo.value.zjlx;
        form.value.dlrzjhm = dlrInfo.value.zjhm;
      }
      dlrInfo.value=null;
      dialog4.title = "";
      dialog4.visible = false;
    }

  }
  // 刷新代理人选择的 id
  const handledUpdateDsrSelectId = (selectDlrInfo) => {
    dlrInfo.value = selectDlrInfo;
  }
  const cancel2 = () => {
    dialog4.title = "";
    dialog4.visible = false;
  }
  // 定义事件
  const emits = defineEmits<{
    (e : 'update-count', vo : DsrxxFrhzzVO) : void;
  }>();

  const updateForm = () => {
    emits('update-count', form.value);
  };
  const init = (vo : DsrxxFrhzzVO) => {
    console.log(vo)
    if (vo.id) {
      form.value = vo;
    }
  }

  const initFrInfo = () => {
    if(props.formPro && props.formPro.dsrId) {
      reqDsrxx(props.formPro.dsrId);
    }
  }

      // 查询当事人信息
  const reqDsrxx = async (id : string | number) => {
    try {
      const res = await getDsrxxFrhzz(id);
      if (res.code === 200 && !!res.data) {
        console.log('获取法人信息成功:', res.data);
        form.value = { ...form.value, ...res.data}
        // const info = res?.data?.zrrBo || {};
        // const info1 = res?.data || {};
        // delete info1?.zrrBo;
        // form.value = { ...info, ...info1 };

        // if(form.value.zp) {
        //   initSfzAvator(form.value.zp);
        // }
        // if (form.value.cardImage1) {
        //   initSfzImg1(form.value.cardImage1);
        // }
        // if (form.value.cardImage2) {
        //   initSfzImg2(form.value.cardImage2);
        // }
      }
    } catch (err: any) {
      console.error('获取当事人信息失败:', err);
      ElMessage.error('获取当事人信息失败');
    } finally {}
  }

  const validate = () => {
    return dsrxxZrrFormRef.value?.validate()
  }



  watch(form, (value) => {
    if (value) {
      updateForm();
    }
  },
    { deep: true });
  defineExpose({
    form,
    updateForm,
    validate
  });
  onMounted(() => {
    // init(props.vo);
    initFrInfo();
  });
</script>



<style scoped>
  .zpcss {
    width: 150px;
    height: 180px;
    border: 1px solid #ccc;
  }

  .dateil-card-main {
    margin-bottom: 10px;
  }

  .dateil-card-heard {
    /* background-color: aliceblue; */
    padding: 10px;
    border-bottom: 1px solid #e7e7e7;
    margin-bottom: 10px;
  }

  :deep(.el-input.is-disabled .el-input__wrapper), :deep(.el-select__wrapper.is-disabled) {
    background-color: none;
    background: none;
  }
</style>
