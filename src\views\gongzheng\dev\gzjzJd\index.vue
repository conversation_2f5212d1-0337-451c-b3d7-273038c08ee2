<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员ID" prop="gzyId">
              <el-input v-model="queryParams.gzyId" placeholder="请输入公证员ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员" prop="gzyXm">
              <el-input v-model="queryParams.gzyXm" placeholder="请输入公证员" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="贷款银行" prop="dkxh">
              <el-select v-model="queryParams.dkxh" placeholder="请选择贷款银行" clearable >
                <el-option v-for="dict in gz_tc_khh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="借贷日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeJdrq"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="到期日期" prop="dqrq">
              <el-date-picker clearable
                v-model="queryParams.dqrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择到期日期"
              />
            </el-form-item>
            <el-form-item label="合同签署日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeHtqsrq"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="借款金额" prop="jkje">
              <el-input v-model="queryParams.jkje" placeholder="请输入借款金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="有无担保" prop="ywdb">
              <el-select v-model="queryParams.ywdb" placeholder="请选择有无担保" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="婚姻状况" prop="hyzk">
              <el-select v-model="queryParams.hyzk" placeholder="请选择婚姻状况" clearable >
                <el-option v-for="dict in gz_dsr_hyzt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}" prop="dsrList">
              <el-input v-model="queryParams.dsrList" placeholder="请输入客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzJd:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzJd:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzJd:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzJd:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzJdList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="公证卷宗公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="公证员ID" align="center" prop="gzyId" />
        <el-table-column label="公证员" align="center" prop="gzyXm" />
        <el-table-column label="贷款银行" align="center" prop="dkxh">
          <template #default="scope">
            <dict-tag :options="gz_tc_khh" :value="scope.row.dkxh"/>
          </template>
        </el-table-column>
        <el-table-column label="借贷日期" align="center" prop="jdrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.jdrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="到期日期" align="center" prop="dqrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.dqrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="合同签署日期" align="center" prop="htqsrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.htqsrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="借款金额" align="center" prop="jkje" />
        <el-table-column label="有无担保" align="center" prop="ywdb">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.ywdb"/>
          </template>
        </el-table-column>
        <el-table-column label="婚姻状况" align="center" prop="hyzk">
          <template #default="scope">
            <dict-tag :options="gz_dsr_hyzt" :value="scope.row.hyzk"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}" align="center" prop="dsrList" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzJd:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzJd:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-借贷对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzJdFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" />
        </el-form-item>
        <el-form-item label="公证员ID" prop="gzyId">
          <el-input v-model="form.gzyId" placeholder="请输入公证员ID" />
        </el-form-item>
        <el-form-item label="公证员" prop="gzyXm">
          <el-input v-model="form.gzyXm" placeholder="请输入公证员" />
        </el-form-item>
        <el-form-item label="贷款银行" prop="dkxh">
          <el-select v-model="form.dkxh" placeholder="请选择贷款银行">
            <el-option
                v-for="dict in gz_tc_khh"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="借贷日期" prop="jdrq">
          <el-date-picker clearable
            v-model="form.jdrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择借贷日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="到期日期" prop="dqrq">
          <el-date-picker clearable
            v-model="form.dqrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择到期日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="合同签署日期" prop="htqsrq">
          <el-date-picker clearable
            v-model="form.htqsrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择合同签署日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="借款金额" prop="jkje">
          <el-input v-model="form.jkje" placeholder="请输入借款金额" />
        </el-form-item>
        <el-form-item label="有无担保" prop="ywdb">
          <el-select v-model="form.ywdb" placeholder="请选择有无担保">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="婚姻状况" prop="hyzk">
          <el-select v-model="form.hyzk" placeholder="请选择婚姻状况">
            <el-option
                v-for="dict in gz_dsr_hyzt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}" prop="dsrList">
            <el-input v-model="form.dsrList" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzJd" lang="ts">
import { listGzjzJd, getGzjzJd, delGzjzJd, addGzjzJd, updateGzjzJd } from '@/api/gongzheng/bzfz/gzjzJd';
import { GzjzJdVO, GzjzJdQuery, GzjzJdForm } from '@/api/gongzheng/bzfz/gzjzJd/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yes_or_no, gz_tc_khh, gz_dsr_hyzt } = toRefs<any>(proxy?.useDict('gz_yes_or_no', 'gz_tc_khh', 'gz_dsr_hyzt'));

const gzjzJdList = ref<GzjzJdVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeJdrq = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeHtqsrq = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const gzjzJdFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzJdForm = {
  id: undefined,
  gzjzId: undefined,
  gzjzGzsxId: undefined,
  gzyId: undefined,
  gzyXm: undefined,
  dkxh: undefined,
  jdrq: undefined,
  dqrq: undefined,
  htqsrq: undefined,
  jkje: undefined,
  ywdb: undefined,
  hyzk: undefined,
  remark: undefined,
  dsrList: undefined,
}
const data = reactive<PageData<GzjzJdForm, GzjzJdQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    gzjzGzsxId: undefined,
    gzyId: undefined,
    gzyXm: undefined,
    dkxh: undefined,
    dqrq: undefined,
    jkje: undefined,
    ywdb: undefined,
    hyzk: undefined,
    dsrList: undefined,
    params: {
      jdrq: undefined,
      htqsrq: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-借贷列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeJdrq.value, 'Jdrq');
  proxy?.addDateRange(queryParams.value, dateRangeHtqsrq.value, 'Htqsrq');
  const res = await listGzjzJd(queryParams.value);
  gzjzJdList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzJdFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeJdrq.value = ['', ''];
  dateRangeHtqsrq.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzJdVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-借贷";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzJdVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzJd(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-借贷";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzJdFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzJd(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzJd(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzJdVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-借贷编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzJd(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzJd/export', {
    ...queryParams.value
  }, `gzjzJd_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
