import { listByRoleKey } from '@/api/system/user';
import { useRoleUsersStore } from '@/store/modules/roleUsers';
/**
 * 获取字典数据
 */
export const useRoleUser = (...args: string[]): { [key: string]: UsersDataOption[] } => {
  const res = ref<{
    [key: string]: UsersDataOption[];
  }>({});
  args.forEach(async (roleKey) => {
    res.value[roleKey] = [];
    const users = useRoleUsersStore().getRoleUsers(roleKey);
    if (users) {
      res.value[roleKey] = users;
    } else {
      await listByRoleKey(roleKey).then((resp) => {
        res.value[roleKey] = resp.data.filter((e)=>e.userId !== 1).map(
          (p): UsersDataOption => ({ label: p.nickName, value: p.userId + '' })
        );
        useRoleUsersStore().setRoleUsers(roleKey, res.value[roleKey]);
      });
    }
  });
  return res.value;
};
