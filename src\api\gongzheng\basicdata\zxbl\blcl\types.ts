/**
 * 在线办理材料管理 - 类型定义
 */

/**
 * 在线办理材料管理VO
 */
export interface ZxblMaterialVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 材料名称
   */
  materialName: string;

  /**
   * 类别：1-个人，2-企业
   */
  category: number;

  /**
   * 说明
   */
  description?: string;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 创建部门
   */
  createDept?: number;

  /**
   * 创建人
   */
  createBy?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 删除标识：0-正常，1-删除
   */
  delFlag?: string;
}

/**
 * 在线办理材料管理Form
 */
export interface ZxblMaterialForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 材料名称
   */
  materialName?: string;

  /**
   * 类别：1-个人，2-企业
   */
  category?: number;

  /**
   * 说明
   */
  description?: string;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 创建部门
   */
  createDept?: number;

  /**
   * 备注
   */
  remark?: string;
}

/**
 * 在线办理材料管理Query
 */
export interface ZxblMaterialQuery extends PageQuery {
  /**
   * 材料名称
   */
  materialName?: string;

  /**
   * 类别：1-个人，2-企业
   */
  category?: number;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
