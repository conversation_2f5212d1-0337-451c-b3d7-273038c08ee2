<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px">
            <el-form-item label="交易哈希" prop="txHash">
              <el-input v-model="queryParams.txHash" placeholder="请输入交易哈希" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="上链时间" prop="txTime">
              <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
                range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"></el-date-picker>
            </el-form-item>
            <el-form-item label="存证类型" prop="proofType">
              <el-select v-model="queryParams.proofType" placeholder="存证类型" clearable>
                <el-option v-for="dict in chain_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="存证文件名" prop="fileName">
              <el-input v-model="queryParams.fileName" placeholder="请输入存证文件名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['gxbass:chain:export']" type="warning" plain icon="Download"
              @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="交易哈希" align="center" prop="txHash" />
        <el-table-column label="存证文件名称" align="center" prop="fileName" />
        <el-table-column label="文件上链IPFS哈希" align="center" prop="ipfsPath" />
        <el-table-column label="账户地址" align="center" prop="fromAddr" />
        <el-table-column label="区块高度" align="center" prop="blockHeight" />
        <el-table-column label="签名方" align="center" prop="signer" />
        <el-table-column label="业务ID" align="center" prop="bizId" />
        <el-table-column label="上链时间" align="center" prop="txTime" />
        <el-table-column label="存证类型" align="center" prop="proofType">
          <template #default="scope">
            <dict-tag :options="chain_type" :value="scope.row.proofType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['gxbass:chain:remove']" link type="danger" icon="Delete"
                @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        :total="total" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="GuiChainLog" lang="ts">
  import { chainList, delChain } from '@/api/guichian/list';
  import { GxbassChainVo, GxbassChainForm, GxbassChainQuery } from '@/api/guichian/list_types';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const list = ref<GxbassChainVo[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const queryFormRef = ref<ElFormInstance>();
  const { chain_type } = toRefs<any>(proxy?.useDict('chain_type'));
  const dateRange = ref<[DateModelType, DateModelType]>(['', '']);


  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GxbassChainForm = {
    id: undefined,
    deptId: undefined,
    userId: undefined,
    orderNum: undefined,
    testKey: undefined,
    value: undefined
  };
  const data = reactive<PageData<GxbassChainForm, GxbassChainQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    rules: {
      id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    }
  });

  const { queryParams, form, rules } = toRefs(data);
  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download(
      'gxbass/chain/export',
      {
        ...queryParams.value
      },
      `demo_${new Date().getTime()}.xlsx`
    );
  };
  /** 查询列表 */
  const getList = async () => {
    loading.value = true;
    const res = await chainList(proxy?.addDateRange(queryParams.value, dateRange.value));
    list.value = res.rows;
    total.value = res.total;
    loading.value = false;
  };
  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    dateRange.value = ['', ''];
    queryFormRef.value?.resetFields();
    handleQuery();
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GxbassChainVo[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  };

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GxbassChainVo) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除桂链上链数据编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
    await delChain(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
  };

  onMounted(() => {
    getList();
  });
</script>

<style>
</style>
