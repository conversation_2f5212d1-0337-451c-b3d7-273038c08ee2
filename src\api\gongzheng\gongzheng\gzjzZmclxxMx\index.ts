import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzZmclxxMxVO, GzjzZmclxxMxForm, GzjzZmclxxMxQuery } from '@/api/gongzheng/gzjzZmclxxMx/types';
import { ZjclBase64Img, ZjclFile } from './types';

/**
 * 查询公证卷宗-公证证明材料信息-明细v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzZmclxxMx = (query?: GzjzZmclxxMxQuery): AxiosPromise<GzjzZmclxxMxVO[]> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证证明材料信息-明细v1.0详细
 * @param id
 */
export const getGzjzZmclxxMx = (id: string | number): AxiosPromise<GzjzZmclxxMxVO> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证证明材料信息-明细v1.0
 * @param data
 */
export const addGzjzZmclxxMx = (data: GzjzZmclxxMxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证证明材料信息-明细v1.0
 * @param data
 */
export const updateGzjzZmclxxMx = (data: GzjzZmclxxMxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证证明材料信息-明细v1.0
 * @param id
 */
export const delGzjzZmclxxMx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/' + id,
    method: 'delete'
  });
};

/**
 * 新增证据材料
 */

export const addZjclFile = (data: ZjclFile) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/addFile',
    method: 'post',
    data
  })
}

/**
 * 新增证据材料-base64图片
 */

export const addZjclImg64 = (data: ZjclBase64Img) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'post',
    data
  })
}



/**
 * 公证卷宗-文书申请翻译批量处理
 */
export const batchWjccxxMxSqfy = (id : string | number | Array<string | number>, gzjzId : string | number, batchStatus : string | number) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/batchWjccxxMxSqfy/' + id,
    method: 'post',
    params: {
      batchStatus: batchStatus,
      gzjzId: gzjzId
    }
  })
}
