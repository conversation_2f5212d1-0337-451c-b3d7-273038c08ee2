export interface GzjzBdcqVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 房屋产权证号/不动产权证号
   */
  bdcqZh: string;

  /**
   * 产权详细地址
   */
  bdcqDz: string;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzBdcqForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 房屋产权证号/不动产权证号
   */
  bdcqZh?: string;

  /**
   * 产权详细地址
   */
  bdcqDz?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzBdcqQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 房屋产权证号/不动产权证号
   */
  bdcqZh?: string;

  /**
   * 产权详细地址
   */
  bdcqDz?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



