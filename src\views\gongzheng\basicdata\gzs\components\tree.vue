<template>
  <div>
    <el-tree class="treeMainCss" ref="gzsxTableRef" v-loading="loading" :data="gzsxList"
      :show-checkbox="props.showCheckBox" default-expand-all node-key="id" highlight-current :props="defaultProps"
      @node-click="handleNodeClick">
    </el-tree>
  </div>
</template>

<script setup name="GzsTreeVsion2" lang="ts">
  import { ref, reactive, toRefs, getCurrentInstance, onMounted, nextTick } from 'vue';
  import { listTree } from '@/api/gongzheng/basicdata/gzs';
  import { GzsVO, GzsQuery, GzsForm } from '@/api/gongzheng/basicdata/gzs/types';
  interface Props {
    selectId : string;
    showCheckBox : Boolean;
  }
  interface GzsOptionsType {
    id : number | string;
    title : string;
    parentCode : string;
    treeCode : string;
    temptreeCode : string;
    parentId : number | string;
    children : GzsOptionsType[];
  }
  const props = defineProps<Props>();
  // 定义事件类型
  interface Emits {
    (e : 'onSave', data : []) : void;
    (e : 'onClick', data : string) : void;
  }
  const gzsxTableRef = ref(null);
  const emit = defineEmits<Emits>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const defaultProps = ref({
    children: 'children',
    label: 'title'
  })
  const loading = ref(true);
  const gzsxList = ref<GzsVO[]>([]);
  const initFormData : GzsForm = {
    id: undefined,
    title: undefined,
    parentCode: undefined,
  }
  const data = reactive<PageData<GzsForm, GzsQuery>>({
    form: { ...initFormData },
    queryParams: {
      title: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      title: [
        { required: true, message: "事项名称不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  const loadTreeData = async (query ?: GzsQuery) => {
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTreeCode<GzsOptionsType>(res.data, 'treeCode', 'parentCode');
    if (data) {
      gzsxList.value = data;
      // 数据加载完成后，自动选中第一个子节点
      nextTick(() => {
        selectFirstChildNode();
      });
    }
    loading.value = false;
  }


  const handleNodeClick = (data, node) => {
    // 这里可以添加其他处理逻辑
    console.log('点击的节点数据:', data.id);
    emit("onClick", data);
  }

  const selectFirstChildNode = () => {
    console.log("进入")
    // 递归查找第一个子节点
    const findFirstChild = (nodes) => {
      if (!nodes || nodes.length === 0) return null;
      // 如果第一个节点有子节点，继续查找子节点中的第一个
      if (nodes[0].children && nodes[0].children.length > 0) {
        return findFirstChild(nodes[0].children);
      }
      // 否则返回第一个节点
      return nodes[0];
    };
    // 找到第一个子节点
    const firstChild = findFirstChild(gzsxList.value);
    if (firstChild) {
      // 选中该节点
      gzsxTableRef.value.setCurrentKey(firstChild.id);
      emit("onClick", firstChild);
    }
  }
  // 显式暴露方法给父组件
  defineExpose({
    loadTreeData,
    selectFirstChildNode
  });
  onMounted(() => {
    loadTreeData();
  });
</script>

<style>
  .treeMainCss {
    min-height: 568px;
    max-height: 860px;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: overlay;
  }
</style>
