// 附件袋信息
export interface AttachmentBagInfo {
  id ?: string | number;
  catalogNumber : string; // 目录号
  itemName : string; // 物品名称
  quantity : number; // 数量
  gzjzId : number | string;//公证卷宗ID
}

// 特殊流程记录
export interface SpecialProcessRecord {
  id ?: string | number;
  applicationType : string; // 申请类型
  applicationTime : string; // 申请时间
  status : string; // 状态
  initiator : string; // 发起人
}

// 归档表单数据
export interface ArchiveFormData {
  id : string | number; // 卷宗ID
  sftg : string; // 1通过
  lcyj : string; // 流程意见
  dahh : string; // 档案盒号
  lczt : string;
  bgqx : string; // 档案期限
  dabh : number; // 档案编号
  sfmj : string; // 是否密卷
  jyfs : string; // 结案方式
  gzGzjzArchiveAttachmentsBos : AttachmentBagInfo[]; // 附件袋信息
  specialProcessRecords : SpecialProcessRecord[]; // 特殊流程记录
}

// 归档查询参数
export interface ArchiveQuery {
  pageNum : number;
  pageSize : number;
  gzjzId ?: string | number;
}

// 归档响应数据
export interface ArchiveResponse {
  code : number;
  message : string;
  data ?: any;
}
