<template>
  <div class="camera-container">
    <!-- <h3 class="title">摄像头拍照上传</h3> -->

    <!-- 摄像头区域 -->
    <div class="camera-wrapper" v-if="!showPreview">
      <video ref="videoEl" autoplay class="camera-preview"></video>
      <div class="camera-actions">
        <el-button type="primary" @click="captureImage">拍照</el-button>
        <el-button type="info" @click="switchCamera">切换摄像头</el-button>
      </div>
    </div>

    <!-- 预览区域 -->
    <div class="preview-wrapper" v-else>
      <img ref="previewImg" :src="imageUrl" class="preview-image" />
      <div class="preview-actions">
        <el-button type="primary" @click="uploadImage">确定</el-button>
        <el-button type="info" @click="retakePhoto">重新拍摄</el-button>
      </div>
    </div>

    <!-- 上传进度 -->
    <el-progress v-if="uploading" :percentage="uploadProgress" status="active"></el-progress>

    <!-- 结果提示 -->
    <el-alert v-if="uploadResult" :title="uploadResult.message" :type="uploadResult.type" :closable="false" show-icon />
  </div>
</template>

<script setup name="PzTemp" lang="ts">
  import { uploadFile } from '@/api/gongzheng/dsr/dsrxxZrrZp';
  import { DsrxxZrrZpUploadForm, DsrxxZrrZpQuery } from '@/api/gongzheng/dsr/dsrxxZrrZp/types';
  const initFormData : DsrxxZrrZpUploadForm = {
    zp: undefined,
    dsrId: undefined
  }
  interface Props {
    dsrId : String | Number;
    dialigEdit : boolean;
  }
  const props = defineProps<Props>();
  // 状态变量
  const videoEl = ref(null);
  const previewImg = ref(null);
  const imageUrl = ref('');
  const showPreview = ref(false);
  const uploading = ref(false);
  const uploadProgress = ref(0);
  const uploadResult = ref(null);
  const stream = ref(null);
  const useFrontCamera = ref(true);
  const data = reactive<PageData<DsrxxZrrZpUploadForm, DsrxxZrrZpQuery>>({
    form: { ...initFormData }
  });
  const { form } = toRefs(data);
  // 初始化摄像头
  const initCamera = async () => {
    try {
      const constraints = {
        video: {
          facingMode: useFrontCamera.value ? 'user' : 'environment'
        }
      };

      stream.value = await navigator.mediaDevices.getUserMedia(constraints);
      videoEl.value.srcObject = stream.value;
    } catch (error) {
      console.error('摄像头访问失败:', error);
      uploadResult.value = {
        type: 'error',
        message: '无法访问摄像头，请确保已授予权限'
      };
    }
  };

  // 拍照
  const captureImage = () => {
    const canvas = document.createElement('canvas');
    const {
      videoWidth,
      videoHeight
    } = videoEl.value;

    // 设置Canvas尺寸与视频相同
    canvas.width = videoWidth;
    canvas.height = videoHeight;

    // 绘制当前视频帧
    const ctx = canvas.getContext('2d');
    ctx.drawImage(videoEl.value, 0, 0, canvas.width, canvas.height);

    // 转换为DataURL
    imageUrl.value = canvas.toDataURL('image/jpeg');
    showPreview.value = true;
  };

  // 切换摄像头
  const switchCamera = async () => {
    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop());
    }

    useFrontCamera.value = !useFrontCamera.value;
    await initCamera();
  };

  // 重新拍摄
  const retakePhoto = () => {
    showPreview.value = false;
  };
  // 定义事件
  const emits = defineEmits<{
    (e : 'update-count', []) : void;
  }>();
  // 上传图片
  const uploadImage = async () => {
    if (!imageUrl.value) return;
    console.log(imageUrl.value)
    emits("update-count", imageUrl.value);
  };


  // 生命周期钩子
  onMounted(() => {
    initCamera();
  });

  onUnmounted(() => {
    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop());
    }
  });
</script>

<style scoped>
  .camera-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    text-align: center;
  }

  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .camera-wrapper,
  .preview-wrapper {
    margin-bottom: 20px;
  }

  .camera-preview,
  .preview-image {
    max-width: 100%;
    height: auto;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .camera-actions,
  .preview-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
  }
</style>
