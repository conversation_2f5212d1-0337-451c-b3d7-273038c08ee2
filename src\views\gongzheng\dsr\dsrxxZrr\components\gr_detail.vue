<template>
  <div>
    <el-drawer :title="props.title" v-model="props.dialog" append-to-body size="100%" @close="cancel">
      <el-card class="no-padding-card dateil-card-main" v-if="props.dialigEdit">
        <div slot="header" class="clearfix dateil-card-heard">
          <span>客户信息</span>
        </div>
        <el-button type="primary" link @click="savePhoto">拍照</el-button>
        <!-- <el-button type="primary" link @click="uploadImg">上传</el-button> -->
        <el-button type="primary" link @click="delPics">批量删除图片</el-button>
        <el-button type="primary" link disabled>显示指纹</el-button>
        <el-button type="primary" link disabled>指纹识别</el-button>
        <el-button type="primary" link disabled>录入指纹</el-button>
        <!-- 当事人照片-->
        <DsrZp v-if="props.dialog" ref="dsrListRef" :vo="form" :dialigEdit="props.dialigEdit"
          @update-count="handleUpdateGrxxzpIds"></DsrZp>
      </el-card>
      <!--证件列表-->
      <Zjlb v-if="props.dialog" ref="zjlbRef" :vo="props.formPro"></Zjlb>
      <el-tabs style="margin-top:10px" v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="基本资料" name="first">
          <Grxx v-if="props.dialog" ref="grxxRef" :vo="props.formPro" :dialigEdit="props.dialigEdit"
            @update-count="handleUpdateGrxx"></Grxx>
        </el-tab-pane>
        <el-tab-pane label="办证记录" name="second">
          <BzList v-if="props.dialog" :dsr-info="props.formPro"></BzList>
        </el-tab-pane>
        <el-tab-pane label="证据材料" name="third">
          <ZjclList ref="zjclListRef" :dialog="props.dialog"></ZjclList>
        </el-tab-pane>
        <el-tab-pane label="身份识别记录" name="fourth">
          <SfsblList :dialog="props.dialog"></SfsblList>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="props.dialigEdit" :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 拍照 -->
    <el-drawer :title="dialog5.title" v-model="dialog5.visible" append-to-body size="100%" @close="cancelPoto">
      <el-row :gutter="10" style="height: 100%;">
        <el-col :span="12">
          <el-card style="height: 100%;">
            <template #header>
              <span>当事人列表</span>
            </template>
            <el-table>
              <el-table-column type="index" width="50" align="center" />
              <el-table-column label="姓名" width="120" align="center" />
              <el-table-column label="证件类型" width="120" align="center" />
              <el-table-column label="证件号码" width="120" align="center" />
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card style="height: 100%;">

          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="takePhoto">拍 照</el-button>
          <el-button>上传图片</el-button>
          <el-button>生成信息表</el-button>
          <el-button>认证/识别记录</el-button>
          <el-button>生成现场记录</el-button>
          <el-button>设置签署位</el-button>
          <el-button @click="cancelPoto">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { listDsrxxZrr, getDsrxxZrr, delDsrxxZrr, addDsrxxZrr, updateDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
  import { DsrxxZrrVO, DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  import { listDsrxxFrhzz, getDsrxxFrhzz, delDsrxxFrhzz, addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import Grxx from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/gr_xx.vue'
  import Zjlb from '@/views/gongzheng/dsr/dsrxxZrr/components/zjlb.vue'
  import Dwxx from '@/views/gongzheng/dsr/dsrxxZrr/components/dw/dw_xx.vue'
  import DsrZp from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/dsr_zp.vue'
  import Pz from '@/components/Gongzheng/pz/pz.vue'
  import BzList from '@/views/gongzheng/gongzheng/components/bz_list.vue'
  import ZjclList from '@/views/gongzheng/gongzheng/components/zjcl_list.vue'
  import SfsblList from '@/views/gongzheng/gongzheng/components/sfsb_list.vue'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_lb, gz_gr_zjlx, gz_dsr_hyzt } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_dsr_hyzt'));
  const grxxRef = ref<InstanceType<typeof Grxx> | null>(null);
  const zjlbRef = ref<InstanceType<typeof Zjlb> | null>(null);
  const dwxxRef = ref<InstanceType<typeof Dwxx> | null>(null);
  const dsrListRef = ref<InstanceType<typeof DsrZp> | null>(null);
  const pzRef = ref<InstanceType<typeof Pz> | null>(null);

  const zjclListRef = ref<InstanceType<typeof ZjclList> | null>(null);

  const dsrxxZrrList = ref<DsrxxZrrVO[]>([]);
  const buttonLoading = ref(false);
  const buttonLoading2 = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const activeName = ref("first");
  const activeName2 = ref("first");
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxZrrFormRef = ref<ElFormInstance>();
  const zsrPhotoIds = ref([]);
  interface Props {
    dialog : boolean;
    dialigEdit : boolean;
    title : string;
    formPro : DsrxxZrrVO;
  }
  const props = defineProps<Props>();

  const photoRef = ref();

  const dialigEdit = ref(true);
  const dialigEdit2 = ref(true);
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog3 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: undefined,
    ywm: undefined,
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined
  }
  const initDwxxFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: "1",
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });

  const { queryParams, form } = toRefs(data);
  /** 单位客户*/
  const data2 = reactive<DsrxxFrhzzForm, DsrxxFrhzzQuery>({
    form2: { ...initDwxxFormData },
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { queryParams2, form2 } = toRefs(data2);

  /** 查询当事人-基本信息-自然人信息列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDsrxxZrr(queryParams.value);
    dsrxxZrrList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    // dialog.visible = false;
    emits('close', '');
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxZrrFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    if (queryParams.value.dsrlb === "1") {
      getList();
    } else {
      getList2();
    }
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxZrrVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = (type) => {
    dialigEdit.value = true;
    if (type == 1) {
      reset();
      dialog.visible = true;
      dialog.title = "新增个人客户";
    } else if (type == 2) {
      reset2();
      dialog2.visible = true;
      dialog2.title = "新增单位客户";
    }
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrxxZrrVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZrr(_id);
    Object.assign(form.value, res.data);
    dialigEdit.value = true;
    dialog.visible = true;
    dialog.title = "编辑个人客户";
  }
  const handleDetail = async (row ?: DsrxxZrrVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZrr(_id);
    Object.assign(form.value, res.data);
    dialigEdit.value = false;
    dialog.visible = true;
    dialog.title = "查看个人客户信息";
  }

  /** 个人客户提交按钮 */
  const submitForm = async () => {
    buttonLoading.value = true;
    form.value.dsrlb = "1";
    if (form.value.id) {
      form.value.id = form.value.dsrId
      await updateDsrxxZrr(form.value).finally(() => buttonLoading.value = false);
    } else {
      await addDsrxxZrr(form.value).finally(() => buttonLoading.value = false);
    }
    proxy?.$modal.msgSuccess("操作成功");
    emits('close', '');
    // queryParams.value.dsrlb = "1";
    // handleQuery();
  }
  /** 个人单位删除按钮操作 */
  const handleDelete = async (row ?: DsrxxZrrVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除个人客户编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxZrr(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }
  //单位客户提交
  const submitForm2 = async () => {
    buttonLoading2.value = true;
    form2.value.dsrlb = "2";
    if (form2.value.id) {
      await updateDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    } else {
      await addDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    }
    proxy?.$modal.msgSuccess("操作成功");
    dialog2.visible = false;
    queryParams.value.dsrlb = "2";
    handleQuery();
  }

  /** 单位客户修改按钮操作 */
  const handleUpdate2 = async (row ?: DsrxxFrhzzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxFrhzz(_id);
    Object.assign(form2.value, res.data);
    dialigEdit2.value = true;
    dialog2.visible = true;
    dialog2.title = "编辑单位客户";
  }
  //单位客户查看详情
  const handleDetail2 = async (row ?: DsrxxFrhzzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxFrhzz(_id);
    Object.assign(form2.value, res.data);
    dialigEdit2.value = false;
    dialog2.visible = true;
    dialog2.title = "查看单位客户信息";
  }
  /** 表单重置 */
  const reset2 = () => {
    form2.value = { ...initDwxxFormData };
  }


  /**单位客户 删除按钮操作 */
  const handleDelete2 = async (row ?: DsrxxFrhzzVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除单位客户编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxFrhzz(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList2();
  }
  /** 导出按钮操作 */
  const handleExport = () => {
    if (queryParams.value.dsrlb === "1") {
      proxy?.download('dsr/dsrxxZrr/export', {
        ...queryParams.value
      }, `个人客户信息_${new Date().getTime()}.xlsx`)
    } else {
      proxy?.download('dsr/dsrxxFrhzz/export', {
        ...queryParams.value
      }, `单位客户信息_${new Date().getTime()}.xlsx`)
    }
  }
  const handleImportDb = (type) => {
    proxy?.$modal.msgWarning("开发中");
  }
  const handleImport = () => {
    dialog4.visible = true;
    dialog4.title = "导入";
  }

  const handleClick = (tab, event) => {
    console.log(tab, event);
  }
  /** 复制个人客户信息*/
  const handleCopy = async (row ?: DsrxxZrrVO) => {
    if (navigator.clipboard) {
      const txt = row.xm + "," + (row.xb === "1" ? "男" : "女") + "," + row.csrq + "出生,证件号码:" + row.zjhm + ",住址:" + row.zz
      await navigator.clipboard.writeText(txt);
      proxy?.$modal.msgSuccess("当事人信息已复制到剪切板");
    } else {
      proxy?.$modal.msgError("当前浏览器不支持该功能");
    }

  }
  /** 复制 单位客户信息*/
  const handleCopy2 = async (row ?: DsrxxFrhzzVO) => {
    if (navigator.clipboard) {
      const txt = row.dwmc + "证件号码:" + row.zjhm;
      await navigator.clipboard.writeText(txt);
      proxy?.$modal.msgSuccess("当事人信息已复制到剪切板");
    } else {
      proxy?.$modal.msgError("当前浏览器不支持该功能");
    }

  }
  /** 取消按钮 */
  const cancel2 = () => {
    dialog2.visible = false;
  }

  const handleUpdateGrxx = (vo : DsrxxZrrVO) => {
    if (vo) {
      form.value = vo;
      console.log(form.value)
    }
  };
  const handleUpdateGwxx = (vo : DsrxxFrhzzVO) => {
    if (vo) {
      form2.value = vo;
      console.log(form2.value)
    }
  };
  const handleUpdateGrxxzpIds = (ids) => {
    console.log(ids)
    if (ids && ids.length > 0) {
      zsrPhotoIds.value = ids;
    }
  }



  /** 查询单位客户列表 */
  const getList2 = async () => {
    loading.value = true;
    const res = await listDsrxxFrhzz(queryParams2.value);
    dsrxxZrrList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }
  /** 拍照*/
  const savePhoto = () => {
    // dsrListRef.value?.savePhoto(form.value?.id);
    if (form.value.id) {
      dialog5.visible = true;
      dialog5.title = "拍照";
      console.log(form.value.id)
    } else {
      proxy?.$modal.msgError("请先保存基础资料");
    }

  }

  const takePhoto = () => {
    // photoRef.value?.takePhoto((img: string) => {
    //   console.log('拍照成功', img);

    // });
  }

  const cancelPoto = () => {
    dialog5.visible = false;
    // photoRef.value?.resetPhoto();
  }

  /** 上传*/
  const uploadImg = () => {
    dsrListRef.value?.uploadImg(form.value?.id);
  }
  /** 批量删除*/
  const delPics = () => {
    dsrListRef.value?.delPics(zsrPhotoIds.value);
  }
  /** 上传成功后刷新图片列表 */
  const handleUpdatePhoto = () => {
    dsrListRef.value?.getList();
  }

  const emits = defineEmits<{
    (e : 'close', vo) : void;
  }>();
  onMounted(() => {
    // form.value = props.formPro;
  });
</script>

<style scoped>
</style>
