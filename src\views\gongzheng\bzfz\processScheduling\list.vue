<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" size="small">
            <el-form-item label="卷宗号" prop="jzbh" style="width: 220px;">
              <el-input v-model="queryParams.jzbh" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证事项" prop="gzsxMc" style="width: 220px;">
              <el-input v-model="queryParams.gzsxMc" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.gzsNf" placeholder="年份" clearable
                style="max-width: 80px; margin-right: 4px;">
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              <el-select v-model="queryParams.gzsZh" placeholder="字号" clearable
                style="max-width: 140px; margin-right: 4px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.gzsLs" clearable @keyup.enter="handleQuery" style="max-width: 60px" />号
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm" style="width: 220px;">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable>
                <el-option v-for="item in gzy" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="流程状态" prop="lczt"  style="width: 380px;">
              <el-select style="width:340px" multiple collapse-tags :max-collapse-tags="3" collapse-tags-tooltip
                v-model="queryParams.lczt" placeholder="请选择" clearable>
                <el-option v-for="dict in filteredLczt" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['gz:sl:query']">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!-- 操作按钮 -->
      <!-- <el-row :gutter="10" class="mb-2">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Search" @click="showSearch = !showSearch">
            {{ showSearch ? '隐藏' : '显示' }}搜索
          </el-button>
        </el-col>
      </el-row> -->

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="gzjzJbxxList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column label="操作" align="center" width="120" fixed="left">
          <template #default="scope">
            <el-dropdown>
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleChangeProcess(scope.row)">
                    <el-icon>
                      <Operation />
                    </el-icon>流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleChangeNumber(scope.row)">
                    <el-icon>
                      <Document />
                    </el-icon>编号
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleChangeIssueTime(scope.row)">
                    <el-icon>
                      <Clock />
                    </el-icon>出证时间
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column label="卷宗号" align="center" prop="jzbh" width="120" show-overflow-tooltip />
        <el-table-column label="公证事项" align="center" prop="gzsxMc" width="200" show-overflow-tooltip />
        <el-table-column label="公证书编号" align="center" prop="gzsBh" width="220"  show-overflow-tooltip/>
        <el-table-column label="公证类别" align="center" prop="lb" width="100"  show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="gz_gzlb" :value="scope.row.lb" />
          </template>
        </el-table-column>
        <el-table-column label="公证书" align="center" prop="gzsMc"  show-overflow-tooltip/>
        <el-table-column label="公证员" align="center" prop="gzyxm" width="120"  show-overflow-tooltip/>
        <el-table-column label="流程" align="center" prop="lczt" width="120"  show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="gz_sl_lczt" :value="scope.row.lczt" />
          </template>
        </el-table-column>
        <el-table-column label="出证时间" align="center" prop="czsj" width="120"  show-overflow-tooltip>
          <!-- <template #default="scope">
            {{ formatDate(scope.row, null, scope.row.czsj) }}
          </template> -->
        </el-table-column>
        <el-table-column label="受理时间" align="center" prop="slrq" width="120"  show-overflow-tooltip>
          <!-- <template #default="scope">
            {{ formatDate(scope.row, null, scope.row.slrq) }}
          </template> -->
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 流程修改弹窗 -->
    <el-dialog v-model="processDialog.visible" title="修改流程" width="500px" append-to-body>
      <el-form ref="processFormRef" :model="processForm" label-width="120px">
        <el-form-item label="原流程:" prop="origLczt">
          <el-select v-model="processForm.origLczt" placeholder="请选择流程" style="width: 100%" disabled>
            <el-option v-for="dict in filteredLczt" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="修改流程:" prop="lczt">
          <el-select v-model="processForm.lczt" placeholder="请选择流程" style="width: 100%">
            <el-option v-for="dict in filteredLczt" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSaveProcess">保存</el-button>
          <el-button @click="processDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编号修改弹窗 -->
    <el-dialog v-model="numberDialog.visible" title="编辑公证书编号" width="600px" append-to-body>
      <el-form ref="numberFormRef" :model="numberForm" label-width="120px">
        <!-- 公证事项选择（当有多条记录时显示） -->
        <!-- <el-form-item v-if="numberForm.gzsbmVoList && numberForm.gzsbmVoList.length > 1" label="公证事项:"
          prop="gzjzGzsxId">
          <el-select v-model="numberForm.gzjzGzsxId" placeholder="请选择公证事项" style="width: 100%"
            @change="handleGzsxChange">
            <el-option v-for="item in numberForm.gzsbmVoList" :key="item.gzjzGzsxId"
              :label="item.gzjzGzsx || item.gzjzGzsxId" :value="item.gzjzGzsxId" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="公证书号头:" prop="gzsbh">
          <div class="gzsbh_line">
            <el-select v-model="numberForm.nf" placeholder="年份" style="width: 25%;">
              <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
            <el-select v-model="numberForm.zhCode" placeholder="字号" style="width: 40%;">
              <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
            <el-input v-model="numberForm.ls" placeholder="流水号" style="width: 25%;" />
            <span style="margin-left: 8px; white-space: nowrap;">号</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSaveNumber">保存</el-button>
          <el-button @click="numberDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出证时间修改弹窗 -->
    <el-dialog v-model="timeDialog.visible" title="修改出证时间" width="500px" append-to-body>
      <el-form ref="timeFormRef" :model="timeForm" label-width="120px">
        <el-form-item label="原出证时间:" prop="origCzsj">
          <el-input v-model="timeForm.origCzsj" readonly />
        </el-form-item>
        <el-form-item label="修改出证时间:" prop="czsj">
          <el-date-picker v-model="timeForm.czsj" type="date" placeholder="选择日期" style="width: 100%"
            format="YYYY年MM月DD日" value-format="YYYY-MM-DD" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSaveTime">保存</el-button>
          <el-button @click="timeDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzChangeList" lang="ts">
  import { provide, nextTick } from 'vue';
  import { genYearOptions, clearEmptyProperty, dictMapFormat, genRecentDate } from '@/utils/ruoyi';
  import {
    changeProcess, changeCzsj, changeGzsbm
  } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import {
    listByChange
  } from '@/api/gongzheng/gongzheng/gzjzGzsx';
  import type { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm, ChangeProcessBo, ChangeGzsbmBo, ChangeCzsjBo } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

  import type { GzjzGzsxChangeQuery, GzjzGzsxChangeVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { ArrowDown, Operation, Document, Clock } from '@element-plus/icons-vue';

  import Sl from '@/views/gongzheng/gongzheng/components/sl/sl.vue';
  import eventBus from '@/utils/eventBus';

  const currentRecord = ref<GzjzJbxxVO>(null);
  const gzsbh_years = genYearOptions(2015);

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzs_zh, gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt', 'gz_gzlb'));
  const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

  // 过滤后的流程状态，只保留指定的流程
  const filteredLczt = computed(() => {
    if (!gz_sl_lczt.value) return [];
    // const allowedProcesses = ['受理', '审批', '制证', '发证', '归档', '上架'];
    // return gz_sl_lczt.value.filter(item => allowedProcesses.includes(item.label));
    return gz_sl_lczt.value;
  });

  const gzjzJbxxList = ref<GzjzGzsxChangeVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');

  // 当前编辑的记录ID，用于传递给sl组件
  const currentRecordId = ref<string | number | null>(null);
  const gzlbId = ref<string | number | null>(null);
  // 流程状态
  const lczt = ref<string | number | null>(null);
  const queryFormRef = ref<ElFormInstance>();

  // 弹窗控制
  const processDialog = ref({
    visible: false
  });
  const numberDialog = ref({
    visible: false
  });
  const timeDialog = ref({
    visible: false
  });

  // 表单数据
  const processForm = ref<ChangeProcessBo>({
    id: '',
    origLczt: '',
    lczt: ''
  });
  const numberForm = ref<ChangeGzsbmBo & { gzsbmVoList ?: any[] }>({
    gzjzId: '',
    gzjzGzsxId: '',
    origId: 0,
    gzsbh: '',
    nf: '',
    zhCode: '',
    ls: 0,
    gzsbmVoList: []
  });
  const timeForm = ref<ChangeCzsjBo>({
    id: '',
    origCzsj: '',
    czsj: ''
  });

  // 查询参数
  const queryParams = ref<GzjzGzsxChangeQuery>({
    pageNum: 1,
    pageSize: 10,
    jzbh: undefined,
    lczt: ['01', '02', '03', '04', '05', '06', '07', '08', '09'],
    gzybm: undefined,
    gzsxMc: undefined,
    gzsNf: undefined,
    gzsZh: undefined,
    gzsLs: undefined,
    sfzf: '0',
    params: {
    }
  });







  /** 取消按钮 */
  const cancel = () => {
    dialog.visible = false;
    currentRecordId.value = null; // 清空当前记录ID
    gzlbId.value = null; // 清空当前记录ID
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    // queryParams.value.params = {};
    console.log(queryParams.value)
    queryParams.value.lczt = ['01', '02', '03', '04', '05', '06', '07', '08', '09'],
    queryParams.value.gzsNf = null;
    queryParams.value.gzsZh = null;
    queryParams.value.gzsLs = null;
    queryParams.value.sfzf = '0';
    handleQuery();
  };



  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzjzJbxxVO[]) => {
    ids.value = selection.map((item : GzjzJbxxVO) => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  };

  /** 日期格式化 */
  const formatDate = (row : any, column : any, cellValue : string) => {
    if (cellValue) {
      const date = new Date(cellValue), year = date.getFullYear(),
        month = String(date.getMonth() + 1).padStart(2, '0'),
        day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
      // return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
    }
    return '';
  };

  const getList = async () => {
    loading.value = true;
    try {
      const cleanParams = clearEmptyProperty(queryParams.value);
      const lczt = cleanParams.lczt;
      // 处理流程状态
      if (lczt && Array.isArray(lczt)) {
        cleanParams.lczt = lczt.join(',');
      }
      const response = await listByChange(cleanParams);
      gzjzJbxxList.value = response.rows || [];
      total.value = response.total || 0;
    } catch (error) {
      console.error('获取列表失败:', error);
      ElMessage.error('获取列表失败');
    } finally {
      loading.value = false;
    }
  };

  /** 流程修改 */
  const handleChangeProcess = (row : GzjzGzsxChangeVO) => {
    processForm.value = {
      id: row.gzjzId,
      origLczt: row.lcztId + '',
      lczt: row.lcztId + ''
    };
    processDialog.value.visible = true;
  };

  /** 编号修改 */
  const handleChangeNumber = (row : GzjzGzsxChangeVO) => {
    numberForm.value = {
      gzjzId: row.gzjzId,
      gzjzGzsxId: row.id || '',
      origId: row.gzsbmId || 0,
      gzsbh: row.gzsBh,
      nf: row.nf || '',
      zhCode: Number(row.zhCode) + '' || '',
      ls: row.ls || 0
    };
    numberDialog.value.visible = true;
  };

  /** 出证时间修改 */
  const handleChangeIssueTime = (row : GzjzGzsxChangeVO) => {
    // 检查原出证时间是否存在
    // if (!row.czsj) {
    //   ElMessage.warning('该记录没有出证时间，无法修改');
    //   return;
    // }

    timeForm.value = {
      id: row.gzjzId,
      origCzsj: row.czsj || '',
      czsj: row.czsj || ''
    };
    timeDialog.value.visible = true;
  };

  /** 保存流程修改 */
  const handleSaveProcess = async () => {
    try {
      await changeProcess(processForm.value);
      ElMessage.success('流程修改成功');
      processDialog.value.visible = false;
      getList();
    } catch (error) {
      console.error('流程修改失败:', error);
      ElMessage.error('流程修改失败');
    }
  };

  /** 保存编号修改 */
  const handleSaveNumber = async () => {
    try {
      const submitData = {
        ...numberForm.value
      };
      await changeGzsbm(submitData);
      ElMessage.success('编号修改成功');
      numberDialog.value.visible = false;
      getList();
    } catch (error) {
      console.error('编号修改失败:', error);
      // ElMessage.error('编号修改失败');
    }
  };
  /** 公证事项切换处理 */
  const handleGzsxChange = (gzjzGzsxId : string) => {
    // 根据选择的公证事项ID，从gzsbmVoList中找到对应的记录
    const selectedGzsbmVo = numberForm.value.gzsbmVoList.find(item => item.gzjzGzsxId === gzjzGzsxId);

    if (selectedGzsbmVo) {
      // 更新origId和公证书编号信息
      numberForm.value.origId = selectedGzsbmVo.id || 0;
      numberForm.value.gzsbh = selectedGzsbmVo.gzsbh || '';
      numberForm.value.nf = selectedGzsbmVo.nf || '';
      numberForm.value.zhCode = selectedGzsbmVo.zhCode || '';
      numberForm.value.ls = selectedGzsbmVo.ls || 0;
    }
  };
  /** 保存出证时间修改 */
  const handleSaveTime = async () => {
    try {
      await changeCzsj(timeForm.value);
      ElMessage.success('出证时间修改成功');
      timeDialog.value.visible = false;
      getList();
    } catch (error) {
      console.error('出证时间修改失败:', error);
      ElMessage.error('出证时间修改失败');
    }
  };

  /**====================================== 事件总线 =======================================**/
  // 触发受理列表更新
  const slListUpdate = (val : any) => {
    getList()
  }
  eventBus.on('sl:list:update', slListUpdate)

  // 提供给子组件的数据和方法
  provide('currentRecordId', currentRecordId);
  provide('gzlbId', gzlbId);
  provide('currentRecord', currentRecord);
  provide('refreshList', getList);

  onMounted(() => {
    getList();
  });

  onBeforeUnmount(() => {
    // 可以考虑组件移除时也移除订阅的事件
    // eventBus.off('sl:list:update', slListUpdate)
  })
</script>

<style scoped>
  .el-dropdown {
    vertical-align: top;
  }

  .el-dropdown+.el-dropdown {
    margin-left: 15px;
  }

  .el-icon-arrow-down {
    font-size: 12px;
  }

  .review-content {
    padding: 20px 0;
  }

  .review-section {
    margin-bottom: 30px;
  }

  .review-section h4 {
    margin-bottom: 15px;
    color: #303133;
    font-weight: 600;
  }

  .checkbox-group {
    margin: 15px 0;
    padding-left: 20px;
  }

  .checkbox-group .el-checkbox {
    display: block;
    margin-bottom: 12px;
    line-height: 1.6;
  }

  .review-section p {
    margin-top: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }

  .confirmation-content {
    padding: 20px 0;
  }

  .confirmation-content p {
    font-size: 16px;
    color: #606266;
    margin-bottom: 20px;
    text-align: center;
  }

  .gzsbh_line {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    width: 100%;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 12px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
