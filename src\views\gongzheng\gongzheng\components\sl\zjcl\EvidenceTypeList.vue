<template>
  <div class="zjtable">
    <div class="header">
      <span>证据类型</span>
      <el-button type="primary" size="small" @click="$emit('add')">新增证据</el-button>
      <!-- <el-button type="primary" size="small" @click="$emit('addDsr')">引用当事人</el-button> -->
    </div>
    <el-table border :data="types" style="width: 100%" highlight-current-row :row-class-name="rowClassName"
      @row-click="onRowClick" :loading="loading" :height="400">
      <el-table-column label="操作" min-width="40">
        <template #default="scope">
          <el-button link type="warning" size="small" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="zmmc" label="证据名称" min-width="120" />
      <el-table-column prop="dsrMc" label="当事人" min-width="180">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="handleSelectDsr(scope.row)">
            请选择
          </el-button>
          {{scope.row.dsrMc}}
        </template>
      </el-table-column>
      <el-table-column prop="num" label="数量" width="100" />
    </el-table>
    <div class="pagination">
      <el-pagination background layout="prev, pager, next" :page-size="pagination.pageSize"
        :current-page="pagination.pageNum" :total="pagination.total" @current-change="$emit('page-change', $event)" />
    </div>

    <el-dialog v-model="visible" title="选择当事人" width="500px">
      <el-form ref="formRef" :model="formData" label-width="120px">
        <el-form-item label="当事人" prop="dsr">
          <el-select v-model="formData.dsr" filterable placeholder="请选择" @change="handledChangeDsr">
            <el-option v-for="item in dsrList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleQrDsr">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { delEvidenceType } from '@/api/gongzheng/gongzheng/zjcl/index';
  import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr/index';
  import type { GzjzDsrVO, GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types'


  const props = defineProps<{
    types : any[];
    selectedTypeId : string;
    loading : boolean;
    pagination : { pageNum : number; pageSize : number; total : number };
    gzjzId : number;
  }>();
  const emits = defineEmits(['select', 'add', 'page-change', 'selectDsrName']);
  const visible = ref(false);
  const formData = ref({
    dsr: null
  });
  const dsrList = ref([
  ]);
  const onRowClick = (row : any) => {
    emits('select', row.id);
  };
  const rowClassName = (row : { row : any }) => {
    return row.row.id === props.selectedTypeId ? 'selected-row' : '';
  };
  const handleDelete = async (row : { row : any }) => {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      type: 'warning'
    }).then(async () => {
      const res = await delEvidenceType(row.id);
      emits('del', row.id);
    });
  }

  //打开选择当事人
  const handleSelectDsr = () => {
    formData.value.dsr = null;
    visible.value = true;
  }
  const handledChangeDsr = (value) => {
    const dsr = dsrList.value.find(t => t.id === value);
    const dsrName = dsr.name
    emits('selectDsrName', props.selectedTypeId, dsrName);
  }
  //确认选择 当事人
  const handleQrDsr = () => {
    emits('selectDsr', formData.value.dsr);
    visible.value = false;
  }

  const handledGzjzDsrByGzjz = async () => {
    const query : GzjzDsrQuery = {
      gzjzId: props.gzjzId,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzjzDsrByGzjz(query);
    if (res.code === 200) {
      dsrList.value = res.rows || []
    } else {
      ElMessage.error('获取当事人列表失败：' + (res.msg || '未知错误'))
      dsrList.value = []
    }
  }
  onMounted(() => {
    formData.value.dsr = null;
    dsrList.value = []
    handledGzjzDsrByGzjz();
  });
</script>

<style scoped>
  .zjtable {
    padding: 10px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .selected-row {
    background: #e6f7ff !important;
  }

  .pagination {
    margin-top: 8px;
    text-align: right;
  }
</style>
