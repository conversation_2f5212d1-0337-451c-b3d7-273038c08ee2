<template>
  <div>
    空组件
  </div>
</template>

<script setup name="Demo" lang="ts">
  import { ElMessage } from 'element-plus'
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import { useRoute } from 'vue-router'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  // 定义 props 类型
  interface Props {
    modelValue ?: string | number
    title ?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '默认标题',
  });

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'success') : void
    (event : 'close') : void
  }>()

  // 监听
  watch(() => props.modelValue, (newVal) => {
    //
  })

  // 定义 data
  const myValue = ref(props.modelValue)

  // 生命周期
  onMounted(() => {
    console.log('mouted');
  });

  // 组件内自定义方法
</script>
