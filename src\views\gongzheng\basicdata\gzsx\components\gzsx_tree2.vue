<template>
  <div>
    <el-tree class="treeMainCss" ref="gzsxTableRef" :loading="loading" :data="gzsxList"
      :show-checkbox="props.showCheckBox" default-expand-all node-key="id" highlight-current :props="defaultProps"
      @node-click="handleNodeClick">
    </el-tree>
  </div>
</template>

<script setup name="GzsxTreeVsion2" lang="ts">
  import { getGzsxByGzfl, listTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO, GzsxQuery, GzsxForm } from '@/api/gongzheng/basicdata/gzsx/types';
  import { treeGzlbpz } from '@/api/gongzheng/basicdata/gzlbpz';
  import { GzlbpzVO, GzlbpzForm, GzlbpzQuery } from '@/api/gongzheng/basicdata/gzlbpz/types';
  interface Props {
    selectId : string[];
    title : string;
    showCheckBox : Boolean;
  }
  interface GzsxOptionsType {
    id : number | string;
    title : string;
    parentCode : string;
    gzsxCode : string;
    children : GzsxOptionsType[];
  }
  const props = defineProps<Props>();
  // 定义事件类型
  interface Emits {
    (e : 'onSave', data : []) : void;
    (e : 'onClick', data : string) : void;
  }
  const gzsxTableRef = ref(null);
  const emit = defineEmits<Emits>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const defaultProps = ref({
    children: 'children',
    label: 'title'
  })
  const loading = ref(true);
  const gzsxList = ref<GzlbpzVO[]>([]);
  const initFormData : GzlbpzForm = {
    id: undefined,
    title: undefined,
    parentCode: undefined,
    gzsxCode: undefined,
    gzlbValue: undefined
  }
  const data = reactive<PageData<GzlbpzForm, GzlbpzQuery>>({
    form: { ...initFormData },
    queryParams: {
      title: undefined,
      gzlbValue: '1',
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      title: [
        { required: true, message: "事项名称不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  const loadTreeData = async (query ?: queryParams) => {
    const res = await treeGzlbpz(queryParams.value);
    const data = proxy?.handleTreeCode<GzsxOptionsType>(res.rows, 'code');
    if (data) {
      gzsxList.value = data;
    }
  }

  const getList = async (_gzlbValue) => {
    loading.value = true;
    if (_gzlbValue && _gzlbValue !== '') {
      queryParams.value.gzlbValue = _gzlbValue;
    }
    await loadTreeData(queryParams);
    loading.value = false;
  }
  const getList2 = async (_gzlbValue) => {
    loading.value = true;
    gzsxList.value = [];
    if (_gzlbValue != null && _gzlbValue !== '') {
      queryParams.value.gzlbValue = _gzlbValue;
    }
    await loadTreeData(queryParams);
    //默认选中第一个
    selectFirstChildNode();
    loading.value = false;
  }

  const getList3 = async (name) => {
    loading.value = true;
    gzsxList.value = [];
    if (name != null && name !== '') {
      queryParams.value.title = name;
    }
    await loadTreeData(queryParams);
    //默认选中第一个
    selectFirstChildNode();
    loading.value = false;
  }
  const handleNodeClick = (data, node) => {
    // 这里可以添加其他处理逻辑
    console.log('点击的节点数据:', data.id);
    emit("onClick", data);
  }

  const selectFirstChildNode = () => {
    console.log("进入")
    // 递归查找第一个子节点
    const findFirstChild = (nodes) => {
      if (!nodes || nodes.length === 0) return null;
      // 如果第一个节点有子节点，继续查找子节点中的第一个
      if (nodes[0].children && nodes[0].children.length > 0) {
        return findFirstChild(nodes[0].children);
      }
      // 否则返回第一个节点
      return nodes[0];
    };
    // 找到第一个子节点
    const firstChild = findFirstChild(gzsxList.value);
    if (firstChild) {
      // 选中该节点
      gzsxTableRef.value.setCurrentKey(firstChild.id);
      emit("onClick", firstChild.id);
    }
  }
  // 显式暴露方法给父组件
  defineExpose({
    getList
  });
  onMounted(() => {
  });
</script>

<style>
  .treeMainCss {
    min-height: 568px;
    max-height: 860px;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: overlay;
  }
</style>
