<template>
  <div class="h-full">
    <div class="h-32px flex items-center">
      <strong>公证事项</strong>
    </div>
    <el-table :data="data" ref="gzsBhTableRef" @current-change="curRowChange" style="height: calc(100% - 32px);" size="small" border stripe highlight-current-row>
      <el-table-column type="index" label="#" width="55" align="center" />
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column prop="gzsBh" label="公证书编号" align="center" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any[]
}

const props = defineProps<Props>();

const emit = defineEmits(['currentChange'])

const gzsBhTableRef = ref<ElTableInstance>(null);

const curRow = ref<any>(null);

const curRowChange = (cur: any, old: any) => {
  curRow.value = cur;
  emit('currentChange', cur, old);
}

const getCurrentRow = () => {
  return curRow.value
}

defineExpose({
  getCurrentRow
})
</script>