<template>
  <div class="zjcl-container">
    <EvidenceMaterial :gzjzId="props.gzjzId"/>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
import EvidenceMaterial from './EvidenceMaterial.vue'
  const props = defineProps<{
    gzjzId : number;
  }>();
</script>

<style scoped>
  .zjcl-container {
    height: 100%;
    padding: 16px;
    background: #f5f7fa;
  }
</style>
