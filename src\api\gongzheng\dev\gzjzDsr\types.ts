export interface GzjzDsrVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 当事人ID
   */
  dsrId: string | number;
  /**
   * 当事人类型
   */
  dsrLx: string;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk: string;

  /**
   * 备注
   */
  remark: string;

  //============ 以下是补充属性 ===============
  /**
   * 姓名
   */
  name: string;
  /**
   * 性别
   */
  sex: string;
  /**
   * 证件类型
   */
  certificateType: string;
  /**
   * 证件号码
   */
  certificateNo: string;
  /**
   * 出生日期
   */
  birthDate: string;
  /**
   * 住址
   */
  address: string;
  /**
   * 联系电话(格式为固话“区号-号码”或手机号码)
   */
  contactTel: string;

}

export interface GzjzDsrForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 当事人ID
   */
  dsrId?: string | number;

  /**
   * 当事人类型
   */
  dsrLx?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js?: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzDsrQuery extends PageQuery {

  /**
   * 当事人ID
   */
  dsrId?: string | number;

  /**
   * 当事人类型
   */
  dsrLx?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js?: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



