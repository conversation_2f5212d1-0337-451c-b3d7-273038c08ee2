<template>
  <el-card>
    <template #header>
      <strong class="text-base">补证记录</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="js" label="角色" width="120" align="center" />
      <el-table-column prop="name" label="当事人姓名" width="120" align="center" />
      <el-table-column prop="dsrLx" label="当事人类型" width="120" align="center" />
      <el-table-column prop="zjlx" label="证件类型" width="120" align="center" />
      <el-table-column prop="zjlxhm" label="证件号码" align="center" />
      <el-table-column prop="zz" label="住址" align="center" show-overflow-tooltip />
      <el-table-column prop="dhhm" label="电话号码" width="120" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
