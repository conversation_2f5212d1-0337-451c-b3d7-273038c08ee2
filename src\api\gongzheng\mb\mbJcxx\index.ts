import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbJcxxVO, MbJcxxForm, MbJcxxQuery } from './types';

/**
 * 查询模板-模板基础信息列表
 * @param query
 * @returns {*}
 */

export const listMbJcxx = (query?: MbJcxxQuery): AxiosPromise<MbJcxxVO[]> => {
  return request({
    url: '/mb/mbJcxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-模板基础信息详细
 * @param id
 */
export const getMbJcxx = (id: string | number): AxiosPromise<MbJcxxVO> => {
  return request({
    url: '/mb/mbJcxx/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-模板基础信息
 * @param data
 */
export const addMbJcxx = (data: MbJcxxForm) => {
  return request({
    url: '/mb/mbJcxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-模板基础信息
 * @param data
 */
export const updateMbJcxx = (data: MbJcxxForm) => {
  return request({
    url: '/mb/mbJcxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-模板基础信息
 * @param id
 */
export const delMbJcxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbJcxx/' + id,
    method: 'delete'
  });
};
