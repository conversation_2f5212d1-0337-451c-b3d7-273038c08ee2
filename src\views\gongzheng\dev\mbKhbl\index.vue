<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="模板基础信息ID" prop="mbId">
              <el-input v-model="queryParams.mbId" placeholder="请输入模板基础信息ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务ID" prop="ywId">
              <el-input v-model="queryParams.ywId" placeholder="请输入业务ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户角色" prop="khjs">
              <el-input v-model="queryParams.khjs" placeholder="请输入客户角色" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户角色样式类型" prop="khjsYs">
              <el-input v-model="queryParams.khjsYs" placeholder="请输入客户角色样式类型" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户属性" prop="khsx">
              <el-input v-model="queryParams.khsx" placeholder="请输入客户属性" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="日期格式" prop="rqgs">
              <el-input v-model="queryParams.rqgs" placeholder="请输入日期格式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="xm">
              <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="民族" prop="mz">
              <el-input v-model="queryParams.mz" placeholder="请输入民族" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别" prop="xb">
              <el-input v-model="queryParams.xb" placeholder="请输入性别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="出生日期" prop="crrq">
              <el-input v-model="queryParams.crrq" placeholder="请输入出生日期" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件名称" prop="zjmc">
              <el-input v-model="queryParams.zjmc" placeholder="请输入证件名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="国际" prop="gj">
              <el-input v-model="queryParams.gj" placeholder="请输入国际" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="住址" prop="zz">
              <el-input v-model="queryParams.zz" placeholder="请输入住址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系地址" prop="lxdz">
              <el-input v-model="queryParams.lxdz" placeholder="请输入联系地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工作单位" prop="gzdw">
              <el-input v-model="queryParams.gzdw" placeholder="请输入工作单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="身前往" prop="sqw">
              <el-input v-model="queryParams.sqw" placeholder="请输入身前往" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mb:mbKhbl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mb:mbKhbl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mb:mbKhbl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mb:mbKhbl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="mbKhblList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="模板基础信息ID" align="center" prop="mbId" />
        <el-table-column label="业务ID" align="center" prop="ywId" />
        <el-table-column label="客户角色" align="center" prop="khjs" />
        <el-table-column label="客户角色样式类型" align="center" prop="khjsYs" />
        <el-table-column label="客户属性" align="center" prop="khsx" />
        <el-table-column label="日期格式" align="center" prop="rqgs" />
        <el-table-column label="姓名" align="center" prop="xm" />
        <el-table-column label="民族" align="center" prop="mz" />
        <el-table-column label="性别" align="center" prop="xb" />
        <el-table-column label="出生日期" align="center" prop="crrq" />
        <el-table-column label="证件名称" align="center" prop="zjmc" />
        <el-table-column label="国际" align="center" prop="gj" />
        <el-table-column label="住址" align="center" prop="zz" />
        <el-table-column label="联系地址" align="center" prop="lxdz" />
        <el-table-column label="工作单位" align="center" prop="gzdw" />
        <el-table-column label="身前往" align="center" prop="sqw" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mb:mbKhbl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mb:mbKhbl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改模板-客户变量对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mbKhblFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板基础信息ID" prop="mbId">
          <el-input v-model="form.mbId" placeholder="请输入模板基础信息ID" />
        </el-form-item>
        <el-form-item label="业务ID" prop="ywId">
          <el-input v-model="form.ywId" placeholder="请输入业务ID" />
        </el-form-item>
        <el-form-item label="客户角色" prop="khjs">
          <el-input v-model="form.khjs" placeholder="请输入客户角色" />
        </el-form-item>
        <el-form-item label="客户角色样式类型" prop="khjsYs">
          <el-input v-model="form.khjsYs" placeholder="请输入客户角色样式类型" />
        </el-form-item>
        <el-form-item label="客户属性" prop="khsx">
          <el-input v-model="form.khsx" placeholder="请输入客户属性" />
        </el-form-item>
        <el-form-item label="日期格式" prop="rqgs">
          <el-input v-model="form.rqgs" placeholder="请输入日期格式" />
        </el-form-item>
        <el-form-item label="姓名" prop="xm">
          <el-input v-model="form.xm" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="民族" prop="mz">
          <el-input v-model="form.mz" placeholder="请输入民族" />
        </el-form-item>
        <el-form-item label="性别" prop="xb">
          <el-input v-model="form.xb" placeholder="请输入性别" />
        </el-form-item>
        <el-form-item label="出生日期" prop="crrq">
          <el-input v-model="form.crrq" placeholder="请输入出生日期" />
        </el-form-item>
        <el-form-item label="证件名称" prop="zjmc">
          <el-input v-model="form.zjmc" placeholder="请输入证件名称" />
        </el-form-item>
        <el-form-item label="国际" prop="gj">
          <el-input v-model="form.gj" placeholder="请输入国际" />
        </el-form-item>
        <el-form-item label="住址" prop="zz">
          <el-input v-model="form.zz" placeholder="请输入住址" />
        </el-form-item>
        <el-form-item label="联系地址" prop="lxdz">
          <el-input v-model="form.lxdz" placeholder="请输入联系地址" />
        </el-form-item>
        <el-form-item label="工作单位" prop="gzdw">
          <el-input v-model="form.gzdw" placeholder="请输入工作单位" />
        </el-form-item>
        <el-form-item label="身前往" prop="sqw">
          <el-input v-model="form.sqw" placeholder="请输入身前往" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MbKhbl" lang="ts">
import { listMbKhbl, getMbKhbl, delMbKhbl, addMbKhbl, updateMbKhbl } from '@/api/gongzheng/mb/mbKhbl';
import { MbKhblVO, MbKhblQuery, MbKhblForm } from '@/api/gongzheng/mb/mbKhbl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const mbKhblList = ref<MbKhblVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const mbKhblFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MbKhblForm = {
  id: undefined,
  mbId: undefined,
  ywId: undefined,
  khjs: undefined,
  khjsYs: undefined,
  khsx: undefined,
  rqgs: undefined,
  xm: undefined,
  mz: undefined,
  xb: undefined,
  crrq: undefined,
  zjmc: undefined,
  gj: undefined,
  zz: undefined,
  lxdz: undefined,
  gzdw: undefined,
  sqw: undefined,
  remark: undefined,
}
const data = reactive<PageData<MbKhblForm, MbKhblQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    mbId: undefined,
    ywId: undefined,
    khjs: undefined,
    khjsYs: undefined,
    khsx: undefined,
    rqgs: undefined,
    xm: undefined,
    mz: undefined,
    xb: undefined,
    crrq: undefined,
    zjmc: undefined,
    gj: undefined,
    zz: undefined,
    lxdz: undefined,
    gzdw: undefined,
    sqw: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    mbId: [
      { required: true, message: "模板基础信息ID不能为空", trigger: "blur" }
    ],
    ywId: [
      { required: true, message: "业务ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询模板-客户变量列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMbKhbl(queryParams.value);
  mbKhblList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  mbKhblFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MbKhblVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加模板-客户变量";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MbKhblVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMbKhbl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改模板-客户变量";
}

/** 提交按钮 */
const submitForm = () => {
  mbKhblFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMbKhbl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMbKhbl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MbKhblVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除模板-客户变量编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMbKhbl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('mb/mbKhbl/export', {
    ...queryParams.value
  }, `mbKhbl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
