<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" fullscreen append-to-body>
    <div v-loading="modelState.loading" class="h-full">
      <el-row class="h-full" :gutter="10">
        <el-col class="h-full" :span="8">
          <GzsBhList ref="gzsxRef" :data="gzsxList" />
        </el-col>
        <el-col class="h-full" :span="16">
          <SyzList ref="syzRef" />
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">领用水印纸</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import GzsBhList from './GzsBhList.vue';
import SyzList from './SyzList.vue';
import { formatDate } from '@/utils/ruoyi';
import { useUserStore } from '@/store/modules/user'
import { updateSyzmxBatch } from '@/api/gongzheng/syz/syzmx';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '水印纸领用'
})

const emit = defineEmits(['done'])

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const { userId, nickname } = useUserStore();

const modelState = reactive({
  visible: false,
  title: '水印纸领用',
  gzjzId: undefined,
  loading: false,
  submitting: false,
})

const gzsxList = ref([])

const gzsxRef = ref(null);
const syzRef = ref(null);

const useDone = ref(false);

const initData = async () => {
  try {
    modelState.loading = true;
    const params = {
      // gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,

    }
  } catch (err: any) {
    console.error('遗嘱信息初始化失败', err)
  } finally {
    modelState.loading = false;
  }
}

const comfirmSave = async () => {
  try {
    modelState.submitting = true

    const curGzsx = gzsxRef.value.getCurrentRow();
    const curSelecteSyz = syzRef.value.getSelectedRows();

    if(!curGzsx) {
      ElMessage.warning('请先选择一个对应公证书编号的事项')
      return;
    }
    if(curSelecteSyz.length === 0) {
      ElMessage.warning('需至少选择一张水印纸')
      return;
    }

    const curDate = formatDate(new Date(), 'YYYY-MM-DD');
    const setVal = {
      gzjzId: modelState.gzjzId,
      gzsBh: curGzsx.gzsBh,
      gzsId: curGzsx.id,
      syzt: '1',
      lyrId: userId,
      lyr: nickname,
      syrId: userId,
      syr: nickname,
      lysj: curDate,
      sysj: curDate
    }

    const params = curSelecteSyz.map((item) => {
      return {
        ...item,
        ...setVal
      }
    });

    const res = await updateSyzmxBatch(params);
    if(res.code === 200) {
      ElMessage.success('领取成功');
      syzRef.value?.reload();
      useDone.value = true;
    }

  } catch (err: any) {
    console.error('遗嘱备案保存失败', err)
  } finally {
    modelState.submitting = false
  }
}

const close = () => {
  modelState.visible = false;
  closed();
}

const closed = () => {
  modelState.gzjzId = undefined;
  if(useDone.value) {
    emit('done')
    useDone.value = false;
  }
}

const open = (data?: any) => {
  modelState.visible = true
  modelState.gzjzId = data?.gzjzId || curGzjz.value.id || currentRecordId.value;
  modelState.title = data?.title || '';
  gzsxList.value = data?.gzsxList || [];

  console.log(data)

  initData();
}

defineExpose({
  open
})

</script>
