<template>
  <div>
    <el-tabs :tab-position="tabPosition" style="height: 200px;">
      <el-tab-pane label="翻译要求">
        <el-input
          v-model="formData.txFyyq"
          type="textarea"
          :rows="8"
          placeholder="请输入翻译要求"
          maxlength="1000"
          show-word-limit
        />
      </el-tab-pane>
      <el-tab-pane label="制证要求">
        <el-input
          v-model="formData.txZzyq"
          type="textarea"
          :rows="8"
          placeholder="请输入制证要求"
          maxlength="1000"
          show-word-limit
        />
      </el-tab-pane>
      <el-tab-pane label="发证提醒">
        <el-input
          v-model="formData.txFztx"
          type="textarea"
          :rows="8"
          placeholder="请输入发证提醒"
          maxlength="1000"
          show-word-limit
        />
      </el-tab-pane>
      <el-tab-pane label="收费提醒">
        <el-input
          v-model="formData.txSftx"
          type="textarea"
          :rows="8"
          placeholder="请输入收费提醒"
          maxlength="1000"
          show-word-limit
        />
      </el-tab-pane>
      <el-tab-pane label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="8"
          placeholder="请输入备注"
          maxlength="1000"
          show-word-limit
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { listGzjzYqtx, addGzjzYqtx, updateGzjzYqtx } from '@/api/gongzheng/gongzheng/gzjzYqtx';
import type { GzjzYqtxForm, GzjzYqtxVO } from '@/api/gongzheng/gongzheng/gzjzYqtx/types';

const tabPosition = ref<'left' | 'right' | 'top' | 'bottom'>('left');

// 注入父组件传递的当前记录ID
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId');

// 表单数据
const formData = reactive<GzjzYqtxForm>({
  id: undefined,
  gzjzId: undefined,
  txFyyq: '',
  txZzyq: '',
  txFztx: '',
  txSftx: '',
  remark: ''
});

// 是否为编辑模式
const isEdit = ref(false);

/**
 * 初始化数据
 */
const initData = async () => {
  if (!currentRecordId?.value) {
    ElMessage.warning('未获取到卷宗ID');
    return;
  }

  try {
    // 设置公证卷宗ID
    formData.gzjzId = currentRecordId.value;

    // 查询是否已存在记录
    const queryParams = {
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 10
    };

    const res = await listGzjzYqtx(queryParams);

    if (res.rows && res.rows.length > 0) {
      // 存在记录，进入编辑模式
      const record = res.rows[0];
      isEdit.value = true;
      formData.id = record.id;
      formData.gzjzId = record.gzjzId;
      formData.txFyyq = record.txFyyq || '';
      formData.txZzyq = record.txZzyq || '';
      formData.txFztx = record.txFztx || '';
      formData.txSftx = record.txSftx || '';
      formData.remark = record.remark || '';
    } else {
      // 不存在记录，进入新增模式
      isEdit.value = false;
      // 重置表单数据（保留gzjzId）
      formData.id = undefined;
      formData.txFyyq = '';
      formData.txZzyq = '';
      formData.txFztx = '';
      formData.txSftx = '';
    }
  } catch (error: any) {
    console.error('查询提醒信息失败:', error);
    ElMessage.error('接口异常，请联系管理员');
  }
};

/**
 * 保存数据
 */
const saveData = async (): Promise<boolean> => {
  if (!currentRecordId?.value) {
    ElMessage.warning('未获取到卷宗ID');
    return false;
  }

  try {
    if (isEdit.value) {
      // 修改
      await updateGzjzYqtx(formData);
      ElMessage.success('修改成功');
    } else {
      // 新增
      await addGzjzYqtx(formData);
      ElMessage.success('保存成功');
    }
    return true;
  } catch (error: any) {
    console.error('保存提醒信息失败:', error);
    ElMessage.error('接口异常，请联系管理员');
    return false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  saveData
});

onMounted(() => {
  initData();
});
</script>

<style scoped>
.btcss {}
</style>
