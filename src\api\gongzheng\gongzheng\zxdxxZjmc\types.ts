export interface ZxdxxZjmcVO {
  /**
   * ID
   */
  zxzjId: string | number;

  /**
   * 咨询ID
   */
  zxId: string | number;

  /**
   * 证据名称ID
   */
  zjmcId: string | number;

  /**
   * 证据名称
   */
  zjmc: string;

  /**
   * 备注
   */
  remark: string;

}

export interface ZxdxxZjmcForm extends BaseEntity {
  /**
   * ID
   */
  zxzjId?: string | number;

  /**
   * 咨询ID
   */
  zxId?: string | number;

  /**
   * 证据名称ID
   */
  zjmcId?: string | number;

  /**
   * 证据名称
   */
  zjmc?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface ZxdxxZjmcQuery extends PageQuery {

  /**
   * 咨询ID
   */
  zxId?: string | number;

  /**
   * 证据名称ID
   */
  zjmcId?: string | number;

  /**
   * 证据名称
   */
  zjmc?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



