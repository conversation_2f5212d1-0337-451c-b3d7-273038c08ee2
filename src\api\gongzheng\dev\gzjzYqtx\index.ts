import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzYqtxVO, GzjzYqtxForm, GzjzYqtxQuery } from '@/api/gongzheng/dev/gzjzYqtx/types';

/**
 * 查询公证卷宗-要求和提醒v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzYqtx = (query?: GzjzYqtxQuery): AxiosPromise<GzjzYqtxVO[]> => {
  return request({
    url: '/gongzheng/gzjzYqtx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-要求和提醒v1.0详细
 * @param id
 */
export const getGzjzYqtx = (id: string | number): AxiosPromise<GzjzYqtxVO> => {
  return request({
    url: '/gongzheng/gzjzYqtx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-要求和提醒v1.0
 * @param data
 */
export const addGzjzYqtx = (data: GzjzYqtxForm) => {
  return request({
    url: '/gongzheng/gzjzYqtx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-要求和提醒v1.0
 * @param data
 */
export const updateGzjzYqtx = (data: GzjzYqtxForm) => {
  return request({
    url: '/gongzheng/gzjzYqtx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-要求和提醒v1.0
 * @param id
 */
export const delGzjzYqtx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzYqtx/' + id,
    method: 'delete'
  });
};
