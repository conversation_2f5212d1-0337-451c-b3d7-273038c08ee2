<template>
  <!-- 详情窗口 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <template #default>

      <div style="min-height:500px; border: 1px solid #e0e0e0;" v-loading="isConnecting || !isConnected"
        element-loading-text="正在连接设备">
        <el-space v-if="isConnected">
          <el-tag v-if="isConnected">已连接</el-tag>
          <el-tag v-else type="danger">未连接</el-tag>
          <el-button type="info" v-else :loading="isReading" v-if="isReading">正在读卡...</el-button>
          <el-button type="primary" @click="handleReadCard" :loading="isReading">读卡</el-button>

        </el-space>
        <div v-if="cardInfo" class="cardInfo">
          <div>
            <div style="float:left;">
              <img style="width: 120px;" v-if="!!cardInfo.photo" :src="`data:image/png;base64,${cardInfo.photo}`" />
            </div>
            <div>
              <el-form label-width="140px">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="姓名：">
                      <span>{{cardInfo.name}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="性别：">
                      <span>{{cardInfo.gender}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="民族：">
                      <span>{{cardInfo.nation}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="出生：">
                      <span>{{cardInfo.birth}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="住址：">
                      <span>{{cardInfo.address}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="公民身份号码：">
                      <span>{{cardInfo.idNum}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="签发机关：">
                      <span>{{cardInfo.signOrg}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="有效期：">
                      <span>{{cardInfo.valid}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="其它：">
                      <span>{{cardInfo.moreInfo}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #ccc; padding-top: 8px;">
        <el-space>
          <div v-if="!isConnecting && !isConnected">
            <el-text type="danger" v-if="connectMessage!= null">{{connectMessage}}</el-text>
          </div>
          <el-button @click="doModalClose()">关 闭</el-button>
        </el-space>
      </div>
    </template>
  </vxe-modal>


</template>

<script setup name="popIdCardReader" lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  import { VxeModalProps, VxeFormProps, VxeFormItemPropTypes, VxeFormListeners } from 'vxe-pc-ui'
  import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
  import type { Action } from 'element-plus'

  import * as signalR from '@microsoft/signalr';


  const route = useRoute()


  // 定义 props 类型
  interface Props {
    title ?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '身份证读卡器'
  });

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'success') : void
    (event : 'close') : void
  }>()

  const autoReconnect = ref(true)
  const cardInfo = ref(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const isReading = ref(false)
  const connectMessage = ref(null)
  let connectTimer = null

  let connection = new signalR.HubConnectionBuilder()
    .withUrl(`http://localhost:32388/device`, {
      accessTokenFactory: () => 'mytoken'
    })
    //.configureLogging(signalR.LogLevel.Information)
    .configureLogging(signalR.LogLevel.Error)
    .build();

  connection.onclose(async () => {
    isConnected.value = false;
    console.log('读卡器:: 连接已断开');
    ElNotification({
      title: '读卡器',
      message: '连接已断开',
      type: 'error',
      position: 'bottom-right',
      duration: 3000,
      showClose: true,
    })
    if (null != connection && autoReconnect.value == true) {
      doConnect();
    }
  });
  connection.on('onConnected', (connId) => {
    isConnected.value = true;
    console.log('读卡器onConnected::' + connId);
    ElNotification({
      title: '读卡器',
      message: "读卡器已连接",
      position: 'bottom-right',
      duration: 2000,
      showClose: true,
    })

  });
  connection.on('onOpenDevice', (devInfo) => {
    console.log('读卡器::打开设备', devInfo);
    ElNotification({
      title: '读卡器',
      message: "设备已打开",
      position: 'bottom-right',
      duration: 2000,
      showClose: true,
    })
  });
  connection.on('onCloseDevice', (devInfo) => {
    console.log('读卡器::设备已关闭', devInfo);
    ElNotification({
      title: '读卡器',
      message: "设备已关闭",
      position: 'bottom-right',
      duration: 2000,
      showClose: true,
    })
  });
  connection.on('onReadCard', (res) => {
    console.log('读卡器::onReadCard', res);
    isReading.value = false;
    if (res.isSuccess) {
      cardInfo.value = res.cardInfo;
      emits('success', res.cardInfo);
      console.log('读卡器::读取卡结果', cardInfo.value);
    } else {
      console.log('读卡器::读取卡失败');
    }
  });
  const doConnect = async () => {
    if (null != connectTimer) {
      clearTimeout(connectTimer)
      connectTimer = null;
    }
    if (isConnected.value === true || isConnecting.value === true) {
      return;
    }
    if (null == connection) {
      return;
    }
    connectMessage.value = null;
    isConnecting.value = true;
    try {
      await connection.start();
      isConnected.value = true;
      console.log("消息服务已连接.");
    } catch (err) {
      connectMessage.value = '连接失败，1秒后重试';
      isConnected.value = false;
      if (autoReconnect.value === true) {
        console.error(err);
        console.error('1秒后重新连接');
        if (null != connection) {
          connectTimer = setTimeout(doConnect, 3000);
        }
      }
    } finally {
      isConnecting.value = false;
    }
  };
  const disposeConnection = () => {
    autoReconnect.value = false;
    if (null != connectTimer) {
      clearTimeout(connectTimer)
      connectTimer = null;
    }
    if (null != connection) {
      connection.stop();
      connection = null;
    }
  };


  const showPopup = ref(false)
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: '650px',
    height: '650px',
    escClosable: true,
    resize: true,
    showMaximize: true
  })

  const open = (option = {}) => {
    console.log('open-dialog', option);
    cardInfo.value = null;
    showPopup.value = true;
    doConnect();
  }

  const close = (option = {}) => {
    console.log('open-dialog', option);
    doModalClose()
  }

  const handleSave = () => {
    console.log('handleSave')
    emits('success')
    doModalClose();
  }

  const doModalClose = () => {
    emits('close')
    disposeConnection();
    showPopup.value = false;
    console.log('窗口关闭了');
  }

  const showElMessage = () => {
    ElMessage('消息提示.')
  }

  const showElNotification = () => {
    ElNotification({
      title: '通知',
      message: '您有新的未读消息',
      duration: 1000 * 3,
    })
  }

  const onSaveSuccess = () => {
    ElMessageBox.alert('保存成功！', '保存提示', {
      // if you want to disable its autofocus
      // autofocus: false,
      confirmButtonText: 'OK',
      callback: (action : Action) => {
        ElMessage({
          type: 'info',
          message: `action: ${action}`,
        })
      },
    })
  }

  const handleReadCard = () => {
    if (isReading.value === true) {
      return;
    }
    isReading.value = true;
    connection.invoke('ReadCard', {
      isMock: false
    });
  };

  onMounted(() => {
    console.log('popIdCardReader:onMounted');
  });
  onUnmounted(() => {
    disposeConnection();
    console.log('popIdCardReader:onUnmounted');
  });

  // 暴露方法给父组件
  defineExpose({
    open, close
  })
</script>

<style scoped>
  .cardInfo {
    margin-top: 15px;
  }
</style>
