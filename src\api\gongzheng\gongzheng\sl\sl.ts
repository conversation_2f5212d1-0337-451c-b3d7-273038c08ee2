// 基本信息表单数据类型
export interface BasicFormData {
  caseNumber: string // 卷宗号
  applicationDate: string // 申请日期
  acceptanceDate: string // 受理日期
  notaryCategory: string // 公证类别
  notaryOfficer: string // 公证员
  assistant: string // 协办人
  useLocation: string // 使用地
  translationLanguage: string // 译文文种
  purpose: string // 用途
  legalReminder: string // 法律提醒
  foreignTranslation: string // 外译种
  urgentAcceptance: boolean // 紧急受理
  feeWaiver: boolean // 费用减免
  electronicSignature: boolean // 电子签名
  electronicNotary: boolean // 电子公证书
}

// 当事人数据类型
export interface PartyPersonData {
  id: number
  name: string // 姓名
  gender: string|number // 性别
  idType: string|number // 证件类型
  idNumber: string // 证件号码
  birthDate: string // 出生日期
  address: string // 住址
  phone: string // 联系电话
  type: string // 类型（申请人/代理人等）
  dsrLx:string|number //当事人身份 1个人 2单位
  js:string|number
  sex:string|number
  certificateType:string|number
  certificateNo:string|number
  contactTel:string|number
  gzjzId:string|number
  dsrId:string|number
  sfdk:string|number
}

// 公证事项数据类型
export interface NotaryMatterData {
  id: number
  sequence: number // 排序
  matter: string // 公证事项
  relatedPerson: string // 关系人
  notaryNumber: string // 公证书编号
  copies: number // 份数
  notaryFee: number // 公证费
  copyFee: number // 副本费
  actualCopyFee: number // 实收副本费
  translationFee: number // 小连件翻译费
  otherFee: number // 其他费用
  subtotal: number // 小计
  status: string // 状态
}

// 表单选项类型
export interface FormOptions {
  notaryCategories: Array<{ label: string, value: string }>
  notaryOfficers: Array<{ label: string, value: string }>
  useLocations: Array<{ label: string, value: string }>
  translationLanguages: Array<{ label: string, value: string }>
  foreignTranslations: Array<{ label: string, value: string }>
  idTypes: Array<{ label: string, value: string }>
  genders: Array<{ label: string, value: string }>
  personTypes: Array<{ label: string, value: string }>
}

// 对话框状态类型
export interface DialogState {
  visible: boolean
  title: string
  type: 'add' | 'edit' | 'view'
}
