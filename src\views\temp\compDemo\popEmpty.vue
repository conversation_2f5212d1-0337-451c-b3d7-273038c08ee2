<template>
  <!-- 空弹窗组件，可复制去使用 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="onModalClose">
    <template #default>
      <div style="height: 1000px; border: 1px solid #e0e0e0;">
        <div>这是弹出的内容 {{new Date()}} !!</div>
        <div class="padding-content">

        </div>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #ccc; padding-top: 8px;">
        <el-space>
          <el-button @click="onModalClose()">关 闭</el-button>

          <el-popconfirm title="您确定要删除吗？">
            <template #reference>
              <el-button>删除</el-button>
            </template>
          </el-popconfirm>

          <el-button @click="handleSave" type="primary">保存</el-button>
        </el-space>
      </div>
    </template>
  </vxe-modal>


</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  import { VxeModalProps, VxeFormProps, VxeFormItemPropTypes, VxeFormListeners } from 'vxe-pc-ui'
  import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
  import type { Action } from 'element-plus'

  const route = useRoute()

  // 定义 props 类型
  const props = defineProps<{
    title ?: '窗口标题'
  }>()

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'success') : void
    (event : 'close') : void
  }>()


  // 定义窗口默认属性
  const showPopup = ref(false)
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: '1000px',
    height: '90%',
    escClosable: true,
    resize: true,
    showMaximize: true
  })

  const open = (option = {}) => {
    console.log('open-dialog', option);
    showPopup.value = true;
  }

  const close = () => {
    this.onModalClose();
  }

  const handleSave = () => {
    console.log('handleSave')
    emits('success')
    onModalClose();
  }

  const onModalClose = () => {
    emits('close')
    showPopup.value = false;
    console.log('窗口关闭了');
  }

  onMounted(() => {
    console.log('compDemo:onMounted');
  });
  onUnmounted(() => {
    console.log('compDemo:onUnmounted');
  });

  // 暴露方法给父组件
  defineExpose({
    open, close
  })

  console.log('一些有用的路由参数')
  console.log('路由地址', route.fullPath)
  console.log('页面标题', route.meta.title)
  console.log('路由参数', route.query)
</script>
