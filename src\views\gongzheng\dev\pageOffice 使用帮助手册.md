## pageOffice 集成使用步骤

# 一、新建模板（已存在模板此步骤可跳过）
1.通过模板管理模块

# 二、打开模板（通过pageOffice）
Java代码：
```   
    /**
     * 根据模板生成文档
     *
     * @param vo
     * @return
     */
    private UnifiedDocumentVO generateWord(GzZxdxxVo vo, GzMbWdVo gzMbWdVo, String savePath) {
        //1、获取模板
        String path = gzMbWdVo.getWdDz();
        //2、替换变量值
        Map<String, Object> extendedProperties = new HashMap<>();
        if (CollUtil.isNotEmpty(gzMbWdVo.getGzMbBlVoList())) {
            for (GzMbBlVo gzMbBlVo : gzMbWdVo.getGzMbBlVoList()) {
                if (gzMbBlVo.getBlName().equals("申请人姓名")) {
                    extendedProperties.put(gzMbBlVo.getBlName(), vo.getZxrXm());
                    continue;
                }
                if (gzMbBlVo.getBlName().equals("受理号")) {
                    extendedProperties.put(gzMbBlVo.getBlName(), vo.getZxDh());
                    continue;
                }
                if (gzMbBlVo.getBlName().equals("调取时间")) {
                    extendedProperties.put(gzMbBlVo.getBlName(), DateUtil.format(vo.getZxRq(), "yyyy-MM-dd"));
                    continue;
                }
                if (gzMbBlVo.getBlName().equals("使用地")) {
                    extendedProperties.put(gzMbBlVo.getBlName(), vo.getSyd());
                    continue;
                }
                if (gzMbBlVo.getBlName().equals("证明材料") && CollUtil.isNotEmpty(vo.getZmclList())) {
                    extendedProperties.put(gzMbBlVo.getBlName(), getZmclMc(vo.getZmclList()));
                    continue;
                }
                if (gzMbBlVo.getBlName().equals("公证事项") && CollUtil.isNotEmpty(vo.getGzsxList())) {
                    extendedProperties.put(gzMbBlVo.getBlName(), vo.getGzsxMc());
                    continue;
                }
            }
        }
        //3、调用pageoffice 统一文档处理 接口，返回UnifiedDocumentVO
        Map<String, Object> formParams = new HashMap<>();
        formParams.put("documentId", vo.getZxId());//文档ID
        formParams.put("documentName", vo.getZxrXm() + "_" + vo.getZxDh() + "_" + "咨询单");//文档名称
        formParams.put("documentPath", path);//文档路径
        formParams.put("documentType", "word");//文档类型
        formParams.put("action", "open");//操作类型
        formParams.put("savePath", savePath);//保存路径 -最后要保存的地址 返回给业务调用存入数据库的地址
        formParams.put("editMode", "read");//编辑模式
        formParams.put("customParams", extendedProperties);//扩展属性，用于传递其他自定义配置 通过扩展属性 替换相关信息
        // 发送 POST 请求并获取响应
        HttpResponse response = HttpRequest.post(pageOfficeProperties.getProcessUrl())
            .header("Content-Type", "application/json") // 设置请求头
            .body(JSONUtil.toJsonStr(formParams)) // 设置 JSON 格式的请求体
            .timeout(5000) // 设置超时时间，单位毫秒
            .execute();
        if (response == null) {
            throw new RuntimeException("生成咨询单失败:打开文档失败");
        }
        if (!response.isOk()) {
            throw new RuntimeException("生成咨询单失败:" + response.getStatus());
        }
        JSONObject resultJson = JSONUtil.parseObj(response.body());
        if (resultJson.getInt("code") != 200) {
            throw new RuntimeException("生成咨询单失败:" + resultJson.getStr("msg"));
        }
        UnifiedDocumentVO unifiedDocumentVO = BeanUtil.copyProperties(resultJson.get("data"), UnifiedDocumentVO.class);
        return unifiedDocumentVO;
    }
```   
代码说明：
1. 获取需打开的模块地址及模板配置的变量
2. 遍历遍历并封装对应变量的内容
3. 调用pageOffice 接口，获取打开文档的代码
   4. 前端集成 pageOffice 代码
     4.1 页面引入  
      ```   import { POBrowser } from "js-pageoffice";```   
     4.2 打开页面按钮事件中，先调用 第二步接口得到 UnifiedDocumentVO 数据,  
     后调用   ```   POBrowser.openWindow("/word", 'width=1300px;height=900px;',JSON.stringify(params));```   
     params 说明就是接口返回的 UnifiedDocumentVO
     /word 无特殊功能，此参数不变
     4.3 打开文档保存后，会调用pageOffice 服务的保存方法，保存成功 ，如有回调参数callbackUrl 传有值，则会自动请求地址，表示保存成功。


# 三、其他
1. 参考使用手册：[](https://www.pageoffice.cn/pages/f464aa/)
2. 咨询单模板文档,在后端 script/docs/咨询单.docx



# UnifiedDocumentVO 字段说明
```   
     /**
     * 文档ID
     */
    private String documentId;

    /**
     * 文档名称
     */
    private String documentName;

    /**
     * 文档类型
     */
    private String documentType;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 编辑模式
     */
    private String editMode;

    /**
     * PageOffice控件HTML代码
     */
    private String htmlCode;

    /**
     * 控件ID
     */
    private String controlId;

    /**
     * 编辑URL
     */
    private String editUrl;

    /**
     * 保存URL
     */
    private String saveUrl;

    /**
     * 删除URL
     */
    private String deleteUrl;

    /**
     * JavaScript代码
     */
    private String jsCode;

    /**
     * 文档状态
     */
    private String status;

    /**
     * 文档路径
     */
    private String documentPath;

    /**
     * 文档网络地址
     */
    private String documentUrl;

    /**
     * 文档大小（字节）
     */
    private Long fileSize;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否启用印章功能
     */
    private Boolean enableSeal;

    /**
     * 是否启用修订模式
     */
    private Boolean enableRevision;

    /**
     * 操作结果码
     */
    private Integer code;

    /**
     * 操作结果消息
     */
    private String message;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendedInfo;
    /**
     * 文档保存地址
     */
    private String savePath;
```