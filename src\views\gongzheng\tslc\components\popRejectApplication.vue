<template>
  <!-- 不予办理申请弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <div class="reject-application-form">
      <!-- 申请人信息 -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="form-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人:" prop="applicant">
              <el-input v-model="formData.applicant" placeholder="请输入申请人" readonly disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期:" prop="applicationDate">
              <el-date-picker v-model="formData.applicationDate" type="date" placeholder="请选择申请日期"
                value-format="YYYY-MM-DD" style="width: 100%" readonly disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审批人*:" prop="approver">
              <el-select v-model="formData.approver" filterable placeholder="请选择审批人" style="width: 100%">
              <el-option v-for="item in tsjzspy" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 决定书 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="决定书:" prop="decisionDocument">
              <el-upload ref="uploadRef" :action="uploadFileUrl" :headers="headers" :before-upload="handleBeforeUpload"
                :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList"
                :show-file-list="true" :limit="1" class="upload-demo">
                <el-button type="primary">选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">请上传决定书文件（支持doc、docx、pdf等格式，大小不超过10MB）</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="决定书编号:" prop="decisionNumber">
              <el-input v-model="formData.decisionNumber" disabled readonly placeholder="请输入决定书编号"
                style="width: 200px; margin-right: 8px;" />
              <span style="margin-right: 8px;">第</span>
              <el-input v-model="formData.decisionSerial" disabled readonly placeholder="序号"
                style="width: 60px; margin-right: 8px;" />
              <span style="margin-right: 8px;">号</span>
              <el-button type="primary" size="small" @click="handleGetNumber">取号</el-button>
              <el-button size="small" @click="handleCancelNumber">销号</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请原因 -->
        <el-form-item label="申请原因*:" prop="applicationReason">
          <el-input v-model="formData.applicationReason" type="textarea" :rows="4" placeholder="请输入申请原因" />
        </el-form-item>

        <el-form-item>
          <div class="reason-checkboxes">
            <el-checkbox v-model="formData.reasons.noCapacity"
              @change="updateReasonText">无民事行为能力人或者限制民事行为能力人没有监护人代理申请办理公证的</el-checkbox>
            <el-checkbox v-model="formData.reasons.noInterest"
              @change="updateReasonText">当事人与申请公证的事项没有利害关系的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.technicalAppraisal"
              @change="updateReasonText">申请公证的事项属专业技术鉴定、评估事项的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.dispute" @change="updateReasonText">当事人之间对申请公证的事项有争议的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.falseMaterials"
              @change="updateReasonText">当事人虚构、隐瞒事实,或者提供虚假证明材料的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.insufficientMaterials"
              @change="updateReasonText">当事人提供的证明材料不充分又无法补充,或者拒绝补充证明材料的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.untrueIllegal"
              @change="updateReasonText">申请公证的事项不真实、不合法的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.violateMorality"
              @change="updateReasonText">申请公证的事项违背社会公德的;</el-checkbox>
            <el-checkbox v-model="formData.reasons.refusePayment"
              @change="updateReasonText">当事人拒绝按照规定支付公证费的;</el-checkbox>
          </div>
        </el-form-item>
      </el-form>

      <!-- 卷宗信息 -->
      <div class="case-section">
        <div class="section-header">
          <h3>卷宗信息</h3>
          <el-button v-has-permi="['tslc:fq:edit']" type="primary" @click="handleSelectCase" v-if="formData.id==null">选择卷宗</el-button>
        </div>

        <el-descriptions :column="3" border v-if="formData.caseInfo">
          <el-descriptions-item label="卷宗号">{{ formData.caseInfo.jzbh }}</el-descriptions-item>
          <el-descriptions-item label="公证员">{{ formData.caseInfo.gzyxm }}</el-descriptions-item>
          <el-descriptions-item label="助理">{{ formData.caseInfo.zlxm }}</el-descriptions-item>
          <el-descriptions-item label="受理日期">{{ formData.caseInfo.slrq }}</el-descriptions-item>
          <el-descriptions-item label="公证类别">  {{ dictMapFormat(gz_gzlb, formData.caseInfo.lb) }}</el-descriptions-item>
          <el-descriptions-item label="当事人">{{ formData.caseInfo.dsrxm }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="formData.caseInfo?.tip" class="case-tip">
          <el-tag type="warning">{{ formData.caseInfo.tip }}</el-tag>
        </div>
        <div v-if="formData.caseInfo==null">
          <el-empty description="请选择卷宗" />
        </div>
      </div>

      <!-- 事项列表 -->
      <div class="item-section">
        <h3>事项列表</h3>
        <el-table :data="formData.itemList" border stripe style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="公证事项" prop="notarizationItem" />
          <el-table-column label="公证书编号" prop="certificateNumber" />
          <el-table-column label="公证书" prop="certificate">
            <template #default="scope">
              <el-button v-if="scope.row.certificate" type="primary" link @click="handleViewCertificate(scope.row)">
                {{ scope.row.certificate }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="公证书译文" prop="certificateTranslation" />
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-has-permi="['tslc:fq:edit']">提交审批</el-button>
        <el-button @click="handleSaveDraft" v-has-permi="['tslc:fq:edit']">保存草稿</el-button>
        <el-button @click="handlePrint" v-has-permi="['tslc:fq:query']">打印申请书</el-button>
        <el-button @click="doModalClose">取消</el-button>
      </div>
    </template>
  </vxe-modal>

  <!-- 选择卷宗对话框 -->
  <SelectCaseDialog ref="selectCaseRef" @success="onCaseSelected" />
</template>

<script setup name="PopRejectApplication" lang="ts">
  import { ref, reactive, computed, getCurrentInstance, toRefs } from 'vue';
  import { ElMessage } from 'element-plus';
  import type { VxeModalProps } from 'vxe-table';
  import SelectCaseDialog from './SelectCaseDialog.vue';
  import { getToken } from '@/utils/auth';
  import { addTslcSqb, saveDraftTslcSqb, getCaseItems, applyDecisionNumber, cancelDecisionNumber, searchCases } from '@/api/gongzheng/tslc/tslcSqb'
  import { useUserStore } from '@/store/modules/user'

  // Props 定义
  interface Props {
    title ?: string;
    width ?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '不予办理申请',
    width: '900px'
  });

  // Emits 定义
  const emits = defineEmits<{
    (event : 'success', data ?: any) : void;
    (event : 'close') : void;
  }>();

  // 获取组件实例
  const { proxy } = getCurrentInstance() as any;
 const { gz_sf_lb, gz_sfzt, gz_gzlb, gz_tslc_tffs } = toRefs<any>(proxy?.useDict('gz_tslc_tffs', 'gz_sf_lb', 'gz_sfzt', 'gz_gzlb'));
  import { dictMapFormat } from '@/utils/ruoyi';
  // 响应式数据
  const showPopup = ref(false);
  const formRef = ref();
  const selectCaseRef = ref<InstanceType<typeof SelectCaseDialog> | null>(null);

  // 审批人列表
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser('tsjzspy'));

  // 上传文件相关
  const uploadRef = ref();
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload');
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  });
  const fileList = ref([]);

  // 弹窗配置
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: props.width,
    height: '80vh',
    escClosable: true,
    resize: true,
    showMaximize: true,
    destroyOnClose: true
  });



  // 表单数据
  const formData = reactive({
    id: null,
    applicant: '',
    applicationDate: new Date().toISOString().split('T')[0],
    approver: '',
    decisionDocument: '',
    decisionNumber: '',
    decisionSerial: '',
    applicationReason: '',
    reasons: {
      noCapacity: false,
      noInterest: false,
      technicalAppraisal: false,
      dispute: false,
      falseMaterials: false,
      insufficientMaterials: false,
      untrueIllegal: false,
      violateMorality: false,
      refusePayment: false
    },
    caseInfo: null as any,
    itemList: [] as any[],
    // 文件相关
    fileUrl: '',
    fileName: '',
    wdOssId: undefined
  });

  // 表单验证规则
  const formRules = reactive({
    approver: [
      { required: true, message: '请选择审批人', trigger: 'change' }
    ],
    applicationReason: [
      { required: true, message: '请输入申请原因', trigger: 'blur' }
    ]
  });

  // 更新申请原因文本
  const updateReasonText = () => {
    const reasons = [];
    if (formData.reasons.noCapacity) {
      reasons.push('无民事行为能力人或者限制民事行为能力人没有监护人代理申请办理公证的');
    }
    if (formData.reasons.noInterest) {
      reasons.push('当事人与申请公证的事项没有利害关系的');
    }
    if (formData.reasons.technicalAppraisal) {
      reasons.push('申请公证的事项属专业技术鉴定、评估事项的');
    }
    if (formData.reasons.dispute) {
      reasons.push('当事人之间对申请公证的事项有争议的');
    }
    if (formData.reasons.falseMaterials) {
      reasons.push('当事人虚构、隐瞒事实,或者提供虚假证明材料的');
    }
    if (formData.reasons.insufficientMaterials) {
      reasons.push('当事人提供的证明材料不充分又无法补充,或者拒绝补充证明材料的');
    }
    if (formData.reasons.untrueIllegal) {
      reasons.push('申请公证的事项不真实、不合法的');
    }
    if (formData.reasons.violateMorality) {
      reasons.push('申请公证的事项违背社会公德的');
    }
    if (formData.reasons.refusePayment) {
      reasons.push('当事人拒绝按照规定支付公证费的');
    }

    formData.applicationReason = reasons.join(';');
  };

  // 打开弹窗
  const open = async (option : any = {}) => {
    showPopup.value = true
    resetForm()
    if (option && option.id) {
      await loadDetailForEdit(option.id)
    }
  };

  // 关闭弹窗
  const close = () => {
    showPopup.value = false;
    emits('close');
  };

  // 弹窗关闭处理
  const doModalClose = () => {
    emits('close');
    showPopup.value = false;
  };

  // 重置表单
  const resetForm = () => {
    const userStore = useUserStore()
    Object.assign(formData, {
      id: null,
      applicant: userStore.nickname,
      applicationDate: new Date().toISOString().split('T')[0],
      approver: '',
      decisionDocument: '',
      decisionNumber: '',
      decisionSerial: '',
      applicationReason: '',
      reasons: {
        noCapacity: false,
        noInterest: false,
        technicalAppraisal: false,
        dispute: false,
        falseMaterials: false,
        insufficientMaterials: false,
        untrueIllegal: false,
        violateMorality: false,
        refusePayment: false
      },
      caseInfo: null,
      itemList: [],
      // 文件相关
      fileUrl: '',
      fileName: '',
      wdOssId: undefined
    });
    // 清空文件列表
    fileList.value = [];
    formRef.value?.clearValidate();
  };

  // 文件上传前的处理
  const handleBeforeUpload = (file : any) => {
    const isValidSize = file.size / 1024 / 1024 < 10;
    if (!isValidSize) {
      ElMessage.error('上传文件大小不能超过 10MB!');
      return false;
    }
    return true;
  };

  // 文件上传成功的处理
  const handleUploadSuccess = (res : any, file : any) => {
    if (res.code === 200) {
      formData.fileUrl = res.data.path;
      formData.fileName = res.data.fileName || file.name;
      formData.wdOssId = res.data.ossId;
      formData.decisionDocument = res.data.fileName || file.name;
      ElMessage.success('文件上传成功');
    } else {
      ElMessage.error('文件上传失败');
    }
  };

  // 文件上传失败的处理
  const handleUploadError = () => {
    ElMessage.error('文件上传失败');
  };

  // 取号
  const handleGetNumber = async () => {
    try {
      const res : any = await applyDecisionNumber({ gzjzId: formData.caseInfo?.gzjzId, jzh: formData.caseInfo?.jzbh, tslcLx: '3' })
      const data = res.data || res
      const bh = (data.jdsbh || '').split('第')
      formData.decisionNumber = bh[0] || ''
      formData.decisionSerial = bh[1] ? bh[1].split('号')[0] : ''
      ElMessage.success('取号成功')
    } catch (e) {
      ElMessage.error('取号失败')
    }
  };

  // 销号
  const handleCancelNumber = async () => {
    try {
      await cancelDecisionNumber({ jdsbh: formData.decisionNumber + '第' + formData.decisionSerial + '号' })
      formData.decisionNumber = ''
      formData.decisionSerial = ''
      ElMessage.success('销号成功')
    } catch (e) {
      ElMessage.error('销号失败')
    }
  };

  // 选择卷宗
  const handleSelectCase = () => {
    selectCaseRef.value?.open();
  };

  // 卷宗选择成功回调
  const onCaseSelected = async (caseInfo : any) => {
    formData.caseInfo = caseInfo;
    await loadItemList(caseInfo);
  };

  // 加载事项列表
  const loadItemList = async (caseInfo : any) => {
    try {
      const res : any = await getCaseItems({ gzjzId: caseInfo.gzjzId, jzh: caseInfo.jzbh })
      const rows = res.rows || res.data?.rows || []
      formData.itemList = rows.map((it : any) => ({
        notarizationItem: it.notarizationItem || it.gzsx,
        certificateNumber: it.certificateNumber || it.gzsbh,
        certificate: it.certificate || it.gzs || '',
        certificateTranslation: it.translation || it.yw || ''
      }))
    } catch (e) {
      ElMessage.error('加载事项明细失败')
    }
  };

  // 编辑模式加载详情
  const loadDetailForEdit = async (id : string | number) => {
    try {
      const { getTslcSqb } = await import('@/api/gongzheng/tslc/tslcSqb')
      const res : any = await getTslcSqb(id)
      const data = res.data || {}
      formData.applicant = data.tslcFqr || formData.applicant
      formData.applicationDate = data.tslcFqrq || formData.applicationDate
      if (data.tslcSprId) {
        formData.approver = data.tslcSprId
      } else if (data.tslcSpr || data.tslcSprName) {
        const name = data.tslcSprName || data.tslcSpr
        const match = (spy?.value || []).find((it : any) => it.label === name)
        formData.approver = match ? match.value : formData.approver
      }
      formData.applicationReason = data.tslcSqyy || ''
      if (data.jdsbh) {
        const bh = data.jdsbh.split('第')
        formData.decisionNumber = bh[0] || ''
        formData.decisionSerial = bh[1] ? bh[1].split('号')[0] : ''
      }
      formData.id = data.id
      formData.wdOssId = data.wdOssId
      formData.fileUrl = data.fileUrl
      formData.fileName = data.fileName
      formData.caseInfo = data.caseInfo
      formData.caseInfo.lb =  data.caseInfo.lb + ''
      await loadItemList(data.caseInfo)
    } catch (e) {
      console.error('编辑加载失败', e)
    }
  }

  // 查看公证书
  const handleViewCertificate = (row : any) => {
    ElMessage.info(`查看公证书: ${row.certificate}`);
  };

  // 提交审批
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      // 验证是否选择了卷宗
      if (!formData.caseInfo) {
        ElMessage.warning('请先选择卷宗');
        return;
      }

             // 验证是否填写或选择了申请原因
       const hasReasonText = formData.applicationReason && formData.applicationReason.trim();
       const hasReasonCheckbox = Object.values(formData.reasons).some(reason => reason);
       if (!hasReasonText && !hasReasonCheckbox) {
         ElMessage.warning('请填写申请原因或至少选择一个申请原因');
         return;
       }

      const payload : any = {
        id:formData.id,
        tslcLx: '3',
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber + '第' + formData.decisionSerial + '号',
        wdOssId: formData.wdOssId,
        fileUrl: formData.fileUrl,
        fileName: formData.fileName,
        items: formData.itemList
      }
      await addTslcSqb(payload)
      ElMessage.success('提交成功')
      emits('success', formData)
      close()
    } catch (error) {
      console.error('表单验证失败:', error);
      ElMessage.error('请检查表单信息');
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      const payload : any = {
        id:formData.id,
        tslcLx: '3',
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber + '第' + formData.decisionSerial + '号',
        wdOssId: formData.wdOssId,
        fileUrl: formData.fileUrl,
        fileName: formData.fileName,
        items: formData.itemList
      }
      await saveDraftTslcSqb(payload)
      ElMessage.success('草稿保存成功')
      emits('success', payload)
      close()
    } catch (e) {
      ElMessage.error('草稿保存失败')
    }
  };

  // 打印申请书
  const handlePrint = () => {
    console.log('打印申请书:', formData);
    ElMessage.info('打印功能待实现');
  };

  // 暴露方法给父组件
  defineExpose({
    open,
    close
  });
</script>

<style scoped>
  .reject-application-form {
    padding: 20px;
  }

  .form-container {
    margin-bottom: 20px;
  }

  .reason-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .reason-checkboxes .el-checkbox {
    margin-right: 0;
  }

  .case-section {
    margin-bottom: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .case-tip {
    margin-top: 10px;
  }

  .item-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: bold;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>
