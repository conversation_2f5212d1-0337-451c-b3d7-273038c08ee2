<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="110px">
            <el-form-item label="本地公证事项" prop="notarizedMattersId">
              <el-input v-model="queryParams.notarizedMattersId" placeholder="请输入本地公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="对照公证事项" prop="compareNotarized">
              <el-input v-model="queryParams.compareNotarized" placeholder="请输入对照公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basicdata:gzsxdzb:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['basicdata:gzsxdzb:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzsxdzbList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="本地公证事项" align="center" prop="notarizedMatters" />
        <el-table-column label="对照公证事项" align="center" prop="compareNotarized" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicdata:gzsxdzb:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basicdata:gzsxdzb:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证事项对照对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzsxdzbFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="本地公证事项" prop="notarizedMatters">
          <el-input v-model="form.notarizedMatters" placeholder="请输入本地公证事项" />
        </el-form-item>
        <el-form-item label="对照公证事项" prop="compareNotarized">
          <el-input v-model="form.compareNotarized" placeholder="请输入对照公证事项" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Gzsxdzb" lang="ts">
import { listGzsxdzb, getGzsxdzb, delGzsxdzb, addGzsxdzb, updateGzsxdzb } from '@/api/gongzheng/basicdata/gzsxdzb';
import { GzsxdzbVO, GzsxdzbQuery, GzsxdzbForm } from '@/api/gongzheng/basicdata/gzsxdzb/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzsxdzbList = ref<GzsxdzbVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzsxdzbFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzsxdzbForm = {
  notarizedMatters: undefined,
  compareNotarized: undefined,
  id: undefined
}
const data = reactive<PageData<GzsxdzbForm, GzsxdzbQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    notarizedMatters: undefined,
    compareNotarized: undefined,
    params: {
    }
  },
  rules: {
    notarizedMatters: [
      { required: true, message: "本地公证事项不能为空", trigger: "blur" }
    ],
    compareNotarized: [
      { required: true, message: "对照公证事项不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证事项对照列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzsxdzb(queryParams.value);
  gzsxdzbList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzsxdzbFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzsxdzbVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证事项对照";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzsxdzbVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzsxdzb(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证事项对照";
}

/** 提交按钮 */
const submitForm = () => {
  gzsxdzbFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzsxdzb(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzsxdzb(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzsxdzbVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证事项对照编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzsxdzb(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('basicdata/gzsxdzb/export', {
    ...queryParams.value
  }, `gzsxdzb_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
