import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { NotaryRegisterVO, NotaryRegisterForm, NotaryRegisterQuery, StatisticsVO } from '@/api/gzb/notaryRegister/types';

/**
 * 查询公证登记簿主列表
 * @param query
 * @returns {*}
 */
export const listNotaryRegister = (query?: NotaryRegisterQuery): AxiosPromise<NotaryRegisterVO[]> => {
  return request({
    url: '/gzb/notaryRegister/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取统计信息
 * @param query
 * @returns {*}
 */
export const getStatistics = (query?: NotaryRegisterQuery): AxiosPromise<StatisticsVO> => {
  return request({
    url: '/gzb/notaryRegister/statistics',
    method: 'get',
    params: query
  });
};

/**
 * 批量上报
 * @param ids
 * @returns {*}
 */
export const batchReport = (ids: Array<string | number>) => {
  return request({
    url: '/gzb/notaryRegister/batchReport',
    method: 'post',
    data: ids
  });
};

/**
 * 异常同步
 * @returns {*}
 */
export const syncException = () => {
  return request({
    url: '/gzb/notaryRegister/syncException',
    method: 'post'
  });
};

/**
 * 查询公证登记簿主详细
 * @param id
 */
export const getNotaryRegister = (id: string | number): AxiosPromise<NotaryRegisterVO> => {
  return request({
    url: '/gzb/notaryRegister/' + id,
    method: 'get'
  });
};

/**
 * 新增公证登记簿主
 * @param data
 */
export const addNotaryRegister = (data: NotaryRegisterForm) => {
  return request({
    url: '/gzb/notaryRegister',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证登记簿主
 * @param data
 */
export const updateNotaryRegister = (data: NotaryRegisterForm) => {
  return request({
    url: '/gzb/notaryRegister',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证登记簿主
 * @param id
 */
export const delNotaryRegister = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gzb/notaryRegister/' + id,
    method: 'delete'
  });
};
