export interface DsrhmdxxVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 机构标识
   */
  jgbs : string;

  /**
   * 机构编码
   */
  jgbm : string;

  /**
   * 机构名称
   */
  jgmc : string;

  /**
   * 查获日期
   */
  chrq : string;

  /**
   * 失信人
   */
  sxr : string;
  sxrId : string | number,

  /**
   * 失信人证件类型
   */
  sxrzjlx : string;

  /**
   * 失信人证件号码
   */
  sxrzjhm : string;

  /**
   * 假证类型
   */
  jzlx : string;
  /**
   *  虚假类型
   */
  xjlx ?: string;

  /**
   * 假证号码
   */
  jzhm : string;

  /**
   * 情况说明
   */
  qksm : string;

  /**
   * 状态
   */
  zt : number;

  /**
   * 冒充对象
   */
  mcdx : string;

  /**
   * 类型
   */
  lx : string;

  /**
   * $column.columnComment
   */
  remark : string;

  createByName : string;
  zp : string;
}

export interface DsrhmdxxForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 机构标识
   */
  jgbs ?: string;

  /**
   * 机构编码
   */
  jgbm ?: string;

  /**
   * 机构名称
   */
  jgmc ?: string;

  /**
   * 查获日期
   */
  chrq ?: string;

  /**
   * 失信人
   */
  sxr ?: string;
  sxrId : string | number,

  /**
   * 失信人证件类型
   */
  sxrzjlx ?: string;

  /**
   * 失信人证件号码
   */
  sxrzjhm ?: string;

  /**
   * 假证类型
   */
  jzlx ?: string;

  /**
   * 假证号码
   */
  jzhm ?: string;
  /**
   *  虚假类型
   */
  xjlx ?: string;

  /**
   * 情况说明
   */
  qksm ?: string;

  /**
   * 状态
   */
  zt ?: number;

  /**
   * 冒充对象
   */
  mcdx ?: string;

  /**
   * 类型
   */
  lx ?: string;

  /**
   * $column.columnComment
   */
  remark ?: string;
  zp?: string;
}

export interface DsrhmdxxQuery extends PageQuery {

  /**
   * 机构标识
   */
  jgbs ?: string;

  /**
   * 机构编码
   */
  jgbm ?: string;

  /**
   * 机构名称
   */
  jgmc ?: string;

  /**
   * 查获日期
   */
  chrq ?: string;

  /**
   * 失信人
   */
  sxr ?: string;
  sxrId : string | number,
  /**
   * 失信人证件类型
   */
  sxrzjlx ?: string;

  /**
   * 失信人证件号码
   */
  sxrzjhm ?: string;
  /**
   *  虚假类型
   */
  xjlx ?: string;
  /**
   * 假证类型
   */
  jzlx ?: string;

  /**
   * 假证号码
   */
  jzhm ?: string;

  /**
   * 情况说明
   */
  qksm ?: string;

  /**
   * 状态
   */
  zt ?: number;

  /**
   * 冒充对象
   */
  mcdx ?: string;

  /**
   * 类型
   */
  lx ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}
