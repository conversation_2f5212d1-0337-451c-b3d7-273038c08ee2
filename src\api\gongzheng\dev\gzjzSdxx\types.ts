export interface GzjzSdxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 通知方式
   */
  tzfs: string;

  /**
   * 送达邮箱
   */
  sdyx: string;

  /**
   * 送达号码
   */
  sdhm: string;

  /**
   * 送达日期
   */
  sdrq: string;

  /**
   * 发送结果
   */
  fsjg: string;

  /**
   * 送达信息ID
   */
  sdxxId: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzSdxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 通知方式
   */
  tzfs?: string;

  /**
   * 送达邮箱
   */
  sdyx?: string;

  /**
   * 送达号码
   */
  sdhm?: string;

  /**
   * 送达日期
   */
  sdrq?: string;

  /**
   * 发送结果
   */
  fsjg?: string;

  /**
   * 送达信息ID
   */
  sdxxId?: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzSdxxQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 通知方式
   */
  tzfs?: string;

  /**
   * 送达邮箱
   */
  sdyx?: string;

  /**
   * 送达号码
   */
  sdhm?: string;

  /**
   * 送达日期
   */
  sdrq?: string;

  /**
   * 发送结果
   */
  fsjg?: string;

  /**
   * 送达信息ID
   */
  sdxxId?: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



