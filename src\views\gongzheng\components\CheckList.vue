<template>
  <div class="flex flex-col">
    <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange" >全选</el-checkbox>
    <el-checkbox-group v-model="checked" @change="checkChange" class="flex flex-col pl-10px">
      <el-checkbox v-for="item in data" :key="item.value" :value="item.value">{{ item.label }}</el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';

type CheckListItem = {
  value: string;
  label: string;
};

interface Props {
  data: CheckListItem[]; // 数据列表
  defaultAll?: boolean; // 是否默认全选
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  defaultAll: false,
});

const checkAll = ref(false);
const isIndeterminate = ref(false);
const allItemValues = computed(() => props.data.map(item => item.value));
const checked = ref<string[]>([]);

function handleCheckAllChange(val: CheckboxValueType) {
  checked.value = val ? allItemValues.value : [];
  isIndeterminate.value = false;
}

function checkChange(value: CheckboxValueType[]) {
  const checkedCount = value.length;
  checkAll.value = checkedCount === allItemValues.value.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < allItemValues.value.length;
}

function init() {
  if (props.defaultAll) {
    checkAll.value = true;
    handleCheckAllChange(true);
  }
}

function isAllChecked(cb?: (val: boolean) => void) {
  if (cb && typeof cb === 'function') {
    cb(checked.value.length === allItemValues.value.length);
  }
  return checked.value.length === allItemValues.value.length;
}

function getChecked(cb?: (val: any[]) => void) {
  if (cb && typeof cb === 'function') {
    cb(checked.value);
  }
  return checked.value;
}

defineExpose({
  isAllChecked,
  getChecked,
})

onMounted(() => {
  init();
})

</script>
