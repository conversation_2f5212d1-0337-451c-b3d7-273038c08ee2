<template>
  <div v-loading="gzsxState.loading" class="tree-wrap h-full rounded overflow-hidden">
    <div class="tree-header h-36px flex flex-nowrap items-center gap-6px border p-x-6px">
      <strong>公证事项</strong>
      <el-input v-model="filterText" @keyup.enter="handleFilter" @clear="handleFilter" size="small" clearable style="width: 180px;" />
      <el-button @click="handleFilter" type="primary" plain icon="Search" size="small" />
    </div>
    <div class="h-[calc(100%-36px)] p-4px">
      <div class="h-full overflow-auto">
        <el-tree
          :data="gzsxState.tree"
          ref="gzsxTreeRef"
          :props="{
            label: 'title',
            children: 'children',
            disabled: 'disabled'
          }"
          node-key="id"
          :filter-node-method="filterGzsxNodes"
          :default-expanded-keys="gzsxState.tree.map(i => i.id)"
          :render-after-expand="false"
          @node-click="nodeClick"
        >
          <template #default="{ node, data }">
            <el-tooltip :content="node.label" :show-after="500">
              <div class="flex flex-nowrap items-center gap-4px h-22px">
                <el-icon v-if="data.childCount">
                  <FolderOpened v-if="node.expanded" />
                  <Folder v-else />
                </el-icon>
                <el-icon v-else><Document /></el-icon>
                <span>{{ node.label }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { listTree } from '@/api/gongzheng/basicdata/gzsx';
import { GzsxVO } from '@/api/gongzheng/basicdata/gzsx/types';
import { nodeFilter } from '@/utils/ruoyi';

interface Props {
  initFilterName?: string
}

const props = defineProps<Props>();

const gzsxState = reactive({
  tree: [],
  loading: false,
})

const gzsxTreeRef = ref<ElTreeInstance>(null)
const filterText = ref('');

interface TreeOption extends GzsxVO {
  // id?: string;
  // ancestors?: any;
  childCount?: number;
  // code?: string;
  // jcsx?: string;
  // level?: number;
  // notarLevel?: number;
  // parentCode?: string;
  // parentId?: string;
  // remark?: string;
  // temptreeCode?: string;
  // title?: string;
  disabled?: boolean; // 是否禁用
}

const nodeCo = (node: any) => {
  console.log('>>', node)
  return ''
}

const emit = defineEmits(['pick'])

const handleFilter = () => {
  gzsxTreeRef.value.filter(filterText.value);
}

const filterGzsxNodes = (value: string, data: TreeOption) => {
  if (!value) return true;
  return data.title.includes(value);
}

const buildTreeData = <T>(data: Array<TreeOption>, suorce: Array<TreeOption>) => {
  let treeData: Array<TreeOption> = [];
  let m: Array<TreeOption> = [], nm: Array<TreeOption> = [];
  treeData = data.map((item) => {
    const { matches, noMatches } = nodeFilter(suorce, (node: TreeOption) => node.parentCode === item.code);
    if(matches.length > 0) {
      m = matches; nm = noMatches;
      item.children = buildTreeData(matches, noMatches);
      item.disabled = true;
    }
    return item;
  });
  return treeData.sort((a, b) => b.childCount - a.childCount);
}

const initData = async () => {
  try {
    gzsxState.loading = true;
    const res = await listTree();
    if(res.code === 200) {
      console.log('gzsxlist', res.data)
      const { matches, noMatches } = nodeFilter(res.data, (item: TreeOption) => !item.parentCode);
      gzsxState.tree = buildTreeData(matches, noMatches);

      nextTick(() => {
        if(props.initFilterName) {
          filterText.value = props.initFilterName;
          handleFilter();
        }
      })
    }
  } catch(err: any) {
    console.error('获取公证事项列表失败', err)
  } finally {
    gzsxState.loading = false;
  }
}

const nodeClick = (obj: any, node: any, tree: any, ev: any) => {
  emit('pick', obj, node, tree, ev)
}

onMounted(() => {
  initData();
})
</script>

<style scoped>
.tree-header {
  border-bottom: 1px solid rgba(128, 128, 128, 0.42);
}

.tree-wrap {
  /* box-shadow: 0 0 3px 0 #7499d580; */
  border: 1px solid rgba(128, 128, 128, 0.42);
  transition: 0.2s;
}

.tree-wrap:hover {
  box-shadow: 0 0 6px 0 #7396cf80;
}

</style>