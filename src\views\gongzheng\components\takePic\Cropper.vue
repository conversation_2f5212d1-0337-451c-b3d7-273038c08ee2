<template>
  <div class="w-full h-full flex flex-col justify-center items-center">
    <div class="w-full flex-1 flex justify-center items-center bg-gray-500 overflow-hidden">
      <VueCropper
        ref="cropper"
        :img="props.img"
        :info="true"
        :auto-crop="cropOptions.autoCrop"
        :fixed="cropOptions.fixed"
        :fixed-box="cropOptions.fixedBox"
        :output-type="cropOptions.outputType"
        :center-box="cropOptions.centerBox"
        :original="cropOptions.original"
        :full="cropOptions.full"
        @real-time="() => {}"
      />
    </div>
    <div class="h-36px w-full flex gap-10px justify-center items-center bg-gray-200">
      <el-button @click="cutRotate('left')" type="success" size="small">左转</el-button>
      <el-button @click="cutRotate('right')" type="success" size="small">右转</el-button>
      <el-button @click="confirm" type="primary" size="small">确认剪切</el-button>
      <el-button @click="cancel" size="small">取消重拍</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import 'vue-cropper/dist/index.css';
import { VueCropper } from 'vue-cropper';

interface CropProps {
  img: string;
}

const props = defineProps<CropProps>();

const cropper = ref(null);
//图片裁剪数据
const cropOptions = reactive({
  img: '',
  autoCrop: true,
  autoCropWidth: 200,
  autoCropHeight: 200,
  fixed: false,
  fixedBox: false,
  outputType: 'png',
  fileName: '',
  previews: null,
  visible: false,
  preShow: false,
  centerBox: true,
  original: false,
  full: true,
});

const emit = defineEmits(['cancel', 'confirm']);

// 截切照片逆时针转90度
const cutRotate = (d: 'left' | 'right' = 'right') => {
  if(d === 'left') {
    cropper.value.rotateLeft();
  } else {
    cropper.value.rotateRight();
  }
}

const confirm = () => {
  cropper.value.getCropData((data: any) => {
    emit('confirm', data);
  })
}

const cancel = () => {
  emit('cancel');
}

</script>
