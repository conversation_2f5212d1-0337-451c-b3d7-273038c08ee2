import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzGzsxVO, GzjzGzsxForm, GzjzGzsxQuery } from '@/api/gongzheng/dev/gzjzGzsx/types';

/**
 * 查询公证卷宗-公证事项v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzGzsx = (query?: GzjzGzsxQuery): AxiosPromise<GzjzGzsxVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证事项v1.0详细
 * @param id
 */
export const getGzjzGzsx = (id: string | number): AxiosPromise<GzjzGzsxVO> => {
  return request({
    url: '/gongzheng/gzjzGzsx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证事项v1.0
 * @param data
 */
export const addGzjzGzsx = (data: GzjzGzsxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证事项v1.0
 * @param data
 */
export const updateGzjzGzsx = (data: GzjzGzsxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证事项v1.0
 * @param id
 */
export const delGzjzGzsx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzGzsx/' + id,
    method: 'delete'
  });
};
