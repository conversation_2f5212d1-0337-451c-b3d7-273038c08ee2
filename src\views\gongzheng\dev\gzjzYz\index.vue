<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="遗嘱人" prop="qzrIds">
              <el-input v-model="queryParams.qzrIds" placeholder="请输入遗嘱人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="代理人" prop="dlrIds">
              <el-input v-model="queryParams.dlrIds" placeholder="请输入代理人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="共同遗嘱" prop="gtyz">
              <el-select v-model="queryParams.gtyz" placeholder="请选择共同遗嘱" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzYz:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzYz:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzYz:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzYz:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzYzList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="遗嘱人" align="center" prop="qzrIds" />
        <el-table-column label="代理人" align="center" prop="dlrIds" />
        <el-table-column label="共同遗嘱" align="center" prop="gtyz">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.gtyz"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzYz:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzYz:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-遗嘱对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzYzFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="遗嘱人" prop="qzrIds">
          <el-input v-model="form.qzrIds" placeholder="请输入遗嘱人" />
        </el-form-item>
        <el-form-item label="代理人" prop="dlrIds">
          <el-input v-model="form.dlrIds" placeholder="请输入代理人" />
        </el-form-item>
        <el-form-item label="共同遗嘱" prop="gtyz">
          <el-select v-model="form.gtyz" placeholder="请选择共同遗嘱">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzYz" lang="ts">
import { listGzjzYz, getGzjzYz, delGzjzYz, addGzjzYz, updateGzjzYz } from '@/api/gongzheng/bzfz/gzjzYz';
import { GzjzYzVO, GzjzYzQuery, GzjzYzForm } from '@/api/gongzheng/bzfz/gzjzYz/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yes_or_no'));

const gzjzYzList = ref<GzjzYzVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzYzFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzYzForm = {
  id: undefined,
  qzrIds: undefined,
  dlrIds: undefined,
  gtyz: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzYzForm, GzjzYzQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    qzrIds: undefined,
    dlrIds: undefined,
    gtyz: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-遗嘱列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzYz(queryParams.value);
  gzjzYzList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzYzFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzYzVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-遗嘱";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzYzVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzYz(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-遗嘱";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzYzFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzYz(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzYz(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzYzVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-遗嘱编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzYz(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzYz/export', {
    ...queryParams.value
  }, `gzjzYz_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
