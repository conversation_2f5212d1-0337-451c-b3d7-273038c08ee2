import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TslcSqbVO, TslcSqbForm, TslcSqbQuery } from '@/api/gongzheng/tslc/tslcSqb/types';

/**
 * 查询特殊流程-申请信息列表
 * @param query
 * @returns {*}
 */

export const listTslcSqb = (query ?: TslcSqbQuery) : AxiosPromise<TslcSqbVO[]> => {
  return request({
    url: '/gongzheng/tslcSqb/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询特殊流程-申请信息详细
 * @param id
 */
export const getTslcSqb = (id : string | number) : AxiosPromise<TslcSqbVO> => {
  return request({
    url: '/gongzheng/tslcSqb/' + id,
    method: 'get'
  });
};

/**
 * 新增特殊流程-申请信息（提交审批）
 * 后端根据 tslcLx 区分不同类型的业务数据；items 为明细列表（如费用或事项）
 */
export const addTslcSqb = (data : TslcSqbForm) => {
  return request({
    url: '/gongzheng/tslcSqb',
    method: 'post',
    data: data
  });
};

/**
 * 保存草稿
 */
export const saveDraftTslcSqb = (data : TslcSqbForm) => {
  return request({
    url: '/gongzheng/tslcSqb/draft',
    method: 'post',
    data
  });
};

/**
 * 修改特殊流程-申请信息
 * @param data
 */
export const updateTslcSqb = (data : TslcSqbForm) => {
  return request({
    url: '/gongzheng/tslcSqb',
    method: 'put',
    data: data
  });
};

/**
 * 删除特殊流程-申请信息
 * @param id
 */
export const delTslcSqb = (id : string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/tslcSqb/' + id,
    method: 'delete'
  });
};

/**
 * 导出特殊流程-申请信息
 * @param query
 */
export const exportTslcSqb = (query ?: TslcSqbQuery) => {
  return request({
    url: '/gongzheng/tslcSqb/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  });
};

/**
 * 卷宗选择-搜索列表
 * 供 SelectCaseDialog 使用
 */
export const searchCases = (params : {
  pageNum ?: number;
  pageSize ?: number;
  caseNumber ?: string;
  partyInvolved ?: string;
  certificateYear ?: string;
  certificateNo ?: string;
  notary ?: string;
}) => {
  return request({
    url: '/gongzheng/tslcSqb/case/search',
    method: 'get',
    params
  });
};

/**
 * 获取卷宗收费明细（退费/减免等使用）
 * 可通过 gzjzId 或 jzh 查询
 */
export const getChargeList = (params : { gzjzId ?: string | number; jzh ?: string; zt ?: string; }) => {
  return request({
    url: '/gongzheng/tslcSqb/charge/list',
    method: 'get',
    params
  });
};

/**
 * 获取卷宗事项明细（终止/不予办理）
 */
export const getCaseItems = (params : { gzjzId ?: string | number; jzh ?: string }) => {
  return request({
    url: '/gongzheng/tslcSqb/case/items',
    method: 'get',
    params
  });
};

/**
 * 决定书编号-取号
 */
export const applyDecisionNumber = (data : { gzjzId ?: string | number; jzh ?: string; tslcLx : string | number }) => {
  return request({
    url: '/gongzheng/tslcSqb/decision/apply',
    method: 'post',
    data
  });
};

/**
 * 决定书编号-销号
 */
export const cancelDecisionNumber = (data : { numberId ?: string | number; jdsbh ?: string }) => {
  return request({
    url: '/gongzheng/tslcSqb/decision/cancel',
    method: 'post',
    data
  });
};

/**
 * 提交审批意见
 */
export const approveTslcSqb = (data : { id : string | number; approve : boolean; opinion : string }) => {
  return request({
    url: '/gongzheng/tslcSqb/approve',
    method: 'post',
    data
  });
};

/**
 * 撤回
 */
export const withdraw = (params : { id ?: string | number }) => {
  return request({
    url: '/gongzheng/tslcSqb/withdraw',
    method: 'get',
    params
  });
};
