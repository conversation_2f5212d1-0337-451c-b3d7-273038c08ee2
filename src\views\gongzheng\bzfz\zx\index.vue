<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" label-width="110px" ref="queryForm" :inline="true" v-show="showSearch" class="search-form">
      <el-form-item label="咨询单号：" prop="consultId">
        <el-input v-model="queryParams.consultId" placeholder="请输入咨询单号" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="咨询人：" prop="consultant">
        <el-input v-model="queryParams.consultant" placeholder="请输入咨询人" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="接待人：" prop="receptionist">
        <el-select v-model="queryParams.receptionist" placeholder="请选择" clearable style="width: 180px">
          <el-option label="请选择" value="" />
          <el-option v-for="item in receptionistOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公证类别：" prop="notaryType">
        <el-select v-model="queryParams.notaryType" placeholder="请选择" clearable style="width: 180px">
          <el-option label="请选择" value="" />
          <el-option v-for="item in notaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公证事项：" prop="notaryItem">
        <el-input v-model="queryParams.notaryItem" placeholder="请输入公证事项" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="咨询日期：" prop="consultDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
        至
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="使用地：" prop="usePlace">
        <el-select v-model="queryParams.usePlace" placeholder="请选择" clearable style="width: 180px">
          <el-option label="请选择" value="" />
          <el-option v-for="item in usePlaceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 咨询列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">咨询列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">新增咨询</el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="consultList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button v-has-permi="['bzfz:zx:query']" type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button v-has-permi="['bzfz:zx:query']" type="primary" link @click="handleDetail(scope.row)">咨询单</el-button>
          </template>
        </el-table-column>
        <el-table-column label="咨询单号" align="center" prop="consultId" />
        <el-table-column label="咨询人" align="center" prop="consultant" />
        <el-table-column label="接待人" align="center" prop="receptionist" />
        <el-table-column label="公证类别" align="center" prop="notaryType" />
        <el-table-column label="公证事项" align="center" prop="notaryItem" />
        <el-table-column label="使用地" align="center" prop="usePlace" />
        <el-table-column label="咨询日期" align="center" prop="consultDate" width="180" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>首页</span>
          <span>上一页</span>
          <el-input v-model="currentPage" class="page-input" />
          <span>共 {{ totalPages }} 页</span>
          <span>下一页</span>
          <span>尾页</span>
          <el-select v-model="pageSize" class="page-size-select">
            <el-option :value="10" label="10" />
          </el-select>
        </div>
        <div class="pagination-count">
          {{ startIndex }} - {{ endIndex }} 共 {{ total }} 条
        </div>
      </div>
    </div>

    <!-- 新增/修改咨询对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="consultFormRef" :model="consultForm" :rules="rules" label-width="100px">
        <div class="form-row">
          <el-form-item label="咨询单号：" prop="consultId">
            <el-input v-model="consultForm.consultId" placeholder="自动生成" :disabled="dialogType === 'edit'" />
          </el-form-item>
          <el-form-item label="咨询日期：" prop="consultDate">
            <el-date-picker
              v-model="consultForm.consultDate"
              type="date"
              placeholder="请选择咨询日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="公证类别：" prop="notaryType">
            <el-select v-model="consultForm.notaryType" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in notaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="咨询人：" prop="consultant">
            <el-input v-model="consultForm.consultant" placeholder="请输入咨询人" style="width: calc(100% - 120px)" />
            <el-button type="primary" link class="id-verify-btn" @click="handleIdVerify">支持身份证识别</el-button>
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="咨询人证件类型：" prop="consultantIdType">
            <el-select v-model="consultForm.consultantIdType" placeholder="请选择" style="width: 100%">
              <el-option label="身份证" value="身份证" />
              <el-option label="护照" value="护照" />
              <el-option label="军官证" value="军官证" />
            </el-select>
          </el-form-item>
          <el-form-item label="咨询人证件号码：" prop="consultantIdNumber">
            <el-input v-model="consultForm.consultantIdNumber" placeholder="请输入证件号码" />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="接待人：" prop="receptionist">
            <el-input v-model="consultForm.receptionist" placeholder="请输入接待人" />
          </el-form-item>
          <el-form-item label="使用地：" prop="usePlace">
            <el-select v-model="consultForm.usePlace" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in usePlaceOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="咨询电话：" prop="consultPhone">
          <el-input v-model="consultForm.consultPhone" placeholder="请输入咨询电话" />
        </el-form-item>

        <el-form-item label="其他情况：" prop="otherInfo">
          <el-input
            v-model="consultForm.otherInfo"
            type="textarea"
            placeholder="请输入咨询其他情况,长度限制0~500"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="咨询记录：" prop="consultRecord">
          <el-input
            v-model="consultForm.consultRecord"
            type="textarea"
            placeholder="请输入咨询记录"
            :rows="4"
          />
        </el-form-item>

        <div class="form-section">
          <div class="section-title">公证事项</div>
          <el-table :data="notaryItemList" border style="width: 100%">
            <el-table-column label="公证事项" align="center" prop="itemName" />
          </el-table>
          <div class="table-actions">
            <el-button type="primary" @click="handleAddNotaryItem">添加公证事项</el-button>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">需提供的证明材料</div>
          <el-table :data="documentList" border style="width: 100%">
            <el-table-column label="证明材料" align="center" prop="docName" />
          </el-table>
          <div class="table-actions">
            <el-button type="primary" @click="handleAddDocument">添加证明材料</el-button>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitForm" v-has-permi="['bzfz:zx:edit']">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加公证事项对话框 -->
    <el-dialog
      title="添加公证事项"
      v-model="notaryItemDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form ref="notaryItemFormRef" :model="notaryItemForm" :rules="notaryItemRules" label-width="100px">
        <el-form-item label="公证事项：" prop="itemName">
          <el-input v-model="notaryItemForm.itemName" placeholder="请输入公证事项" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="notaryItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitNotaryItemForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加证明材料对话框 -->
    <el-dialog
      title="添加证明材料"
      v-model="documentDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form ref="documentFormRef" :model="documentForm" :rules="documentRules" label-width="100px">
        <el-form-item label="证明材料：" prop="docName">
          <el-input v-model="documentForm.docName" placeholder="请输入证明材料名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="documentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDocumentForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'

// 显示搜索条件
const showSearch = ref(true)

// 加载状态
const loading = ref(false)

// 接待人选项
const receptionistOptions = ref([
  { value: '1', label: '杨忠凯' },
  { value: '2', label: '李明' },
  { value: '3', label: '王芳' }
])

// 公证类别选项
const notaryTypeOptions = ref([
  { value: '1', label: '国内民事' },
  { value: '2', label: '涉外民事' },
  { value: '3', label: '经济' }
])

// 使用地选项
const usePlaceOptions = ref([
  { value: '1', label: '国内' },
  { value: '2', label: '国外' }
])

// 查询参数
const queryParams = reactive({
  consultId: '',
  consultant: '',
  receptionist: '',
  notaryType: '',
  notaryItem: '',
  startDate: '',
  endDate: '',
  usePlace: '',
  pageNum: 1,
  pageSize: 10
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(1)

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value + 1)
const endIndex = computed(() => Math.min(currentPage.value * pageSize.value, total.value))

// 咨询列表数据
const consultList = ref([
  {
    consultId: '2019007325',
    consultant: '张三',
    receptionist: '杨忠凯',
    notaryType: '国内民事',
    notaryItem: '',
    usePlace: '',
    consultDate: '2019年09月03日'
  }
])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add') // add或edit
const dialogTitle = computed(() => dialogType.value === 'add' ? '新增咨询' : '咨询修改')

// 表单引用
const consultFormRef = ref<FormInstance>()

// 表单对象
const consultForm = reactive({
  consultId: '',
  consultDate: '',
  notaryType: '',
  consultant: '',
  consultantIdType: '身份证',
  consultantIdNumber: '',
  receptionist: '',
  usePlace: '',
  consultPhone: '',
  otherInfo: '',
  consultRecord: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  consultDate: [{ required: true, message: '请选择咨询日期', trigger: 'change' }],
  notaryType: [{ required: true, message: '请选择公证类别', trigger: 'change' }],
  consultant: [{ required: true, message: '请输入咨询人', trigger: 'blur' }],
  consultantIdType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
  receptionist: [{ required: true, message: '请输入接待人', trigger: 'blur' }]
})

// 公证事项相关
const notaryItemList = ref<any[]>([])
const notaryItemDialogVisible = ref(false)
const notaryItemFormRef = ref<FormInstance>()
const notaryItemForm = reactive({
  itemName: ''
})
const notaryItemRules = reactive<FormRules>({
  itemName: [{ required: true, message: '请输入公证事项', trigger: 'blur' }]
})

// 证明材料相关
const documentList = ref<any[]>([])
const documentDialogVisible = ref(false)
const documentFormRef = ref<FormInstance>()
const documentForm = reactive({
  docName: ''
})
const documentRules = reactive<FormRules>({
  docName: [{ required: true, message: '请输入证明材料名称', trigger: 'blur' }]
})

// 查询咨询列表
const getList = () => {
  loading.value = true
  // 这里应该是实际的API调用
  setTimeout(() => {
    // 模拟API返回数据
    consultList.value = [
      {
        consultId: '2019007325',
        consultant: '张三',
        receptionist: '杨忠凯',
        notaryType: '国内民事',
        notaryItem: '',
        usePlace: '',
        consultDate: '2019年09月03日'
      }
    ]
    total.value = 1
    loading.value = false
  }, 300)
}

// 搜索按钮操作
const handleQuery = () => {
  currentPage.value = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.consultId = ''
  queryParams.consultant = ''
  queryParams.receptionist = ''
  queryParams.notaryType = ''
  queryParams.notaryItem = ''
  queryParams.startDate = ''
  queryParams.endDate = ''
  queryParams.usePlace = ''
  handleQuery()
}

// 新增咨询
const handleAdd = () => {
  resetForm()
  dialogType.value = 'add'
  dialogVisible.value = true
  notaryItemList.value = []
  documentList.value = []
}

// 查看详情
const handleView = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  const consultData = {
    consultId: row.consultId,
    consultDate: row.consultDate,
    notaryType: row.notaryType,
    consultant: row.consultant,
    consultantIdType: '身份证',
    consultantIdNumber: '',
    receptionist: row.receptionist,
    usePlace: row.usePlace,
    consultPhone: '',
    otherInfo: '',
    consultRecord: ''
  }

  // 重置表单
  resetForm()

  // 填充表单数据
  Object.assign(consultForm, consultData)

  // 模拟获取公证事项和证明材料
  notaryItemList.value = []
  documentList.value = []
}

// 查看咨询单
const handleDetail = (row: any) => {
  ElMessage.success(`查看咨询单: ${row.consultId}`)
  // 实际查看咨询单逻辑
}

// 身份证识别
const handleIdVerify = () => {
  ElMessage.success('启动身份证识别')
  // 实际身份证识别逻辑
}

// 重置表单
const resetForm = () => {
  if (consultFormRef.value) {
    consultFormRef.value.resetFields()
  }
  consultForm.consultId = ''
  consultForm.consultDate = ''
  consultForm.notaryType = ''
  consultForm.consultant = ''
  consultForm.consultantIdType = '身份证'
  consultForm.consultantIdNumber = ''
  consultForm.receptionist = ''
  consultForm.usePlace = ''
  consultForm.consultPhone = ''
  consultForm.otherInfo = ''
  consultForm.consultRecord = ''
}

// 提交表单
const submitForm = () => {
  if (!consultFormRef.value) return

  consultFormRef.value.validate((valid) => {
    if (valid) {
      if (dialogType.value === 'add') {
        // 新增咨询逻辑
        ElMessage.success('新增咨询成功')
      } else {
        // 修改咨询逻辑
        ElMessage.success('修改咨询成功')
      }
      dialogVisible.value = false
      getList()
    }
  })
}

// 添加公证事项
const handleAddNotaryItem = () => {
  notaryItemForm.itemName = ''
  notaryItemDialogVisible.value = true
}

// 提交公证事项表单
const submitNotaryItemForm = () => {
  if (!notaryItemFormRef.value) return

  notaryItemFormRef.value.validate((valid) => {
    if (valid) {
      notaryItemList.value.push({
        itemName: notaryItemForm.itemName
      })
      notaryItemDialogVisible.value = false
    }
  })
}

// 添加证明材料
const handleAddDocument = () => {
  documentForm.docName = ''
  documentDialogVisible.value = true
}

// 提交证明材料表单
const submitDocumentForm = () => {
  if (!documentFormRef.value) return

  documentFormRef.value.validate((valid) => {
    if (valid) {
      documentList.value.push({
        docName: documentForm.docName
      })
      documentDialogVisible.value = false
    }
  })
}

// 组件挂载时
onMounted(() => {
  // 初始加载数据
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 15px;
}

.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-input {
  width: 50px;
}

.page-size-select {
  width: 80px;
  margin-left: 10px;
}

.pagination-count {
  font-size: 14px;
  color: #606266;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .el-form-item {
  flex: 1;
  margin-bottom: 18px;
}

.form-section {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.id-verify-btn {
  margin-left: 10px;
}
</style>
