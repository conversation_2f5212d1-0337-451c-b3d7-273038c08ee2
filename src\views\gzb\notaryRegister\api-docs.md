# 公证登记簿API接口文档

## 1. 查询公证登记簿列表

**接口路径**: `GET /gzb/notaryRegister/list`

**接口描述**: 根据查询条件分页查询公证登记簿数据

**请求参数**:
```typescript
interface NotaryRegisterQuery {
  pageNum?: number;           // 页码
  pageSize?: number;          // 每页数量
  caseNumber?: string;        // 券案号
  notarizationType?: string;  // 公证类型 (委托/声明/证明/保全)
  notaryItem?: string;        // 公证事项
  notaryNumType?: string;     // 公证书编号类型 (numbered: 已编号, unnumbered: 未编号)
  notaryNumStatus?: string;   // 公证书编号状态 (normal: 正常, abnormal: 异常)
  isCompleted?: string;       // 是否完成结案 (completed: 已完成, incomplete: 未完成)
  certVerifyInfo?: string;    // 证件检验信息 (verified: 已验证, unverified: 未验证)
  reportStatus?: string;      // 公证上报状态 (reported: 已上报, unreported: 未上报)
  provinceReportStatus?: string; // 省外上报状态 (reported: 已上报, unreported: 未上报)
  params?: {
    beginDate?: string;       // 开始日期 (YYYY-MM-DD)
    endDate?: string;         // 结束日期 (YYYY-MM-DD)
  };
}
```

**响应数据**:
```typescript
interface NotaryRegisterVO {
  id: string | number;              // 主键ID
  caseNumber: string;               // 券案号
  applicantName: string;            // 当事人姓名
  notaryItem: string;               // 公证事项
  notaryPersonName: string;         // 公证员姓名
  auditPerson: string;              // 审批人
  acceptDate: number;               // 受理日期(毫秒时间戳)
  issueDate: number;                // 出证日期(毫秒时间戳)
  notarizationType: string;         // 公证类型
  reportStatus: string;             // 公证上报状态
  provinceReportStatus: string;     // 省外上报状态
  notaryNum: string;                // 公证书编号
  itemCharge: number;               // 实收金额
  isCompleted: string;              // 是否完成结案
  certVerifyInfo: string;           // 证件检验信息
  remark: string;                   // 备注
}
```

## 2. 获取统计信息

**接口路径**: `GET /gzb/notaryRegister/statistics`

**接口描述**: 根据当前查询条件获取统计信息

**请求参数**: 同查询列表接口参数

**响应数据**:
```typescript
interface StatisticsVO {
  total: number;           // 总数
  verified: number;        // 已验证数量
  normalReported: number;  // 正常上报数量
  abnormalReported: number; // 异常上报数量
  unverified: number;      // 未验证数量
  completed: number;       // 已完成数量
  incomplete: number;      // 未完成数量
  uploaded: number;        // 已上传数量
  totalFee: number;        // 总费用
}
```

## 3. 批量上报

**接口路径**: `POST /gzb/notaryRegister/batchReport`

**接口描述**: 批量上报选中的公证登记簿数据

**请求参数**:
```typescript
Array<string | number>  // 公证登记簿ID数组
```

**响应数据**: 
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 4. 异常同步

**接口路径**: `POST /gzb/notaryRegister/syncException`

**接口描述**: 同步异常的公证登记簿数据

**请求参数**: 无

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 5. 导出Excel

**接口路径**: `GET /gzb/notaryRegister/export`

**接口描述**: 根据查询条件导出公证登记簿数据为Excel文件

**请求参数**: 同查询列表接口参数

**响应数据**: Excel文件流

## 6. 查询详情

**接口路径**: `GET /gzb/notaryRegister/{id}`

**接口描述**: 根据ID查询公证登记簿详细信息

**请求参数**:
- `id`: 公证登记簿ID

**响应数据**: 同NotaryRegisterVO

## 7. 新增公证登记簿

**接口路径**: `POST /gzb/notaryRegister`

**接口描述**: 新增公证登记簿数据

**请求参数**: NotaryRegisterForm

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 8. 修改公证登记簿

**接口路径**: `PUT /gzb/notaryRegister`

**接口描述**: 修改公证登记簿数据

**请求参数**: NotaryRegisterForm (必须包含id字段)

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 9. 删除公证登记簿

**接口路径**: `DELETE /gzb/notaryRegister/{id}`

**接口描述**: 删除公证登记簿数据

**请求参数**:
- `id`: 公证登记簿ID，支持单个ID或多个ID用逗号分隔

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有日期字段使用毫秒时间戳格式
2. 分页查询默认页码为1，每页数量为10
3. 查询条件为空时返回所有数据
4. 导出功能支持根据当前查询条件过滤数据
5. 批量操作需要先选择数据行
6. 异常同步会自动处理所有异常状态的数据
