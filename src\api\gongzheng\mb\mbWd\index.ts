import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbWdVO, MbWdForm, MbWdQuery } from './types';

/**
 * 查询模板-模板文档列表
 * @param query
 * @returns {*}
 */

export const listMbWd = (query?: MbWdQuery): AxiosPromise<MbWdVO[]> => {
  return request({
    url: '/mb/mbWd/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-模板文档详细
 * @param id
 */
export const getMbWd = (id: string | number): AxiosPromise<MbWdVO> => {
  return request({
    url: '/mb/mbWd/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-模板文档
 * @param data
 */
export const addMbWd = (data: MbWdForm) => {
  return request({
    url: '/mb/mbWd',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-模板文档
 * @param data
 */
export const updateMbWd = (data: MbWdForm) => {
  return request({
    url: '/mb/mbWd',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-模板文档
 * @param id
 */
export const delMbWd = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbWd/' + id,
    method: 'delete'
  });
};

/**
 * 模板下载
 */
export const downloadTemplate = (id: string): AxiosPromise<Blob> => {
  return request({
    url: `/mb/mbWd/download/${id}`,
    method: 'get',
    responseType: 'blob'
  });
};

/**
 * 查询指定模版类型的模版文档文件信息列表
 * @param data
 * @returns
 */
export const queryMbFiles = (data: { wdLb?: number, ywId?: string}) => {
  return request({
    url: '/mb/mbWd/getMbList',
    method: 'post',
    data
  })
}

/**
 * 查询 拟定公证书（2流程文档） 或 文档拟定（1） 下的模版类别列表
 * @param params
 * @returns
 */
export const queryMbLbList = (params: { type: 1 | 2 }) => {
  return request({
    url: '/mb/mbWd/getMbLbList',
    method: 'get',
    params
  })
}
