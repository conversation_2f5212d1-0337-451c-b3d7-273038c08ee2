<template>
  <div>
    <div class="tipc">请询问当事人当出证后，期望送达方式</div>
    <div class="mainx">
      <div class="title">公证书信息</div>
      <el-table v-loading="gzsxLoading" :data="gzsxList" border>
        <el-table-column label="事项" align="center" prop="gzsxMc" />
        <el-table-column label="公证书编号" align="center" prop="gzsBh" />
        <el-table-column label="总份数" align="center" prop="gzsFs" />
        <el-table-column label="可领份数" align="center" prop="gzsFs" />
      </el-table>
      <div class="title">发证信息</div>
      <div>
        <div class="action-wrap">
          <el-button type="primary" @click="addEvent">新增</el-button>
          <el-button type="danger" @click="delSdxxList" :loading="sdxxState.deleteing">删除</el-button>
        </div>
        <el-table v-loading="sdxxState.sdxxListLoading" :data="sdxxState.sdxxList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column prop="tzfs" label="送达方式" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              {{ dictMapFormat(gz_fzxx_sdfs, row.tzfs) }}
            </template>
          </el-table-column>
          <el-table-column prop="sdrId" label="送大人" align="center" show-overflow-tooltip/>
          <el-table-column prop="zjlx" label="证件类型" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              {{ dictMapFormat(gz_gr_zjlx, row.zjlx) }}
            </template>
          </el-table-column>
          <el-table-column prop="zjhm" label="证件号码" align="center" show-overflow-tooltip/>
          <el-table-column prop="lxdh" label="联系电话" align="center" show-overflow-tooltip/>
          <el-table-column prop="lqdd" label="领取地点" align="center" show-overflow-tooltip/>
          <el-table-column prop="lqxx" label="领取信息" align="center" show-overflow-tooltip/>
          <el-table-column prop="sdlx" label="送达类型" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              {{ dictMapFormat(gz_fzxx_sdlx, row.sdlx) }}
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip/>
        </el-table>
        <!-- <vxe-grid ref="gridRef" v-bind="gridOptions">
          <template #toolbarButtons>
            <vxe-button status="primary" icon="vxe-icon-add" @click="addEvent">新增</vxe-button>
            <vxe-button status="error" icon="vxe-icon-no-drop" @click="pendingSelect">删除</vxe-button>
          </template>
        </vxe-grid> -->
      </div>
    </div>
    <el-dialog v-model="sdxxState.editShow" :title="sdxxState.editTitle" @close="cancelSave" style="min-width: 670px;max-width: 970px;">
      <el-form :model="sdxxState.editForm" :rules="sdxxState.editRules" ref="sdxxFormRef" label-width="90px" inline>
        <el-form-item prop="tzfs" label="送达方式">
          <!-- <el-select v-model="sdxxState.editForm.tzfs" style="max-width: 200px;">
            <el-option v-for="item in gz_fzxx_sdfs" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
          <el-radio-group v-model="sdxxState.editForm.tzfs" style="max-width: 200px;">
            <el-radio v-for="item in gz_fzxx_sdfs" :key="item.value" :label="item.label" :value="item.value" :disabled="item.value==='3'"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="sdrId" label="送达人">
          <el-input v-model="sdxxState.editForm.sdrId" style="max-width: 200px;" />
        </el-form-item>
        <el-form-item prop="zjlx" label="证件类型">
          <el-select v-model="sdxxState.editForm.zjlx" style="max-width: 200px;">
            <el-option v-for="item in gz_gr_zjlx" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="zjhm" label="证件号码">
          <el-input v-model="sdxxState.editForm.zjhm" style="max-width: 200px;" />
        </el-form-item>
        <el-form-item prop="lxdh" label="联系电话">
          <el-input v-model="sdxxState.editForm.lxdh" style="max-width: 200px;" />
        </el-form-item>
        <el-form-item prop="lqdd" label="领取地点">
          <el-input v-model="sdxxState.editForm.lqdd" style="max-width: 200px;" />
        </el-form-item>
        <el-form-item prop="lqxx" label="领取信息">
          <!-- <el-input v-model="sdxxState.editForm.lqxx" style="max-width: 200px;" /> -->
          <el-radio-group v-model="sdxxState.editForm.lqxx" style="max-width: 200px;">
            <el-radio v-for="item in gz_fzxx_lqxx" :key="item.value" :label="item.label" :value="item.value"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="sdlx" label="送达类型">
          <!-- <el-select v-model="sdxxState.editForm.sdlx" style="max-width: 200px;">
            <el-option v-for="item in gz_fzxx_sdlx" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
          <el-radio-group v-model="sdxxState.editForm.sdlx" style="max-width: 200px;">
            <el-radio v-for="item in gz_fzxx_sdlx" :key="item.value" :label="item.label" :value="item.value"></el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item prop="remark" label="备注">
          <el-input v-model="sdxxState.editForm.remark" type="textarea" style="width: 400px;" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="sdxxSave" type="primary" :loading="sdxxState.saving">保 存</el-button>
          <el-button @click="cancelSave">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { VxeGridInstance, VxeGridProps, VxeColumnPropTypes } from 'vxe-table'
import { VxeSelectProps } from 'vxe-pc-ui'
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx'
import { listGzjzSdxx, addGzjzSdxx, updateGzjzSdxx, delGzjzSdxx } from '@/api/gongzheng/gongzheng/gzjzSdxx'
import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
import type { GzjzSdxxVO, GzjzSdxxForm } from '@/api/gongzheng/gongzheng/gzjzSdxx/types';
import { dictMapFormat } from '@/utils/ruoyi';

interface RowVO {
  id?: number
  gzjzId?: number
  tzfs?: string // 送达方式/通知方式
  sdrId?: number // 送达人
  zjlx?: string // 证件类型
  zjhm?: string // 证件号码
  lxdh?: string // 联系电话
  lqdd?: string // 领取地点
  lqxx?: string // 领取信息
  sdlx?: string // 送达类型
  sdyx?: string // 送达邮箱
  sdhm?: string // 送达号码
  sdrq?: string // 送达日期
  fsjg?: string // 发送结果
  remark?: string // 备注
  sdrVo?: object // 送达人信息
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_fzxx_sdfs, gz_fzxx_sdlx, gz_gr_zjlx, gz_fzxx_lqxx } = toRefs<any>(proxy?.useDict('gz_fzxx_sdfs', 'gz_fzxx_sdlx', 'gz_gr_zjlx', 'gz_fzxx_lqxx'));

// 接收父组件传递的公证卷宗ID
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));

const gzsxLoading = ref(false);
const gzsxList = ref<GzjzGzsxVO[]>([]);
const gridRef = ref<VxeGridInstance<RowVO>>()

interface SdxxForm {
  id?: number | string; // 新增后生成的id
  tzfs?: string; // 送达方式/通知方式
  sdrId?: number; // 送达人
  zjlx?: string; // 证件类型
  zjhm?: string; // 证件号码
  lxdh?: string; // 联系电话
  lqdd?: string; // 领取地点
  lqxx?: string; // 领取信息
  sdlx?: string; // 送达类型
  remark?: string; // 备注
  sdrVo?: object; // 送达人信息
}

// sdxx表单实例
const sdxxFormRef = ref();
// 新增sdxx表单数据
const sdxxState = reactive({
  editForm: {
    id: undefined,
    tzfs: '1',
    sdrId: undefined,
    zjlx: '',
    zjhm: '',
    lxdh: '',
    lqdd: '',
    lqxx: '1',
    sdlx: '1',
    remark: ''
  } as SdxxForm,
  sdxxList: [],
  sdxxListLoading: false,
  selectedSdxx: [],
  editShow: false,
  editTitle: '',
  editRules: {
    tzfs: [
      { required: true, message: '送达方式不能为空', trigger: 'blur' },
      { required: true, message: '送达方式不能为空', trigger: 'change' },
    ],
  },
  saving: false,
  deleteing: false,
})

// 获取公证书信息
const getGzsxList = async () => {
  if (!currentRecordId.value) return;

  gzsxLoading.value = true;
  try {
    const res = await listGzjzGzsx({
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    });
    gzsxList.value = res.rows || [];
  } catch (error) {
    console.error('获取公证书信息失败:', error);
    proxy?.$modal.msgError('获取公证书信息失败');
  } finally {
    gzsxLoading.value = false;
  }
};

// 获取送达信息列表
const getSdxxList = async () => {
  if (!currentRecordId.value) return;

  try {
    const res = await listGzjzSdxx({
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    });

    // const $grid = gridRef.value;
    if (res.rows) {
      // 转换数据格式以适配表格
      const transformedData = res.rows.map(item => ({
        id: item.id,
        gzjzId: item.gzjzId,
        tzfs: item.tzfs,
        sdrId: '', // 送达人，可能需要从其他接口获取
        zjlx: '', // 证件类型
        zjhm: '', // 证件号码
        lxdh: item.sdhm,
        lqdd: '', // 领取地点
        lqxx: '', // 领取信息
        sdlx: '', // 送达类型
        sdyx: item.sdyx,
        sdhm: item.sdhm,
        sdrq: item.sdrq,
        fsjg: item.fsjg,
        remark: item.remark
      }));

      // await $grid.loadData(transformedData);
    }
  } catch (error) {
    console.error('获取送达信息失败:', error);
    proxy?.$modal.msgError('获取送达信息失败');
  }
};

const querySdxxList = async () => {
  if (!currentRecordId.value) return;
  try {
    sdxxState.sdxxListLoading = true;
    const res = await listGzjzSdxx({
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    });
    if(res.rows) {
      sdxxState.sdxxList = res.rows;
    }
  } catch (err: any) {
    console.log('送达信息列表查询失败', err);
    ElMessage.error('送达信息列表查询失败');
  } finally {
    sdxxState.sdxxListLoading = false;
  }
}

const sdxxFormReset = () => {
  sdxxFormRef.value.resetFields();
  sdxxState.editForm = {
    id: undefined,
    tzfs: '1',
    sdrId: undefined,
    zjlx: '',
    zjhm: '',
    lxdh: '',
    lqdd: '',
    lqxx: '1',
    sdlx: '1',
    remark: ''
  }
};

const addEvent = async () => {
  sdxxState.editShow = true;
  sdxxState.editTitle = '新增送达信息';
  return;

  // const $grid = gridRef.value
  // if ($grid) {
  //   const record: RowVO = {
  //     gzjzId: currentRecordId.value as number,
  //     tzfs: "",
  //     sdr: "",
  //     zjlx: "",
  //     zjhm: "",
  //     lxdh: "",
  //     lqdd: "",
  //     lqxx: "",
  //     sdlx: ""
  //   }
  //   const { row: newRow } = await $grid.insertAt(record, null)
  //   await $grid.setEditRow(newRow)
  // }
}

// // 送达方式选项
// const tzfsOptions = [
//   { label: '短信通知', value: '01' },
//   { label: '邮箱通知', value: '02' },
//   { label: '电话通知', value: '03' },
//   { label: '上门送达', value: '04' },
//   { label: '邮寄送达', value: '05' }
// ];

// // 证件类型选项
// const zjlxOptions = [
//   { label: '身份证', value: '01' },
//   { label: '护照', value: '02' },
//   { label: '军官证', value: '03' },
//   { label: '营业执照', value: '04' },
//   { label: '其他', value: '99' }
// ];

// // 送达类型选项
// const sdlxOptions = [
//   { label: '自取', value: '01' },
//   { label: '邮寄', value: '02' },
//   { label: '快递', value: '03' }
// ];

// const tzfsEditRender = reactive<VxeColumnPropTypes.EditRender<RowVO, VxeSelectProps>>({
//   name: 'VxeSelect',
//   options: tzfsOptions
// });

// const zjlxEditRender = reactive<VxeColumnPropTypes.EditRender<RowVO, VxeSelectProps>>({
//   name: 'VxeSelect',
//   options: zjlxOptions
// });

// const sdlxEditRender = reactive<VxeColumnPropTypes.EditRender<RowVO, VxeSelectProps>>({
//   name: 'VxeSelect',
//   options: sdlxOptions
// });

// const gridOptions = reactive<VxeGridProps<RowVO>>({
//   border: true,
//   loading: false,
//   stripe: true,
//   showOverflow: true,
//   showFooterOverflow: true,
//   keepSource: true,
//   height: '400px',
//   columnConfig: {
//     resizable: true,
//     drag: true
//   },
//   columnDragConfig: {
//     trigger: 'cell',
//     showIcon: false,
//     showGuidesStatus: true
//   },
//   rowConfig: {
//     useKey: true,
//     isHover: true
//   },
//   resizableConfig: {
//     isDblclickAutoWidth: true
//   },
//   toolbarConfig: {
//     custom: true,
//     zoom: true,
//     slots: {
//       buttons: 'toolbarButtons'
//     }
//   },
//   checkboxConfig: {
//     range: true
//   },
//   editConfig: {
//     mode: 'cell',
//     trigger: 'dblclick',
//     showStatus: true
//   },
//   mouseConfig: {
//     selected: true
//   },
//   keyboardConfig: {
//     isEdit: true,
//     isArrow: true,
//     isEnter: true,
//     isBack: true,
//     isDel: true,
//     isEsc: true
//   },
//   scrollX: {
//     gt: 0,
//     enabled: true
//   },
//   scrollY: {
//     gt: 0,
//     enabled: true
//   },
//   editRules: {
//     tzfs: [
//       { required: true, content: '请选择送达方式' }
//     ]
//   },
//   columns: [
//     { field: 'seq', type: 'seq', fixed: 'left', width: 60 },
//     { field: 'checkbox', type: 'checkbox', fixed: 'left', width: 60 },
//     { field: 'tzfs', title: '送达方式', minWidth: 150, editRender: tzfsEditRender },
//     { field: 'sdr', title: '送达人', width: 120, editRender: { name: 'VxeInput' } },
//     { field: 'zjlx', title: '证件类型', width: 140, editRender: zjlxEditRender },
//     { field: 'zjhm', title: '证件号码', width: 180, editRender: { name: 'VxeInput' } },
//     { field: 'lxdh', title: '联系电话', width: 140, editRender: { name: 'VxeInput' } },
//     { field: 'lqdd', title: '领取地点', width: 140, editRender: { name: 'VxeInput' } },
//     { field: 'lqxx', title: '领取信息', width: 140, editRender: { name: 'VxeInput' } },
//     { field: 'sdlx', title: '送达类型', width: 140, editRender: sdlxEditRender },
//     { field: 'remark', title: '备注', width: 140, editRender: { name: 'VxeInput' } },
//   ],
//   footerData: []
// });

const pendingSelect = async () => {
  const $grid = gridRef.value;
  if ($grid) {
    const checkboxRecords = $grid.getCheckboxRecords();
    if (checkboxRecords.length === 0) {
      proxy?.$modal.msgWarning('请选择要删除的数据');
      return;
    }

    try {
      await proxy?.$modal.confirm('确认要删除选中的送达信息吗？');

      // 分离新增和已存在的记录
      const existingIds: (string | number)[] = [];
      const newRows: RowVO[] = [];

      checkboxRecords.forEach(row => {
        if (row.id) {
          existingIds.push(row.id);
        } else {
          newRows.push(row);
        }
      });

      // 删除服务端数据
      if (existingIds.length > 0) {
        await delGzjzSdxx(existingIds);
      }

      // 删除本地数据
      await $grid.removeCheckboxRow();

      proxy?.$modal.msgSuccess('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      proxy?.$modal.msgError('删除失败');
    }
  }
};

const delSdxxList = async () => {
  if(sdxxState.selectedSdxx.length === 0) {
    ElMessage.warning('请选择要删除的信息');
    return;
  }
  sdxxState.deleteing = true;
  try {
    const res = await delGzjzSdxx(sdxxState.selectedSdxx);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      sdxxState.selectedSdxx = [];
      await querySdxxList();
    }
  } catch (err: any) {
    console.error('删除失败:', err);
    ElMessage.error('删除失败');
  } finally {
    sdxxState.deleteing = false;
  }
}

// 信息选择
const handleSelectionChange = (rows: RowVO[]) => {
  sdxxState.selectedSdxx = rows.map(row => row.id);
};

// 送达信息保存
const sdxxSave = () => {
  console.log('新增/修改', sdxxState.editForm);
  sdxxFormRef.value.validate(async (valid: boolean) => {
    if (valid)  {
      sdxxState.saving = true;
      try {
        let res;
        if (sdxxState.editForm.id) {
          res = await updateGzjzSdxx({ ...sdxxState.editForm, gzjzId: currentRecordId.value });
        } else {
          res = await addGzjzSdxx({ ...sdxxState.editForm, gzjzId: currentRecordId.value });
        }
        if (res.code === 200) {
          ElMessage.success('保存成功');
          cancelSave();
          await querySdxxList(); // 重新加载数据
        }
      } catch (err: any) {
        console.error('保存失败:', err);
        ElMessage.error('保存失败');
      } finally {
        sdxxState.saving = false;
      }
    }
  });
};
// 取消送达信息保存
const cancelSave = () => {
  sdxxFormReset();
  sdxxState.editShow = false;
}

// 保存数据的方法
const saveData = async (): Promise<boolean> => {
  const $grid = gridRef.value;
  if (!$grid || !currentRecordId.value) {
    return false;
  }

  try {
    // 获取所有数据
    const allData = $grid.getTableData().fullData;

    for (const row of allData) {
      const formData: GzjzSdxxForm = {
        id: row.id,
        gzjzId: currentRecordId.value,
        tzfs: row.tzfs,
        sdyx: row.sdyx,
        sdhm: row.lxdh, // 联系电话作为送达号码
        sdrq: row.sdrq,
        fsjg: row.fsjg,
        remark: row.remark
      };


      if (row.id) {
        // 更新
        await updateGzjzSdxx(formData);
      } else {
        // 新增
        await addGzjzSdxx(formData);
      }
    }

    proxy?.$modal.msgSuccess('保存成功');
    // 重新加载数据
    await getSdxxList();
    return true;
  } catch (error) {
    console.error('保存失败:', error);
    proxy?.$modal.msgError('保存失败');
    return false;
  }
};

// 监听currentRecordId变化
watch(currentRecordId, async (newVal) => {
  if (newVal) {
    // await Promise.all([getGzsxList(), getSdxxList()]);
    await Promise.all([getGzsxList(), querySdxxList()]);
  }
}, { immediate: true });

// 暴露给父组件的方法
defineExpose({
  saveData
});
</script>

<style scoped>
.tipc {
  text-align: center;
  font-size: 12px;
  color: red;
}

.mainx {
  margin-top: 25px;
}

.title {
  font-weight: bold;
  font-size: 15px;
  margin: 10px 0px;
}
</style>
