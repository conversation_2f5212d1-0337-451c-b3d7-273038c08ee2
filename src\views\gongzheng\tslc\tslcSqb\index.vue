<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卷宗号" prop="jzh">
              <el-input v-model="queryParams.jzh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="tslcZt">
              <el-select v-model="queryParams.tslcZt" placeholder="请选择状态" clearable >
                <el-option v-for="dict in gz_tslc_zt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="tslcLx">
              <el-select v-model="queryParams.tslcLx" placeholder="请选择类型" clearable >
                <el-option v-for="dict in gz_tslc_lx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="发起人ID" prop="tslcFqrId">
              <el-input v-model="queryParams.tslcFqrId" placeholder="请输入发起人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发起人" prop="tslcFqr">
              <el-input v-model="queryParams.tslcFqr" placeholder="请输入发起人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发起日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeTslcFqrq"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="queryParams.gzsbh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="决定书编号" prop="jdsbh">
              <el-input v-model="queryParams.jdsbh" placeholder="请输入决定书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="决定书文件路径" prop="jdswj">
              <el-input v-model="queryParams.jdswj" placeholder="请输入决定书文件路径" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否取号" prop="sfQh">
              <el-select v-model="queryParams.sfQh" placeholder="请选择是否取号" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否销号" prop="sfXh">
              <el-select v-model="queryParams.sfXh" placeholder="请选择是否销号" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="申请原因" prop="tslcSqyy">
              <el-input v-model="queryParams.tslcSqyy" placeholder="请输入申请原因" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批人ID" prop="tslcSprId">
              <el-input v-model="queryParams.tslcSprId" placeholder="请输入审批人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批人" prop="tslcSpr">
              <el-input v-model="queryParams.tslcSpr" placeholder="请输入审批人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeTslcSprq"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="审批意见" prop="tslcSpyj">
              <el-input v-model="queryParams.tslcSpyj" placeholder="请输入审批意见" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批表文件路径" prop="tslcSpbwj">
              <el-input v-model="queryParams.tslcSpbwj" placeholder="请输入审批表文件路径" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:tslcSqb:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:tslcSqb:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:tslcSqb:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:tslcSqb:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="tslcSqbList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="卷宗号" align="center" prop="jzh" />
        <el-table-column label="状态" align="center" prop="tslcZt">
          <template #default="scope">
            <dict-tag :options="gz_tslc_zt" :value="scope.row.tslcZt"/>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" prop="tslcLx">
          <template #default="scope">
            <dict-tag :options="gz_tslc_lx" :value="scope.row.tslcLx"/>
          </template>
        </el-table-column>
        <el-table-column label="发起人ID" align="center" prop="tslcFqrId" />
        <el-table-column label="发起人" align="center" prop="tslcFqr" />
        <el-table-column label="发起日期" align="center" prop="tslcFqrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.tslcFqrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="决定书编号" align="center" prop="jdsbh" />
        <el-table-column label="决定书文件路径" align="center" prop="jdswj" />
        <el-table-column label="是否取号" align="center" prop="sfQh">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfQh"/>
          </template>
        </el-table-column>
        <el-table-column label="是否销号" align="center" prop="sfXh">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfXh"/>
          </template>
        </el-table-column>
        <el-table-column label="申请原因" align="center" prop="tslcSqyy" />
        <el-table-column label="审批人ID" align="center" prop="tslcSprId" />
        <el-table-column label="审批人" align="center" prop="tslcSpr" />
        <el-table-column label="审批日期" align="center" prop="tslcSprq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.tslcSprq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批意见" align="center" prop="tslcSpyj" />
        <el-table-column label="审批表文件路径" align="center" prop="tslcSpbwj" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:tslcSqb:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:tslcSqb:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改特殊流程-申请信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="tslcSqbFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="卷宗号" prop="jzh">
          <el-input v-model="form.jzh" placeholder="请输入卷宗号" />
        </el-form-item>
        <el-form-item label="状态" prop="tslcZt">
          <el-select v-model="form.tslcZt" placeholder="请选择状态">
            <el-option
                v-for="dict in gz_tslc_zt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="tslcLx">
          <el-select v-model="form.tslcLx" placeholder="请选择类型">
            <el-option
                v-for="dict in gz_tslc_lx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发起人ID" prop="tslcFqrId">
          <el-input v-model="form.tslcFqrId" placeholder="请输入发起人ID" />
        </el-form-item>
        <el-form-item label="发起人" prop="tslcFqr">
          <el-input v-model="form.tslcFqr" placeholder="请输入发起人" />
        </el-form-item>
        <el-form-item label="发起日期" prop="tslcFqrq">
          <el-date-picker clearable
            v-model="form.tslcFqrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发起日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsbh">
          <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="决定书编号" prop="jdsbh">
          <el-input v-model="form.jdsbh" placeholder="请输入决定书编号" />
        </el-form-item>
        <el-form-item label="决定书文件路径" prop="jdswj">
          <el-input v-model="form.jdswj" placeholder="请输入决定书文件路径" />
        </el-form-item>
        <el-form-item label="是否取号" prop="sfQh">
          <el-select v-model="form.sfQh" placeholder="请选择是否取号">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否销号" prop="sfXh">
          <el-select v-model="form.sfXh" placeholder="请选择是否销号">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请原因" prop="tslcSqyy">
            <el-input v-model="form.tslcSqyy" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="审批人ID" prop="tslcSprId">
          <el-input v-model="form.tslcSprId" placeholder="请输入审批人ID" />
        </el-form-item>
        <el-form-item label="审批人" prop="tslcSpr">
          <el-input v-model="form.tslcSpr" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="审批日期" prop="tslcSprq">
          <el-date-picker clearable
            v-model="form.tslcSprq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择审批日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见" prop="tslcSpyj">
            <el-input v-model="form.tslcSpyj" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="审批表文件路径" prop="tslcSpbwj">
          <el-input v-model="form.tslcSpbwj" placeholder="请输入审批表文件路径" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TslcSqb" lang="ts">
import { listTslcSqb, getTslcSqb, delTslcSqb, addTslcSqb, updateTslcSqb } from '@/api/gongzheng/tslc/tslcSqb';
import { TslcSqbVO, TslcSqbQuery, TslcSqbForm } from '@/api/gongzheng/tslc/tslcSqb/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_tslc_zt, gz_yes_or_no, gz_tslc_lx } = toRefs<any>(proxy?.useDict('gz_tslc_zt', 'gz_yes_or_no', 'gz_tslc_lx'));

const tslcSqbList = ref<TslcSqbVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeTslcFqrq = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeTslcSprq = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const tslcSqbFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TslcSqbForm = {
  id: undefined,
  gzjzId: undefined,
  jzh: undefined,
  tslcZt: undefined,
  tslcLx: undefined,
  tslcFqrId: undefined,
  tslcFqr: undefined,
  tslcFqrq: undefined,
  gzsbh: undefined,
  jdsbh: undefined,
  jdswj: undefined,
  sfQh: undefined,
  sfXh: undefined,
  tslcSqyy: undefined,
  tslcSprId: undefined,
  tslcSpr: undefined,
  tslcSprq: undefined,
  tslcSpyj: undefined,
  tslcSpbwj: undefined,
  remark: undefined,
}
const data = reactive<PageData<TslcSqbForm, TslcSqbQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    jzh: undefined,
    tslcZt: undefined,
    tslcLx: undefined,
    tslcFqrId: undefined,
    tslcFqr: undefined,
    gzsbh: undefined,
    jdsbh: undefined,
    jdswj: undefined,
    sfQh: undefined,
    sfXh: undefined,
    tslcSqyy: undefined,
    tslcSprId: undefined,
    tslcSpr: undefined,
    tslcSpyj: undefined,
    tslcSpbwj: undefined,
    params: {
      tslcFqrq: undefined,
      tslcSprq: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    gzjzId: [
      { required: true, message: "公证卷宗ID不能为空", trigger: "blur" }
    ],
    jzh: [
      { required: true, message: "卷宗号不能为空", trigger: "blur" }
    ],
    tslcFqrId: [
      { required: true, message: "发起人ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询特殊流程-申请信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeTslcFqrq.value, 'TslcFqrq');
  proxy?.addDateRange(queryParams.value, dateRangeTslcSprq.value, 'TslcSprq');
  const res = await listTslcSqb(queryParams.value);
  tslcSqbList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  tslcSqbFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeTslcFqrq.value = ['', ''];
  dateRangeTslcSprq.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TslcSqbVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加特殊流程-申请信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: TslcSqbVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getTslcSqb(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改特殊流程-申请信息";
}

/** 提交按钮 */
const submitForm = () => {
  tslcSqbFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTslcSqb(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addTslcSqb(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: TslcSqbVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除特殊流程-申请信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delTslcSqb(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/tslcSqb/export', {
    ...queryParams.value
  }, `tslcSqb_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
