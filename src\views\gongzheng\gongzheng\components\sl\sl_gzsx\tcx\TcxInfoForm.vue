<template>
  <el-form :model="tcxFormData" :rules="rules" ref="tcxFormRef" label-width="120">
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="提存人：" prop="tcrId">
          <el-select placeholder="请选择提存人" v-model="tcxFormData.tcrId" @change="setTcrXx">
            <el-option
              v-for="item in gzjzDsrList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

      </el-col>
      <el-col :span="12">
        <el-form-item prop="tclb" label="提存类别：">
          <el-select disabled v-model="tcxFormData.tclb" placeholder="请选择提存类别" clearable>
            <el-option v-for="dict in tcx_gzsx_data" :key="dict.gzsxId" :label="dict.gzsxMc" :value="dict.gzsxId" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item prop="tcyy" label="提存原因：">
          <el-input placeholder="请输入提存原因" type="textarea" v-model="tcxFormData.tcyy" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item prop="ywlb" label="业务类型：">
          <el-select :disabled="ywlbDisable" placeholder="请选择业务类型" v-model="tcxFormData.ywlb"
                     @change="ywlbChange">
            <el-option
              v-for="dict in gz_tc_ywlx"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="tcrq" label="提存日期：">
          <el-date-picker
            type="date"
            placeholder="请选择提存日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="tcxFormData.tcrq"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item prop="tcbd" label="提存标的：">
          <el-select :disabled="tcbdDisable" placeholder="请选择提存标的" v-model="tcxFormData.tcbd"
                     @change="tcbdChange">
            <el-option
              v-for="dict in gz_tc_bd"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showBz">
        <el-form-item prop="hbLx" label="币种：">
          <el-select :disabled="hbLxDisable" placeholder="请选择币种" v-model="tcxFormData.hbLx" >
            <el-option
              v-for="dict in gz_tc_bz"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="showHbje">
        <el-form-item prop="hbJe" label="金额：">
          <el-input placeholder="请输入金额" type="number" v-model="tcxFormData.hbJe"></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showHbZhrmb">
        <el-form-item prop="hbZhrmb" label="折合人民币：">
          <template v-slot>
            <div style="display: flex">
              <el-input placeholder="请输入折合人民币" type="number" v-model="tcxFormData.hbZhrmb" />
              <span style="margin-left: 5px">元</span>
            </div>
          </template>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showHbKhh">
        <el-form-item prop="hbKhh" label="开户行：">
          <el-select placeholder="请选择开户行" v-model="tcxFormData.hbKhh">
            <el-option
              v-for="dict in gz_tc_khh"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="showHbYhzh">
        <el-form-item prop="hbYhzh" label="账号：">
          <el-input placeholder="请输入账号" v-model="tcxFormData.hbYhzh"></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showRzsj">
        <el-form-item prop="hbRzsj" label="入账时间：">
          <el-date-picker
            type="date"
            placeholder="请选择入账时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="tcxFormData.hbRzsj"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showWpMc">
        <el-form-item prop="wpMc" :label="wpLable.wpmc.title">
          <el-input :placeholder="wpLable.wpmc.placeholder" v-model="tcxFormData.wpMc"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="showWpSl">
        <el-form-item prop="wpSl" :label="wpLable.wpsl.title">
          <el-input :placeholder="wpLable.wpsl.placeholder" v-model="tcxFormData.wpSl"></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12" v-if="showWpBz">
        <el-form-item prop="wpBz" label="备注：">
          <el-input type="textarea" v-model="tcxFormData.wpBz" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item prop="lqtj" label="领取条件：">
          <el-input placeholder="请输入领取条件" type="textarea" v-model="tcxFormData.lqtj" />
        </el-form-item>
      </el-col>
    </el-row>

  </el-form>
</template>
<script lang="ts" setup>

import { computed } from 'vue';
import { GzjzTcxVO } from '@/api/gongzheng/bzfz/gzjzTcx/types';
import { ref } from '@vue/reactivity';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { tcx_gzsx_data } from '@/views/gongzheng/gongzheng/components/sl/sl_gzsx/tcx_preset';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  gz_tc_bd,
  gz_tc_ywlx,
  gz_tc_bz,
  gz_tc_khh
} = toRefs<any>(proxy?.useDict('gz_tc_bd', 'gz_tc_ywlx', 'gz_tc_bz', 'gz_tc_khh'));

interface Props {
  modelValue: GzjzTcxVO,
}

const tcxFormRef = ref<ElFormInstance>(null);
const props = defineProps<Props>();

const rules = {
  tcrId: [
    { required: true, message: '提存人不能为空', trigger: ['blur'] }
  ],
  tclb: [
    { required: true, message: '提存类别不能为空', trigger: ['blur'] }
  ],
  tcyy: [
    { required: true, message: '提存原因不能为空', trigger: ['blur'] }
  ],
  ywlb: [
    { required: true, message: '业务类别不能为空', trigger: ['blur'] }
  ],
  tcrq: [
    { required: true, message: '提存日期不能为空', trigger: ['blur'] }
  ],
  tcbd: [
    { required: true, message: '提存标的不能为空', trigger: ['blur'] }
  ],

  hbLx: [{ required: true, message: '币种不能为空', trigger: ['blur'] }],
  hbJe: [{ required: true, message: '金额不能为空', trigger: ['blur'] }],
  hbZhrmb: [{ required: true, message: '折合人民币不能为空', trigger: ['blur'] }],
  hbKhh: [{ required: true, message: '开户行不能为空', trigger: ['blur'] }],
  hbYhzh: [{ required: true, message: '账号不能为空', trigger: ['blur'] }],
  hbRzsj: [{ required: true, message: '入账时间不能为空', trigger: ['blur'] }],
  wpMc: [{ required: true, message: '名称不能为空', trigger: ['blur'] }],
  wpSl: [{ required: true, message: '数量不能为空', trigger: ['blur'] }],
  lqtj: [
    { required: true, message: '领取条件不能为空', trigger: ['blur'] }
  ],
}

const gzjzDsrList = inject<Ref<GzjzDsrVO>>('gzjzDsrList', ref(null))

const emit = defineEmits(['update:modelValue']);

const tcxFormData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});


//设置提存人信息
const setTcrXx = () => {
  for (let i = 0; i < gzjzDsrList.value.length; i++) {
    let item = gzjzDsrList.value[i];
    if (item.id === tcxFormData.value.tcrId) {
      tcxFormData.value.tcrXm = item.name;
      return;
    }
  }

};

const showBz = ref(false);
const showHbje = ref(false);
const showHbZhrmb = ref(false);
const showHbKhh = ref(false);
const showHbYhzh = ref(false);
const showRzsj = ref(false);
const showWpMc = ref(false);
const showWpSl = ref(false);
const showWpBz = ref(false);

const ywlbDisable = ref(false);
const tcbdDisable = ref(false);
const hbLxDisable = ref(false);

const wpLable=reactive({
  wpmc:{title:'名称',placeholder:'请输入名称'},
  wpsl:{title:'数量',placeholder:'请输入数量'},
})


//业务类型 - 控制
const ywlbChange = () => {
  let ywlb = tcxFormData.value.ywlb;
  showBz.value = true;
  showHbje.value = true;

  showHbZhrmb.value = false;
  showHbKhh.value = false;
  showHbYhzh.value = false;
  showRzsj.value = false;

  showWpMc.value = false;
  showWpSl.value = false;
  showWpBz.value = false;

  tcbdDisable.value = false;
  hbLxDisable.value = false;

  if (ywlb === '2') {
    tcbdDisable.value = true;
    hbLxDisable.value = true;
    tcxFormData.value.tcbd = '1';
    tcxFormData.value.hbLx = '1';
  } else {
    tcbdDisable.value = false;
    hbLxDisable.value = false;

    showHbZhrmb.value = true;
    showHbKhh.value = true;
    showHbYhzh.value = true;
    showRzsj.value = true;
  }
  if (tcxFormData.value.id) {
    ywlbDisable.value = true;
  }else {
    ywlbDisable.value = false;
  }
};

//提存标的 - 控制
const tcbdChange = () => {
  let tcbd = tcxFormData.value.tcbd;
  showBz.value = false;
  showHbje.value = false;
  showHbZhrmb.value = false;
  showHbKhh.value = false;
  showHbYhzh.value = false;
  showRzsj.value = false;
  showWpMc.value = false;
  showWpSl.value = false;
  showWpBz.value = false;
  if (tcbd === '1') {
    if(tcxFormData.value.ywlb!=='2') {
      showHbZhrmb.value = true;
      showHbKhh.value = true;
      showHbYhzh.value = true;
      showRzsj.value = true;
    }
    //提存标的-货币
    showBz.value = true;
    showHbje.value = true;

  } else if (tcbd === '2') {
    //提存标的-有价证券
    showWpMc.value = true;
    showWpSl.value = true;
    showWpBz.value = true;
    wpLable.wpmc.title='证券名称'
    wpLable.wpmc.placeholder='请输入证券名称'
    wpLable.wpsl.title='证券数量'
    wpLable.wpsl.placeholder='请输入证券数量'
  } else {
    //提存标的-有价证券
    showWpMc.value = true;
    showWpSl.value = true;
    showWpBz.value = true;
    wpLable.wpmc.title='名称'
    wpLable.wpmc.placeholder='请输入名称'
    wpLable.wpsl.title='数量'
    wpLable.wpsl.placeholder='请输入数量'
  }
};



const validate = () => {
  return tcxFormRef.value?.validate();
};

defineExpose({
  validate,
  ywlbChange,
  tcbdChange
});


</script>
<style lang="scss" scoped>

:deep(.el-input input::-webkit-outer-spin-button ),
:deep(.el-input input::-webkit-inner-spin-button  ) {
  -webkit-appearance: none !important;
}

:deep(.el-input input[type='number'] ) {
  -moz-appearance: textfield;
}

</style>
