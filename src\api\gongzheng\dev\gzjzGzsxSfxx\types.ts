export interface GzjzGzsxSfxxVO {
  /**
   * ID
   */
  id: string | number;

  /** 公证卷宗公证事项ID */
  gzjzGzsxId: string | number;
  /** 公证事项 */
  gzjzGzsx: string;
  /** 公证书编号 */
  gzsbh: string;

  /** 费用类型（字典） */
  fylx: string;

  /** 是否记账 */
  sfjz: string;

  /** 计价方式（字典） */
  jjfs: string;

  /** 收费场景（字典） */
  sfcj: string;

  /** 应收金额 */
  fyys: number;

  /** 减免金额 */
  fyjm: number;
  /** 应付金额(应收金额-减免金额)*/
  yfje: number;
  /** 实收（缴费）金额 */
  fyss: number;
  /** 未收金额(应付金额-实收（缴费）金额) */
  wsje: number;
  /** 缴费金额 = 未收金额 */
  jfje: number;
  /** 减免申请ID */
  fyjmId: string | number;



  /** 退费金额 */
  fytf: number;

  /** 退费小计 */
  fytfXj: number;

  /** 退费审批ID */
  fytfId: string | number;

  /**
   * 收费状态（01未收费、02已完成）
   */
  sfzt: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 收费及发票ID
   */
  sfjfpId: string | number;

  /**
   * 收费来源
   */
  sfly: string;

}

export interface GzjzGzsxSfxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 费用类型（字典）
   */
  fylx?: string;

  /**
   * 是否记账
   */
  sfjz?: string;

  /**
   * 计价方式（字典）
   */
  jjfs?: string;

  /**
   * 收费场景（字典）
   */
  sfcj?: string;

  /**
   * 应收金额
   */
  fyys?: number;

  /**
   * 减免金额
   */
  fyjm?: number;

  /**
   * 减免申请ID
   */
  fyjmId?: string | number;

  /**
   * 实收（缴费）金额
   */
  fyss?: number;

  /**
   * 退费金额
   */
  fytf?: number;

  /**
   * 退费小计
   */
  fytfXj?: number;

  /**
   * 退费审批ID
   */
  fytfId?: string | number;

  /**
   * 收费状态（01未收费、02已完成）
   */
  sfzt?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 收费及发票ID
   */
  sfjfpId?: string | number;

  /**
   * 收费来源
   */
  sfly?: string;

}

export interface GzjzGzsxSfxxQuery extends PageQuery {

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 费用类型（字典）
   */
  fylx?: string;

  /**
   * 是否记账
   */
  sfjz?: string;

  /**
   * 计价方式（字典）
   */
  jjfs?: string;

  /**
   * 收费场景（字典）
   */
  sfcj?: string;

  /**
   * 应收金额
   */
  fyys?: number;

  /**
   * 减免金额
   */
  fyjm?: number;

  /**
   * 减免申请ID
   */
  fyjmId?: string | number;

  /**
   * 实收（缴费）金额
   */
  fyss?: number;

  /**
   * 退费金额
   */
  fytf?: number;

  /**
   * 退费小计
   */
  fytfXj?: number;

  /**
   * 退费审批ID
   */
  fytfId?: string | number;

  /**
   * 收费状态（01未收费、02已完成）
   */
  sfzt?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 收费及发票ID
   */
  sfjfpId?: string | number;

  /**
   * 收费来源
   */
  sfly?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




