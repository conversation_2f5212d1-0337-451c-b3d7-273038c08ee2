export interface TodoNotificationVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 待办标题
   */
  todoTitle: string;

  /**
   * 待办内容描述
   */
  todoContent: string;

  /**
   * 接收人用户ID
   */
  receiverId: string | number;

  /**
   * 接收人姓名
   */
  receiverName: string;

  /**
   * 是否已读
   */
  isRead: number;

  /**
   * 读取时间
   */
  readTime: string;

  /**
   * 任务ID
   */
  taskId: string | number;

  /**
   * 截止时间
   */
  dueDate: string;

  /**
   * 扩展数据（JSON格式）
   */
  extData: string;

  /**
   * 备注
   */
  remark: string;

}

export interface TodoNotificationForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 待办标题
   */
  todoTitle?: string;

  /**
   * 待办内容描述
   */
  todoContent?: string;

  /**
   * 接收人用户ID
   */
  receiverId?: string | number;

  /**
   * 接收人姓名
   */
  receiverName?: string;

  /**
   * 是否已读
   */
  isRead?: number;

  /**
   * 读取时间
   */
  readTime?: string;

  /**
   * 任务ID
   */
  taskId?: string | number;

  /**
   * 截止时间
   */
  dueDate?: string;

  /**
   * 扩展数据（JSON格式）
   */
  extData?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface TodoNotificationQuery extends PageQuery {

  /**
   * 待办标题
   */
  todoTitle?: string;

  /**
   * 待办内容描述
   */
  todoContent?: string;

  /**
   * 接收人用户ID
   */
  receiverId?: string | number;

  /**
   * 接收人姓名
   */
  receiverName?: string;

  /**
   * 是否已读
   */
  isRead?: number;

  /**
   * 读取时间
   */
  readTime?: string;

  /**
   * 任务ID
   */
  taskId?: string | number;

  /**
   * 截止时间
   */
  dueDate?: string;

  /**
   * 扩展数据（JSON格式）
   */
  extData?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



