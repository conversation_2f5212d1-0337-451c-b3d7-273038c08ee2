export interface GzjzSfjfpVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;
  gzjzJbxxVo: object;
  sfxxVoList: Array<object>;

  /**
   * 收费清单号
   */
  sfqdh: string;

  /**
   * 缴费人ID
   */
  jfrId: string | number;

  /**
   * 缴费人
   */
  jfr: string;

  /**
   * 支付方式
   */
  zffs: string;

  /**
   * 收费日期
   */
  sfrq: string;

  /**
   * 收费金额
   */
  sfje: number;

  /**
   * 收费人ID
   */
  sfrId: string | number;

  /**
   * 收费人
   */
  sfr: string;

  /**
   * 是否打印收据（0否，1是）
   */
  sfdysj: string;

  /**
   * 是否打印收据证明（0否，1是）
   */
  sfdysjzm: string;

  /**
   * 发票抬头
   */
  fptt: string;

  /**
   * 发票类型
   */
  fplx: string;

  /**
   * 发票号码
   */
  fphm: string;

  /**
   * 开票金额
   */
  fpje: number;

  /**
   * 开票日期
   */
  fprq: string;

  /**
   * 已收金额
   */
  ysje: number;

  /**
   * 退费金额
   */
  tfje: number;

  /**
   * 减免金额
   */
  jmje: number;

  /**
   * 是否收费
   */
  sfsf: string;

  /**
   * 收费来源
   */
  sfly: string;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzSfjfpForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 收费清单号
   */
  sfqdh?: string;

  /**
   * 缴费人ID
   */
  jfrId?: string | number;

  /**
   * 缴费人
   */
  jfr?: string;

  /**
   * 支付方式
   */
  zffs?: string;

  /**
   * 收费日期
   */
  sfrq?: string;

  /**
   * 收费金额
   */
  sfje?: number;

  /**
   * 收费人ID
   */
  sfrId?: string | number;

  /**
   * 收费人
   */
  sfr?: string;

  /**
   * 是否打印收据（0否，1是）
   */
  sfdysj?: string;

  /**
   * 是否打印收据证明（0否，1是）
   */
  sfdysjzm?: string;

  /**
   * 发票抬头
   */
  fptt?: string;

  /**
   * 发票类型
   */
  fplx?: string;

  /**
   * 发票号码
   */
  fphm?: string;

  /**
   * 开票金额
   */
  fpje?: number;

  /**
   * 开票日期
   */
  fprq?: string;

  /**
   * 已收金额
   */
  ysje?: number;

  /**
   * 退费金额
   */
  tfje?: number;

  /**
   * 减免金额
   */
  jmje?: number;

  /**
   * 是否收费
   */
  sfsf?: string;

  /**
   * 收费来源
   */
  sfly?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzSfjfpQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 收费清单号
   */
  sfqdh?: string;

  /**
   * 缴费人ID
   */
  jfrId?: string | number;

  /**
   * 缴费人
   */
  jfr?: string;

  /**
   * 支付方式
   */
  zffs?: string;

  /**
   * 收费日期
   */
  sfrq?: string;

  /**
   * 收费金额
   */
  sfje?: number;

  /**
   * 收费人ID
   */
  sfrId?: string | number;

  /**
   * 收费人
   */
  sfr?: string;

  /**
   * 是否打印收据（0否，1是）
   */
  sfdysj?: string;

  /**
   * 是否打印收据证明（0否，1是）
   */
  sfdysjzm?: string;

  /**
   * 发票抬头
   */
  fptt?: string;

  /**
   * 发票类型
   */
  fplx?: string;

  /**
   * 发票号码
   */
  fphm?: string;

  /**
   * 开票金额
   */
  fpje?: number;

  /**
   * 开票日期
   */
  fprq?: string;

  /**
   * 已收金额
   */
  ysje?: number;

  /**
   * 退费金额
   */
  tfje?: number;

  /**
   * 减免金额
   */
  jmje?: number;

  /**
   * 是否收费
   */
  sfsf?: string;

  /**
   * 收费来源
   */
  sfly?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



