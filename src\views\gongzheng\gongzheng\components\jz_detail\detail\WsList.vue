<template>
  <el-card>
    <template #header>
      <strong class="text-base">文书</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="zmmc" label="文书名称" align="center" />
      <el-table-column prop="name" label="译文" align="center" />
      <el-table-column prop="dsrLx" label="状态" align="center" />
      <el-table-column prop="zjlx" label="操作" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
