<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="机构编码" prop="jgbm">
              <el-input v-model="queryParams.jgbm" placeholder="请输入机构编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="受理编号" prop="slbh">
              <el-input v-model="queryParams.slbh" placeholder="请输入受理编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卷宗编号" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="queryParams.gzsbh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="归档人姓名" prop="gdrxm">
              <el-input v-model="queryParams.gdrxm" placeholder="请输入归档人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="归档日期" prop="gdrq">
              <el-date-picker clearable
                v-model="queryParams.gdrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择归档日期"
              />
            </el-form-item>
            <el-form-item label="保管/档案期限" prop="bgqx">
              <el-select v-model="queryParams.bgqx" placeholder="请选择保管/档案期限" clearable >
                <el-option v-for="dict in gz_dabh_qx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="保管类型" prop="bglx">
              <el-select v-model="queryParams.bglx" placeholder="请选择保管类型" clearable >
                <el-option v-for="dict in gz_dalx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb">
              <el-select v-model="queryParams.lb" placeholder="请选择公证类别" clearable >
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证事项" prop="gzsx">
              <el-select v-model="queryParams.gzsx" placeholder="请选择公证事项" clearable >
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="流程状态" prop="lczt">
              <el-select v-model="queryParams.lczt" placeholder="请选择流程状态" clearable >
                <el-option v-for="dict in gz_sl_lczt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证员ID" prop="gzybm">
              <el-input v-model="queryParams.gzybm" placeholder="请输入公证员ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员姓名" prop="gzyxm">
              <el-input v-model="queryParams.gzyxm" placeholder="请输入公证员姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="助理ID" prop="zlbm">
              <el-input v-model="queryParams.zlbm" placeholder="请输入助理ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="助理姓名" prop="zlxm">
              <el-input v-model="queryParams.zlxm" placeholder="请输入助理姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="受理地点" prop="sldd">
              <el-input v-model="queryParams.sldd" placeholder="请输入受理地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="受理日期" prop="slrq">
              <el-date-picker clearable
                v-model="queryParams.slrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择受理日期"
              />
            </el-form-item>
            <el-form-item label="使用地" prop="syd">
              <el-select v-model="queryParams.syd" placeholder="请选择使用地" clearable >
                <el-option v-for="dict in gz_sl_syd" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否认证" prop="rz">
              <el-select v-model="queryParams.rz" placeholder="请选择是否认证" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="紧急度" prop="jjd">
              <el-select v-model="queryParams.jjd" placeholder="请选择紧急度" clearable >
                <el-option v-for="dict in gz_sl_jjcd" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="领证地点" prop="lzdd">
              <el-input v-model="queryParams.lzdd" placeholder="请输入领证地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="领证日期" prop="lzrq">
              <el-date-picker clearable
                v-model="queryParams.lzrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择领证日期"
              />
            </el-form-item>
            <el-form-item label="用途" prop="yt">
              <el-select v-model="queryParams.yt" placeholder="请选择用途" clearable >
                <el-option v-for="dict in gz_yt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="译文文种" prop="ywwz">
              <el-select v-model="queryParams.ywwz" placeholder="请选择译文文种" clearable >
                <el-option v-for="dict in gz_yw_wz" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否密卷" prop="sfmj">
              <el-select v-model="queryParams.sfmj" placeholder="请选择是否密卷" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="档案类型" prop="dalx">
              <el-select v-model="queryParams.dalx" placeholder="请选择档案类型" clearable >
                <el-option v-for="dict in gz_dalx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="协办人ID" prop="xbrbh">
              <el-input v-model="queryParams.xbrbh" placeholder="请输入协办人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="协办人姓名" prop="xbrxm">
              <el-input v-model="queryParams.xbrxm" placeholder="请输入协办人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="法律援助" prop="flxz">
              <el-select v-model="queryParams.flxz" placeholder="请选择法律援助" clearable >
                <el-option v-for="dict in gz_flyz" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否外译中" prop="sfwyz">
              <el-select v-model="queryParams.sfwyz" placeholder="请选择是否外译中" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="电票领取电话" prop="dplqdh">
              <el-input v-model="queryParams.dplqdh" placeholder="请输入电票领取电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否零接触" prop="sfljc">
              <el-select v-model="queryParams.sfljc" placeholder="请选择是否零接触" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否电子签名" prop="sfdzqm">
              <el-select v-model="queryParams.sfdzqm" placeholder="请选择是否电子签名" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否电子公证书" prop="sfdzgzs">
              <el-select v-model="queryParams.sfdzgzs" placeholder="请选择是否电子公证书" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="归档人ID" prop="gdrbh">
              <el-input v-model="queryParams.gdrbh" placeholder="请输入归档人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="翻译人ID" prop="fyrbh">
              <el-input v-model="queryParams.fyrbh" placeholder="请输入翻译人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="翻译人姓名" prop="fyrxm">
              <el-input v-model="queryParams.fyrxm" placeholder="请输入翻译人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="外部订单号" prop="wbddh">
              <el-input v-model="queryParams.wbddh" placeholder="请输入外部订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务类型" prop="ywlx">
              <el-select v-model="queryParams.ywlx" placeholder="请选择业务类型" clearable >
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="制证时间" prop="zzsj">
              <el-date-picker clearable
                v-model="queryParams.zzsj"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择制证时间"
              />
            </el-form-item>
            <el-form-item label="出证时间" prop="czsj">
              <el-date-picker clearable
                v-model="queryParams.czsj"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择出证时间"
              />
            </el-form-item>
            <el-form-item label="审批人ID" prop="sprbm">
              <el-input v-model="queryParams.sprbm" placeholder="请输入审批人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批人姓名" prop="sprxm">
              <el-input v-model="queryParams.sprxm" placeholder="请输入审批人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人编码(多人时以“, ”分隔)" prop="dsrbm">
              <el-input v-model="queryParams.dsrbm" placeholder="请输入当事人编码(多人时以“, ”分隔)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人姓名(多人时以“, ”分隔)" prop="dsrxm">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入当事人姓名(多人时以“, ”分隔)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人编码(多人时以“, ”分隔)" prop="sqrbm">
              <el-input v-model="queryParams.sqrbm" placeholder="请输入申请人编码(多人时以“, ”分隔)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人姓名(多人时以“, ”分隔)" prop="sqrxm">
              <el-input v-model="queryParams.sqrxm" placeholder="请输入申请人姓名(多人时以“, ”分隔)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否作废" prop="sfzf">
              <el-select v-model="queryParams.sfzf" placeholder="请选择是否作废" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="签名章" prop="qmz">
              <el-input v-model="queryParams.qmz" placeholder="请输入签名章" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结案方式" prop="jyfs">
              <el-select v-model="queryParams.jyfs" placeholder="请选择结案方式" clearable >
                <el-option v-for="dict in gz_gzjz_jafs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="合成状态" prop="hczt">
              <el-select v-model="queryParams.hczt" placeholder="请选择合成状态" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="打印状态" prop="dyzt">
              <el-select v-model="queryParams.dyzt" placeholder="请选择打印状态" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="档案编号" prop="dabh">
              <el-input v-model="queryParams.dabh" placeholder="请输入档案编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzJbxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzJbxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzJbxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzJbxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzJbxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="${comment}" align="center" prop="id" v-if="true" />
        <el-table-column label="机构编码" align="center" prop="jgbm" />
        <el-table-column label="受理编号" align="center" prop="slbh" />
        <el-table-column label="卷宗编号" align="center" prop="jzbh" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="归档人姓名" align="center" prop="gdrxm" />
        <el-table-column label="归档日期" align="center" prop="gdrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.gdrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保管/档案期限" align="center" prop="bgqx">
          <template #default="scope">
            <dict-tag :options="gz_dabh_qx" :value="scope.row.bgqx"/>
          </template>
        </el-table-column>
        <el-table-column label="保管类型" align="center" prop="bglx">
          <template #default="scope">
            <dict-tag :options="gz_dalx" :value="scope.row.bglx"/>
          </template>
        </el-table-column>
        <el-table-column label="公证类别" align="center" prop="lb">
          <template #default="scope">
            <dict-tag :options="gz_gzlb" :value="scope.row.lb"/>
          </template>
        </el-table-column>
        <el-table-column label="公证事项" align="center" prop="gzsx">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.gzsx"/>
          </template>
        </el-table-column>
        <el-table-column label="流程状态" align="center" prop="lczt">
          <template #default="scope">
            <dict-tag :options="gz_sl_lczt" :value="scope.row.lczt"/>
          </template>
        </el-table-column>
        <el-table-column label="公证员ID" align="center" prop="gzybm" />
        <el-table-column label="公证员姓名" align="center" prop="gzyxm" />
        <el-table-column label="助理ID" align="center" prop="zlbm" />
        <el-table-column label="助理姓名" align="center" prop="zlxm" />
        <el-table-column label="受理地点" align="center" prop="sldd" />
        <el-table-column label="受理日期" align="center" prop="slrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.slrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用地" align="center" prop="syd">
          <template #default="scope">
            <dict-tag :options="gz_sl_syd" :value="scope.row.syd"/>
          </template>
        </el-table-column>
        <el-table-column label="是否认证" align="center" prop="rz">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.rz"/>
          </template>
        </el-table-column>
        <el-table-column label="紧急度" align="center" prop="jjd">
          <template #default="scope">
            <dict-tag :options="gz_sl_jjcd" :value="scope.row.jjd"/>
          </template>
        </el-table-column>
        <el-table-column label="领证地点" align="center" prop="lzdd" />
        <el-table-column label="领证日期" align="center" prop="lzrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lzrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="用途" align="center" prop="yt">
          <template #default="scope">
            <dict-tag :options="gz_yt" :value="scope.row.yt"/>
          </template>
        </el-table-column>
        <el-table-column label="译文文种" align="center" prop="ywwz">
          <template #default="scope">
            <dict-tag :options="gz_yw_wz" :value="scope.row.ywwz"/>
          </template>
        </el-table-column>
        <el-table-column label="是否密卷" align="center" prop="sfmj">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfmj"/>
          </template>
        </el-table-column>
        <el-table-column label="档案类型" align="center" prop="dalx">
          <template #default="scope">
            <dict-tag :options="gz_dalx" :value="scope.row.dalx"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="协办人ID" align="center" prop="xbrbh" />
        <el-table-column label="协办人姓名" align="center" prop="xbrxm" />
        <el-table-column label="法律援助" align="center" prop="flxz">
          <template #default="scope">
            <dict-tag :options="gz_flyz" :value="scope.row.flxz"/>
          </template>
        </el-table-column>
        <el-table-column label="是否外译中" align="center" prop="sfwyz">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfwyz"/>
          </template>
        </el-table-column>
        <el-table-column label="电票领取电话" align="center" prop="dplqdh" />
        <el-table-column label="是否零接触" align="center" prop="sfljc">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfljc"/>
          </template>
        </el-table-column>
        <el-table-column label="是否电子签名" align="center" prop="sfdzqm">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfdzqm"/>
          </template>
        </el-table-column>
        <el-table-column label="是否电子公证书" align="center" prop="sfdzgzs">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfdzgzs"/>
          </template>
        </el-table-column>
        <el-table-column label="归档人ID" align="center" prop="gdrbh" />
        <el-table-column label="翻译人ID" align="center" prop="fyrbh" />
        <el-table-column label="翻译人姓名" align="center" prop="fyrxm" />
        <el-table-column label="外部订单号" align="center" prop="wbddh" />
        <el-table-column label="业务类型" align="center" prop="ywlx">
          <template #default="scope">
            <dict-tag :options="gz_gzlb" :value="scope.row.ywlx"/>
          </template>
        </el-table-column>
        <el-table-column label="制证时间" align="center" prop="zzsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.zzsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="出证时间" align="center" prop="czsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.czsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批人ID" align="center" prop="sprbm" />
        <el-table-column label="审批人姓名" align="center" prop="sprxm" />
        <el-table-column label="当事人编码(多人时以“, ”分隔)" align="center" prop="dsrbm" />
        <el-table-column label="当事人姓名(多人时以“, ”分隔)" align="center" prop="dsrxm" />
        <el-table-column label="申请人编码(多人时以“, ”分隔)" align="center" prop="sqrbm" />
        <el-table-column label="申请人姓名(多人时以“, ”分隔)" align="center" prop="sqrxm" />
        <el-table-column label="是否作废" align="center" prop="sfzf">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfzf"/>
          </template>
        </el-table-column>
        <el-table-column label="签名章" align="center" prop="qmz" />
        <el-table-column label="结案方式" align="center" prop="jyfs">
          <template #default="scope">
            <dict-tag :options="gz_gzjz_jafs" :value="scope.row.jyfs"/>
          </template>
        </el-table-column>
        <el-table-column label="合成状态" align="center" prop="hczt">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.hczt"/>
          </template>
        </el-table-column>
        <el-table-column label="打印状态" align="center" prop="dyzt">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.dyzt"/>
          </template>
        </el-table-column>
        <el-table-column label="档案编号" align="center" prop="dabh" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzJbxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzJbxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-基本信息v1.4对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body>
      <el-form ref="gzjzJbxxFormRef" :model="form" :rules="rules" label-width="200px">
        <el-form-item label="机构编码" prop="jgbm">
          <el-input v-model="form.jgbm" placeholder="请输入机构编码" />
        </el-form-item>
        <el-form-item label="受理编号" prop="slbh">
          <el-input v-model="form.slbh" placeholder="请输入受理编号" />
        </el-form-item>
        <el-form-item label="卷宗编号" prop="jzbh">
          <el-input v-model="form.jzbh" placeholder="请输入卷宗编号" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsbh">
          <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="归档人姓名" prop="gdrxm">
          <el-input v-model="form.gdrxm" placeholder="请输入归档人姓名" />
        </el-form-item>
        <el-form-item label="归档日期" prop="gdrq">
          <el-date-picker clearable
            v-model="form.gdrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择归档日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保管/档案期限" prop="bgqx">
          <el-select v-model="form.bgqx" placeholder="请选择保管/档案期限">
            <el-option
                v-for="dict in gz_dabh_qx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保管类型" prop="bglx">
          <el-select v-model="form.bglx" placeholder="请选择保管类型">
            <el-option
                v-for="dict in gz_dalx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公证类别" prop="lb">
          <el-select v-model="form.lb" placeholder="请选择公证类别">
            <el-option
                v-for="dict in gz_gzlb"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公证事项" prop="gzsx">
          <el-select v-model="form.gzsx" placeholder="请选择公证事项">
            <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流程状态" prop="lczt">
          <el-select v-model="form.lczt" placeholder="请选择流程状态">
            <el-option
                v-for="dict in gz_sl_lczt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公证员ID" prop="gzybm">
          <el-input v-model="form.gzybm" placeholder="请输入公证员ID" />
        </el-form-item>
        <el-form-item label="公证员姓名" prop="gzyxm">
          <el-input v-model="form.gzyxm" placeholder="请输入公证员姓名" />
        </el-form-item>
        <el-form-item label="助理ID" prop="zlbm">
          <el-input v-model="form.zlbm" placeholder="请输入助理ID" />
        </el-form-item>
        <el-form-item label="助理姓名" prop="zlxm">
          <el-input v-model="form.zlxm" placeholder="请输入助理姓名" />
        </el-form-item>
        <el-form-item label="受理地点" prop="sldd">
          <el-input v-model="form.sldd" placeholder="请输入受理地点" />
        </el-form-item>
        <el-form-item label="受理日期" prop="slrq">
          <el-date-picker clearable
            v-model="form.slrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择受理日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用地" prop="syd">
          <el-select v-model="form.syd" placeholder="请选择使用地">
            <el-option
                v-for="dict in gz_sl_syd"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否认证" prop="rz">
          <el-radio-group v-model="form.rz">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="紧急度" prop="jjd">
          <el-select v-model="form.jjd" placeholder="请选择紧急度">
            <el-option
                v-for="dict in gz_sl_jjcd"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="领证地点" prop="lzdd">
          <el-input v-model="form.lzdd" placeholder="请输入领证地点" />
        </el-form-item>
        <el-form-item label="领证日期" prop="lzrq">
          <el-date-picker clearable
            v-model="form.lzrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择领证日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用途" prop="yt">
          <el-select v-model="form.yt" placeholder="请选择用途">
            <el-option
                v-for="dict in gz_yt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="译文文种" prop="ywwz">
          <el-select v-model="form.ywwz" placeholder="请选择译文文种">
            <el-option
                v-for="dict in gz_yw_wz"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否密卷" prop="sfmj">
          <el-radio-group v-model="form.sfmj">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="档案类型" prop="dalx">
          <el-select v-model="form.dalx" placeholder="请选择档案类型">
            <el-option
                v-for="dict in gz_dalx"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="协办人ID" prop="xbrbh">
          <el-input v-model="form.xbrbh" placeholder="请输入协办人ID" />
        </el-form-item>
        <el-form-item label="协办人姓名" prop="xbrxm">
          <el-input v-model="form.xbrxm" placeholder="请输入协办人姓名" />
        </el-form-item>
        <el-form-item label="法律援助" prop="flxz">
          <el-select v-model="form.flxz" placeholder="请选择法律援助">
            <el-option
                v-for="dict in gz_flyz"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否外译中" prop="sfwyz">
          <el-radio-group v-model="form.sfwyz">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="电票领取电话" prop="dplqdh">
          <el-input v-model="form.dplqdh" placeholder="请输入电票领取电话" />
        </el-form-item>
        <el-form-item label="是否零接触" prop="sfljc">
          <el-radio-group v-model="form.sfljc">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否电子签名" prop="sfdzqm">
          <el-radio-group v-model="form.sfdzqm">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否电子公证书" prop="sfdzgzs">
          <el-radio-group v-model="form.sfdzgzs">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="归档人ID" prop="gdrbh">
          <el-input v-model="form.gdrbh" placeholder="请输入归档人ID" />
        </el-form-item>
        <el-form-item label="翻译人ID" prop="fyrbh">
          <el-input v-model="form.fyrbh" placeholder="请输入翻译人ID" />
        </el-form-item>
        <el-form-item label="翻译人姓名" prop="fyrxm">
          <el-input v-model="form.fyrxm" placeholder="请输入翻译人姓名" />
        </el-form-item>
        <el-form-item label="外部订单号" prop="wbddh">
          <el-input v-model="form.wbddh" placeholder="请输入外部订单号" />
        </el-form-item>
        <el-form-item label="业务类型" prop="ywlx">
          <el-select v-model="form.ywlx" placeholder="请选择业务类型">
            <el-option
                v-for="dict in gz_gzlb"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="制证时间" prop="zzsj">
          <el-date-picker clearable
            v-model="form.zzsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择制证时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="出证时间" prop="czsj">
          <el-date-picker clearable
            v-model="form.czsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择出证时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批人ID" prop="sprbm">
          <el-input v-model="form.sprbm" placeholder="请输入审批人ID" />
        </el-form-item>
        <el-form-item label="审批人姓名" prop="sprxm">
          <el-input v-model="form.sprxm" placeholder="请输入审批人姓名" />
        </el-form-item>
        <el-form-item label="当事人编码(多人时以“, ”分隔)" prop="dsrbm">
          <el-input v-model="form.dsrbm" placeholder="请输入当事人编码(多人时以“, ”分隔)" />
        </el-form-item>
        <el-form-item label="当事人姓名(多人时以“, ”分隔)" prop="dsrxm">
          <el-input v-model="form.dsrxm" placeholder="请输入当事人姓名(多人时以“, ”分隔)" />
        </el-form-item>
        <el-form-item label="申请人编码(多人时以“, ”分隔)" prop="sqrbm">
          <el-input v-model="form.sqrbm" placeholder="请输入申请人编码(多人时以“, ”分隔)" />
        </el-form-item>
        <el-form-item label="申请人姓名(多人时以“, ”分隔)" prop="sqrxm">
          <el-input v-model="form.sqrxm" placeholder="请输入申请人姓名(多人时以“, ”分隔)" />
        </el-form-item>
        <el-form-item label="是否作废" prop="sfzf">
          <el-radio-group v-model="form.sfzf">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签名章" prop="qmz">
          <el-input v-model="form.qmz" placeholder="请输入签名章" />
        </el-form-item>
        <el-form-item label="结案方式" prop="jyfs">
          <el-select v-model="form.jyfs" placeholder="请选择结案方式">
            <el-option
                v-for="dict in gz_gzjz_jafs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合成状态" prop="hczt">
          <el-select v-model="form.hczt" placeholder="请选择合成状态">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打印状态" prop="dyzt">
          <el-select v-model="form.dyzt" placeholder="请选择打印状态">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="档案编号" prop="dabh">
          <el-input v-model="form.dabh" placeholder="请输入档案编号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzJbxx" lang="ts">
import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx } from '@/api/gongzheng/dev/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/dev/gzjzJbxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_dalx, gz_sl_jjcd, gz_flyz, gz_sl_syd, gz_yes_or_no, gz_yw_wz, gz_dabh_qx, gz_gzlb, sys_yes_no, gz_gzjz_jafs, gz_sl_lczt, gz_yt } = toRefs<any>(proxy?.useDict('gz_dalx', 'gz_sl_jjcd', 'gz_flyz', 'gz_sl_syd', 'gz_yes_or_no', 'gz_yw_wz', 'gz_dabh_qx', 'gz_gzlb', 'sys_yes_no', 'gz_gzjz_jafs', 'gz_sl_lczt', 'gz_yt'));

const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzJbxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzJbxxForm = {
  id: undefined,
  jgbm: undefined,
  slbh: undefined,
  jzbh: undefined,
  gzsbh: undefined,
  gdrxm: undefined,
  gdrq: undefined,
  bgqx: undefined,
  bglx: undefined,
  lb: undefined,
  gzsx: undefined,
  lczt: undefined,
  gzybm: undefined,
  gzyxm: undefined,
  zlbm: undefined,
  zlxm: undefined,
  sldd: undefined,
  slrq: undefined,
  syd: undefined,
  rz: undefined,
  jjd: undefined,
  lzdd: undefined,
  lzrq: undefined,
  yt: undefined,
  ywwz: undefined,
  sfmj: undefined,
  dalx: undefined,
  remark: undefined,
  xbrbh: undefined,
  xbrxm: undefined,
  flxz: undefined,
  sfwyz: undefined,
  dplqdh: undefined,
  sfljc: undefined,
  sfdzqm: undefined,
  sfdzgzs: undefined,
  gdrbh: undefined,
  fyrbh: undefined,
  fyrxm: undefined,
  wbddh: undefined,
  ywlx: undefined,
  zzsj: undefined,
  czsj: undefined,
  sprbm: undefined,
  sprxm: undefined,
  dsrbm: undefined,
  dsrxm: undefined,
  sqrbm: undefined,
  sqrxm: undefined,
  sfzf: undefined,
  qmz: undefined,
  jyfs: undefined,
  hczt: undefined,
  dyzt: undefined,
  dabh: undefined
}
const data = reactive<PageData<GzjzJbxxForm, GzjzJbxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jgbm: undefined,
    slbh: undefined,
    jzbh: undefined,
    gzsbh: undefined,
    gdrxm: undefined,
    gdrq: undefined,
    bgqx: undefined,
    bglx: undefined,
    lb: undefined,
    gzsx: undefined,
    lczt: undefined,
    gzybm: undefined,
    gzyxm: undefined,
    zlbm: undefined,
    zlxm: undefined,
    sldd: undefined,
    slrq: undefined,
    syd: undefined,
    rz: undefined,
    jjd: undefined,
    lzdd: undefined,
    lzrq: undefined,
    yt: undefined,
    ywwz: undefined,
    sfmj: undefined,
    dalx: undefined,
    xbrbh: undefined,
    xbrxm: undefined,
    flxz: undefined,
    sfwyz: undefined,
    dplqdh: undefined,
    sfljc: undefined,
    sfdzqm: undefined,
    sfdzgzs: undefined,
    gdrbh: undefined,
    fyrbh: undefined,
    fyrxm: undefined,
    wbddh: undefined,
    ywlx: undefined,
    zzsj: undefined,
    czsj: undefined,
    sprbm: undefined,
    sprxm: undefined,
    dsrbm: undefined,
    dsrxm: undefined,
    sqrbm: undefined,
    sqrxm: undefined,
    sfzf: undefined,
    qmz: undefined,
    jyfs: undefined,
    hczt: undefined,
    dyzt: undefined,
    dabh: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-基本信息v1.4列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzJbxx(queryParams.value);
  gzjzJbxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzJbxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzJbxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-基本信息v1.4";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzJbxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzJbxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-基本信息v1.4";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzJbxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzJbxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzJbxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzJbxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-基本信息v1.4编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzJbxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzJbxx/export', {
    ...queryParams.value
  }, `gzjzJbxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
