import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzGzsxVO, GzjzGzsxForm, GzjzGzsxQuery,GzjzGzsxChangeQuery,GzjzGzsxChangeVO } from './types';

/**
 * 查询公证事项列表
 */
export const listGzjzGzsx = (query: GzjzGzsxQuery): AxiosPromise<GzjzGzsxVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证事项详细
 */
export const getGzjzGzsx = (id: string | number): AxiosPromise<GzjzGzsxVO> => {
  return request({
    url: '/gongzheng/gzjzGzsx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证事项
 */
export const addGzjzGzsx = (data: GzjzGzsxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证事项
 */
export const updateGzjzGzsx = (data: GzjzGzsxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证事项
 */
export const delGzjzGzsx = (ids: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzGzsx/' + ids,
    method: 'delete'
  });
};

/**
 * 导出公证事项列表
 */
export const exportGzjzGzsx = (query: GzjzGzsxQuery) => {
  return request({
    url: '/gongzheng/gzjzGzsx/export',
    method: 'post',
    data: query
  });
};

/**
 * 公证事项 公证书取号
 */
export const gzsxTakeNumber = (data: { gzjzId: string | number }) => {
  return request({
    url: '/gongzheng/gzjzGzsx/getGzsTakeNumber',
    method: 'post',
    data: data
  });
};
/**
 * 公证事项 公证书销号  gzjzId  param[gzjzGzsxIds]
 */
export const revocationGzsNumber = (data: { gzjzId: string | number, ids: Array<string | number> }) => {
  return request({
    url: '/gongzheng/gzjzGzsx/revocationGzsNumber',
    method: 'post',
    data: data
  });
};

//流程调整 -列表
export const listByChange = (query?: GzjzGzsxChangeQuery): AxiosPromise<GzjzGzsxChangeVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsx/listByChange',
    method: 'get',
    params: query
  });
};
