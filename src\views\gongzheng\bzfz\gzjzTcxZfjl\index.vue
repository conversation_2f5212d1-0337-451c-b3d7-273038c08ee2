<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="提存项ID" prop="tcxId">
              <el-input v-model="queryParams.tcxId" placeholder="请输入提存项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="xm">
              <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付类型" prop="zflx">
              <el-select v-model="queryParams.zflx" placeholder="请选择支付类型" clearable >
                <el-option v-for="dict in gz_zffs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="客户姓名" prop="khxm">
              <el-input v-model="queryParams.khxm" placeholder="请输入客户姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开户行" prop="khh">
              <el-select v-model="queryParams.khh" placeholder="请选择开户行" clearable >
                <el-option v-for="dict in gz_tc_khh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="交易卡号" prop="jykh">
              <el-input v-model="queryParams.jykh" placeholder="请输入交易卡号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeZfsj"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzTcxZfjl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzTcxZfjl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzTcxZfjl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzTcxZfjl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzTcxZfjlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="提存项ID" align="center" prop="tcxId" />
        <el-table-column label="姓名" align="center" prop="xm" />
        <el-table-column label="支付类型" align="center" prop="zflx">
          <template #default="scope">
            <dict-tag :options="gz_zffs" :value="scope.row.zflx"/>
          </template>
        </el-table-column>
        <el-table-column label="客户姓名" align="center" prop="khxm" />
        <el-table-column label="开户行" align="center" prop="khh">
          <template #default="scope">
            <dict-tag :options="gz_tc_khh" :value="scope.row.khh"/>
          </template>
        </el-table-column>
        <el-table-column label="交易卡号" align="center" prop="jykh" />
        <el-table-column label="支付金额" align="center" prop="zfje" />
        <el-table-column label="支付时间" align="center" prop="zfsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.zfsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzTcxZfjl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzTcxZfjl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改提存项-支付记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzTcxZfjlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="提存项ID" prop="tcxId">
          <el-input v-model="form.tcxId" placeholder="请输入提存项ID" />
        </el-form-item>
        <el-form-item label="姓名" prop="xm">
          <el-input v-model="form.xm" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="支付类型" prop="zflx">
          <el-select v-model="form.zflx" placeholder="请选择支付类型">
            <el-option
                v-for="dict in gz_zffs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户姓名" prop="khxm">
          <el-input v-model="form.khxm" placeholder="请输入客户姓名" />
        </el-form-item>
        <el-form-item label="开户行" prop="khh">
          <el-select v-model="form.khh" placeholder="请选择开户行">
            <el-option
                v-for="dict in gz_tc_khh"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易卡号" prop="jykh">
          <el-input v-model="form.jykh" placeholder="请输入交易卡号" />
        </el-form-item>
        <el-form-item label="支付金额" prop="zfje">
          <el-input v-model="form.zfje" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="支付时间" prop="zfsj">
          <el-date-picker clearable
            v-model="form.zfsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择支付时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzTcxZfjl" lang="ts">
import { listGzjzTcxZfjl, getGzjzTcxZfjl, delGzjzTcxZfjl, addGzjzTcxZfjl, updateGzjzTcxZfjl } from '@/api/gongzheng/bzfz/gzjzTcxZfjl';
import { GzjzTcxZfjlVO, GzjzTcxZfjlQuery, GzjzTcxZfjlForm } from '@/api/gongzheng/bzfz/gzjzTcxZfjl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_zffs, gz_tc_khh } = toRefs<any>(proxy?.useDict('gz_zffs', 'gz_tc_khh'));

const gzjzTcxZfjlList = ref<GzjzTcxZfjlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeZfsj = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const gzjzTcxZfjlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzTcxZfjlForm = {
  id: undefined,
  tcxId: undefined,
  xm: undefined,
  zflx: undefined,
  khxm: undefined,
  khh: undefined,
  jykh: undefined,
  zfje: undefined,
  zfsj: undefined,
}
const data = reactive<PageData<GzjzTcxZfjlForm, GzjzTcxZfjlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tcxId: undefined,
    xm: undefined,
    zflx: undefined,
    khxm: undefined,
    khh: undefined,
    jykh: undefined,
    params: {
      zfsj: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    tcxId: [
      { required: true, message: "提存项ID不能为空", trigger: "blur" }
    ],
    xm: [
      { required: true, message: "姓名不能为空", trigger: "blur" }
    ],
    zfsj: [
      { required: true, message: "支付时间不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询提存项-支付记录列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeZfsj.value, 'Zfsj');
  const res = await listGzjzTcxZfjl(queryParams.value);
  gzjzTcxZfjlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzTcxZfjlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeZfsj.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzTcxZfjlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加提存项-支付记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzTcxZfjlVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzTcxZfjl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改提存项-支付记录";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzTcxZfjlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzTcxZfjl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzTcxZfjl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzTcxZfjlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除提存项-支付记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzTcxZfjl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzTcxZfjl/export', {
    ...queryParams.value
  }, `gzjzTcxZfjl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
