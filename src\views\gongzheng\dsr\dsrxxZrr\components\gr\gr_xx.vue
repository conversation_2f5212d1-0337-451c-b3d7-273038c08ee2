<template>
  <div>
    <el-form ref="dsrxxZrrFormRef" :model="form" :rules="rules" label-width="110px">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="18" :lg="18">
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="客户号" prop="khh">
                <el-input v-model="form.khh" disabled readonly placeholder="系统生成" />
                <el-popconfirm ref="ocrPopRef" width="200" icon="InfoFilled" title="选择识别提交方式">
                  <template #reference>
                    <el-button type="primary" size="small" link>OCR识别</el-button>
                  </template>
                  <template #actions="{ confirm, cancel }">
                    <div class="flex justify-center items-center px-10px gap-10px">
                      <el-upload ref="ocrUploadRef" :limit="1" :on-exceed="ocrFileExceed" :on-change="ocrFileChange" :auto-upload="false" :show-file-list="false" accept=".jpg,.jpeg,.png">
                        <template #trigger>
                          <el-button @click="confirm" size="small">上传照片</el-button>
                        </template>
                      </el-upload>
                      <el-button @click="() => { confirm; takePicStart(8); }" size="small">现场拍照</el-button>
                    </div>
                  </template>
                </el-popconfirm>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="姓名" prop="xm">
                <el-input v-model="form.xm" placeholder="请输入姓名" :disabled="!props.dialigEdit" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="曾用名" prop="cym">
                <el-input v-model="form.cym" placeholder="请输入曾用名" :disabled="!props.dialigEdit" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="译文名" prop="ywm">
                <el-input v-model="form.ywm" placeholder="请输入译文名" :disabled="!props.dialigEdit" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="国籍" prop="gj">
                <el-select v-model="form.gj" placeholder="请选择国籍" :disabled="!props.dialigEdit" filterable>
                  <el-option v-for="dict in areas" :key="dict.numberCode" :label="dict.areaName"
                    :value="dict.numberCode"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="民族" prop="mz">
                <el-input v-model="form.mz" placeholder="请输入民族"  :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="证件类型" prop="zjlx">
                <el-select v-model="form.zjlx" placeholder="请选择证件类型" :disabled="!props.dialigEdit">
                  <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="证件号码" prop="zjhm">
                <el-input v-model="form.zjhm" placeholder="请输入证件号码"  :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="签发机关" prop="signOffice">
                <el-input v-model="form.signOffice" placeholder="输入签发机关" :disabled="!props.dialigEdit" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="证件有效期" prop="cardValidDate">
                <el-input v-model="form.cardValidDate" placeholder="请输入证件有效期" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="性别" prop="xb">
                <el-select v-model="form.xb" placeholder="请选择性别" :disabled="!props.dialigEdit">
                  <el-option v-for="dict in gz_xb" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="主要证件" prop="zjzt" :disabled="!props.dialigEdit">
                <!-- <el-checkbox v-model="form.zjzt" :disabled="!props.dialigEdit">是</el-checkbox> -->
                <el-switch v-model="form.zjzt" inline-prompt active-text="是" inactive-text="否" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="出生日期" prop="csrq">
                <el-date-picker clearable v-model="form.csrq" type="date" value-format="YYYY-MM-DD"
                  placeholder="请选择出生日期" :disabled="!props.dialigEdit">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="联系电话" prop="lxdh">
                <el-input v-model="form.lxdh" placeholder="请输入联系电话" type="tel" :disabled="!props.dialigEdit" @input="handleTelInput"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="电子邮件" prop="dzyj">
                <el-input v-model="form.dzyj" placeholder="请输入电子邮件" type="email" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="婚姻状态" prop="hyzk">
                <el-select v-model="form.hyzk" placeholder="请选择婚姻状态" :disabled="!props.dialigEdit">
                  <el-option v-for="dict in gz_dsr_hyzt" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="指纹一" prop="zw">
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8">
              <el-form-item label="指纹二" prop="zw">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="工作单位" prop="gzdw">
                <el-input v-model="form.gzdw" placeholder="请输入工作单位" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="住址" prop="zz">
                <el-input v-model="form.zz" placeholder="请输入住址" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="联系住址" prop="lxzz">
                <el-input v-model="form.lxzz" placeholder="请输入联系住址" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="微信号" prop="wxh">
                <el-input v-model="form.wxh" placeholder="请输入微信号" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="开户行名称" prop="khhmc">
                <el-input v-model="form.khhmc" placeholder="请输入开户行名称" :disabled="!props.dialigEdit"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="开户行账号" prop="khhzh">
                <el-input v-model="form.khhzh" placeholder="请输入开户行账号" :disabled="!props.dialigEdit" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :xs="24" :sm="24" :md="6" :lg="6">
          <el-form-item label="照片采集" prop="zp">
            <el-row>
              <el-col :span="24">
                <!-- <el-image :src="form.zp" class="zpcss"></el-image> -->
                <div class="flex flex-col">
                  <SfzImg :src="takePicShow.sfzAvator.base64" @take-pic="() => takePicStart(9)" @upload="() => showUpload(9)" :hasControl="false" class="zpcss"/>
                  <el-progress v-if="takePicShow.zeroPr > 0" :percentage="takePicShow.zeroPr" :format="prFM" />
                </div>
              </el-col>
              <el-col :span="24">
                <el-button @click="idCardReaderStart" type="primary" link style="margin-top: 10p;" v-if="props.dialigEdit">采集</el-button>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="身份证正面">
            <div class="flex flex-col">
              <SfzImg :src="takePicShow.sfzImg1.base64" @take-pic="() => takePicStart(1)" @upload="() => showUpload(1)" class="sfz_css"/>
              <el-progress v-if="takePicShow.onePr > 0" :percentage="takePicShow.onePr" :format="prFM" />
            </div>
          </el-form-item>
          <el-form-item label="身份证反面">
            <div class="flex flex-col">
              <SfzImg :src="takePicShow.sfzImg2.base64" @take-pic="() => takePicStart(0)" @upload="() => showUpload(0)" class="sfz_css"/>
              <el-progress v-if="takePicShow.twoPr > 0" :percentage="takePicShow.twoPr" :format="prFM" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <TakePic v-model="takePicShow.show" v-if="takePicShow.show" @comfirm="takenPic" />
    <IdCardReader ref="idCardReaderRef" v-if="idCardReaderState.show" @close="() => {idCardReaderState.show = false}" @success="idCardReaderSuccess" ></IdCardReader>
    <DragUpload v-if="takePicShow.upShow" v-model="takePicShow.upShow" @on-all-done="onUploadDone" :multiple="false" accept=".png,.jpg,.jpeg" title="上传图片"/>
  </div>
</template>

<script setup name="Grxx" lang="ts">
import { getCurrentInstance, ref, reactive, toRefs, onBeforeMount, onMounted } from 'vue';
import { listAreaname } from '@/api/gongzheng/basicdata/areaname';
import { DsrxxZrrVO, DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
import { getGzjzDsr } from '@/api/gongzheng/gongzheng/gzjzDsr';
import SfzImg from './SfzImg.vue';
import TakePic from '@/views/gongzheng/components/takePic/index.vue'
import IdCardReader from '@/views/components/popIdCardReader.vue'
import DragUpload from '@/components/FileUpload/DragUpload.vue'
import { queryOssInfo, uploadFile } from '@/api/gongzheng/gongzheng/oss';
import { AxiosProgressEvent } from 'axios';
import { base64ToFile, fileToBase64 } from '@/utils/ruoyi';
import { validEmail, validIdCard } from '@/utils/validate';
import { getIdCardInfoByOcr } from '@/api/gongzheng/gongzheng/ocr';
import { genFileId, UploadFiles, UploadRawFile, UploadUserFile } from 'element-plus';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_lb, gz_gr_zjlx, gz_dsr_hyzt, gz_xb, gz_gj } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_dsr_hyzt', 'gz_xb', 'gz_gj'));
  const dsrxxZrrFormRef = ref<ElFormInstance>();
  interface Props {
    vo : DsrxxZrrVO;
    dialigEdit : boolean;
  }
  const props = defineProps<Props>();
  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    cardImage1: undefined,
    cardImage2: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: "无",
    ywm: "无",
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined,
    lxzz: undefined,
    zjzt: undefined,
    signOffice: undefined,
    cardValidDate: undefined,
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData, gj: '156', zjlx: '1', xb: '1', zjzt: true },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: undefined,
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      // khh: [
      //   { required: true, message: "客户号不能为空", trigger: "blur" }
      // ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "民族不能为空", trigger: "blur" }
      ],
      zjlx: [
        { required: true, message: "证件类型为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" },
        { validator: idCardValid, message: "证件号码格式不正确", trigger: "change" },
        { validator: idCardValid, message: "证件号码格式不正确", trigger: "blur" }
      ],
      dzyj: [
        { validator: emailValid, message: "邮箱格式不正确", trigger: "change" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });

  const areas = ref<any>([]);

  const { form, rules } = toRefs(data);

  const takePicShow = reactive({
    show: false,
    upShow: false,
    imgType: 1,   // 1正面 0反面 9当事人头像

    sfzAvator: {
      base64: '',
      file: null,
    },
    zeroPr: 0,

    sfzPic: null,   // 身份证证件照片
    sfzImg1: {
      base64: '',
      file: null
    },  // 身份证反面
    onePr: 0,
    sfzImg2: {
      base64: '',
      file: null
    },  // 身份证反面
    twoPr: 0,
  })

  const ocrUploadRef = ref<ElUploadInstance>(null)
  const ocrPopRef = ref(null)

  const idCardReaderState = reactive({
    show: false,
  })

  const idCardReaderRef = ref(null)

  // 定义事件
  const emits = defineEmits<{
    (e : 'update-count', vo : Partial<DsrxxZrrVO>) : void;
  }>();

function idCardValid(rule: any, value: any, callback: any) {
  if(!validIdCard(value)) {
    callback(new Error('证件号码格式不正确'))
  } else {
    callback()
  }
}

function emailValid(rule: any, value: any, callback: any) {
  if(value === '' || !value) {
    callback()
  } else {
    if(!validEmail(value)) {
      callback(new Error('邮箱格式不正确'))
    } else {
      callback()
    }
  }
}

const handleTelInput = (val: string) => {
  // 只允许输入数字
  form.value.lxdh = val.replace(/[^\d]/g, '');
  // 限制长度为11位
  if (form.value.lxdh.length > 11) {
    form.value.lxdh = form.value.lxdh.slice(0, 11);
  }
}

  const updateForm = () => {
    emits('update-count', form.value);
    console.log('grxx >>> ', form.value);
  };

  const takePicStart = (type: 0 | 1 | 8 | 9) => {
    takePicShow.show = true;
    takePicShow.imgType = type;
  }

  const takenPic = async (data: any) => {
    console.log('takenPic >>> ', data);
    if(takePicShow.imgType === 8) {
      ocrAction(data.base64)
    } else if(takePicShow.imgType === 9) {
      takePicShow.sfzAvator = data;
      ossFileSave(data.file, (val: AxiosProgressEvent) => {
        takePicShow.zeroPr = Math.round(val.progress * 100)
      }).then((res: any) => {
        if (res.code === 200) {
          form.value.zp = res.data.ossId;
        }
      })
    } else if(takePicShow.imgType === 1) {
      takePicShow.sfzImg1 = data
      ossFileSave(data.file, (val: AxiosProgressEvent) => {
        takePicShow.onePr = Math.round(val.progress * 100)
      }).then((res: any) => {
        if (res.code === 200) {
          form.value.cardImage1 = res.data.ossId;
        }
      })
    } else if(takePicShow.imgType === 0) {
      takePicShow.sfzImg2 = data
      ossFileSave(data.file, (val: AxiosProgressEvent) => {
        takePicShow.twoPr = Math.round(val.progress * 100)
      }).then((res: any) => {
        if (res.code === 200) {
          form.value.cardImage2 = res.data.ossId;
        }
      })
    }
    takePicShow.show = false;
  }

  const prFM = (val: any) => {
    return (val === 100 ? 'ok' : `${val}%`)
  }

  const ossFileSave = (file: File, progress?: (val: AxiosProgressEvent) => void) => {
    if(file) {
      const formData = new FormData();
      formData.append('file', file);
      return uploadFile(formData, progress)
    } else {
      return Promise.reject('请先上传身份证照片')
    }
  }

  const ocrFileExceed = (files: File[], uploadFiles: UploadUserFile[]) => {
    ocrUploadRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    ocrUploadRef.value!.handleStart(file)
  }

  const ocrFileChange = async (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
    console.log('ocrFileChange >> ', uploadFile)
    const base64 = await fileToBase64(uploadFile.raw);
    console.log('ocrFileChange >> base64', base64);
    ocrAction(base64)
  }

  const ocrAction = async (base64: string) => {
    console.log('::::::', ocrPopRef.value)

    const loadService = ElLoading.service({
      lock: true,
      text: '正在识别，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    try {
      const base64Prefix = 'data:image/png;base64,';
      const base64Split = base64.split(',');
      const bsLen = base64Split.length;
      // const res = await getIdCardInfoByOcr(base64);
      const res = await getIdCardInfoByOcr(bsLen === 2 ? base64Split[1] : base64Split[0]);
      if(res.data && res.data.name) {
        const { name, gender, birth, nationality, address, idNum, image, valid, signOrg } = res.data;
        // 返回数据填入表单

        const d = birth.replace('日', '').replace(/年|月/g, '-')
        const ds = d.split('-');
        ds[1] = String(ds[1]).padStart(2, '0');
        ds[2] = String(ds[2]).padStart(2, '0');

        form.value.csrq  = ds.join('-');
        form.value.zjlx = '1';
        form.value.zjhm = idNum || '';
        form.value.xm = name || '';
        form.value.xb = gender === '男' ? '1' : '2';
        form.value.mz = nationality || '';
        form.value.zz = address || '';
        // form.value.sfdk = '1';
        form.value.cardValidDate = valid || '';
        form.value.signOffice = signOrg || '';

        const base64 = base64Prefix + image;
        const file = await base64ToFile(base64);
        const doneData = {
          base64,
          file
        }

        takePicShow.sfzAvator = doneData;
        const avatorRes = await ossFileSave(doneData.file, (val: AxiosProgressEvent) => {
          takePicShow.zeroPr = Math.round(val.progress * 100)
        });

        if (avatorRes.code === 200) {
          form.value.zp = res.data.ossId;
        }

        ElMessage.success('识别成功')
      } else {
        ElMessage.error(res.msg || '识别失败')
      }
    } catch (err: any) {
      console.log('ocr识别异常 > ', err)
      ElMessage.error('识别失败')
    } finally {
      loadService.close()
    }
  }

  const idCardReaderStart = () => {
    idCardReaderState.show = true;
    nextTick(() => {
      idCardReaderRef.value?.open({});
    })
    console.log('idCardReaderStart >>> ');
  }

  const idCardReaderSuccess = async (data: any) => {
    if(!data) return;
    const {
      address,
      birth, // "1990年1月14日"
      errorMessage,
      gender,
      idNum,
      moreInfo,
      name,
      nation,
      photo, // 无前缀的base64
      signOrg, // 发证地点
      valid,  // "1990年1月14日"
    } = data;

    console.log('采集', data)

    const d = birth.replace('日', '').replace(/年|月/g, '-')
    const ds = d.split('-');
    ds[1] = String(ds[1]).padStart(2, '0');
    ds[2] = String(ds[2]).padStart(2, '0');

    const base64 = `data:image/png;base64,${ photo }`;
    const file = await base64ToFile(base64);
    const doneData = {
      base64,
      file
    }

    takePicShow.sfzAvator = doneData;
    ossFileSave(doneData.file, (val: AxiosProgressEvent) => {
      takePicShow.zeroPr = Math.round(val.progress * 100)
    }).then((res: any) => {
      if (res.code === 200) {
        form.value.zp = res.data.ossId;
      }
    })

    form.value.csrq  = ds.join('-');
    form.value.zjlx = '1';
    form.value.zjhm = idNum;
    form.value.xm = name;
    form.value.xb = gender === '男' ? '1' : '2';
    form.value.mz = nation;
    form.value.zz = address;
    form.value.sfdk = '1';
    form.value.cardValidDate = valid;
    form.value.signOffice = signOrg;
  }

  const showUpload = (type: 0 | 1 | 9) => {
    takePicShow.imgType = type
    takePicShow.upShow = true;
  }

  const onUploadDone = (data: any[]) => {
    const { ossId, url, fileName, path } = data[0]
    if(takePicShow.imgType === 9) {
      takePicShow.sfzAvator.base64 = url;
      form.value.zp = ossId;
    } else if(takePicShow.imgType === 1) {
      takePicShow.sfzImg1.base64 = url;
      form.value.cardImage1 = ossId;
    } else if(takePicShow.imgType === 0) {
      takePicShow.sfzImg2.base64 = url;
      form.value.cardImage2 = ossId;
    }

    takePicShow.upShow = false
  }

  const init = (vo : DsrxxZrrVO) => {
    if (vo.id) {
      form.value = vo;
      reqDsrxx(vo.id);
    }
  }

    // 查询当事人信息
  const reqDsrxx = async (id : string | number) => {
    try {
      const res = await getGzjzDsr(id);
      if (res.code === 200 && !!res.data) {
        console.log('获取当事人信息成功:', res.data);
        const info = res?.data?.zrrBo || {};
        const info1 = res?.data || {};
        delete info1?.zrrBo;
        form.value = { ...info, ...info1 };

        if(form.value.zp) {
          initSfzAvator(form.value.zp);
        }
        if (form.value.cardImage1) {
          initSfzImg1(form.value.cardImage1);
        }
        if (form.value.cardImage2) {
          initSfzImg2(form.value.cardImage2);
        }
      }
    } catch (err: any) {
      console.error('获取当事人信息失败:', err);
      ElMessage.error('获取当事人信息失败');
    } finally {}
  }

const getAreaName = async () => {
  try {
    const params = {
      pageSize: 300,
      pageNum: 1
    }
    const res = await listAreaname(params);
    if (res.code === 200) {
      areas.value = res.rows;
    }
  } catch (err: any) {
    console.error('获取地区名称失败:', err);
  }
}

const initSfzAvator = (ossId: string) => {
  queryOssInfo(ossId).then((res) => {
    if (res.code === 200) {
      takePicShow.sfzAvator.base64 = res.data[0].url || '';
    }
  })
}

const initSfzImg1 = (ossId: string) => {
  queryOssInfo(ossId).then((res) => {
    if (res.code === 200) {
      takePicShow.sfzImg1.base64 = res.data[0].url || '';
    }
  })
}
const initSfzImg2 = (ossId: string) => {
  queryOssInfo(ossId).then((res) => {
    if (res.code === 200) {
      takePicShow.sfzImg2.base64 = res.data[0].url || '';
    }
  })
}

const validate = () => {
  return dsrxxZrrFormRef.value?.validate()
}

onBeforeMount(() => {
  getAreaName();
})


watch(form, (value) => {
  if (value) {
    updateForm();
  }
},
{ deep: true });

defineExpose({
  form,
  updateForm,
  validate
});

onMounted(() => {
  console.log(props.dialigEdit)
  init(props.vo);
});
</script>

<style scoped>
  .zpcss {
    width: 150px;
    height: 180px;
    border: 1px solid #ccc;
  }

  .sfz_css {
    width: 150px;
    height: 122px;
    border: 1px solid #ccc;
  }
</style>
