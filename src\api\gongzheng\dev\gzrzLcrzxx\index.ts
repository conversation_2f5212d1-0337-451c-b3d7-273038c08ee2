import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzrzLcrzxxVO, GzrzLcrzxxForm, GzrzLcrzxxQuery } from '@/api/gongzheng/dev/gzrzLcrzxx/types';

/**
 * 查询公证日志-流程日志列表
 * @param query
 * @returns {*}
 */

export const listGzrzLcrzxx = (query?: GzrzLcrzxxQuery): AxiosPromise<GzrzLcrzxxVO[]> => {
  return request({
    url: '/gongzheng/gzrzLcrzxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证日志-流程日志详细
 * @param id
 */
export const getGzrzLcrzxx = (id: string | number): AxiosPromise<GzrzLcrzxxVO> => {
  return request({
    url: '/gongzheng/gzrzLcrzxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证日志-流程日志
 * @param data
 */
export const addGzrzLcrzxx = (data: GzrzLcrzxxForm) => {
  return request({
    url: '/gongzheng/gzrzLcrzxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证日志-流程日志
 * @param data
 */
export const updateGzrzLcrzxx = (data: GzrzLcrzxxForm) => {
  return request({
    url: '/gongzheng/gzrzLcrzxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证日志-流程日志
 * @param id
 */
export const delGzrzLcrzxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzrzLcrzxx/' + id,
    method: 'delete'
  });
};
