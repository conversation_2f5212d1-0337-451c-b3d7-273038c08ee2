<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="咨询ID" prop="zxId">
              <el-input v-model="queryParams.zxId" placeholder="请输入咨询ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证据名称ID" prop="zjmcId">
              <el-input v-model="queryParams.zjmcId" placeholder="请输入证据名称ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证据名称" prop="zjmc">
              <el-input v-model="queryParams.zjmc" placeholder="请输入证据名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:zxdxxZjmc:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:zxdxxZjmc:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:zxdxxZjmc:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:zxdxxZjmc:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="zxdxxZjmcList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="zxzjId" v-if="true" />
        <el-table-column label="咨询ID" align="center" prop="zxId" />
        <el-table-column label="证据名称ID" align="center" prop="zjmcId" />
        <el-table-column label="证据名称" align="center" prop="zjmc" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:zxdxxZjmc:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:zxdxxZjmc:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证-咨询单-证据名称对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="zxdxxZjmcFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="咨询ID" prop="zxId">
          <el-input v-model="form.zxId" placeholder="请输入咨询ID" />
        </el-form-item>
        <el-form-item label="证据名称ID" prop="zjmcId">
          <el-input v-model="form.zjmcId" placeholder="请输入证据名称ID" />
        </el-form-item>
        <el-form-item label="证据名称" prop="zjmc">
          <el-input v-model="form.zjmc" placeholder="请输入证据名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ZxdxxZjmc" lang="ts">
import { listZxdxxZjmc, getZxdxxZjmc, delZxdxxZjmc, addZxdxxZjmc, updateZxdxxZjmc } from '@/api/gongzheng/gongzheng/zxdxxZjmc';
import { ZxdxxZjmcVO, ZxdxxZjmcQuery, ZxdxxZjmcForm } from '@/api/gongzheng/gongzheng/zxdxxZjmc/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const zxdxxZjmcList = ref<ZxdxxZjmcVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const zxdxxZjmcFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ZxdxxZjmcForm = {
  zxzjId: undefined,
  zxId: undefined,
  zjmcId: undefined,
  zjmc: undefined,
  remark: undefined,
}
const data = reactive<PageData<ZxdxxZjmcForm, ZxdxxZjmcQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    zxId: undefined,
    zjmcId: undefined,
    zjmc: undefined,
    params: {
    }
  },
  rules: {
    zxzjId: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证-咨询单-证据名称列表 */
const getList = async () => {
  loading.value = true;
  const res = await listZxdxxZjmc(queryParams.value);
  zxdxxZjmcList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  zxdxxZjmcFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ZxdxxZjmcVO[]) => {
  ids.value = selection.map(item => item.zxzjId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证-咨询单-证据名称";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ZxdxxZjmcVO) => {
  reset();
  const _zxzjId = row?.zxzjId || ids.value[0]
  const res = await getZxdxxZjmc(_zxzjId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证-咨询单-证据名称";
}

/** 提交按钮 */
const submitForm = () => {
  zxdxxZjmcFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.zxzjId) {
        await updateZxdxxZjmc(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addZxdxxZjmc(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ZxdxxZjmcVO) => {
  const _zxzjIds = row?.zxzjId || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证-咨询单-证据名称编号为"' + _zxzjIds + '"的数据项？').finally(() => loading.value = false);
  await delZxdxxZjmc(_zxzjIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/zxdxxZjmc/export', {
    ...queryParams.value
  }, `zxdxxZjmc_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
