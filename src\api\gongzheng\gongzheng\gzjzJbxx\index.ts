import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzJbxxVO, GzjzJbxxForm, GzjzJbxxQuery, ApprovalType, CertifiCationType,ChangeProcessBo,ChangeGzsbmBo,ChangeCzsjBo, GzjzJbxxQueryByDsr } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

/**
 * 查询公证卷宗-基本信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzJbxx = (query?: GzjzJbxxQuery): AxiosPromise<GzjzJbxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzJbxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-基本信息v1.0详细
 * @param id
 */
export const getGzjzJbxx = (id: string | number): AxiosPromise<GzjzJbxxVO> => {
  return request({
    url: '/gongzheng/gzjzJbxx/' + id,
    method: 'get'
  });
};

/**
 * 查询公证卷宗-基本信息（含所有子表）
 * @param id
 */
export const getGzjzJbxxAll = (id: string | number): AxiosPromise<GzjzJbxxVO> => {
  return request({
    url: '/gongzheng/gzjzJbxx/all/' + id,
    method: 'get'
  });
};

/**
 * 根据dsr 查询公证卷宗-基本信息v1.0列表
 */
export const getGzjzJbxxByDsr = (params: GzjzJbxxQueryByDsr): AxiosPromise<GzjzJbxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzJbxx/listByDsrId',
    method: 'get',
    params,
  });
};

/**
 * 新增公证卷宗-基本信息v1.0
 * @param data
 */
export const addGzjzJbxx = (data: GzjzJbxxForm) => {
  return request({
    url: '/gongzheng/gzjzJbxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗备注
 */
export const updateGzjzBz = (data: { id: string, remark: string }) => {
  return request({
    url: '/gongzheng/gzjzJbxx/remark',
    method: 'post',
    data
  })
}

/**
 * 修改公证卷宗-基本信息v1.0
 * @param data
 */
export const updateGzjzJbxx = (data: GzjzJbxxForm) => {
  return request({
    url: '/gongzheng/gzjzJbxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-基本信息v1.0
 * @param id
 */
export const delGzjzJbxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzJbxx/' + id,
    method: 'delete'
  });
};
/**
 * 发起初核
 * @param data
 */
export const initialKernel = (data: { id: string | number }) => {
  return request({
    url: '/gongzheng/gzjzJbxx/initialKernel',
    method: 'post',
    data: data
  });
};

/**
 * 发起受理
 * @param data
 */
export const initiateAcceptance = (data: { id: string | number; slrq?: string }) => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateAcceptance',
    method: 'post',
    data: data
  });
};

/**
 * 发起审查
 * @param data
 */
export const initiateReview = (data: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateReview',
    method: 'post',
    data
  });
};

/**
 * 卷宗作废
 * @param data
 */
export const invalidation = (data: { id: string | number; zfyy?: string }): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/invalidation',
    method: 'post',
    data
  });
};

/**
 * 上报审批
 * @param data
 */
export const initiateApproval = (data: { id: string | number; lcyj?: string; sftg?: string }): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateApproval',
    method: 'post',
    data
  });
};

/**
 * 提交制证
 * @param data
 */
export const initiateProduction = (data: ApprovalType): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateProduction',
    method: 'post',
    data
  });
};

/**
 * 提交发证
 * @param data
 */
export const initiateSigned = (data: CertifiCationType): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateSigned',
    method: 'post',
    data
  });
};

/**
 * 提交归档
 * @param data
 */
export const initiateArchiving = (data: CertifiCationType): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiateArchiving',
    method: 'post',
    data
  });
};


export const listByCaiWu = (query?: GzjzJbxxQuery): AxiosPromise<GzjzJbxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzJbxx/listByCaiWu',
    method: 'get',
    params: query
  });
};


export const listByFanYi = (query?: GzjzJbxxQuery): AxiosPromise<GzjzJbxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzJbxx/listByFanYi',
    method: 'get',
    params: query
  });
};


// 流程变更
export const changeProcess = (data: ChangeProcessBo): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/changeProcess',
    method: 'post',
    data
  });
};
// 公证时间变更
export const changeCzsj = (data: ChangeCzsjBo): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/changeCzsj',
    method: 'post',
    data
  });
};
//公证书编号变更
export const changeGzsbm = (data: ChangeGzsbmBo): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzJbxx/changeGzsbm',
    method: 'post',
    data
  });
};
