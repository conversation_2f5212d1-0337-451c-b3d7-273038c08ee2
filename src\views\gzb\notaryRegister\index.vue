<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['gzb:notaryRegister:add']">批量上报</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus"  @click="handleUpdate()"
              v-hasPermi="['gzb:notaryRegister:edit']">异常同步</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Download" @click="handleExport"
              v-hasPermi="['gzb:notaryRegister:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="notaryRegisterList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" fixed="left" align="center" class-name="small-padding fixed-width">
          <template #default="scope">

          </template>
        </el-table-column>
        <el-table-column label="异常登记信息" align="center" prop="executeResult" />
        <el-table-column label="公协上报状态" align="center" prop="zyzbh" />
        <el-table-column label="省协上报状态" align="center" prop="zyzbh" />
        <el-table-column label="卷宗号" align="center" prop="zyzbh" />
        <el-table-column label="当事人" align="center" prop="notaryPersonName" />
        <el-table-column label="公证书编号" align="center" prop="notaryNum" />



        <el-table-column label="公证员执业证编号" align="center" prop="zyzbh" />
        <el-table-column label="公证处名称" align="center" prop="jgmc" />
        <el-table-column label="用户名" align="center" prop="notaryPersonName" />
        <el-table-column label="受理日期" align="center" prop="acceptDate" />
        <el-table-column label="公证业务类别" align="center" prop="itemName" />
        <el-table-column label="公证事项关联细项" align="center" prop="itemSubType" />
        <el-table-column label="是否补证" align="center" prop="isAdd" />
        <el-table-column label="公证类型" align="center" prop="notarizationType" />
        <el-table-column label="公证书年份" align="center" prop="notaryNumYear" />
        <el-table-column label="公证书编号后缀" align="center" prop="notaryNumSubfix" />

        <el-table-column label="补证公证书年份" align="center" prop="addNotaryNumYear" />
        <el-table-column label="补证公证书后缀" align="center" prop="addNotaryNumSubfix" />
        <el-table-column label="补证公证书编号" align="center" prop="addNotaryNum" />
        <el-table-column label="办证进度" align="center" prop="bzjd" />
        <el-table-column label="是否密卷" align="center" prop="isSecret" />
        <el-table-column label="用途" align="center" prop="useType" />
        <el-table-column label="公益法律服务" align="center" prop="welfareLawService" />
        <el-table-column label="使用地" align="center" prop="usePlace" />
        <el-table-column label="新型公证业务" align="center" prop="newNotarizationBusiness" />
        <el-table-column label="保护内容" align="center" prop="protectionContent" />
        <el-table-column label="涉疫情/灾情公证" align="center" prop="epidemicNotarization" />
        <el-table-column label="服务类型" align="center" prop="serviceType" />
        <el-table-column label="遗嘱保管取回日期" align="center" prop="takebackDate" />
        <el-table-column label="是否共同遗嘱" align="center" prop="typeCommon" />
        <el-table-column label="结案方式" align="center" prop="endType" />
        <el-table-column label="办结日期/不予办理日期/终止公证日期" align="center" prop="endDate" />
        <el-table-column label="审批人" align="center" prop="auditPerson" />
        <el-table-column label="实收金额" align="center" prop="itemCharge" />
        <el-table-column label="减免金额" align="center" prop="remitItemCharge" />
        <el-table-column label="公证书状态" align="center" prop="registerBookStatus" />
        <el-table-column label="撤证日期" align="center" prop="revokeDate" />
        <el-table-column label="执行金额" align="center" prop="executeMoney" />
        <el-table-column label="执行结果" align="center" prop="executeResult" />
        <el-table-column label="备注" align="center" prop="remark" />

      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="NotaryRegister" lang="ts">
  import { listNotaryRegister, getStatistics, batchReport, syncException } from '@/api/gzb/notaryRegister';
  import { NotaryRegisterVO, NotaryRegisterQuery, StatisticsVO } from '@/api/gzb/notaryRegister/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const notaryRegisterList = ref<NotaryRegisterVO[]>([]);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const dateRange = ref<[string, string]>(['2025-08-01', '2025-08-17']);

  // 统计信息
  const statistics = ref<StatisticsVO>({
    total: 0,
    verified: 0,
    normalReported: 0,
    abnormalReported: 0,
    unverified: 0,
    completed: 0,
    incomplete: 0,
    uploaded: 0,
    totalFee: 0
  });

  const queryFormRef = ref<ElFormInstance>();

  const data = reactive<{ queryParams : NotaryRegisterQuery }>({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      caseNumber: undefined,
      notarizationType: undefined,
      notaryItem: undefined,
      notaryNumType: undefined,
      notaryNumStatus: undefined,
      isCompleted: undefined,
      certVerifyInfo: undefined,
      reportStatus: undefined,
      provinceReportStatus: undefined,
      params: {}
    }
  });

  const { queryParams } = toRefs(data);

  /** 格式化日期 */
  const formatDate = (timestamp : number) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleDateString('zh-CN');
  };

  /** 查询公证登记簿主列表 */
  const getList = async () => {
    loading.value = true;
    // 添加日期范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginDate: dateRange.value[0],
        endDate: dateRange.value[1]
      };
    }

    const res = await listNotaryRegister(queryParams.value);
    notaryRegisterList.value = res.rows;
    total.value = res.total;
    loading.value = false;

    // 获取统计数据
    await getStatisticsData();
  };

  /** 获取统计数据 */
  const getStatisticsData = async () => {
    const res = await getStatistics(queryParams.value);
    statistics.value = res.data;
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    dateRange.value = ['2025-08-01', '2025-08-17'];
    handleQuery();
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : NotaryRegisterVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  };

  /** 批量上报 */
  const handleBatchReport = async () => {
    if (ids.value.length === 0) {
      proxy?.$modal.msgWarning('请选择要上报的数据');
      return;
    }

    await proxy?.$modal.confirm('是否确认批量上报选中的数据？');
    await batchReport(ids.value);
    proxy?.$modal.msgSuccess('批量上报成功');
    await getList();
  };

  /** 异常同步 */
  const handleSyncException = async () => {
    await proxy?.$modal.confirm('是否确认同步异常数据？');
    await syncException();
    proxy?.$modal.msgSuccess('异常同步成功');
    await getList();
  };

  /** 查看异常登记信息 */
  const handleViewRecord = (row : NotaryRegisterVO) => {
    proxy?.$modal.msgInfo(`查看异常登记信息: ${row.caseNumber}`);
  };

  /** 导出Excel */
  const handleExport = () => {
    proxy?.download('gzb/notaryRegister/export', {
      ...queryParams.value
    }, `notaryRegister_${new Date().getTime()}.xlsx`);
  };

  onMounted(() => {
    getList();
  });
</script>

<style scoped>
  .statistics-info {
    padding: 10px 0;
    line-height: 1.8;
  }

  .stat-text {
    color: #606266;
    font-size: 14px;
  }

  .stat-item {
    color: #409eff;
    font-weight: 500;
    margin-right: 10px;
  }
</style>
