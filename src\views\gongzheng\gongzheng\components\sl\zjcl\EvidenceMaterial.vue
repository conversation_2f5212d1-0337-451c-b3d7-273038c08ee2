<template>
  <div class="evidence-material">
    <div class="left-panel">
      <EvidenceTypeList :gzjzId="props.gzjzId" :types="evidenceTypes" :selectedTypeId="selectedTypeId"
        @select="handleTypeSelect" @selectDsr="handleDsreSelect" @selectDsrName="handleDsreSelectName"
        @add="openAddDialog" :loading="loadingTypes" :pagination="pagination" @page-change="handlePageChange"
        @del="handlePageDel" />
    </div>
    <div class="right-panel">
      <EvidenceFilePanel v-if="selectedTypeId" :selectedDsrId="selectedDsrId" :gzjzId="props.gzjzId"
        :typeId="selectedTypeId" :typeName="selectedTypeName" />
    </div>
    <EvidenceAddDialog :gzjzId="props.gzjzId" v-if="addDialogVisible" v-model:visible="addDialogVisible"
      :allTypes="allTypes" @confirm="handleAddTypes" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import EvidenceTypeList from './EvidenceTypeList.vue';
  import EvidenceAddDialog from './EvidenceAddDialog.vue';
  import EvidenceFilePanel from './EvidenceFilePanel.vue';
  import { listEvidenceTypes } from '@/api/gongzheng/gongzheng/zjcl/index';
  const props = defineProps<{
    gzjzId : number;
  }>();
  const evidenceTypes = ref<any[]>([]);
  const allTypes = ref<any[]>([]);
  const selectedTypeId = ref<string>('');
  const selectedDsrId = ref<string>('');
  const selectedDsrName = ref<string>('');
  const selectedTypeName = computed(() => {
    return evidenceTypes.value.find(t => t.id === selectedTypeId.value)?.zmmc || '';
  });
  const loadingTypes = ref(false);
  const addDialogVisible = ref(false);
  const pagination = ref({
    pageNum: 1,
    pageSize: 50,
    total: 0
  });

  const fetchTypes = async () => {
    loadingTypes.value = true;
    try {
      const res = await listEvidenceTypes({
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        gzjzId: props.gzjzId
      });
      // 保证每项都带有dsrMc字段
      evidenceTypes.value = (res.rows || []).map(item => ({
        ...item,
        dsrMc: item.dsrMc || ''
      }));
      console.log('evidenceTypes:', evidenceTypes.value);
      pagination.value.total = res.total || 0;
      allTypes.value = res.rows || [];
      if (!selectedTypeId.value && evidenceTypes.value.length) {
        selectedTypeId.value = evidenceTypes.value[0].id;
      }
    } finally {
      loadingTypes.value = false;
    }
  };

  const handleTypeSelect = (id : string) => {
    selectedTypeId.value = id;
    selectedDsrId.value = null;
  };
  const handleDsreSelect = (id : string) => {
    selectedDsrId.value = id;
  };
  const handleDsreSelectName = (rowId : string, dsrName : string) => {
    // 找到对应的类型，更新dsrMc
    const row = evidenceTypes.value.find(t => t.id === rowId);
    if (row) {
      row.dsrMc = dsrName;
      console.log(row)
    }
  };
  const openAddDialog = () => {
    nextTick(() => {
      addDialogVisible.value = true;
    })
  };
  const handleAddTypes = () => {
    addDialogVisible.value = false;
    fetchTypes();
  };
  const handlePageChange = (page : number) => {
    pagination.value.pageNum = page;
    fetchTypes();
  };
  const handlePageDel = () => {
    ElMessageBox.alert('删除成功', '系统提示', { type: 'success' });
    fetchTypes();
  }
  onMounted(fetchTypes);
</script>

<style scoped>
  .evidence-material {
    display: flex;
    height: 100%;
  }

  .left-panel {
    width: 40%;
    border-right: 1px solid #eee;
    padding: 16px 0;
    background: #fafbfc;
    overflow-y: auto;
  }

  .right-panel {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }
</style>
