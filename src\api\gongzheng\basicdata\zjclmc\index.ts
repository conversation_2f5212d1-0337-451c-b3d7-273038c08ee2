import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZjclmcVO, ZjclmcForm, ZjclmcQuery } from '@/api/basicdata/zjclmc/types';

/**
 * 查询证据名称列表
 * @param query
 * @returns {*}
 */

export const listZjclmc = (query?: ZjclmcQuery): AxiosPromise<ZjclmcVO[]> => {
  return request({
    url: '/basicdata/zjclmc/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询证据名称详细
 * @param id
 */
export const getZjclmc = (id: string | number): AxiosPromise<ZjclmcVO> => {
  return request({
    url: '/basicdata/zjclmc/' + id,
    method: 'get'
  });
};

/**
 * 新增证据名称
 * @param data
 */
export const addZjclmc = (data: ZjclmcForm) => {
  return request({
    url: '/basicdata/zjclmc',
    method: 'post',
    data: data
  });
};

/**
 * 修改证据名称
 * @param data
 */
export const updateZjclmc = (data: ZjclmcForm) => {
  return request({
    url: '/basicdata/zjclmc',
    method: 'put',
    data: data
  });
};

/**
 * 删除证据名称
 * @param id
 */
export const delZjclmc = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/zjclmc/' + id,
    method: 'delete'
  });
};


export const listTree = (query?: ZjclmcQuery): AxiosPromise<ZjclmcVO[]> => {
  return request({
    url: '/basicdata/zjclmc/listTree',
    method: 'get',
    params: query
  });
};



export const listExcludeChild = (gzsId: string | number): AxiosPromise<ZjclmcVO[]> => {
  return request({
    url: '/basicdata/zjclmc/list/exclude/' + gzsId,
    method: 'get'
  });
};
