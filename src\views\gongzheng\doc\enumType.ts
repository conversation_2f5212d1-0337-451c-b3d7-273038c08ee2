/**
 * 文档类型
 */
export enum EnumDocType {
  // ========================== 基础文档类型
  /**
   * 文书 1
   */
  WS = 'WS',

  /**
   * 笔录 2
   */
  BL = 'BL',

  /**
   * 公证书 3
   */
  GZS = 'GZS',

  /**
   * 公证书译文 4
   */
  GZSYW = 'GZSYW',

  /**
   * 当事人信息
   */
  DSR_INFO = 'DSR_INFO',

  /**
   * 个人信息 6
   */
  GRXX = 'GRXX',

  /**
   * 申请表 8
   */
  SQB = 'SQB',

  // ============================== 审批表类型
  /**
   * 受理通知单 9
   */
  SLTZD = 'SLTZD',

  /**
   * 公证审批表 11
   */
  GZSPB = 'GZSPB',

  /**
   * 公证涉外审批表 12
   */
  GZSWSPB = 'GZSWSPB',

  /**
   * 送达回执 13
   */
  SDHZ = 'SDHZ',

  /**
   * 签发稿 20
   */
  QFG = 'QFG',

  /**
   * 指纹合成 23
   */
  ZWHC = 'ZWHC',

  // =================================== 查询和调取类型
  /**
   * 存款查询涵 79
   */
  CKXH = 'CKXH',

  /**
   * 介绍信（内部） 86
   */
  JSX = 'JSX',

  /**
   * 介绍信（外部） 87
   */
  JSXWB = 'JSXWB',

  // ================================== 费用相关
  /**
   * 收费单 77
   */
  SFD = 'SFD',

  /**
   * 收据单 78
   */
  SJD = 'SJD',

  /**
   * 费用补退审批表 92
   */
  FYBTSPB = 'FYBTSPB',

  /**
   * 补正模板 93
   */
  BZB = 'BZB',

  // =================================== 比对类型
  /**
   * 面部识别对比 94
   */
  MBRDB = 'MBRDB',

  /**
   * 指纹对比 95
   */
  ZWDB = 'ZWDB',

  /**
   * 核实记录单 96
   */
  HSJLD = 'HSJLD',

  // ================================= 系统管理类型
  /**
   * 退费单 97
   */
  TFD = 'TFD',

  /**
   * 公证咨询单 99
   */
  ZXD = 'ZXD',

  /**
   * 终止公证情况说明 100
   */
  ZZGZQKSM = 'ZZGZQKSM',

  /**
   * 不予办理公证情况说明 101
   */
  BYBLGZQKSM = 'BYBLGZQKSM',

  /**
   * 书函模板 102
   */
  SHMB = 'SHMB',

  /**
   * 电子公证书封面 109
   */
  DZGZFM = 'DZGZFM',

  // ================================= 卷宗和档案类型
  /**
   * 卷宗封皮 10
   */
  JZFP = 'JZFP',

  /**
   * 调档回执 89
   */
  DDHZ = 'DDHZ',

  /**
   * 呈批表 81
   */
  CPB = 'CPB',

  /**
   * 呈批表 82
   */
  BPB = 'BPB',

  /**
   * 人事档案表 80
   */
  RSDAB = 'RSDAB',

  // ========================= 申请表格类型
  /**
   *  继承人申请表格 110
   */
  JCRQSQB = 'JCRQSQB',

  /**
   * 放弃继承人申请表格 111
   */
  FQJCRQSQB = 'FQJCRQSQB',

  /**
   * 外国收养申请表格 112
   */
  WGSYSQB = 'WGSYSQB',

  /**
   * 采集样本回执表格 113
   */
  CJYBHZB = 'CJYBHZB',
}

/**
 * 文档操作类型
 */
export enum EnumDocActionType {
  /**
   * 查看文档
   */
  VIEW = 'view',

  /**
   * 编辑文档
   */
  EDIT = 'edit',

  /**
   * 生成文档
   */
  GEN = 'generate',
}

/**
 * 附件类别 文档拟定1 证据材料2 笔录3 代书(文书)4 其他 9
 */
export enum EnumDocFileType {
  /**
   * 文档拟定
   */
  WDND = '1',

  /**
   * 证据材料
   */
  ZJCL = '2',

  /**
   * 笔录
   */
  BL = '3',

  /**
   * 代书(文书)
   */
  DS = '4',

  /**
   * 其他
   */
  QT = '9',
}
