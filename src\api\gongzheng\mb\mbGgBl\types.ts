export interface MbGgBlVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 变量key
   */
  blKey: string;

  /**
   * 变量类型
   */
  blType: string;

  /**
   * 变量名称
   */
  blName: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 有效状态
   */
  yxZt: number;

  /**
   * 变量格式
   */
  blGs: string;

}

export interface MbGgBlForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 变量key
   */
  blKey?: string;

  /**
   * 变量类型
   */
  blType?: string;

  /**
   * 变量名称
   */
  blName?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 有效状态
   */
  yxZt?: number;

  /**
   * 变量格式
   */
  blGs?: string;

}

export interface MbGgBlQuery extends PageQuery {

  /**
   * 变量key
   */
  blKey?: string;

  /**
   * 变量类型
   */
  blType?: string;

  /**
   * 变量名称
   */
  blName?: string;

  /**
   * 有效状态
   */
  yxZt?: number;

  /**
   * 变量格式
   */
  blGs?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



