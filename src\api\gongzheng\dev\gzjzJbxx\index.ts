import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzJbxxVO, GzjzJbxxForm, GzjzJbxxQuery } from '@/api/gongzheng/dev/gzjzJbxx/types';

/**
 * 查询公证卷宗-基本信息v1.4列表
 * @param query
 * @returns {*}
 */

export const listGzjzJbxx = (query?: GzjzJbxxQuery): AxiosPromise<GzjzJbxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzJbxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-基本信息v1.4详细
 * @param id
 */
export const getGzjzJbxx = (id: string | number): AxiosPromise<GzjzJbxxVO> => {
  return request({
    url: '/gongzheng/gzjzJbxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-基本信息v1.4
 * @param data
 */
export const addGzjzJbxx = (data: GzjzJbxxForm) => {
  return request({
    url: '/gongzheng/gzjzJbxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-基本信息v1.4
 * @param data
 */
export const updateGzjzJbxx = (data: GzjzJbxxForm) => {
  return request({
    url: '/gongzheng/gzjzJbxx',
    method: 'put',
    data: data
  });
};

/**
 * 修改公证卷宗备注
 */
export const updateGzjzBz = (data: { id: string, remark: string }) => {
  return request({
    url: '/gongzheng/gzjzJbxx/remark',
    method: 'post',
    data
  })
}

/**
 * 删除公证卷宗-基本信息v1.4
 * @param id
 */
export const delGzjzJbxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzJbxx/' + id,
    method: 'delete'
  });
};
