<template>
  <!-- 先予归档申请弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <div class="pre-archiving-form">
      <!-- 申请人信息 -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="form-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人:" prop="applicant">
              <el-input v-model="formData.applicant" placeholder="请输入申请人" readonly disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期:" prop="applicationDate">
              <el-date-picker v-model="formData.applicationDate" type="date" placeholder="请选择申请日期"
                value-format="YYYY-MM-DD" style="width: 100%" readonly disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审批人*:" prop="approver">
              <el-select v-model="formData.approver" filterable placeholder="请选择审批人" style="width: 100%">
               <el-option v-for="item in tsjzspy" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请原因 -->
        <el-form-item label="申请原因*:" prop="applicationReason">
          <el-input v-model="formData.applicationReason" type="textarea" :rows="4" placeholder="请输入申请原因" />
        </el-form-item>
      </el-form>

      <!-- 卷宗列表 -->
      <div class="case-section">
        <div class="section-header">
          <h3>卷宗列表</h3>
          <div class="search-section">
            <el-form-item label="条形码:" style="margin-bottom: 0; margin-right: 10px;">
              <el-input v-model="barcodeInput" placeholder="请输入条形码" style="width: 200px;" />
            </el-form-item>
            <el-button v-has-permi="['tslc:fq:edit']" type="primary" @click="handleSelectCase" >选择卷宗</el-button>
          </div>
        </div>

        <el-table :data="formData.caseList" border stripe style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="卷宗号" prop="jzbh" />
          <el-table-column label="公证书编号" prop="gzsbh" />
          <el-table-column label="当事人" prop="dsrxm" />
          <el-table-column label="公证事项" prop="gzsx" />
          <el-table-column label="公证员" prop="gzyxm" />
          <el-table-column label="助理/受理人" prop="zlxm" />
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button v-has-permi="['tslc:fq:edit']" type="danger" size="small" @click="handleRemoveCase(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-has-permi="['tslc:fq:edit']">提交审批</el-button>
        <el-button @click="handleSaveDraft" v-has-permi="['tslc:fq:edit']">保存草稿</el-button>
        <el-button @click="doModalClose">取消</el-button>
      </div>
    </template>
  </vxe-modal>

  <!-- 选择卷宗对话框 -->
  <SelectCaseDialog ref="selectCaseRef" @success="onCaseSelected" />
</template>

<script setup name="PopPreArchiving" lang="ts">
  import { ref, reactive, computed, getCurrentInstance, toRefs } from 'vue';
  import { ElMessage } from 'element-plus';
  import type { VxeModalProps } from 'vxe-table';
  import SelectCaseDialog from './SelectCaseDialog.vue';
  import { getToken } from '@/utils/auth';
  import { addTslcSqb, saveDraftTslcSqb } from '@/api/gongzheng/tslc/tslcSqb'
  import { useUserStore } from '@/store/modules/user'

  // Props 定义
  interface Props {
    title ?: string;
    width ?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '先予归档申请',
    width: '900px'
  });

  // Emits 定义
  const emits = defineEmits<{
    (event : 'success', data ?: any) : void;
    (event : 'close') : void;
  }>();

  // 获取组件实例
  const { proxy } = getCurrentInstance() as any;

  // 响应式数据
  const showPopup = ref(false);
  const formRef = ref();
  const selectCaseRef = ref<InstanceType<typeof SelectCaseDialog> | null>(null);
  const barcodeInput = ref('');

  // 审批人列表
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser('tsjzspy'));

  const userStore = useUserStore()

  // 弹窗配置
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: props.width,
    height: '80vh',
    escClosable: true,
    resize: true,
    showMaximize: true,
    destroyOnClose: true
  });

  // 表单数据
  const formData = reactive({
    id: null,
    applicant: userStore.nickname,
    applicationDate: new Date().toISOString().split('T')[0],
    approver: '',
    applicationReason: '',
    caseList: [] as any[]
  });

  // 表单验证规则
  const formRules = reactive({
    approver: [
      { required: true, message: '请选择审批人', trigger: 'change' }
    ],
    applicationReason: [
      { required: true, message: '请输入申请原因', trigger: 'blur' }
    ]
  });

  // 打开弹窗
  const open = async (option : any = {}) => {
    showPopup.value = true
    resetForm()
    if (option && option.id) {
      await loadDetailForEdit(option.id)
    }
  };

  // 关闭弹窗
  const close = () => {
    showPopup.value = false;
    emits('close');
  };

  // 弹窗关闭处理
  const doModalClose = () => {
    emits('close');
    showPopup.value = false;
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: null,
      applicant: userStore.nickname,
      applicationDate: new Date().toISOString().split('T')[0],
      approver: '',
      applicationReason: '',
      caseList: []
    });
    barcodeInput.value = '';
    formRef.value?.clearValidate();
  };

  // 选择卷宗
  const handleSelectCase = () => {
    selectCaseRef.value?.open();
  };

  // 卷宗选择成功回调
  const onCaseSelected = (caseInfo : any) => {
    // 检查是否已经添加过该卷宗
    const exists = formData.caseList.some(caseItem => caseItem.jzbh === caseInfo.jzbh);
    if (exists) {
      ElMessage.warning('该卷宗已存在，请勿重复添加');
      return;
    }

    // 添加到卷宗列表
    formData.caseList.push({
      id: caseInfo.id,
      jzbh: caseInfo.jzbh,
      gzsbh: caseInfo.gzsbh || '',
      dsrxm: caseInfo.dsrxm,
      gzsx: caseInfo.gzsx || '',
      gzyxm: caseInfo.gzyxm,
      zlxm: caseInfo.zlxm
    });

    ElMessage.success('卷宗添加成功');
  };

  // 移除卷宗
  const handleRemoveCase = (index : number) => {
    formData.caseList.splice(index, 1);
    ElMessage.success('卷宗移除成功');
  };

  // 编辑模式加载详情
  const loadDetailForEdit = async (id : string | number) => {
    try {
      const { getTslcSqb } = await import('@/api/gongzheng/tslc/tslcSqb')
      const res : any = await getTslcSqb(id)
      const data = res.data || {}
      formData.id = data.id;
      formData.applicant = data.tslcFqr || formData.applicant
      formData.applicationDate = data.tslcFqrq || formData.applicationDate
      formData.approver = data.tslcSprId
      formData.applicationReason = data.tslcSqyy || ''
      formData.caseList = data.caseList;
    } catch (e) {
      console.error('编辑加载失败', e)
    }
  }

  // 提交审批
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      // 验证是否选择了卷宗
      if (formData.caseList.length === 0) {
        ElMessage.warning('请先选择卷宗');
        return;
      }

      const payload : any = {
        tslcLx: '9',
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        caseList: formData.caseList
      }
      await addTslcSqb(payload)
      ElMessage.success('提交成功')
      emits('success', formData)
      close()
    } catch (error) {
      console.error('表单验证失败:', error);
      ElMessage.error('请检查表单信息');
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      const payload : any = {
        tslcLx: '9',
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        caseList: formData.caseList
      }
      await saveDraftTslcSqb(payload)
      ElMessage.success('草稿保存成功')
      emits('success', formData)
      close()
    } catch (e) {
      ElMessage.error('草稿保存失败')
    }
  };

  // 暴露方法给父组件
  defineExpose({
    open,
    close
  });
</script>

<style scoped>
  .pre-archiving-form {
    padding: 20px;
  }

  .form-container {
    margin-bottom: 20px;
  }

  .case-section {
    margin-bottom: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .search-section {
    display: flex;
    align-items: center;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>
