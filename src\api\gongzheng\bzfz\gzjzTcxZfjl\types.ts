export interface GzjzTcxZfjlVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 提存项ID
   */
  tcxId: string | number;

  /**
   * 姓名
   */
  xm: string;

  /**
   * 支付类型
   */
  zflx: string;

  /**
   * 客户姓名
   */
  khxm: string;

  /**
   * 开户行
   */
  khh: string;

  /**
   * 交易卡号
   */
  jykh: string;

  /**
   * 支付金额
   */
  zfje: number;

  /**
   * 支付时间
   */
  zfsj: string;

}

export interface GzjzTcxZfjlForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 姓名
   */
  xm?: string;

  /**
   * 支付类型
   */
  zflx?: string;

  /**
   * 客户姓名
   */
  khxm?: string;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 交易卡号
   */
  jykh?: string;

  /**
   * 支付金额
   */
  zfje?: number;

  /**
   * 支付时间
   */
  zfsj?: string;

}

export interface GzjzTcxZfjlQuery extends PageQuery {

  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 姓名
   */
  xm?: string;

  /**
   * 支付类型
   */
  zflx?: string;

  /**
   * 客户姓名
   */
  khxm?: string;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 交易卡号
   */
  jykh?: string;

  /**
   * 支付时间
   */
  zfsj?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



