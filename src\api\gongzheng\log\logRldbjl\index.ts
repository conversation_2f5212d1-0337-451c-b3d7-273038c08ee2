import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LogRldbjlVO, LogRldbjlForm, LogRldbjlQuery } from '@/api/gongzheng/log/logRldbjl/types';

/**
 * 查询日志-人脸对比记录列表
 * @param query
 * @returns {*}
 */

export const listLogRldbjl = (query?: LogRldbjlQuery): AxiosPromise<LogRldbjlVO[]> => {
  return request({
    url: '/log/logRldbjl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询日志-人脸对比记录详细
 * @param id
 */
export const getLogRldbjl = (id: string | number): AxiosPromise<LogRldbjlVO> => {
  return request({
    url: '/log/logRldbjl/' + id,
    method: 'get'
  });
};

/**
 * 新增日志-人脸对比记录
 * @param data
 */
export const addLogRldbjl = (data: LogRldbjlForm) => {
  return request({
    url: '/log/logRldbjl',
    method: 'post',
    data: data
  });
};

/**
 * 修改日志-人脸对比记录
 * @param data
 */
export const updateLogRldbjl = (data: LogRldbjlForm) => {
  return request({
    url: '/log/logRldbjl',
    method: 'put',
    data: data
  });
};

/**
 * 删除日志-人脸对比记录
 * @param id
 */
export const delLogRldbjl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/log/logRldbjl/' + id,
    method: 'delete'
  });
};
