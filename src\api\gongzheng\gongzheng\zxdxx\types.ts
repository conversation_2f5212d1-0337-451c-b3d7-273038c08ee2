export interface ZxdxxVO {
  /**
   * ID
   */
  zxId: string | number;

  /**
   * 咨询单号
   */
  zxDh: string;

  /**
   * 咨询日期
   */
  zxRq: string;

  /**
   * 咨询人ID
   */
  zxrId: string | number;

  /**
   * 咨询人姓名
   */
  zxrXm: string;

  /**
   * 公证类别（字典）
   */
  gzlb: string;

  /**
   * 接待人ID
   */
  jdrId: string | number;

  /**
   * 接待人姓名
   */
  jdrXm: string;

  /**
   * 使用地（字典）
   */
  syd: string;

  /**
   * 咨询电话
   */
  zxdh: string;

  /**
   * 其他情况
   */
  qtqk: string;

  /**
   * 咨询记录
   */
  zxjl: string;

  /**
   * 咨询单文件地址
   */
  zxdWjdz: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 公证事项名（多个逗号分隔）
   */
  gzsxMc: string;

}

export interface ZxdxxForm extends BaseEntity {
  /**
   * ID
   */
  zxId?: string | number;

  /**
   * 咨询单号
   */
  zxDh?: string;

  /**
   * 咨询日期
   */
  zxRq?: string;

  /**
   * 咨询人ID
   */
  zxrId?: string | number;

  /**
   * 咨询人姓名
   */
  zxrXm?: string;

  /**
   * 公证类别（字典）
   */
  gzlb?: string;

  /**
   * 接待人ID
   */
  jdrId?: string | number;

  /**
   * 接待人姓名
   */
  jdrXm?: string;

  /**
   * 使用地（字典）
   */
  syd?: string;

  /**
   * 咨询电话
   */
  zxdh?: string;

  /**
   * 其他情况
   */
  qtqk?: string;

  /**
   * 咨询记录
   */
  zxjl?: string;

  /**
   * 咨询单文件地址
   */
  zxdWjdz?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 公证事项名（多个逗号分隔）
   */
  gzsxMc?: string;

}

export interface ZxdxxQuery extends PageQuery {

  /**
   * 咨询单号
   */
  zxDh?: string;

  /**
   * 咨询日期
   */
  zxRq?: string;

  /**
   * 咨询人ID
   */
  zxrId?: string | number;

  /**
   * 咨询人姓名
   */
  zxrXm?: string;

  /**
   * 公证类别（字典）
   */
  gzlb?: string;

  /**
   * 接待人ID
   */
  jdrId?: string | number;

  /**
   * 接待人姓名
   */
  jdrXm?: string;

  /**
   * 使用地（字典）
   */
  syd?: string;

  /**
   * 咨询电话
   */
  zxdh?: string;

  /**
   * 其他情况
   */
  qtqk?: string;

  /**
   * 咨询记录
   */
  zxjl?: string;

  /**
   * 咨询单文件地址
   */
  zxdWjdz?: string;

  /**
   * 公证事项名（多个逗号分隔）
   */
  gzsxMc?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



