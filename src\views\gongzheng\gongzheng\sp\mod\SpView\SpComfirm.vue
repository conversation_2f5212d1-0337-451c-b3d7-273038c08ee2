<template>
  <el-dialog v-model="visible" :title="title" show-close>
    <div class="flex flex-col gap-10px">
      <p class="p-0 m-0">在审批环节，办证系统应当包括对下列内容的确认：</p>
      <CheckList ref="checkListRef" :data="checkList" default-all />
      <p class="p-0 m-0">前款第（五）项应当包括下列内容：</p>
      <p class="p-0 m-0">（一）公证事项及相应的收费标准、计算方法；</p>
      <p class="p-0 m-0">（二）因法律援助或者其它条件减免的申请与审核；</p>
      <p class="p-0 m-0">（三）公证预收费及公证费用核定；</p>
      <p class="p-0 m-0">（四）公证收费发票。</p>
    </div>
    <template #footer>
      <div class="flex items-center justify-end gap-10px">
        <el-button @click="confirm" type="primary">审批确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { initiateApproval, initiateProduction } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, computed } from 'vue';
import CheckList from '@/views/gongzheng/components/CheckList.vue';
import eventBus from '@/utils/eventBus';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
  spData: {
    spsj: string | Date;
    zzsj: string | Date;
    lcyj: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  title: '核实审查'
})

const checkList = ref([
  {
    value: '1',
    label: '（一）申请公证的事项及其文书真实、合法；'
  },
  {
    value: '2',
    label: '（二）公证事项的证明材料真实、合法、充分；'
  },
  {
    value: '3',
    label: '（三）办证程序符合《公证法》《公证程序规则》及有关办证规则的规定；'
  },
  {
    value: '4',
    label: '（四）拟出具的公证书内容、表述和格式符合相关规定；'
  },
  {
    value: '5',
    label: '（五）收取公证费符合收费标准规定；'
  },
  {
    value: '6',
    label: '（六）重大、复杂的公证事项，已提交公证机构集体讨论。'
  }
])

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['update:modelValue', 'pass', 'close', 'closed'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const checkListRef = ref<InstanceType<typeof CheckList> | null>(null);

// 提交审批
async function confirmSp() {
  const load = ElLoading.service({
    lock: true,
    text: '正在提交审批，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {
    const { spData } = props
    const params = {
      id: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      lcyj: spData.lcyj,
      spsj: spData.spsj,
      zzsj: spData.zzsj,
      sftg: '1',
    }
    const res = await initiateProduction(params);
    if (res.code === 200) {
      ElMessage.success('审批提交完成');
      close()
      emit('pass')
      eventBus.emit('sp:list:update', {
        msg: '审批通过已提交，更新审批列表'
      })
    }
    load.close()
  } catch(err: any) {
    console.error('审批提交失败', err)
    ElMessage.error('审批提交失败');
    load.close()
  } finally {
  }
}

function confirm() {
  const clrIns = checkListRef.value;
  if (!clrIns) return;

  if(clrIns.isAllChecked()) {
    confirmSp();
  } else {
    ElMessage.error('请确保各事项完备并勾选再提交确认')
  }
}

function close() {
  emit('close')
  emit('update:modelValue', false)
}

</script>
