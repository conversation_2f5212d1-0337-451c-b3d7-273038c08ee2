export interface GzjzYqtxVO {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq?: string;

  /**
   * 提醒-制证要求
   */
  txZzyq?: string;

  /**
   * 提醒-发证提醒
   */
  txFztx?: string;

  /**
   * 提醒-收费提醒
   */
  txSftx?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface GzjzYqtxForm {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq?: string;

  /**
   * 提醒-制证要求
   */
  txZzyq?: string;

  /**
   * 提醒-发证提醒
   */
  txFztx?: string;

  /**
   * 提醒-收费提醒
   */
  txSftx?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface GzjzYqtxQuery extends PageQuery {
  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq?: string;

  /**
   * 提醒-制证要求
   */
  txZzyq?: string;

  /**
   * 提醒-发证提醒
   */
  txFztx?: string;

  /**
   * 提醒-收费提醒
   */
  txSftx?: string;
}
