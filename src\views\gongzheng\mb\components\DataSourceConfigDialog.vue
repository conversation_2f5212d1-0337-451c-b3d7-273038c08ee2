<template>
  <el-dialog :model-value="visible" @update:model-value="emit('update:visible', $event)" title="数据源配置" width="80%"
    :before-close="handleDialogClose">
    <div class="data-source-config">
      <!-- 当前变量信息 -->
      <el-card style="margin-bottom: 20px;">
        <template #header>
          <span>当前变量：{{ variable?.blName }} - {{ variable?.blMc }}</span>
        </template>
        <p>变量类型：{{ variable?.blType }}</p>
        <p>是否必填：{{ variable?.isMandatory ? '是' : '否' }}</p>
      </el-card>

      <!-- 数据源配置列表 -->
      <el-table :data="dataSourceConfigs" border style="width: 100%">
        <el-table-column prop="sjyType" label="数据源类型" width="120">
          <template #default="scope">
            <el-tag :type="getDataSourceTypeTag(scope.row.sjyType)">
              {{ getDataSourceTypeName(scope.row.sjyType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priorityOrder" label="优先级" width="80" />
        <el-table-column prop="sjyConfig" label="配置内容" min-width="300">
          <template #default="scope">
            <div class="config-preview">
              {{ formatConfigPreview(scope.row.sjyConfig) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="80">
          <template #default="scope">
            <el-switch v-model="scope.row.isActive" active-value="1" inactive-value="0"
              @change="updateConfigStatus(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="editDataSource(scope.row)">编辑</el-button>
            <el-button type="success" size="small" @click="testDataSource(scope.row)">测试</el-button>
            <el-button type="danger" size="small" @click="deleteDataSource(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button type="primary" @click="addDataSource" style="margin-top: 20px;">添加数据源</el-button>
    </div>

    <!-- 数据源编辑弹窗 -->
    <el-dialog :model-value="editDataSourceDialogVisible" @update:model-value="editDataSourceDialogVisible = $event"
      title="编辑数据源" width="70%">
      <el-form :model="editingDataSource" label-width="120px">
        <el-form-item label="数据源类型" required>
          <el-select v-model="editingDataSource.sjyType" @change="onDataSourceTypeChange">
            <el-option v-for="item in dataSourceTypeOptions" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" required>
          <el-input-number v-model="editingDataSource.priorityOrder" :min="0" :max="100" />
          <span style="margin-left: 10px; color: #999;">数值越小优先级越高</span>
        </el-form-item>
        <el-form-item label="条件表达式">
          <el-input v-model="editingDataSource.condition" placeholder="如：#grVo.xm != null，留空表示总是执行" />
        </el-form-item>
        <!-- 实体字段配置 -->
        <div v-if="editingDataSource.sjyType === 'ENTITY_FIELD'">
          <el-form-item label="实体类" required>
            <el-select v-model="editingDataSource.entityClass" filterable>
              <el-option v-for="entity in entityClasses" :key="entity.value" :label="entity.label"
                :value="entity.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="字段名" required>
            <el-input v-model="editingDataSource.fieldName" placeholder="如：xm" />
          </el-form-item>
          <el-form-item label="上下文键" required>
            <el-input v-model="editingDataSource.contextKey" placeholder="如：grVo" />
          </el-form-item>
        </div>
        <!-- SQL查询配置 -->
        <div v-if="editingDataSource.sjyType === 'SQL_QUERY'">
          <el-form-item label="SQL语句" required>
            <el-input v-model="editingDataSource.sql" type="textarea" rows="3"
              placeholder="SELECT name FROM user WHERE id = ?" />
          </el-form-item>
          <el-form-item label="参数列表">
            <el-input v-model="editingDataSource.params" placeholder="参数表达式，如：#grVo.id" />
          </el-form-item>
        </div>
        <!-- Bean方法配置 -->
        <div v-if="editingDataSource.sjyType === 'BEAN_METHOD'">
          <el-form-item label="Bean名称" required>
            <el-input v-model="editingDataSource.beanName" placeholder="如：gzDsrxxZrrService" />
          </el-form-item>
          <el-form-item label="方法名" required>
            <el-input v-model="editingDataSource.methodName" placeholder="如：queryById" />
          </el-form-item>
          <el-form-item label="参数列表">
            <el-input v-model="editingDataSource.params" placeholder="参数表达式，如：#grVo.id" />
          </el-form-item>
        </div>
        <!-- 静态值配置 -->
        <div v-if="editingDataSource.sjyType === 'STATIC_VALUE'">
          <el-form-item label="静态值" required>
            <el-input v-model="editingDataSource.value" placeholder="直接值或表达式，如：#{#grVo.xm + '先生'}" />
          </el-form-item>
        </div>
        <el-form-item label="格式化模式">
          <el-input v-model="editingDataSource.formatPattern" placeholder="日期格式化模式，如：yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editingDataSource.remark" type="textarea" rows="2" placeholder="配置说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDataSourceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDataSource">保存</el-button>
        <el-button type="success" @click="testCurrentDataSource">测试配置</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/api/gongzheng/mb/mbBlSjy'
interface Props {
    blId : string | number,
    visible: boolean,
    variable: any
}
const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'refresh'])

const dataSourceConfigs = ref<any[]>([])
const dataSourceTypeOptions = ref<any[]>([])
const entityClasses = ref<any[]>([])
const editDataSourceDialogVisible = ref(false)
const editingDataSource = reactive<any>({
  id: null,
  sjyType: '',
  priorityOrder: 0,
  condition: '',
  entityClass: '',
  fieldName: '',
  contextKey: '',
  sql: '',
  params: '',
  beanName: '',
  methodName: '',
  value: '',
  formatPattern: '',
  remark: ''
})

watch(() => props.visible, (val) => {
  if (val && props.variable) {
    loadDataSourceConfigs(props.variable.id)
    loadDataSourceTypes()
    loadEntityClasses()
  }
})

const loadDataSourceConfigs = async (variableId: string) => {
  try {
    const res = await api.getDataSourceConfigs(variableId)
    if (res.data.code === 0) {
      dataSourceConfigs.value = res.data.data || []
    } else {
      dataSourceConfigs.value = []
      ElMessage.error(res.data.msg || '获取数据源配置失败')
    }
  } catch (e) {
    dataSourceConfigs.value = []
    ElMessage.error('获取数据源配置异常')
  }
}

const loadDataSourceTypes = async () => {
  try {
    const res = await api.getDataSourceTypes()
    if (res.data.code === 0) {
      dataSourceTypeOptions.value = res.data.data || []
    }
  } catch {}
}

const loadEntityClasses = async () => {
  try {
    const res = await api.getEntityClasses()
    if (res.data.code === 0) {
      entityClasses.value = res.data.data || []
    }
  } catch {}
}

const getDataSourceTypeTag = (type: string) => {
  const tagMap: any = {
    'ENTITY_FIELD': 'primary',
    'SQL_QUERY': 'success',
    'BEAN_METHOD': 'warning',
    'STATIC_VALUE': 'info'
  }
  return tagMap[type] || 'default'
}
const getDataSourceTypeName = (type: string) => {
  const found = dataSourceTypeOptions.value.find((item: any) => item.value === type)
  return found ? found.label : type
}
const formatConfigPreview = (configStr: string) => {
  try {
    const config = JSON.parse(configStr)
    const keys = Object.keys(config)
    if (keys.length <= 2) {
      return JSON.stringify(config, null, 2)
    }
    const preview: any = {}
    keys.slice(0, 2).forEach(key => {
      preview[key] = config[key]
    })
    return JSON.stringify(preview, null, 2) + '\n...'
  } catch (e) {
    return configStr
  }
}
const addDataSource = () => {
  resetEditingDataSource()
  editDataSourceDialogVisible.value = true
}
const editDataSource = (dataSource: any) => {
  try {
    const config = JSON.parse(dataSource.sjyConfig)
    editingDataSource.value = {
      id: dataSource.id,
      sjyType: dataSource.sjyType,
      priorityOrder: dataSource.priorityOrder,
      condition: config.condition || '',
      entityClass: config.entityClass || '',
      fieldName: config.fieldName || '',
      contextKey: config.contextKey || '',
      sql: config.sql || '',
      params: config.params ? config.params.join(',') : '',
      beanName: config.beanName || '',
      methodName: config.methodName || '',
      value: config.value || '',
      formatPattern: config.formatPattern || '',
      remark: dataSource.remark || ''
    }
    editDataSourceDialogVisible.value = true
  } catch (e) {
    ElMessage.error('配置数据解析失败')
  }
}
const resetEditingDataSource = () => {
  editingDataSource.value = {
    id: null,
    sjyType: dataSourceTypeOptions.value[0]?.value || 'ENTITY_FIELD',
    priorityOrder: 0,
    condition: '',
    entityClass: '',
    fieldName: '',
    contextKey: '',
    sql: '',
    params: '',
    beanName: '',
    methodName: '',
    value: '',
    formatPattern: '',
    remark: ''
  }
}
const saveDataSource = async () => {
  const config: any = {}
  if (editingDataSource.value.condition) config.condition = editingDataSource.value.condition
  if (editingDataSource.value.sjyType === 'ENTITY_FIELD') {
    config.entityClass = editingDataSource.value.entityClass
    config.fieldName = editingDataSource.value.fieldName
    config.contextKey = editingDataSource.value.contextKey
  } else if (editingDataSource.value.sjyType === 'SQL_QUERY') {
    config.sql = editingDataSource.value.sql
    config.params = editingDataSource.value.params ? editingDataSource.value.params.split(',') : []
  } else if (editingDataSource.value.sjyType === 'BEAN_METHOD') {
    config.beanName = editingDataSource.value.beanName
    config.methodName = editingDataSource.value.methodName
    config.params = editingDataSource.value.params ? editingDataSource.value.params.split(',') : []
  } else if (editingDataSource.value.sjyType === 'STATIC_VALUE') {
    config.value = editingDataSource.value.value
  }
  if (editingDataSource.value.formatPattern) config.formatPattern = editingDataSource.value.formatPattern
  try {
    if (editingDataSource.value.id) {
      await api.updateDataSourceConfig(editingDataSource.value.id, {
        blId: props.variable.id,
        sjyType: editingDataSource.value.sjyType,
        sjyConfig: JSON.stringify(config),
        priorityOrder: editingDataSource.value.priorityOrder,
        remark: editingDataSource.value.remark
      })
    } else {
      await api.addDataSourceConfig({
        blId: props.variable.id,
        sjyType: editingDataSource.value.sjyType,
        sjyConfig: JSON.stringify(config),
        priorityOrder: editingDataSource.value.priorityOrder,
        remark: editingDataSource.value.remark
      })
    }
    ElMessage.success('保存成功')
    editDataSourceDialogVisible.value = false
    await loadDataSourceConfigs(props.variable.id)
    emit('refresh')
  } catch (e) {
    ElMessage.error('保存失败')
  }
}
const testDataSource = async (dataSource: any) => {
  try {
    const config = JSON.parse(dataSource.sjyConfig)
    const res = await api.testDataSourceConfig({
      sjyType: dataSource.sjyType,
      sjyConfig: JSON.stringify(config)
    })
    if (res.data.code === 0) {
      ElMessage.success('测试成功：' + (res.data.data ?? ''))
    } else {
      ElMessage.error(res.data.msg || '测试失败')
    }
  } catch (e) {
    ElMessage.error('测试异常')
  }
}
const testCurrentDataSource = async () => {
  try {
    const config: any = {}
    if (editingDataSource.value.condition) config.condition = editingDataSource.value.condition
    if (editingDataSource.value.sjyType === 'ENTITY_FIELD') {
      config.entityClass = editingDataSource.value.entityClass
      config.fieldName = editingDataSource.value.fieldName
      config.contextKey = editingDataSource.value.contextKey
    } else if (editingDataSource.value.sjyType === 'SQL_QUERY') {
      config.sql = editingDataSource.value.sql
      config.params = editingDataSource.value.params ? editingDataSource.value.params.split(',') : []
    } else if (editingDataSource.value.sjyType === 'BEAN_METHOD') {
      config.beanName = editingDataSource.value.beanName
      config.methodName = editingDataSource.value.methodName
      config.params = editingDataSource.value.params ? editingDataSource.value.params.split(',') : []
    } else if (editingDataSource.value.sjyType === 'STATIC_VALUE') {
      config.value = editingDataSource.value.value
    }
    if (editingDataSource.value.formatPattern) config.formatPattern = editingDataSource.value.formatPattern
    const res = await api.testDataSourceConfig({
      sjyType: editingDataSource.value.sjyType,
      sjyConfig: JSON.stringify(config)
    })
    if (res.data.code === 0) {
      ElMessage.success('测试成功：' + (res.data.data ?? ''))
    } else {
      ElMessage.error(res.data.msg || '测试失败')
    }
  } catch (e) {
    ElMessage.error('测试异常')
  }
}
const deleteDataSource = (dataSource: any) => {
  ElMessageBox.confirm('确定要删除这个数据源配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await api.deleteDataSourceConfig(dataSource.id)
      ElMessage.success('删除成功')
      await loadDataSourceConfigs(props.variable.id)
      emit('refresh')
    } catch (e) {
      ElMessage.error('删除失败')
    }
  })
}
const updateConfigStatus = async (config: any) => {
  try {
    await api.updateConfigStatus(config.id, config.isActive)
    ElMessage.success('状态更新成功')
  } catch (e) {
    ElMessage.error('状态更新失败')
  }
}
const onDataSourceTypeChange = (type: string) => {
  if (type !== 'ENTITY_FIELD') {
    editingDataSource.value.entityClass = ''
    editingDataSource.value.fieldName = ''
    editingDataSource.value.contextKey = ''
  }
  if (type !== 'SQL_QUERY') {
    editingDataSource.value.sql = ''
  }
  if (type !== 'BEAN_METHOD') {
    editingDataSource.value.beanName = ''
    editingDataSource.value.methodName = ''
  }
  if (type !== 'STATIC_VALUE') {
    editingDataSource.value.value = ''
  }
  if (type !== 'SQL_QUERY' && type !== 'BEAN_METHOD') {
    editingDataSource.value.params = ''
  }
}
const handleDialogClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.data-source-config {
  min-height: 400px;
}
.config-preview {
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}
</style>
