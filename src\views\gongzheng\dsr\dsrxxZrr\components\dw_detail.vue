<template>
  <div>
    <el-drawer :title="props.title" v-model="visible" append-to-body size="100%" @close="cancel2">
      <Dwxx v-if="visible" ref="dwxxRef" :vo="form2" :form-pro="formPro" :dialigEdit="props.dialigEdit"
        @update-count="handleUpdateGwxx">
      </Dwxx>
      <!--证件列表-->
      <Zjlb v-if="visible" ref="zjlbRef" :vo="form2"></Zjlb>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="props.dialigEdit" :loading="buttonLoading2" type="primary" @click="submitForm2">保
            存</el-button>
          <el-button @click="cancel2">关 闭</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  import { addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import Zjlb from '@/views/gongzheng/dsr/dsrxxZrr/components/zjlb.vue'
  import Dwxx from '@/views/gongzheng/dsr/dsrxxZrr/components/dw/dw_xx.vue'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const zjlbRef = ref<InstanceType<typeof Zjlb> | null>(null);
  const dwxxRef = ref<InstanceType<typeof Dwxx> | null>(null);
  const buttonLoading2 = ref(false);

  interface Props {
    dialog : boolean;
    dialigEdit : boolean;
    title : string;
    formPro?: any;
  }
  const props = defineProps<Props>();

  const emits = defineEmits(['update:dialog', 'close']);

  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: undefined,
    ywm: undefined,
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined
  }
  const initDwxxFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: "1",
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });

  const visible = computed({
    get: () => props.dialog,
    set: (val) => emits('update:dialog', val)
  })

  const { queryParams, form } = toRefs(data);
  /** 单位客户*/
  const data2 = reactive<DsrxxFrhzzForm, DsrxxFrhzzQuery>({
    form2: { ...initDwxxFormData },
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { queryParams2, form2 } = toRefs(data2);

  //单位客户提交
  const submitForm2 = async () => {
    buttonLoading2.value = true;
    form2.value.dsrlb = "2";
    if (form2.value.id) {
      await updateDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    } else {
      await addDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    }
    proxy?.$modal.msgSuccess("操作成功");
    emits('update:dialog', false)
    emits('close', '');
    // queryParams.value.dsrlb = "2";
    // handleQuery();
  }
  /** 取消按钮 */
  const cancel2 = () => {
    // dialog2.visible = false;
    emits('update:dialog', false)
    emits('close', '');
  }


  const handleUpdateGwxx = (vo : DsrxxFrhzzVO) => {
    if (vo) {
      form2.value = vo;
      console.log(form2.value)
    }
  };
</script>

<style>
</style>
