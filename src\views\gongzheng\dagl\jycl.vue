<template>
  <div class="app-container">
    <!-- 视图切换 -->
    <div class="view-toggle">
      <el-radio-group v-model="viewMode">
        <el-radio-button label="history">历史档案记录</el-radio-button>
        <el-radio-button label="processing">处理中</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 借阅列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">借阅列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="handleBorrow">申请借阅</el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="borrowList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="受理人" align="center" prop="handler" />
        <el-table-column label="申请日期" align="center" prop="applyDate" width="180" />
        <el-table-column label="公证书号" align="center" prop="certNumber" />
        <el-table-column label="审批人" align="center" prop="approver" />
        <el-table-column label="归还日期" align="center" prop="returnDate" width="180" />
        <el-table-column label="档案流程" align="center" prop="archiveProcess" />
        <el-table-column label="操作档案移交人员" align="center" prop="transferPerson" />
        <el-table-column label="操作档案归还人员" align="center" prop="returnPerson" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>首页</span>
          <span>上一页</span>
          <el-input v-model="currentPage" class="page-input" />
          <span>共 {{ totalPages }} 页</span>
          <span>下一页</span>
          <span>尾页</span>
          <el-select v-model="pageSize" class="page-size-select">
            <el-option :value="10" label="10" />
          </el-select>
        </div>
        <div class="pagination-count">
          {{ startIndex }} - {{ endIndex }} 共 {{ total }} 条
        </div>
      </div>
    </div>

    <!-- 借阅申请对话框 -->
    <el-dialog
      title="借阅申请"
      v-model="dialogVisible"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="borrowFormRef" :model="borrowForm" :rules="rules" label-width="100px">
        <el-form-item label="内部调性：">
          <el-radio v-model="borrowForm.borrowType" label="internal" checked>内部调性</el-radio>
        </el-form-item>
        
        <el-form-item label="受理人：" prop="handler">
          <el-input v-model="borrowForm.handler" placeholder="请输入受理人" style="width: 200px" />
          <span class="form-date">申请日期：{{ currentDate }}</span>
        </el-form-item>

        <el-form-item label="拟调取公证书编号：" prop="certNumber">
          <div class="cert-input-group">
            <el-radio v-model="borrowForm.certType" label="system">系统案件</el-radio>
            <el-input v-model="borrowForm.certNumber" placeholder="请输入公证书编号" style="width: 400px" />
          </div>
        </el-form-item>

        <el-form-item label="请选择调档类别：" prop="archiveType">
          <el-select v-model="borrowForm.archiveType" placeholder="请选择" style="width: 200px">
            <el-option label="复印证件材料" value="复印证件材料" />
            <el-option label="查阅" value="查阅" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="请填写调档原因：" prop="reason">
          <el-input
            v-model="borrowForm.reason"
            type="textarea"
            placeholder="请填写调档原因"
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="调档费用：" prop="fee">
          <el-input v-model="borrowForm.fee" placeholder="请输入费用" style="width: 200px" />
        </el-form-item>

        <el-form-item label="请选择审批人：" prop="approver">
          <el-select v-model="borrowForm.approver" placeholder="请选择" style="width: 200px">
            <el-option v-for="item in approverOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBorrowForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'

// 视图模式
const viewMode = ref('history')

// 加载状态
const loading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value + 1)
const endIndex = computed(() => Math.min(currentPage.value * pageSize.value, total.value))
const currentDate = computed(() => {
  const date = new Date()
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
})

// 审批人选项
const approverOptions = ref([
  { value: '1', label: '张三' },
  { value: '2', label: '李四' },
  { value: '3', label: '王五' }
])

// 借阅列表数据
const borrowList = ref([
  {
    handler: '黄锡光',
    applyDate: '2018年11月09日',
    certNumber: '(2018) 桂东博证字第8306号',
    approver: '',
    returnDate: '',
    archiveProcess: '受理',
    transferPerson: '',
    returnPerson: ''
  },
  {
    handler: '廖梅发',
    applyDate: '2018年11月08日',
    certNumber: '(2018) 桂东博证字第7941号',
    approver: '刘北辛',
    returnDate: '',
    archiveProcess: '完成',
    transferPerson: '廖梅发',
    returnPerson: ''
  }
])

// 对话框相关
const dialogVisible = ref(false)
const borrowFormRef = ref<FormInstance>()

// 借阅表单对象
const borrowForm = reactive({
  borrowType: 'internal',
  handler: '',
  certType: 'system',
  certNumber: '',
  archiveType: '',
  reason: '',
  fee: '',
  approver: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  handler: [{ required: true, message: '请输入受理人', trigger: 'blur' }],
  certNumber: [{ required: true, message: '请输入公证书编号', trigger: 'blur' }],
  archiveType: [{ required: true, message: '请选择调档类别', trigger: 'change' }],
  reason: [{ required: true, message: '请填写调档原因', trigger: 'blur' }],
  approver: [{ required: true, message: '请选择审批人', trigger: 'change' }]
})

// 查询借阅列表
const getList = () => {
  loading.value = true
  // 这里应该是实际的API调用
  setTimeout(() => {
    // 根据视图模式过滤数据
    if (viewMode.value === 'processing') {
      borrowList.value = [
        {
          handler: '黄锡光',
          applyDate: '2018年11月09日',
          certNumber: '(2018) 桂东博证字第8306号',
          approver: '',
          returnDate: '',
          archiveProcess: '受理',
          transferPerson: '',
          returnPerson: ''
        }
      ]
      total.value = 1
    } else {
      borrowList.value = [
        {
          handler: '黄锡光',
          applyDate: '2018年11月09日',
          certNumber: '(2018) 桂东博证字第8306号',
          approver: '',
          returnDate: '',
          archiveProcess: '受理',
          transferPerson: '',
          returnPerson: ''
        },
        {
          handler: '廖梅发',
          applyDate: '2018年11月08日',
          certNumber: '(2018) 桂东博证字第7941号',
          approver: '刘北辛',
          returnDate: '',
          archiveProcess: '完成',
          transferPerson: '廖梅发',
          returnPerson: ''
        }
      ]
      total.value = 2
    }
    loading.value = false
  }, 300)
}

// 查看详情
const handleView = (row: any) => {
  ElMessage.success(`查看详情: ${row.certNumber}`)
  // 实际查看详情逻辑
}

// 申请借阅
const handleBorrow = () => {
  // 重置表单
  if (borrowFormRef.value) {
    borrowFormRef.value.resetFields()
  }
  
  // 显示对话框
  dialogVisible.value = true
}

// 提交借阅申请
const submitBorrowForm = () => {
  if (!borrowFormRef.value) return
  
  borrowFormRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success('借阅申请提交成功')
      dialogVisible.value = false
      // 刷新列表
      getList()
    }
  })
}

// 监听视图模式变化
const watchViewMode = () => {
  getList()
}

// 组件挂载时
onMounted(() => {
  // 初始加载数据
  getList()
  
  // 监听视图模式变化
  watch(viewMode, () => {
    watchViewMode()
  })
})

// 导入watch
import { watch } from 'vue'
</script>

<style scoped>
.app-container {
  padding: 15px;
}

.view-toggle {
  margin-bottom: 15px;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-input {
  width: 50px;
}

.page-size-select {
  width: 80px;
  margin-left: 10px;
}

.pagination-count {
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-date {
  margin-left: 20px;
}

.cert-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style> 