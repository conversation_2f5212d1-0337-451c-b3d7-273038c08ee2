export interface DsrxxZwxxVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 当事人ID
   */
  dsrId : string | number;

  /**
   * 指纹图片
   */
  zwtp : string;

  /**
   * 指纹code
   */
  zwxx : string;

  /**
   * 创建时间
   */
  createTime : string;

  /**
   * 签名信息
   */
  qmxx : string;

  /**
   * 手印信息
   */
  syxx : string;
  xm : string;
  xb : string;
  zjlx : string;
  csrq : string;
  zz : string;
  zp : string;
  zjhm : string;

}

export interface DsrxxZwxxForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 指纹图片
   */
  zwtp ?: string;

  /**
   * 指纹code
   */
  zwxx ?: string;

  /**
   * 创建时间
   */
  createTime ?: string;

  /**
   * 签名信息
   */
  qmxx ?: string;

  /**
   * 手印信息
   */
  syxx ?: string;
  xm : string;
  xb : string;
  zjlx : string;
  csrq : string;
  zz : string;
  zp : string;
  zjhm : string;

}

export interface DsrxxZwxxQuery extends PageQuery {
  xm ?: string,
  zjhm ?: string,
  /**
   * 日期范围参数
   */
  params ?: any;
}
