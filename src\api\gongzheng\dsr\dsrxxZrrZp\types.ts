export interface DsrxxZrrZpVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 照片
   */
  zp: string;

  /**
   * 当事人id
   */
  dsrId: string | number;

}

export interface DsrxxZrrZpForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 照片
   */
  zp?: string;

  /**
   * 当事人id
   */
  dsrId?: string | number;

}
export interface DsrxxZrrZpUploadForm extends BaseEntity {

  /**
   * 照片
   */
  zp?: string;

  /**
   * 当事人id
   */
  dsrId?: string | number;

}

export interface DsrxxZrrZpQuery extends PageQuery {

  /**
   * 照片
   */
  zp?: string;

  /**
   * 当事人id
   */
  dsrId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



