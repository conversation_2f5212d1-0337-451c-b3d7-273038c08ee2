export interface GzjzZmclxxMxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 主表ID
   */
  parentId: string | number;

  /**
   * 信息名称
   */
  xxmc: string;

  /**
   * 录入方式
   */
  lrfs: string;

  /**
   * 保存路径
   */
  bclj: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh: number;

}

export interface GzjzZmclxxMxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 主表ID
   */
  parentId?: string | number;

  /**
   * 信息名称
   */
  xxmc?: string;

  /**
   * 录入方式
   */
  lrfs?: string;

  /**
   * 保存路径
   */
  bclj?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

}

export interface GzjzZmclxxMxQuery extends PageQuery {

  /**
   * 主表ID
   */
  parentId?: string | number;

  /**
   * 信息名称
   */
  xxmc?: string;

  /**
   * 录入方式
   */
  lrfs?: string;

  /**
   * 保存路径
   */
  bclj?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}

export interface ZjclFile {
  /**
   * 卷宗ID
   */
  gzjzId: string | number;
  /**
   * 关联的GzGzjcZmclxx表ID
   */
  parentId: string | number;

  /**
   * 文件名称
   */
  xxmc?: string;
  /**
   * 保存路径
   */
  bclj?: string;

  files?: Array<{
    /**
     * 文件名称
     */
    xxmc: string;
    /**
     * 保存路径
     */
    bclj: string;
  }>;
}

export interface ZjclBase64Img {
  /**
   * 卷宗ID
   */
  gzjzId: string | number;
  /**
   * 关联的GzGzjcZmclxx表ID
   */
  parentId: string | number;
  /**
   * 文件名称
   */
  xxmc?: string;
  /**
   * 关联的当事人ID
   */
  dsrId?: string;
  /**
   * base64照片字符串
   */
  zp: string;
}
