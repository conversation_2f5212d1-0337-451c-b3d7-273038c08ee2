import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TodoNotificationVO, TodoNotificationForm, TodoNotificationQuery } from '@/api/notification/todoNotification/types';

/**
 * 查询待办事项通知主（站内信）列表
 * @param query
 * @returns {*}
 */

export const listTodoNotification = (query?: TodoNotificationQuery): AxiosPromise<TodoNotificationVO[]> => {
  return request({
    url: '/notification/todoNotification/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询待办事项通知主（站内信）详细
 * @param id
 */
export const getTodoNotification = (id: string | number): AxiosPromise<TodoNotificationVO> => {
  return request({
    url: '/notification/todoNotification/' + id,
    method: 'get'
  });
};

/**
 * 新增待办事项通知主（站内信）
 * @param data
 */
export const addTodoNotification = (data: TodoNotificationForm) => {
  return request({
    url: '/notification/todoNotification',
    method: 'post',
    data: data
  });
};

/**
 * 修改待办事项通知主（站内信）
 * @param data
 */
export const updateTodoNotification = (data: TodoNotificationForm) => {
  return request({
    url: '/notification/todoNotification',
    method: 'put',
    data: data
  });
};

/**
 * 删除待办事项通知主（站内信）
 * @param id
 */
export const delTodoNotification = (id: string | number | Array<string | number>) => {
  return request({
    url: '/notification/todoNotification/' + id,
    method: 'delete'
  });
};
