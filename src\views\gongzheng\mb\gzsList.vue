<!-- 告知模板管理页面 -->
<template>
  <div class="template-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card style="min-height: 768px; max-height: 1060px;">
          <div style="min-height: 568px; max-height: 860px; overflow: hidden;">
            <Tree ref="gzsTreeRef" :selectId="selectId" :showCheckBox="false" @onSave="handleSave"
              :default-checked-keys="defaultCheckedKeys" @onClick="handleClickTree"></Tree>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card v-if="selectId!=''">
          <div class="content-header">
            <h3>{{title}}-告知模板</h3>
            <div class="header-actions">
              <el-button type="primary" @click="handleAddTemplate">新增告知模板</el-button>
            </div>
          </div>

          <div class="table-container">
            <el-table :data="templateList" style="width: 100%" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                  <el-button type="text" size="small" @click="handleSign(scope.row)">签章</el-button>
                  <el-button type="text" style="color:red" size="small" @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
              <el-table-column label="下载" width="220">
                <template #default="scope">
                  <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
                  <el-button type="text" size="small" @click="handleEditWord(scope.row)">编辑文档</el-button>
                  <el-button type="text" size="small" @click="handleDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="模板名称" />
              <!-- <el-table-column prop="fileName" label="文件名称" /> -->
              <el-table-column prop="createTime" label="创建时间" />
              <el-table-column prop="remark" label="备注" show-overflow-tooltip />

            </el-table>

            <div class="pagination-container">
              <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
          </div>
        </el-card>
        <el-card v-else>
          <div class="empty-state">
            <p>请选择公证事项查看对应的告知模板</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增模板对话框 -->
    <el-dialog v-model="addDialogVisible" title="新增告知模板" width="800px">
      <el-form :model="templateForm" label-width="100px">
        <el-form-item label="模板名称" required>
          <el-input v-model="templateForm.title" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板文档">
          <el-upload ref="uploadRef" :action="uploadFileUrl" :headers="headers" :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList" :show-file-list="true"
            :limit="1" class="upload-demo">
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">请上传模板文档（支持doc、docx、pdf等格式，大小不超过10MB）</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否默认">
          <el-radio-group v-model="templateForm.defaultStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="templateForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑模板对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑告知模板" width="800px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="模板名称" required>
          <el-input v-model="editForm.title" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板文档">
          <el-upload ref="uploadRef" :action="uploadFileUrl" :headers="headers" :before-upload="handleBeforeUpload"
            :on-success="handleEditUploadSuccess" :on-error="handleUploadError" :file-list="editFileList"
            :show-file-list="true" :limit="1" class="upload-demo">
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">请上传模板文档（支持doc、docx、pdf等格式，大小不超过10MB）</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否默认">
          <el-radio-group v-model="editForm.defaultStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 签章对话框 -->
    <el-dialog v-model="signDialogVisible" title="文档签章" width="600px">
      <div class="sign-container">
        <div class="document-info">
          <h4>当前文档：{{ currentDocument }}</h4>
        </div>
        <div class="sign-role">
          <el-form label-width="100px">
            <el-form-item label="签章角色">
              <el-select v-model="signRole" placeholder="请选择签章角色">
                <el-option label="申请人" value="申请人" />
                <el-option label="公证员" value="公证员" />
                <el-option label="审核员" value="审核员" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="signDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSignConfirm">确定签章</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, getCurrentInstance, toRefs } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { ElFormInstance } from 'element-plus'
  import Tree from '@/views/gongzheng/basicdata/gzs/components/tree.vue';
  import * as api from '@/api/gongzheng/mb/mbJcxx'
  import * as api2 from '@/api/gongzheng/mb/mbWd'
  import { MbJcxxQuery, MbJcxxForm, MbJcxxVO } from '@/api/gongzheng/mb/mbJcxx/types'
  import { getToken } from '@/utils/auth'

  import { docOpenEdit, docOpenShow } from '@/views/gongzheng/doc/DocEditor'

  const gzsTreeRef = ref<InstanceType<typeof Tree> | null>(null);
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb, gz_mb_wdlb, gz_mb_mblx, gz_mb_classify } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_mb_wdlb', 'gz_mb_mblx', 'gz_mb_classify'));

  // 上传文件相关
  const uploadRef = ref()
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload')
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  })
  const fileList = ref([]);
  const editFileList = ref([]);
  const gzsId = ref('')
  const selectId = ref('');
  const title = ref('');
  const loading = ref(true);
  // 模板列表数据
  const templateList = ref([])

  // 分页相关
  const total = ref(1)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const selectedTemplates = ref([])

  // 对话框相关
  const addDialogVisible = ref(false)
  const editDialogVisible = ref(false)
  const signDialogVisible = ref(false)
  const templateForm = reactive<MbJcxxForm>({
    id: undefined,
    title: '',
    templateType: 0,
    ywId: undefined,
    remark: '',
    defaultStatus: 0,
    classify: 2, // 固定为告知书
    fileUrl: '',
    fileName: '',
    wdOssId: undefined
  })
  const editForm = reactive<MbJcxxForm>({
    id: undefined,
    title: '',
    templateType: 0,
    ywId: undefined,
    remark: '',
    classify: 2,
    fileUrl: '',
    defaultStatus: 0,
    fileName: '',
    wdOssId: undefined
  })

  const handleClickTree = (data) => {
    console.log("handleClickTree 选中：", data)
    if (data) {
      nextTick(() => {
        selectId.value = data.id;
        title.value = data.title;
        gzsId.value = data.id;
        fetchList(gzsId.value)
      })
    }
  }

  //模板列表查询
  const fetchList = async (ywId) => {
    loading.value = true;
    templateList.value = [];
    const params : MbJcxxQuery = {
      ywId: ywId,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      classify: 2
    }
    const res = await api.listMbJcxx(params)
    templateList.value = res.rows
    total.value = res.total
    loading.value = false;
  }

  const handleSave = () => {

  }

  // 签章相关
  const currentDocument = ref('告知书')
  const signRole = ref('申请人')

  // 处理树节点点击
  const handleNodeClick = (data : any) => {
    ElMessage.info(`选择了节点: ${data.label}`)
    // 这里可以根据选择的节点加载相应的模板列表
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection : any[]) => {
    selectedTemplates.value = selection
  }

  // 处理分页变化
  const handleCurrentChange = (val : number) => {
    currentPage.value = val
    if (gzsId.value) {
      fetchList(gzsId.value)
    }
  }

  // 处理每页显示数量变化
  const handleSizeChange = (val : number) => {
    pageSize.value = val
    currentPage.value = 1
    if (gzsId.value) {
      fetchList(gzsId.value)
    }
  }

  // 处理新增模板
  const handleAddTemplate = () => {
    if (!gzsId.value) {
      ElMessage.warning('请先选择告知书分类')
      return
    }
    // 重置表单
    templateForm.id = undefined
    templateForm.title = ''
    templateForm.templateType = 0
    templateForm.ywId = gzsId.value
    templateForm.remark = ''
    templateForm.classify = 2
    templateForm.docCategory = 110
    templateForm.fileUrl = ''
    templateForm.fileName = ''
    templateForm.defaultStatus = 0
    templateForm.wdOssId = undefined
    fileList.value = []
    addDialogVisible.value = true
  }

  // 处理新增确认
  const handleAddConfirm = async () => {
    if (!templateForm.title) {
      ElMessage.warning('请输入模板名称')
      return
    }
    if (!templateForm.fileUrl) {
      ElMessage.warning('请上传模板文档')
      return
    }

    try {
      await api.addMbJcxx(templateForm)
      ElMessage.success('新增模板成功')
      addDialogVisible.value = false
      fetchList(gzsId.value)
    } catch (error) {
      console.error('新增模板失败', error)
      ElMessage.error('新增失败，请重试')
    }
  }

  // 处理编辑
  const handleEdit = async (row : any) => {
    try {
      // 通过接口获取模板详情
      const res = await api.getMbJcxx(row.id)
      const templateData = res.data

      // 填充表单数据
      editForm.id = templateData.id
      editForm.title = templateData.title
      editForm.templateType = templateData.templateType
      editForm.ywId = templateData.ywId
      editForm.remark = templateData.remark
      editForm.classify = templateData.classify
      editForm.fileUrl = templateData.fileUrl || ''
      editForm.fileName = templateData.fileName || ''
      editForm.wdOssId = templateData.wdOssId
      editForm.defaultStatus = templateData.defaultStatus
      editForm.docCategory = 110
      // 如果有文件，添加到文件列表中
      editFileList.value = []
      if (templateData.fileName && templateData.fileUrl) {
        editFileList.value.push({
          name: templateData.fileName,
          url: templateData.fileUrl
        })
      }

      editDialogVisible.value = true
    } catch (error) {
      console.error('获取模板详情失败', error)
      ElMessage.error('获取模板详情失败，请重试')
    }
  }

  // 处理编辑确认
  const handleEditConfirm = async () => {
    if (!editForm.title) {
      ElMessage.warning('请输入模板名称')
      return
    }

    try {
      await api.updateMbJcxx(editForm)
      ElMessage.success('编辑模板成功')
      editDialogVisible.value = false
      fetchList(gzsId.value)
    } catch (error) {
      console.error('编辑模板失败', error)
      ElMessage.error('编辑失败，请重试')
    }
  }

  // 处理删除
  const handleDelete = async (row : any) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除模板"${row.title}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      await api.delMbJcxx(row.id)
      ElMessage.success('删除模板成功')
      fetchList(gzsId.value)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除模板失败', error)
        ElMessage.error('删除失败，请重试')
      }
    }
  }

  // 处理签章
  const handleSign = (row : any) => {
    // currentDocument.value = row.name
    // signDialogVisible.value = true
    ElMessage.error("开发中")
  }

  // 处理签章确认
  const handleSignConfirm = () => {
    ElMessage.error("开发中")
    // ElMessage.success(`${signRole.value}签章成功`)
    // signDialogVisible.value = false
    // 这里添加实际的签章逻辑
  }
  // 文件上传前的处理
  const handleBeforeUpload = (file) => {
    const isValidSize = file.size / 1024 / 1024 < 10
    if (!isValidSize) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      return false
    }
    return true
  }

  // 文件上传成功的处理（新增）
  const handleUploadSuccess = (res, file) => {
    if (res.code === 200) {
      templateForm.fileUrl = res.data.path
      templateForm.fileName = res.data.fileName || file.name
      templateForm.wdOssId = res.data.ossId
      ElMessage.success('文件上传成功')
    } else {
      ElMessage.error('文件上传失败')
    }
  }

  // 文件上传成功的处理（编辑）
  const handleEditUploadSuccess = (res, file) => {
    if (res.code === 200) {
      editForm.fileUrl = res.data.path
      editForm.fileName = res.data.fileName || file.name
      editForm.wdOssId = res.data.ossId
      ElMessage.success('文件上传成功')
    } else {
      ElMessage.error('文件上传失败')
    }
  }

  // 文件上传失败的处理
  const handleUploadError = () => {
    ElMessage.error('文件上传失败')
  }

  //下载
  const handleDownload = (row) => {
    if (row.wdOssId == null) {
      ElMessage.error('文件不存在')
    } else {
      proxy?.$download.oss(row.wdOssId);
    }
    // // 创建隐藏的下载链接
    // const link = document.createElement('a')
    // link.href = row.fileUrl
    // link.download = row.fileName || '模板文件'
    // link.style.display = 'none'
    // document.body.appendChild(link)
    // link.click()
    // document.body.removeChild(link)
  }
  const handleEditWord = (row) => {
    if (row.wdPath) {
      docOpenEdit(row.wdPath)
    }
  }
  const handleView = (row) => {
    if (row.wdPath) {
      docOpenShow(row.wdPath)
    }
  }

  // 组件挂载时
  onMounted(() => {
    // 初始化逻辑
    if (selectId.value) {
      gzsId.value = selectId.value;
    }
  })
</script>

<style scoped>
  .template-container {
    padding: 20px;
  }

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .content-header h3 {
    margin: 0;
    color: #303133;
  }

  .table-container {
    margin-top: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .empty-state {
    text-align: center;
    padding: 60px 0;
    color: #909399;
  }

  .sign-container {
    padding: 20px 0;
  }

  .document-info h4 {
    margin: 0 0 20px 0;
    color: #303133;
  }

  .sign-role {
    margin-top: 20px;
  }
</style>
