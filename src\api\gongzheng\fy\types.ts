import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

export interface GzGzjzFyglVo extends GzjzJbxxVO {

  /**
   * 卷宗文件总体翻译状态
   */
  fyStatus?: string;
  /**
   * 卷宗文件储存列表
   */
  wjccxxList?: Array<Object>;
  /**
   * 收费明细列表
   */
  sfxxList?: Array<Object>;

  /**
   * 相关要求提醒
   */
  gzjzYqtx?: Object;
}

export interface GzGzjzFyglQuerye extends PageQuery {
  /**
   * 卷宗编号
   */
  jzbh?: string;
  /**
   * 公证员ID
   */
  gzybm?: string;
  /**
   * 当事人id
   */
  dsrId?: string;
  /**
   * 公证书编号
   */
  gzsbh?: string;
  /**
   * 翻译状态列表
   */
  fyztList: Array<string | number>;
  /**
   * 译文文种
   */
  ywwz?: string;

  /**
   * 公证书编号参数
   */
  params?: any;

  /**
   * 翻译人姓名
   */
  fyrxm?: string;
}
