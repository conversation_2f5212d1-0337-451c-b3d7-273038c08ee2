<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="当事人ID" prop="dsrId">
              <el-input v-model="queryParams.dsrId" placeholder="请输入当事人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="对比指纹图片" prop="dbzw">
              <el-input v-model="queryParams.dbzw" placeholder="请输入对比指纹图片" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="对比指纹信息" prop="dbzwxx">
              <el-input v-model="queryParams.dbzwxx" placeholder="请输入对比指纹信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="对比结果" prop="dbjg">
              <el-input v-model="queryParams.dbjg" placeholder="请输入对比结果" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['log:logZwdbjl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['log:logZwdbjl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['log:logZwdbjl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['log:logZwdbjl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="logZwdbjlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="当事人ID" align="center" prop="dsrId" />
        <el-table-column label="对比指纹图片" align="center" prop="dbzw" />
        <el-table-column label="对比指纹信息" align="center" prop="dbzwxx" />
        <el-table-column label="对比结果" align="center" prop="dbjg" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['log:logZwdbjl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['log:logZwdbjl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改日志-指纹对比记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="logZwdbjlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="当事人ID" prop="dsrId">
          <el-input v-model="form.dsrId" placeholder="请输入当事人ID" />
        </el-form-item>
        <el-form-item label="对比指纹图片" prop="dbzw">
          <el-input v-model="form.dbzw" placeholder="请输入对比指纹图片" />
        </el-form-item>
        <el-form-item label="对比指纹信息" prop="dbzwxx">
          <el-input v-model="form.dbzwxx" placeholder="请输入对比指纹信息" />
        </el-form-item>
        <el-form-item label="对比结果" prop="dbjg">
          <el-input v-model="form.dbjg" placeholder="请输入对比结果" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LogZwdbjl" lang="ts">
import { listLogZwdbjl, getLogZwdbjl, delLogZwdbjl, addLogZwdbjl, updateLogZwdbjl } from '@/api/gongzheng/log/logZwdbjl';
import { LogZwdbjlVO, LogZwdbjlQuery, LogZwdbjlForm } from '@/api/gongzheng/log/logZwdbjl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const logZwdbjlList = ref<LogZwdbjlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const logZwdbjlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: LogZwdbjlForm = {
  id: undefined,
  dsrId: undefined,
  dbzw: undefined,
  dbzwxx: undefined,
  dbjg: undefined,
  remark: undefined,
}
const data = reactive<PageData<LogZwdbjlForm, LogZwdbjlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dsrId: undefined,
    dbzw: undefined,
    dbzwxx: undefined,
    dbjg: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询日志-指纹对比记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listLogZwdbjl(queryParams.value);
  logZwdbjlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  logZwdbjlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: LogZwdbjlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加日志-指纹对比记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: LogZwdbjlVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getLogZwdbjl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改日志-指纹对比记录";
}

/** 提交按钮 */
const submitForm = () => {
  logZwdbjlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateLogZwdbjl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addLogZwdbjl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: LogZwdbjlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除日志-指纹对比记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delLogZwdbjl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('log/logZwdbjl/export', {
    ...queryParams.value
  }, `logZwdbjl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
