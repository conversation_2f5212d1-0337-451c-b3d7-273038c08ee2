export interface DsrxxZjxxVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 当事人
   */
  dsrId : string | number;

  /**
   * 名称
   */
  xm : string;

  /**
   * 证件类型
   */
  zjlx : string;

  /**
   * 是否主要证件
   */
  zjzt : number;

  /**
   * 备注
   */
  bz : string;

  /**
   * 证件号码
   */
  zjhm : string;

}

export interface DsrxxZjxxForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 当事人
   */
  dsrId ?: string | number;

  /**
   * 名称
   */
  xm ?: string;

  /**
   * 证件类型
   */
  zjlx ?: string;

  /**
   * 是否主要证件
   */
  zjzt ?: number;

  /**
   * 备注
   */
  bz ?: string;

  /**
   * 证件号码
   */
  zjhm ?: string;
  createTime ?: Date;

}

export interface DsrxxZjxxQuery extends PageQuery {

  /**
   * 当事人
   */
  dsrId ?: string | number;

  /**
   * 名称
   */
  xm ?: string;

  /**
   * 证件类型
   */
  zjlx ?: string;

  /**
   * 是否主要证件
   */
  zjzt ?: number;

  /**
   * 备注
   */
  bz ?: string;

  /**
   * 证件号码
   */
  zjhm ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}
