<!-- 模板变量配置页面 -->
<template>
  <div class="variable-container">
    <div class="search-container">
      <div class="search-form">
        <div class="form-item">
          <span class="label">变量：</span>
          <el-input v-model="searchKeyword" placeholder="请输入变量名" style="width: 180px;"></el-input>
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>查询
          </el-button>
          <el-button @click="resetSearch">
            <el-icon>
              <Refresh />
            </el-icon>重置
          </el-button>
        </div>
      </div>
    </div>

    <div class="variable-list">
      <div class="list-header">
        <div class="header-buttons">
          <el-button type="primary" @click="handleAdd">新增</el-button>
          <el-button @click="handleBatchDelete">删除</el-button>
        </div>
      </div>

      <el-table :data="variableList" border stripe @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <!-- <el-button type="primary" link @click="handleZdpz(scope.row)">配置</el-button> -->
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="blName" label="变量" align="center" />
        <el-table-column prop="yxZt" label="是否有效" align="center">
          <template #default="scope">
            <span>{{ scope.row.yxZt===0?'有效':'无效' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination background layout="prev, pager, next, sizes, total, jumper" :total="total"
          :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange" @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 新增/编辑变量对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增变量' : '编辑变量'" width="500px">
      <el-form :model="variableForm" label-width="100px">
        <el-form-item label="变量名称" required>
          <el-input v-model="variableForm.blName" placeholder="请输入变量名称" />
          <div v-if="showNameError" class="error-text">变量名称不能为空</div>
        </el-form-item>
        <el-form-item label="是否有效">
          <el-radio-group v-model="variableForm.yxZt">
            <el-radio :label="0">有效</el-radio>
            <el-radio :label="1">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 变量配置对话框 -->
    <vxe-modal v-model="dialog2.visible" :title="dialog2.title" width="1200" height="800" resize>
      <TemplateVariableConfig :blId="blId" />
    </vxe-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import * as api from '@/api/gongzheng/mb/mbBl'
  import { MbBlVO, MbBlForm, MbBlQuery } from '@/api/gongzheng/mb/mbBl/types'
  import TemplateVariableConfig from './components/templateVariableConfig.vue'
  
  // 搜索关键词
  const searchKeyword = ref('')
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  // 变量列表数据
  const variableList = ref<MbBlVO[]>([])
  const blId = ref(null);
  // 分页相关
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const selectedVariables = ref<MbBlVO[]>([])

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const variableForm = reactive<MbBlForm>({
    id: '',
    blName: '',
    yxZt: 0
  })
  const showNameError = ref(false)

  // 查询变量列表
  const fetchList = async () => {
    const params : MbBlQuery = {
      blName: searchKeyword.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    const res = await api.listMbBl(params)
    variableList.value = res.rows
    total.value = res.total
  }

  // 处理搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchList()
  }

  // 重置搜索
  const resetSearch = () => {
    searchKeyword.value = ''
    handleSearch()
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection : MbBlVO[]) => {
    selectedVariables.value = selection
  }

  // 处理分页变化
  const handleCurrentChange = (val : number) => {
    currentPage.value = val
    fetchList()
  }

  // 处理每页显示数量变化
  const handleSizeChange = (val : number) => {
    pageSize.value = val
    currentPage.value = 1
    fetchList()
  }

  // 处理新增
  const handleAdd = () => {
    dialogType.value = 'add'
    variableForm.id = ''
    variableForm.blName = ''
    variableForm.yxZt = 0
    showNameError.value = false
    dialogVisible.value = true
  }

  // 处理编辑
  const handleEdit = (row : MbBlVO) => {
    dialogType.value = 'edit'
    variableForm.id = row.id
    variableForm.blName = row.blName
    variableForm.yxZt = row.yxZt
    showNameError.value = false
    dialogVisible.value = true
  }

  // 处理删除单个变量
  const handleDelete = (row : MbBlVO) => {
    ElMessageBox.confirm(`确认删除变量 "${row.blName}"?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await api.delMbBl(row.id)
      ElMessage.success(`删除变量 "${row.blName}" 成功`)
      fetchList()
    })
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedVariables.value.length === 0) {
      ElMessage.warning('请选择要删除的变量')
      return
    }
    
    ElMessageBox.confirm(`确认删除选中的 ${selectedVariables.value.length} 个变量?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = selectedVariables.value.map(item => item.id)
      await api.delMbBl(ids.join(','))
      ElMessage.success('批量删除成功')
      fetchList()
    })
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!variableForm.blName.trim()) {
      showNameError.value = true
      return
    }
    
    try {
      if (dialogType.value === 'add') {
        await api.addMbBl(variableForm)
        ElMessage.success('新增变量成功')
      } else {
        await api.updateMbBl(variableForm)
        ElMessage.success('编辑变量成功')
      }
      dialogVisible.value = false
      fetchList()
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  // 组件挂载时
  onMounted(() => {
    fetchList()
  })
</script>

<style scoped>
  .variable-container {
    padding: 20px;
  }

  .search-container {
    margin-bottom: 20px;
  }

  .search-form {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .label {
    font-weight: 500;
    color: #606266;
  }

  .variable-list {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .header-buttons {
    display: flex;
    gap: 10px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .error-text {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
  }
</style>
