export interface LogZwdbjlVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 当事人ID
   */
  dsrId: string | number;

  /**
   * 对比指纹图片
   */
  dbzw: string;

  /**
   * 对比指纹信息
   */
  dbzwxx: string;

  /**
   * 对比结果
   */
  dbjg: number;

  /**
   * 备注
   */
  remark: string;

}

export interface LogZwdbjlForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 当事人ID
   */
  dsrId?: string | number;

  /**
   * 对比指纹图片
   */
  dbzw?: string;

  /**
   * 对比指纹信息
   */
  dbzwxx?: string;

  /**
   * 对比结果
   */
  dbjg?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface LogZwdbjlQuery extends PageQuery {

  /**
   * 当事人ID
   */
  dsrId?: string | number;

  /**
   * 对比指纹图片
   */
  dbzw?: string;

  /**
   * 对比指纹信息
   */
  dbzwxx?: string;

  /**
   * 对比结果
   */
  dbjg?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



