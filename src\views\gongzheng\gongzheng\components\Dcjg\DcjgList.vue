<template>
  <div class="h-full">
    <div class="flex justify-between items-center h-32px pb-10px">
      <strong>调查记录列表</strong>
      <el-button type="primary" @click="addDcsq" size="small">新增调查申请</el-button>
    </div>
    <div class="h-[calc(100%-42px)] min-h-200px max-h-600px overflow-hidden">
      <el-table :data="ListData" style="height: 100%" border stripe>
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <div class="flex flex-wrap gap-4px">
              <el-button @click="showDcCt(row)" type="primary" link size="small">申请内容</el-button>
              <el-button @click="revokeDc(row)" type="primary" link size="small">撤回申请</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请调查日期" prop="sqdcrq" />
        <el-table-column label="调查结束日期" prop="dcjsrq" />
        <el-table-column label="状态" prop="dczt" width="140">
          <template #default="{ row }">
            <el-tag>{{ dictMapFormat(gz_jz_dczt, row.dczt) }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="mt-5px"></div>
    </div>

    <AddDc v-model="addDcShow" :gzjz-id="gzjzId" @success="loadListData"/>
    <DcContent v-model="DcCtShow" :data="dcCtData" />
  </div>
</template>

<script setup lang="ts">
import { delGzjzDchsjl, listGzjzDchsjl } from '@/api/gongzheng/bzfz/gzjzDchsjl';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { inject } from 'vue';
import AddDc from './AddDc.vue'
import DcContent from './DcContent.vue'
import { dictMapFormat } from '@/utils/ruoyi';

interface Props {
  gzjzId?: string | number;
}

const props = defineProps<Props>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_jz_dczt } = toRefs<any>(proxy?.useDict('gz_jz_dczt'))

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const ListData = ref([]);
const ListLoading = ref(false);

const addDcShow = ref(false)
const DcCtShow = ref(false)
const dcCtData = ref(null)

const addDcsq = () => {
  addDcShow.value = true;
}

const showDcCt = (data: any) => {
  dcCtData.value = data;
  DcCtShow.value = true;
}

const revokeDc = async (data: any) => {
  if(Number(data.dczt) > 2) {
    ElMessage.warning('正在调查中的无法撤回')
    return;
  }

  ElMessageBox.confirm('确认撤回调查吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await delGzjzDchsjl(data.id);
      if(res.code === 200) {
        ElMessage.success('操作成功');
        loadListData();
      }
    } catch(err: any) {
      ElMessage.error('操作失败')
      console.error('操作失败', err)
    }
  })
}

const loadListData = async () => {
  try {
    ListLoading.value = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      pageSize: 200,
      pageNum: 1
    }
    const res = await listGzjzDchsjl(params);
    if(res.code === 200) {
      ListData.value = res.rows || [];
    }
  } catch (err: any) {
    console.log('调查结果查询失败', err);
    ElMessage.error('调查结果查询失败');
  } finally {
    ListLoading.value = false;
  }
}

onMounted(() => {
  loadListData();
})
</script>
