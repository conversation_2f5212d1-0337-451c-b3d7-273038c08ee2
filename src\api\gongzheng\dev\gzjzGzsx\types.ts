export interface GzjzGzsxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证事项ID
   */
  gzsxId: string | number;

  /**
   * 公证事项
   */
  gzsxMc: string;

  /**
   * 关系人ID
   */
  gxrId: string | number;

  /**
   * 关系人
   */
  gxrMc: string;

  /**
   * 公证书ID
   */
  gzsId: string | number;

  /**
   * 公证书编号
   */
  gzsBh: string;

  /**
   * 公证书名称
   */
  gzsMc: string;

  /**
   * 合成公证书ID
   */
  hcGzsId: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc: string;

  /**
   * 份数
   */
  gzsFs: number;

  /**
   * 是否备案（0否，1是）
   */
  sfBa: string;

  /**
   * 备案信息ID
   */
  baxxId: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzGzsxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证事项ID
   */
  gzsxId?: string | number;

  /**
   * 公证事项
   */
  gzsxMc?: string;

  /**
   * 关系人ID
   */
  gxrId?: string | number;

  /**
   * 关系人
   */
  gxrMc?: string;

  /**
   * 公证书ID
   */
  gzsId?: string | number;

  /**
   * 公证书编号
   */
  gzsBh?: string;

  /**
   * 公证书名称
   */
  gzsMc?: string;

  /**
   * 合成公证书ID
   */
  hcGzsId?: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc?: string;

  /**
   * 份数
   */
  gzsFs?: number;

  /**
   * 是否备案（0否，1是）
   */
  sfBa?: string;

  /**
   * 备案信息ID
   */
  baxxId?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzGzsxQuery extends PageQuery {

  /**
   * 公证事项ID
   */
  gzsxId?: string | number;

  /**
   * 公证事项
   */
  gzsxMc?: string;

  /**
   * 关系人ID
   */
  gxrId?: string | number;

  /**
   * 关系人
   */
  gxrMc?: string;

  /**
   * 公证书ID
   */
  gzsId?: string | number;

  /**
   * 公证书编号
   */
  gzsBh?: string;

  /**
   * 公证书名称
   */
  gzsMc?: string;

  /**
   * 合成公证书ID
   */
  hcGzsId?: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc?: string;

  /**
   * 份数
   */
  gzsFs?: number;

  /**
   * 是否备案（0否，1是）
   */
  sfBa?: string;

  /**
   * 备案信息ID
   */
  baxxId?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



