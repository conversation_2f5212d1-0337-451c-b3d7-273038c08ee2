<template>
  <!-- 制证 -->
  <gz-dialog v-model="dialog.visible" :title="dialog.title" @closed="cancel" show-close destroy-on-close fullscreen>
    <div class="zz-container">
      <!-- 卷宗基本信息 -->
      <div class="page-header flex flex-col gap-14px p-20px">
        <div class="flex items-center gap-12px flex-wrap">
          <span class="flex items-center">
            <el-text>卷宗号：</el-text>
            <el-text type="info">{{ gzjzJbxx?.jzbh || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>公证员：</el-text>
            <el-text type="info">{{ gzjzJbxx?.gzyxm || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>助理/受理人：</el-text>
            <el-text type="info">{{ gzjzJbxx?.zlxm || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>受理日期：</el-text>
            <el-text type="info">{{ formatDate(gzjzJbxx?.slrq) || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>公证类别：</el-text>
            <el-text type="info">{{ dictMapFormat(gz_gzlb ,gzjzJbxx?.lb) || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>使用地：</el-text>
            <el-text type="info">{{ getAreaName(gzjzJbxx?.syd) || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>译文文种：</el-text>
            <el-text type="info">{{ dictMapFormat(gz_yw_wz, gzjzJbxx?.ywwz) || '-' }}</el-text>
          </span>
        </div>
        <div class="flex items-center">
          <el-text>制证要求：</el-text>
          <el-text type="info">{{ gzjzJbxx?.yqtxVo?.txZzyq || '-' }}</el-text>
        </div>
      </div>

      <!-- 制证列表 -->
      <div class="section-container">
        <div class="section-header">制证列表</div>
        <div class="section-content">
          <el-table v-loading="loading" :data="gzsxList" border stripe  height="250px">
            <el-table-column prop="gzsxMc" label="公证事项" align="center" show-overflow-tooltip />
            <el-table-column prop="gzsBh" label="公证书编号" align="center" show-overflow-tooltip width="240" />
            <el-table-column prop="notaryDoc" label="公证书" align="center" show-overflow-tooltip>
              <template #default="{ row }">
                <!-- <el-button @click="() => genGzs(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.wbmc == null" size="small" link>新建</el-button> -->
                <el-button @click="() => openGzsDoc(row)" type="primary" v-if="row.gzsFile?.wblj" size="small" link>证词</el-button>
                <!-- <el-button @click="() => delGzsDoc(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.wbmc != null" size="small" link>删除</el-button> -->
              </template>
            </el-table-column>
            <el-table-column prop="combinedDoc" label="合成公证书" align="center" show-overflow-tooltip >
              <template #default="scope">
                <el-button type="primary" link @click="handleBuliding(scope.row)">
                  {{ getYwMc(scope.row.id) }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="notaryTranslation" label="公证书译文" align="center" show-overflow-tooltip >
              <template #default="{ row }">
                <!-- <el-button @click="() => genGzs(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.ywmc == null" size="small" link>新建</el-button> -->
                <el-button v-if="row.gzsFile?.ywlj && row.gzsFile?.fyzt != '2'" @click="() => openGzsDoc(row, true)" type="primary" size="small" link>{{ `译文${mapFyzt(row.gzsFile?.fyzt)}` }}</el-button>
                <!-- <el-button @click="() => delGzsDoc(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.ywmc != null" size="small" link>删除</el-button> -->
              </template>
            </el-table-column>
            <el-table-column prop="translationOperation" label="译文操作" align="center" show-overflow-tooltip width="140" >
              <template #default="{ row }">
                <el-button type="primary" link @click="triggerYwGen(row)" size="small">
                  生成
                </el-button>
                <el-button type="primary" link @click="toUploadDoc(row, 'gzsyw')" size="small">
                  上传
                </el-button>
                <el-button v-if="row.gzsFile?.fyzt == '1'" @click="toRejectYw(row)" type="primary" link size="small">
                  驳回校对
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="gzsFs" label="公证书份数" align="center" width="100" />
            <el-table-column prop="hasBackup" label="盖章?" align="center" width="80" />
            <el-table-column prop="hasPrinted" label="打印?" align="center" width="80" />
            <el-table-column label="操作" align="center" width="120">
              <template #default="scope">
                <el-button type="primary" link @click="handleBuliding(scope.row)" size="small">
                  合成公证书
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 文书和水印纸使用清单 -->
      <div class="dual-section">
        <!-- 文书 -->
        <div class="section-container half-width">
          <div class="section-header">文书</div>
          <div class="section-content">
            <el-table v-loading="loading" :data="wsList" border stripe height="200px">
              <el-table-column prop="wbmc" label="文书名称" align="center" show-overflow-tooltip >
                <template #default="{ row }">
                  <el-button v-if="row.wbmc" type="primary" link @click="openDoc(row)" size="small">
                    {{ row.wbmc }}
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="ywmc" label="译文" align="center" show-overflow-tooltip >
                <template #default="scope">
                  <el-button v-if="scope.row.ywmc" type="primary" link @click="handleBuliding(scope.row)" size="small">
                    {{ scope.row.ywmc }}
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="translationOperation" label="译文操作" align="center">
                <template #default="scope">
                  <el-button type="primary" link @click="toUploadDoc(scope.row, 'dsyw')" size="small">
                    上传
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 水印纸使用清单 -->
        <div class="section-container half-width">
          <div class="section-header">水印纸使用清单</div>
          <div class="section-content">
            <el-table v-loading="loading" :data="syzList" border stripe height="200px">
              <el-table-column prop="syzbh" label="水印纸编号" align="center" show-overflow-tooltip />
              <el-table-column prop="gzsBh" label="公证书编号" align="center" show-overflow-tooltip width="240" />
              <el-table-column prop="sysj" label="使用日期" align="center" show-overflow-tooltip width="160">
                <template #default="{ row }">
                  {{ formatDate(row.sysj) }}
                </template>
              </el-table-column>
              <el-table-column prop="syzt" label="状态" align="center" show-overflow-tooltip width="120">
                <template #default="{ row }">
                  {{ dictMapFormat(gz_syz_zt, row.syzt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button type="primary" link @click="syzDestroy(scope.row)" size="small" v-if="scope.row.syzt == '1'">
                    作废
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="gzsGenState.show" title="公证书译文生成" @closed="() => {}" draggable :modal="false" show-close destroy-on-close width="400">
      <div class="h-100px flex items-center justify-center">
        <div class="flex items-center justify-center gap-10px">
          <strong>文档模版：</strong>
          <el-select v-model="gzsGenState.mbId" default-first-option filterable style="width: 200px;">
            <el-option v-for="item in gzsGenState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button type="primary" @click="ywDocGen" :loading="gzsGenState.loading" :disabled="gzsGenState.loading">确认生成</el-button>
          <el-button @click="gzsGenClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <gz-dialog v-model="ywRjState.show" title="驳回译文">
      <el-form :model="ywRjState" :rules="ywRjRules" ref="ywRjRef">
        <el-form-item label="驳回意见" prop="remark">
          <el-input v-model="ywRjState.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button @click="ywRjComfirm" :loading="ywRjState.loading" :disabled="ywRjState.loading" type="danger">确认驳回</el-button>
          <el-button @click="ywRjClose">关闭</el-button>
        </div>
      </template>
    </gz-dialog>

    <!-- 制证结束并通知 -->
    <PopFormByZzhtz ref="PopFormByZzhtzRef" @callback="zzhtzCallback" />

    <!-- 申请翻译对话框 -->
    <sqfy-dialog ref="sqfyDialogRef" @callback="sqfyCallback" ></sqfy-dialog>

    <ZzReject v-model="zzVisible" :gzjz-id="gzjzId" />

    <DragUpload v-model="upLoadState.show" @on-all-done="upLoadAllDone" title="上传文件" :multiple="false" :limit="1" accept=".doc,.docx" />

    <WdndDialog v-model="wdndState.show" :gzjz-id="gzjzId" />

    <!-- 手动取水印纸 -->
    <Syz ref="syzRef" @done="getInfo" />

    <template #footer>
      <div class="dialog-footer">
        <el-button type="warning" @click="zzBhComfirm">驳回</el-button>
          <!-- <el-button @click="handlePrinterSettings">打印机设置</el-button> -->
          <el-button @click="openDocPrint('20')">签发稿</el-button>
          <el-button @click="openDocPrint('11')">审批表</el-button>
          <!-- <el-button @click="handlePrintNotary">打印公证书</el-button>
          <el-button @click="handlePrintDoc">打印文书</el-button>
          <el-button @click="handlePrintNotaryTranslation">打印公证书译文</el-button>
          <el-button @click="handlePrintDocTranslation">打印文书译文</el-button> -->
          <el-button @click="openDocPrint('10')">卷宗封皮</el-button>
          <el-button @click="wdndState.show = true">生成缴费清单</el-button>
          <!-- <el-button @click="handleReservePrint">留底打印</el-button> -->
          <!-- <el-button @click="handleReservePrintInProcess">留底打印(中)</el-button> -->
          <el-button @click="handleSyz">手动取水印纸</el-button>
          <el-button @click="handleSqfy()">申请翻译</el-button>
          <el-button @click="wdndState.show = true">生成送达回执</el-button>
          <el-button type="primary" @click="handleGenerateResult">制证结束并通知</el-button>
          <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup name="PopIndexByZz" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { getGzjzJbxxAll, initiateSigned } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import type { GzjzJbxxVO, ApprovalType } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
  import { listAreaname } from '@/api/gongzheng/basicdata/areaname';
  import PopFormByZzhtz from './popFormByZzhtz.vue';
  import { clearEmptyProperty, dictMapFormat, formatDate, nodeFilter } from '@/utils/ruoyi';
  import SqfyDialog from '@/views/gongzheng/gongzheng/components/sl/SqfyDialog.vue';
  import ZzReject from './ZzReject.vue';
  import { addGzjzWjccxx, listGzjzWjccxx, updateGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
  import { docGenerator, docOpenEdit, docOpenShow } from '@/views/gongzheng/doc/DocEditor';
  import DragUpload from '@/components/FileUpload/DragUpload.vue';
  import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
  import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
  import { UserDocGenParams } from '@/views/gongzheng/doc/type';
  import WdndDialog from '@/views/gongzheng/gongzheng/components/sl/wdnd/WdndDialog.vue';
  import Syz from './syz/index.vue'
import { syzInvalidation } from '@/api/gongzheng/syz/syzmx';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb, gz_sl_syd, gz_yw_wz, gz_fzxx_lqxx, gz_syz_zt } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_sl_syd', 'gz_yw_wz', 'gz_fzxx_lqxx', 'gz_syz_zt'));

  // 由父组件传入的卷宗信息
  const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

  const dialog = reactive<DialogOption>({
    visible: false,
    title: '制证'
  });

  const PopFormByZzhtzRef = ref<InstanceType<typeof PopFormByZzhtz>>(null);

  const loading = ref(false);
  const gzjzJbxx = ref<GzjzJbxxVO>(null);
  const gzsxList = ref<GzjzGzsxVO[]>([]);
  const gzswjList = ref<any[]>([]);
  const wsList = ref<any[]>([]);
  const syzList = ref<any[]>([]);

  const areas = ref([]);

  const gzjzId = ref<string | number>(null);
  const sqfyDialogRef = ref<InstanceType<typeof SqfyDialog>>(null);

  const zzVisible = ref(false)

  const syzRef = ref(null)

  const upLoadState = reactive({
    show: false,
    type: '',
    gzsxInfo: null,
    dsInfo: null
  })

  const gzsGenState = reactive({
    show: false,
    loading: false,
    mbId: '',
    typeData: [],
    gzsxInfo: null,
  })

  const wdndState = reactive({
    show: false,
  })

  const ywRjState = reactive({
    rjInfo: null,
    show: false,
    loading: false,
    remark: ''
  });

  const ywRjRef = ref<ElFormInstance>(null);

  const ywRjRules = {
    remark: [
      { required: true, message: '请输入驳回原因', trigger: 'change' },
      { required: true, message: '请输入驳回原因', trigger: 'blur' }
    ]
  }

  // 提供给子组件的数据和方法
  provide('view', dialog.visible);
  provide('gzjzId', gzjzId);

  const loadGzsDocList = async (yw: boolean = false) => {
    try {
      const params = {
        pageNum: 1,
        pageSize: 100,
        lx: '3',  // 公证书 3, 公证书译文 5
        gzjzId: curGzjz.value.id || gzjzId.value
      }
      const res = await listGzjzWjccxx(params);
      if(res.code === 200) {
        placeGzsDoc((res.rows || []))
      }
    } catch (err: any) {
      console.log('文档文件列表查询失败', err)
    }
  }

  // 打开文档打印
  const openDocPrint = (type: string) => {
    const { matches, noMatches } = nodeFilter(gzswjList.value, (node) => {
      const obj = JSON.parse(node.wblj || node.ywlj || '{}');
      return node.lx == type;
    });

    if(matches.length > 0) {
      const docInfo = JSON.parse(matches[0].wblj || matches[0].ywlj || '{}');
      if(docInfo?.path) docOpenEdit(docInfo.path);
    } else {
      ElMessage.error('未找到需要打印的文档')
    };
  }

  // 给公证事项分配 公证书
  const placeGzsDoc = (list: any[]) => {
    let source: any[] = list
    gzsxList.value.map((item: any) => {

      const { matches, noMatches } = nodeFilter(source, (node) => {
        const obj = JSON.parse(node.wblj || node.ywlj || '{}');
        return (node.lx == '3') && ((item.id === obj.gzjzGzsxId) || (item.id === node.gzjzGzsxId));
      });

      if(matches.length > 0) {
        item.gzsFile = matches[0];
      } else {
        item.gzsFile = null;
      }

      source = noMatches;
      return item;
    })

    console.log('gzsxList.value', gzsxList.value);
  }

  // 打开公证书
  const openGzsDoc = (data: any, yw: boolean = false) => {
    let docInfo = null;
    if(yw) {
      // 公证书译文
      docInfo = JSON.parse(data.gzsFile.ywlj || '{}');
    } else {
      // 公证书
      docInfo = JSON.parse(data.gzsFile.wblj || '{}');
    }

    if(docInfo?.path) {
      docOpenShow(docInfo.path)
    }
  }

  // 打开文档
  const openDoc = (data: any) => {
    let docInfo = JSON.parse(data.wblj);
    docOpenShow(docInfo.path)
  }

  // 触发公证书译文生成
  const triggerYwGen = (data: any) => {
    const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  queryMbFiles({ wdLb: 5, ywId: data.gzsxId}).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        gzsGenState.typeData = res.data;
        gzsGenState.mbId = res.data[0].id;
        gzsGenState.show = true;
        gzsGenState.gzsxInfo = data;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loading.close();
  })
  }

  // 公证书译文生成
  const ywDocGen = () => {
    if (!gzsGenState.mbId) {
      ElMessage.warning('未选择生成指定模版')
      return;
    }

    let params: UserDocGenParams = {
      bizId: (curGzjz.value.id || gzjzId.value) as string,
      // type: gzsGenState.sxRow.typeCode,
      // fjlb: EnumDocFileType.QT,
      mbWdId: gzsGenState.mbId,
      extraParams: {
        gzxsId: gzsGenState.gzsxInfo.gzsxId,
      }
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    gzsGenState.loading = true;

    docGenerator(params, {
      success: (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        const { gzsxId, id, gzsxMc, gzsBh, gzsFile } = gzsGenState.gzsxInfo;
        const { typeCode, typeName } = {
          typeCode: 5,
          typeName: '公证书译文'
        };
        const fileName = `${typeName}_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`;
        let docInfo: GzjzWjccxxForm = {
          lx: String(typeCode),
          gzjzGzsxId: id, // gzjzGzsxId
          gzsx: gzsxMc,
          gzsbh: gzsBh,
          ywmc: fileName,
          ywlj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            typeCode,
            typeName,
            mbWdId: gzsGenState.mbId,
            gzsx: gzsxMc,
            gzsxId,
            gzjzGzsxId: id,
            // dsrIds,
          }),
          sfFy: '1'
        }

        if(gzsFile) {
          updateDoc({
            id: gzsFile.id,
            ywmc: fileName,
            ywlj: docInfo.ywlj
          })
        } else {
          addDoc(docInfo);
        }

        gzsGenState.show = false;
      }
    }).catch((err: any) => {
      console.error('文档生成失败', err)
      ElMessage.error('生成失败')
    }).finally(() => {
      gzsGenState.loading = false;
      loading.close()
      getInfo();
    })
  }

  const gzsGenClose = () => {
    gzsGenState.show = false;
  }

  // 上传文件
  const toUploadDoc = (data: any, type: 'gzsyw' | 'dsyw') => {
    upLoadState.show = true;
    upLoadState.type = type;
    if(type === 'gzsyw') {
      upLoadState.gzsxInfo = data;
    } else {
      upLoadState.dsInfo = data;
    }
    console.log(data)
  }

  // 0未分配待翻译，1已完成待校对，2驳回校对，3已校对，9驳回翻译-保留
  const mapFyzt = (zt: '0' | '1' | '2' | '3' | '9') => {
    let fyzt = '';
    switch(zt) {
      case '0':
        fyzt = '(待翻译)'
      break;
      case '1':
        fyzt = '(待校对)'
      break;
      case '2':
        fyzt = '(校对驳回中)'
      break;
      case '3':
        fyzt = '(已校对)'
      break;
      case '9':
        fyzt = '(翻译驳回中)'
      break;
    }
    return fyzt;
  }

  // 驳回译文
  const toRejectYw = (data: any) => {
    ywRjState.show = true;
    ywRjState.rjInfo = data.gzsFile;
  }

  const ywRjComfirm = async () => {
    const { id } = (ywRjState.rjInfo || {});
    if(!id) {
      ElMessage.warning('获取译文信息异常，请刷新重试');
      console.error('驳回译文翻译-获取译文信息异常', ywRjState.rjInfo)
      return;
    }
    const isValidPass = await ywRjRef.value.validate()
    if(!isValidPass) return;

    ywRjState.loading = true;

    const params = {
      id: ywRjState.rjInfo.id,
      fyzt: '2',
      remark: ywRjState.remark
    }
    updateGzjzWjccxx(params).then((res) => {
      if(res.code === 200) {
        ElMessage.success('操作成功');
        getInfo();
        ywRjClose();
      }
    }).catch((err: any) => {
      ElMessage.error('操作异常');
      console.error('驳回校对异常', err)
    }).finally(() => {
      ywRjState.loading = false;
    })
  }

  // 关闭译文驳回窗口
  const ywRjClose = () => {
    ywRjState.show = false
    ywRjState.remark = ''
  }

  const addDoc = async (docInfo: GzjzWjccxxForm) => {
    try {

      const res = await addGzjzWjccxx(docInfo);
    } catch(err: any) {
      console.log('新增失败', err)
    }
  }

  const updateDoc = async (docInfo: GzjzWjccxxForm) => {
    try {
      const res = await updateGzjzWjccxx(docInfo);
    } catch(err: any) {
      console.log('修改失败', err)
    }
  }

  // 文书代书文档文件列表查询
  const loadDsDocList = async () => {
    try {
      const params = {
        pageNum: 1,
        pageSize: 200,
        gzjzId: curGzjz.value.id || gzjzId.value,
        lx: '1'
      }
      const res = await listGzjzWjccxx(params);
      if(res.code === 200) {
        // placeDsDoc((res.rows || []))
        wsList.value = res.rows || [];
        console.log('文书文件列表', wsList.value);
      }
    } catch (err: any) {
      console.log('文档文件列表查询失败', err)
    }
  }

  // 上传
  const upLoadAllDone = (data: any[]) => {
    const workList = data.map(async (item) => {
      const { fileName, ossId, path } = item;
      let docInfo: GzjzWjccxxForm;

      if(upLoadState.type === 'gzsyw') {
        // 公证书译文上传
        docInfo = {
          ywmc: fileName,
          ywlj: JSON.stringify({
            fileName,
            fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
            ossId,
            path,
            gzsxId: upLoadState.gzsxInfo.gzsxId,
            gzsx: upLoadState.gzsxInfo.gzsxMc,
          }),
        }
        if(upLoadState.gzsxInfo.gzsFile) {
          // 判断存在调用修改接口
          docInfo = {
            id: upLoadState.gzsxInfo.gzsFile.id,
            ...docInfo
          }
          return updateDoc(docInfo)
        } else {
          // 不存在调用新增接口
          docInfo = {
            gzjzGzsxId: upLoadState.gzsxInfo.id,
            gzsx: upLoadState.gzsxInfo.gzsxMc,
            gzjzId: curGzjz.value.id || gzjzId.value,
            lx: '3',
            ...docInfo
          }
          return addDoc(docInfo)
        }
      } else if(upLoadState.type === 'dsyw') {
        // 文书代书译文上传
        docInfo = {
          ywmc: fileName,
          ywlj: JSON.stringify({
            fileName,
            fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
            ossId,
            path,
          }),
        }
        docInfo = {
          ...upLoadState.dsInfo,
          ...docInfo
        }
        return updateDoc(docInfo)
      }
    });

    Promise.all(workList).then(() => {
      getInfo(); // 重新获取数据
    })
  }

  const getInfo = async () => {
    loading.value = true;
    const res = await getGzjzJbxxAll(gzjzId.value).finally(()=>{loading.value = false;});
    console.log('popIndexByZz.getInfo', res);
    if(res?.data){
      gzjzJbxx.value = res?.data;
      gzsxList.value = res.data.gzsxVoList || [];
      gzswjList.value = res.data.wjccxxVoList || [];
      if(res.data.wsVoList && res.data.wsVoList.length > 0){
        let wswjList = [];
        res.data.wsVoList.forEach((item) => {
          wswjList.push(item.zmclxxMxList);
        });
        wsList.value = wswjList;
      }else{
        wsList.value = [];
      }
      syzList.value = res.data.syzmxVoList || [];

      placeGzsDoc(gzswjList.value)

      wsList.value = nodeFilter(gzswjList.value, (node) => {
        return node.lx == '1';
      }).matches;

      nextTick(() => {
        // loadGzsDocList();
        // loadGzsDocList(true);

        // loadDsDocList();
      })
    }
  }

  const getAreaList = async () => {
    try {
      const params = {
        pageSize: 300,
        pageNum: 1
      }
      const res = await listAreaname(params);
      if (res.code === 200) {
        areas.value = res.rows;
      }
    } catch (err: any) {
      console.error('获取地区名称失败:', err);
    }
  }

  const getAreaName = (_code ?: string | number) => {
    let an = '';
    if(areas.value && _code){
      areas.value.forEach((item => {
        if(_code === item.numberCode){
          an = item.areaName;
        }
      }));
    }
    return an;
  }

  const getGzsMc = (_id: number | string) => {
    if(!gzswjList.value.length){
      let gzswj;
      gzswjList.value.forEach((item) => {
        if(item.gzjzGzsxId === _id){
          gzswj = item;
        }
      });
      return gzswj?.wbmc;
    }
    return '';
  }

  const getYwMc = (_id: number | string) => {
    if(!gzswjList.value.length){
      let gzswj;
      gzswjList.value.forEach((item) => {
        if(item.gzjzGzsxId === _id){
          gzswj = item;
        }
      });
      return gzswj?.ywmc;
    }
    return '';
  }

  // 初始数据
  const init = async () => {
    await getInfo();
    await getAreaList();
  }

  /** 新增按钮操作 */
  const handleBuliding = (_row ?: any) => {
    proxy?.$modal.alertWarning("功能建设中...");
  }
  // 制证结束与通知
  const handleGenerateResult = () => {
    PopFormByZzhtzRef.value.open(gzjzId.value);
  }
  // 制证结束与通知 回调
  const zzhtzCallback = (_options : CallbackOption) => {
    if(_options?.success){
      // 成功 关闭当前弹窗
      cancel();
      emit('callback', {success: true});
    } else {
      // 失败 刷新数据
      getInfo();
    }
    _options?.fun();
  }
  // 打开
  const open = (_gzjzId: string | number) => {
    console.log('popIndexByZz.gzjzId', _gzjzId);
    if(!_gzjzId){
      proxy?.$modal.msgWarning("卷宗编号为空！");
      return;
    }
    gzjzId.value = _gzjzId;
    init();
    dialog.visible = true;
  }
  // 取消或关闭
  const cancel = () => {
    dialog.visible = false;
  }

  //申请翻译
  const handleSqfy = () => {
    sqfyDialogRef.value.handleSqfy(gzjzId.value);
  }

  const handleSyz = () => {
    syzRef.value?.open({
      gzsxList: gzsxList.value,
      gzjzId: curGzjz.value.id || gzjzId.value
    })
  }

  // 水印纸作废
  const syzDestroy = (row: any) => {
    ElMessageBox.confirm('确认作废当前所选水印纸吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await syzInvalidation(row.id);
        if(res.code === 200) {
          ElMessage.success('操作成功');
          getInfo();
        }
      } catch (err: any) {
        console.error('水印纸作废异常', err)
      }
    })
  }

  const sqfyCallback = (data : any) => {
    console.log('sqfyCallback', data);
    getInfo();// 刷新编辑页
  }

  const zzBhComfirm = () => {
    zzVisible.value = true;
  }

  // 回调方法
  const emit = defineEmits(['callback']);
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  });

  onMounted(() => {
  });
</script>

<style scoped>
  .zz-container {
    /* padding: 20px; */
    height: 100%;
    /* background: #f5f7fa; */
    overflow: auto;
  }

  .page-header {
    background: #f5f7fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .basic-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
  }

  .info-item {
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .requirement {
    width: 100%;
  }

  .label {
    color: #606266;
    font-weight: bold;
    padding-left: 15px;
    padding-right: 5px;
  }

  .value {
    color: #303133;
  }

  .section-container {
    background: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .section-header {
    padding: 12px 15px;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  .section-content {
    padding: 0;
  }

  .dual-section {
    display: flex;
    gap: 20px;
  }

  .half-width {
    flex: 1;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
  }

  /* 响应式样式 */
  @media (max-width: 1200px) {
    .dual-section {
      flex-direction: column;
    }

    .half-width {
      width: 100%;
    }
  }
</style>

