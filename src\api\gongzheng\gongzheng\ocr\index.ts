import request from '@/utils/request';
import { AxiosProgressEvent, AxiosPromise } from 'axios';
import { IdCardInfoVo } from './types';

export const getIdCardInfoByOcr = (base64Image: string): AxiosPromise<IdCardInfoVo> => {
  // const formData = new FormData();
  // formData.append('withImage', 'true');
  // formData.append('base64Image', base64Image)
  
  return request({
    url: '/ocr/idCardInfo',
    method: 'post',
    data: {
      withImage: true,
      base64Image
    }
  })
}