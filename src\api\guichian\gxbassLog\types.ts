export interface GxbassLogVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 上链描述
   */
  chainDec: string;

  /**
   * 上链数据
   */
  chainData: string;

  /**
   * 上链文件
   */
  ossId: string | number;

  /**
   * 存证类型 1文件存证 2 数据存证
   */
  chainType: number;

  /**
   * 虚拟通道ID，非虚拟通道加密方式返回空
   */
  channelId: string | number;

  /**
   * 加密方式
   */
  encryptMode: number;

  /**
   * 交易哈希
   */
  txHash: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 业务ID
   */
  bizId: string | number;

  /**
   * 业务分类
   */
  bizClassify: string;

}

export interface GxbassLogForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 上链描述
   */
  chainDec?: string;

  /**
   * 上链数据
   */
  chainData?: string;

  /**
   * 上链文件
   */
  ossId?: string | number;

  /**
   * 存证类型 1文件存证 2 数据存证
   */
  chainType?: number;

  /**
   * 虚拟通道ID，非虚拟通道加密方式返回空
   */
  channelId?: string | number;

  /**
   * 加密方式
   */
  encryptMode?: number;

  /**
   * 交易哈希
   */
  txHash?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 业务ID
   */
  bizId?: string | number;

  /**
   * 业务分类
   */
  bizClassify?: string;

}

export interface GxbassLogQuery extends PageQuery {

  /**
   * 上链描述
   */
  chainDec?: string;

  /**
   * 上链数据
   */
  chainData?: string;

  /**
   * 上链文件
   */
  ossId?: string | number;

  /**
   * 存证类型 1文件存证 2 数据存证
   */
  chainType?: number;

  /**
   * 虚拟通道ID，非虚拟通道加密方式返回空
   */
  channelId?: string | number;

  /**
   * 加密方式
   */
  encryptMode?: number;

  /**
   * 交易哈希
   */
  txHash?: string;

  /**
   * 业务ID
   */
  bizId?: string | number;

  /**
   * 业务分类
   */
  bizClassify?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



