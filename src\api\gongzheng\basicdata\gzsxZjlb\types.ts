export interface GzsxZjlbVO {
  /**
   * 公证事项
   */
  gzsxId : string | number;

  /**
   * 序号
   */
  id : string | number;

  /**
   * 证据名称
   */
  zjId : string | number;

  gzsxCode : string;

  gzlbValue : string;
}

export interface GzsxZjlbForm extends BaseEntity {
  /**
   * 公证事项
   */
  gzsxId ?: string | number;

  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 证据名称
   */
  zjId ?: string | number;

  selectId : [];

  gzsxCode ?: string;

  gzlbValue ?: string;
}

export interface GzsxZjlbQuery extends PageQuery {

  /**
   * 公证事项
   */
  gzsxId ?: string | number;

  /**
   * 证据名称
   */
  zjId ?: string | number;

  gzsxCode ?: string;

  gzlbValue ?: string;
  /**
   * 日期范围参数
   */
  params ?: any;
}
