<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="客户号" prop="khh">
              <el-input v-model="queryParams.khh" placeholder="请输入客户号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户姓名" prop="xm">
              <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码" prop="zjhm">
              <el-input v-model="queryParams.zjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户类型" prop="dsrlb">
              <el-select v-model="queryParams.dsrlb" placeholder="请选择客户类型" clearable>
                <el-option v-for="dict in gz_dsr_lb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd(1)"
              v-hasPermi="['dsr:dsrxxZrr:add']">新增个人客户</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd(2)"
              v-hasPermi="['dsr:dsrxxZrr:add']">新增单位客户</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Upload" @click="handleImport"
              v-hasPermi="['dsr:dsrxxZrr:import']">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['dsr:dsrxxZrr:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxZrrList" @selection-change="handleSelectionChange">
        <el-table-column label="客户号" align="center" prop="khh">
          <template #default="scope">
            {{scope.row.dsrlb==='1'?scope.row.khh:scope.row.hyhm}}
          </template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" prop="xm">
          <template #default="scope">
            {{scope.row.dsrlb==='1'?scope.row.xm:scope.row.dwmc}}
          </template>
        </el-table-column>
        <el-table-column label="客户类型" align="center" prop="dsrlb">
          <template #default="scope">
            <dict-tag :options="gz_dsr_lb" :value="scope.row.dsrlb"/>
          </template>
        </el-table-column>
        <el-table-column label="证件号码" align="center" prop="zjhm" />
        <el-table-column label="联系电话" align="center" prop="lxdh" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button v-if="scope.row.dsrlb==='1'" size="small" type="primary" link icon="InfoFilled"
                @click="handleDetail(scope.row)" v-hasPermi="['dsr:dsrxxZrr:query']">查看</el-button>
              <el-button v-if="scope.row.dsrlb==='2'" size="small" type="primary" link icon="InfoFilled"
                @click="handleDetail2(scope.row)" v-hasPermi="['dsr:dsrxxFrhzz:query']">查看</el-button>
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button v-if="scope.row.dsrlb==='1'" size="small" type="primary" link icon="Compass"
                @click="handleCopy(scope.row)" v-hasPermi="['dsr:dsrxxZrr:query']">复制</el-button>
              <el-button v-if="scope.row.dsrlb==='2'" size="small" type="primary" link icon="Compass"
                @click="handleCopy2(scope.row)" v-hasPermi="['dsr:dsrxxFrhzz:query']">复制</el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-if="scope.row.dsrlb==='1'" size="small" type="primary" link icon="Edit"
                @click="handleUpdate(scope.row)" v-hasPermi="['dsr:dsrxxZrr:edit']">修改</el-button>
              <el-button v-if="scope.row.dsrlb==='2'" size="small" type="primary" link icon="Edit"
                @click="handleUpdate2(scope.row)" v-hasPermi="['dsr:dsrxxFrhzz:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button size="small" v-if="scope.row.dsrlb==='1'" type="primary" link icon="Delete"
                @click="handleDelete(scope.row)" v-hasPermi="['dsr:dsrxxZrr:remove']">删除</el-button>
              <el-button size="small" v-if="scope.row.dsrlb==='2'" type="primary" link icon="Delete"
                @click="handleDelete2(scope.row)" v-hasPermi="['dsr:dsrxxFrhzz:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改个人客户-->
    <el-drawer :title="dialog.title" v-model="dialog.visible" append-to-body size="90%">
      <el-card class="no-padding-card dateil-card-main" v-if="dialigEdit">
        <div slot="header" class="clearfix dateil-card-heard">
          <span>客户信息</span>
        </div>
        <el-button link @click="savePhoto">拍照</el-button>
        <!-- <el-button link @click="uploadImg">上传</el-button> -->
        <el-button link @click="delPics">批量删除图片</el-button>
        <el-button link disabled>显示指纹</el-button>
        <el-button link disabled>指纹识别</el-button>
        <el-button link disabled>录入指纹</el-button>
        <!-- 当事人照片-->
        <DsrZp v-if="dialog.visible" ref="dsrListRef" :vo="form" :dialigEdit="dialigEdit"
          @update-count="handleUpdateGrxxzpIds"></DsrZp>
      </el-card>
      <!--证件列表-->
      <Zjlb v-if="dialog.visible" ref="zjlbRef" :vo="form"></Zjlb>
      <el-tabs style="margin-top:10px" v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="基本资料" name="first">
          <Grxx v-if="dialog.visible" ref="grxxRef" :vo="form" :dialigEdit="dialigEdit"
            @update-count="handleUpdateGrxx"></Grxx>
        </el-tab-pane>
        <el-tab-pane label="办证记录" name="second">
          <BzList v-if="dialog.visible"></BzList>
        </el-tab-pane>
        <el-tab-pane label="证据材料" name="third"></el-tab-pane>
        <el-tab-pane label="身份识别记录" name="fourth"></el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="dialigEdit" :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 添加或修改单位客户-->
    <el-drawer :title="dialog2.title" v-model="dialog2.visible" append-to-body size="90%">
      <Dwxx v-if="dialog2.visible" ref="dwxxRef" :vo="form2" :dialigEdit="dialigEdit2" @update-count="handleUpdateGwxx">
      </Dwxx>
      <!--证件列表-->
      <Zjlb v-if="dialog2.visible" ref="zjlbRef" :vo="form2"></Zjlb>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="dialigEdit2" :loading="buttonLoading2" type="primary" @click="submitForm2">保 存</el-button>
          <el-button @click="cancel2">关 闭</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 导入-->
    <el-dialog :title="dialog4.title" v-model="dialog4.visible" width="500px" append-to-body>
      <el-tabs style="margin-top:10px" v-model="activeName2" type="border-card">
        <el-tab-pane label="个人客户" name="first">
          <el-button link @click="handleExport">下载导入模板</el-button>
          <el-button link @click="handleImportDb(1)">上传并导入</el-button>
        </el-tab-pane>
        <el-tab-pane label="单位客户" name="second">
          <el-button link @click="handleExport">下载导入模板</el-button>
          <el-button link @click="handleImportDb(2)">上传并导入</el-button>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <!-- 拍照-->
    <el-dialog :title="dialog5.title" v-model="dialog5.visible" width="500px" append-to-body>
      <Pz v-if="dialog5.visible" ref="pzRef" :dsrId="form.id" @update-count="handleUpdatePhoto"></Pz>
    </el-dialog>
  </div>
</template>

<script setup name="DsrxxZrr" lang="ts">
  import { listDsrxxZrr, getDsrxxZrr, delDsrxxZrr, addDsrxxZrr, updateDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
  import { DsrxxZrrVO, DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  import { listDsrxxFrhzz, getDsrxxFrhzz, delDsrxxFrhzz, addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import Grxx from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/gr_xx.vue'
  import Zjlb from '@/views/gongzheng/dsr/dsrxxZrr/components/zjlb.vue'
  import Dwxx from '@/views/gongzheng/dsr/dsrxxZrr/components/dw/dw_xx.vue'
  import DsrZp from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/dsr_zp.vue'
  import Pz from '@/components/Gongzheng/pz/pz.vue'
  import BzList from '@/views/gongzheng/gongzheng/components/bz_list.vue'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_lb, gz_gr_zjlx, gz_dsr_hyzt } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_dsr_hyzt'));
  const grxxRef = ref<InstanceType<typeof Grxx> | null>(null);
  const zjlbRef = ref<InstanceType<typeof Zjlb> | null>(null);
  const dwxxRef = ref<InstanceType<typeof Dwxx> | null>(null);
  const dsrListRef = ref<InstanceType<typeof DsrZp> | null>(null);
  const pzRef = ref<InstanceType<typeof Pz> | null>(null);
  const dsrxxZrrList = ref<DsrxxZrrVO[]>([]);
  const buttonLoading = ref(false);
  const buttonLoading2 = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const activeName = ref("first");
  const activeName2 = ref("first");
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxZrrFormRef = ref<ElFormInstance>();
  const zsrPhotoIds = ref([]);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialigEdit = ref(true);
  const dialigEdit2 = ref(true);
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog3 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: undefined,
    ywm: undefined,
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined
  }
  const initDwxxFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: "1",
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });

  const { queryParams, form } = toRefs(data);
  /** 单位客户*/
  const data2 = reactive<DsrxxFrhzzForm, DsrxxFrhzzQuery>({
    form2: { ...initDwxxFormData },
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { queryParams2, form2 } = toRefs(data2);

  /** 查询当事人-基本信息-自然人信息列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDsrxxZrr(queryParams.value);
    dsrxxZrrList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxZrrFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    if (queryParams.value.dsrlb === "1") {
      getList();
    } else {
      getList2();
    }
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxZrrVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = (type) => {
    dialigEdit.value = true;
    if (type == 1) {
      reset();
      dialog.visible = true;
      dialog.title = "新增个人客户";
    } else if (type == 2) {
      reset2();
      dialog2.visible = true;
      dialog2.title = "新增单位客户";
    }
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrxxZrrVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZrr(_id);
    Object.assign(form.value, res.data);
    dialigEdit.value = true;
    dialog.visible = true;
    dialog.title = "编辑个人客户";
  }
  const handleDetail = async (row ?: DsrxxZrrVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZrr(_id);
    Object.assign(form.value, res.data);
    dialigEdit.value = false;
    dialog.visible = true;
    dialog.title = "查看个人客户信息";
  }

  /** 个人客户提交按钮 */
  const submitForm = async () => {
    buttonLoading.value = true;
    form.value.dsrlb = "1";
    if (form.value.id) {
      await updateDsrxxZrr(form.value).finally(() => buttonLoading.value = false);
    } else {
      await addDsrxxZrr(form.value).finally(() => buttonLoading.value = false);
    }
    proxy?.$modal.msgSuccess("操作成功");
    dialog.visible = false;
    queryParams.value.dsrlb = "1";
    handleQuery();
  }
  /** 个人单位删除按钮操作 */
  const handleDelete = async (row ?: DsrxxZrrVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除个人客户编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxZrr(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }
  //单位客户提交
  const submitForm2 = async () => {
    buttonLoading2.value = true;
    form2.value.dsrlb = "2";
    if (form2.value.id) {
      await updateDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    } else {
      await addDsrxxFrhzz(form2.value).finally(() => buttonLoading2.value = false);
    }
    proxy?.$modal.msgSuccess("操作成功");
    dialog2.visible = false;
    queryParams.value.dsrlb = "2";
    handleQuery();
  }

  /** 单位客户修改按钮操作 */
  const handleUpdate2 = async (row ?: DsrxxFrhzzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxFrhzz(_id);
    Object.assign(form2.value, res.data);
    dialigEdit2.value = true;
    dialog2.visible = true;
    dialog2.title = "编辑单位客户";
  }
  //单位客户查看详情
  const handleDetail2 = async (row ?: DsrxxFrhzzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxFrhzz(_id);
    Object.assign(form2.value, res.data);
    dialigEdit2.value = false;
    dialog2.visible = true;
    dialog2.title = "查看单位客户信息";
  }
  /** 表单重置 */
  const reset2 = () => {
    form2.value = { ...initDwxxFormData };
  }


  /**单位客户 删除按钮操作 */
  const handleDelete2 = async (row ?: DsrxxFrhzzVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除单位客户编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxFrhzz(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList2();
  }
  /** 导出按钮操作 */
  const handleExport = () => {
    if (queryParams.value.dsrlb === "1") {
      proxy?.download('dsr/dsrxxZrr/export', {
        ...queryParams.value
      }, `个人客户信息_${new Date().getTime()}.xlsx`)
    } else {
      proxy?.download('dsr/dsrxxFrhzz/export', {
        ...queryParams.value
      }, `单位客户信息_${new Date().getTime()}.xlsx`)
    }
  }
  const handleImportDb = (type) => {
    proxy?.$modal.msgWarning("开发中");
  }
  const handleImport = () => {
    dialog4.visible = true;
    dialog4.title = "导入";
  }

  const handleClick = (tab, event) => {
    console.log(tab, event);
  }
  /** 复制个人客户信息*/
  const handleCopy = async (row ?: DsrxxZrrVO) => {
    if (navigator.clipboard) {
      const txt = row.xm + "," + (row.xb === "1" ? "男" : "女") + "," + row.csrq + "出生,证件号码:" + row.zjhm + ",住址:" + row.zz
      await navigator.clipboard.writeText(txt);
      proxy?.$modal.msgSuccess("当事人信息已复制到剪切板");
    } else {
      proxy?.$modal.msgError("当前浏览器不支持该功能");
    }

  }
  /** 复制 单位客户信息*/
  const handleCopy2 = async (row ?: DsrxxFrhzzVO) => {
    if (navigator.clipboard) {
      const txt = row.dwmc + "证件号码:" + row.zjhm;
      await navigator.clipboard.writeText(txt);
      proxy?.$modal.msgSuccess("当事人信息已复制到剪切板");
    } else {
      proxy?.$modal.msgError("当前浏览器不支持该功能");
    }

  }
  /** 取消按钮 */
  const cancel2 = () => {
    dialog2.visible = false;
  }

  const handleUpdateGrxx = (vo : DsrxxZrrVO) => {
    if (vo) {
      form.value = vo;
      console.log(form.value)
    }
  };
  const handleUpdateGwxx = (vo : DsrxxFrhzzVO) => {
    if (vo) {
      form2.value = vo;
      console.log(form2.value)
    }
  };
  const handleUpdateGrxxzpIds = (ids) => {
    console.log(ids)
    if (ids && ids.length > 0) {
      zsrPhotoIds.value = ids;
    }
  }



  /** 查询单位客户列表 */
  const getList2 = async () => {
    loading.value = true;
    const res = await listDsrxxFrhzz(queryParams2.value);
    dsrxxZrrList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }
  /** 拍照*/
  const savePhoto = () => {
    // dsrListRef.value?.savePhoto(form.value?.id);
    if (form.value.id) {
      dialog5.visible = true;
      dialog5.title = "拍照";
      console.log(form.value.id)
    } else {
      proxy?.$modal.msgError("请先保存基础资料");
    }

  }
  /** 上传*/
  const uploadImg = () => {
    dsrListRef.value?.uploadImg(form.value?.id);
  }
  /** 批量删除*/
  const delPics = () => {
    dsrListRef.value?.delPics(zsrPhotoIds.value);
  }
  /** 上传成功后刷新图片列表 */
  const handleUpdatePhoto = () => {
    dsrListRef.value?.getList();
  }
  onMounted(() => {
    if (queryParams.value.dsrlb === "1") {
      getList();
    } else {
      getList2();
    }

  });
</script>
<style scoped>
  .no-padding-card .el-card__body {
    padding: 0;
  }

  .dateil-card-main {
    max-height: 500px;
    height: 300px;
    margin-bottom: 10px;
  }

  .dateil-card-heard {
    /* background-color: aliceblue; */
    padding: 10px;
    border-bottom: 1px solid #e7e7e7;
  }
</style>
