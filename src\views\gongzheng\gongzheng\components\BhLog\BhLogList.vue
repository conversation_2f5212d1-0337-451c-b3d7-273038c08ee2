<template>
  <div class="h-full">
    <div class="h-full min-h-200px max-h-600px overflow-hidden">
      <el-table :data="ListData" v-loading="ListLoading" style="height: 100%" border stripe>
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column prop="czrxm" label="操作人" align="center" />
        <el-table-column prop="czrq" label="操作时间" align="center" width="180">
          <template #default="{ row }">
            {{ formatDate(row.czrq, 'YYYY-MM-DD hh:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="lcyj" label="流程意见" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { listGzrzLcrzxx } from '@/api/gongzheng/gongzheng/gzrzLcrzxx';
import { formatDate } from '@/utils/ruoyi';
import { inject } from 'vue';

interface Props {
  gzjzId?: string | number;
}

const props = defineProps<Props>();

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const ListData = ref([]);
const ListLoading = ref(false);

const loadListData = async () => {
  try {
    ListLoading.value = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      sftg: '0',
      pageSize: 200,
      pageNum: 1
    }
    const res = await listGzrzLcrzxx(params);
    if(res.code === 200) {
      ListData.value = res.rows || [];
    }
  } catch (err: any) {
    console.log('调查结果查询失败', err);
    ElMessage.error('调查结果查询失败');
  } finally {
    ListLoading.value = false;
  }
}

watch()

onMounted(() => {
  loadListData();
})
</script>
