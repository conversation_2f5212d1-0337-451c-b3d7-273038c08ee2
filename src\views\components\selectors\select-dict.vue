<template>
  <el-select :disabled="disabled" v-if="displayType=='select'" :style="[width!=''?`width:${width}`:'']"
    v-loading="isLoading" v-model="selectedIds" @change="onSelectChange" :placeholder="placeholder" :multiple="false"
    :filterable="filterable" :clearable="clearable" @clear="onSelectChange">
    <el-option v-for="item in compDataList" :key="item[valueKey]" :label="item.dictLabel" :value="item[valueKey]" />
  </el-select>
  <el-radio-group v-if="displayType=='radio'" v-loading="isLoading" v-model="selectedIds"
    @change="onSelectChange">
    <el-radio  v-for="item in compDataList" :key="item[valueKey]"
      :label="item[valueKey]">{{item.dictLabel}}</el-radio>
  </el-radio-group>
</template>

<script setup name="Demo" lang="ts">
  // 数据字典选择组件
  import { ElMessage } from 'element-plus'
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import { useRoute } from 'vue-router'
  import { getDicts } from '@/api/system/dict/data';
  import { getType } from '@/api/system/dict/type';
  import { DictTypeVO } from '@/api/system/dict/type/types';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  console.log('proxy', proxy);

  // 定义 props 类型
  interface Props {
    modelValue ?: string | number
    dictType : string
    displayType ?: 'select'
    width ?: string
    placeholder ?: string
    disabled ?: false
    filterable ?: false
    clearable ?: false,
    datalist ?: any[],
  }
  const props = withDefaults(defineProps<Props>(), {
    displayType: 'select',
    placeholder: '请选择',
    width: '100px'
  });

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'update:modelValue', value : string | number) : void
    (event : 'changed') : void
    (event : 'ready') : void
    (event : 'close') : void
  }>()

  // 监听
  watch(() => props.modelValue, (newVal) => {
    selectedIds.value = newVal;
    emits('changed');
  })

  // 定义 data
  const isLoading = ref(false)
  const selectedIds = ref(props.modelValue)
  const valueKey = ref('dictValue')
  const compDataList = ref([])

  // 生命周期
  onMounted(() => {
    console.log('mouted');
  });

  // 组件内自定义方法
  const onSelectChange = () => {
    emits('update:modelValue', selectedIds.value)
    emits('changed');
  }

  const requestData = async () => {
    isLoading.value = true;
    try {
      const res = await getDicts(proxy.dictType);
      console.log('dict-data', res)
      compDataList.value = res.data || [];
      emits('ready');
    } finally {
      isLoading.value = false;
    }
  }

  const getcompDataList = () => {
    return compDataList.value;
  }

  if (null == props.datalist || props.datalist.length == 0) {
    requestData();
  }

  // 暴露方法给父组件
  defineExpose({
    requestData,
    getcompDataList,
  })
</script>
