<template>
  <el-card>
    <template #header>
      <strong class="text-base">送达记录</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="js" label="通知方式" width="120" align="center" />
      <el-table-column prop="name" label="送达邮箱" align="center" />
      <el-table-column prop="dsrLx" label="送达号码" width="120" align="center" />
      <el-table-column prop="zjlx" label="送达日期" width="120" align="center" />
      <el-table-column prop="zjlxhm" label="发送结果" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
