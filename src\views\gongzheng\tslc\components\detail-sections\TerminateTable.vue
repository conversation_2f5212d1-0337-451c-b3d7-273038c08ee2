<template>
  <el-table :data="items" border stripe style="width: 100%">
    <el-table-column type="index" label="序号" width="60" align="center" />
    <el-table-column label="公证事项" prop="notarizationItem" />
    <el-table-column label="公证书编号" prop="certificateNumber" />
    <el-table-column label="公证书" prop="certificate" />
    <el-table-column label="公证书译文" prop="certificateTranslation" />
  </el-table>
</template>

<script setup lang="ts">
interface Props {
  items: any[];
}

defineProps<Props>();
</script>
