import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DzjzmlVO, DzjzmlForm, DzjzmlQuery } from '@/api/basicdata/dzjzml/types';

/**
 * 查询电子卷宗目录列表
 * @param query
 * @returns {*}
 */

export const listDzjzml = (query?: DzjzmlQuery): AxiosPromise<DzjzmlVO[]> => {
  return request({
    url: '/basicdata/dzjzml/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询电子卷宗目录详细
 * @param id
 */
export const getDzjzml = (id: string | number): AxiosPromise<DzjzmlVO> => {
  return request({
    url: '/basicdata/dzjzml/' + id,
    method: 'get'
  });
};

/**
 * 新增电子卷宗目录
 * @param data
 */
export const addDzjzml = (data: DzjzmlForm) => {
  return request({
    url: '/basicdata/dzjzml',
    method: 'post',
    data: data
  });
};

/**
 * 修改电子卷宗目录
 * @param data
 */
export const updateDzjzml = (data: DzjzmlForm) => {
  return request({
    url: '/basicdata/dzjzml',
    method: 'put',
    data: data
  });
};

/**
 * 删除电子卷宗目录
 * @param id
 */
export const delDzjzml = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/dzjzml/' + id,
    method: 'delete'
  });
};
