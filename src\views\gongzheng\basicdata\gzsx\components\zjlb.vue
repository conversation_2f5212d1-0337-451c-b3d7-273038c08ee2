<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <span style="line-height: 24px; display: block; width: 160px;">证 据 列 表</span>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="primary" icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:gzsxZjlb:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="danger" icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['basicdata:gzsxZjlb:remove']">删除</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzsxZjlbList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="证据名称" align="center" prop="zjId">
          <template #default="scope">
            <el-tree-select v-model="scope.row.zjId" :data="zjclmcList"
            :disabled="true"
              :props="{ value: 'id', label: 'title', children: 'children' } as any" value-key="parentId"
              placeholder="选择证据名称" check-strictly />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzsxZjlb:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzsxZjlb:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="loadData" />
    </el-card>
    <!-- 添加或修改证据列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzsxZjlbFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="证据名称" prop="zjId">
          <el-tree-select v-model="form.zjId" :data="zjclmcList"
            :props="{ value: 'id', label: 'title', children: 'children' } as any" value-key="parentId"
            placeholder="选择证据名称" check-strictly />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzsxZjlb" lang="ts">
  import { listGzsxZjlb, getGzsxZjlb, delGzsxZjlb, addGzsxZjlb, updateGzsxZjlb } from '@/api/gongzheng/basicdata/gzsxZjlb';
  import { GzsxZjlbVO, GzsxZjlbQuery, GzsxZjlbForm } from '@/api/gongzheng/basicdata/gzsxZjlb/types';
  import { listTree } from '@/api/gongzheng/basicdata/zjclmc';
  import { ZjclmcVO } from '@/api/gongzheng/basicdata/zjclmc/types';
  interface Props {
    gzsxId : string | Number;
    selectId : [];
    gzsxCode: string;
    gzlbValue: string;
  }
  const props = defineProps<Props>();

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const gzsxZjlbList = ref<GzsxZjlbVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const loading2 = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const zjclmcList = ref<ZjclmcVO[]>([]);
  const queryFormRef = ref<ElFormInstance>();
  const gzsxZjlbFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxZjlbForm = {
    id: undefined,
    gzsxId: undefined,
    zjId: undefined,
    selectId: undefined,
    gzsxCode: undefined,
    gzlbValue: undefined
  }
  const data = reactive<PageData<GzsxZjlbForm, GzsxZjlbQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzsxId: undefined,
      zjId: undefined,
      gzsxCode: undefined,
      gzlbValue: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      zjId: [
        { required: true, message: "证据名称不能为空", trigger: "blur" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询证据列表列表 */
  const getList = async (_gzsxCode, _gzlbValue) => {
    if(_gzsxCode && _gzlbValue){
      loading.value = true;
      queryParams.value.gzsxCode = _gzsxCode;
      queryParams.value.gzlbValue = _gzlbValue;
      const res = await listGzsxZjlb(queryParams.value);
      gzsxZjlbList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    }else{
      gzsxZjlbList.value = [];
      total.value = 0;
    }
  }

  const loadData = async () => {
    getList(props.gzsxCode, props.gzlbValue);
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsxZjlbFormRef.value?.resetFields();
  }


  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsxZjlbVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加证据列表";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzsxZjlbVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getGzsxZjlb(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改证据列表";
  }

  /** 提交按钮 */
  const submitForm = () => {
    form.value.gzsxId = props.gzsxId;
    form.value.gzsxCode = props.gzsxCode;
    form.value.gzlbValue = props.gzlbValue;
    gzsxZjlbFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzsxZjlb(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzsxZjlb(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList(props.gzsxCode, props.gzlbValue);
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzsxZjlbVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除证据列表编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGzsxZjlb(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList(props.gzsxCode, props.gzlbValue);
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzsxZjlb/export', {
      ...queryParams.value
    }, `gzsxZjlb_${new Date().getTime()}.xlsx`)
  }

  /** 查询证据名称列表 */
  const getZjList = async () => {
    loading2.value = true;
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTreeCode<ZjclmcVO>(res.data, 'treeCode');
    if (data) {
      zjclmcList.value = data;
    }
    loading2.value = false;
  }
  const init = (_gzsxCode, _gzlbValue) => {
    console.log("选中公证事项：" + _gzsxCode);
    console.log("选中公证类别：" + _gzlbValue);
    getList(_gzsxCode, _gzlbValue)
  }
  const setGzsxIds = (data) => {
    form.value.selectId = data;
  }
  // 显式暴露方法给父组件
  defineExpose({
    init,
    setGzsxIds
  });
  onMounted(() => {
    getList(props.gzsxCode, props.gzlbValue);
    getZjList();
  });
</script>
