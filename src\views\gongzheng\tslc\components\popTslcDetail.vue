<template>
  <vxe-modal v-model="visible" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="handleClose">
    <div class="detail-container">
      <!-- 顶部：申请信息 -->
      <!--   <el-form :model="detail" label-width="120px" class="block-card">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请人:"><el-input v-model="detail.tslcFqr" readonly disabled /></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请日期:"><el-input :model-value="detail.tslcFqrq" readonly disabled /></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审批人:"><el-input v-model="detail.tslcSpr" readonly disabled /></el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="申请原因:">
          <el-input v-model="detail.tslcSqyy" type="textarea" :rows="2" readonly disabled />
        </el-form-item>
      </el-form> -->
      <div class="block-card">
        <div class="section-title">申请信息</div>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="申请类型"> {{ dictMapFormat(gz_tslc_lx, detail.tslcLx) }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ detail.tslcFqr }}</el-descriptions-item>
          <el-descriptions-item label="申请日期">{{ detail.tslcFqrq }}</el-descriptions-item>
          <el-descriptions-item label="审批人">{{ detail.tslcSpr }}</el-descriptions-item>
          <el-descriptions-item label="申请原因">{{ detail.tslcSqyy }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 卷宗信息 -->
      <div class="block-card" v-if="detail.caseInfo">
        <div class="section-title">卷宗信息</div>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="卷宗号" label-width="90px">{{ detail.caseInfo.jzbh }}</el-descriptions-item>
          <el-descriptions-item label="公证员" label-width="90px">{{ detail.caseInfo.gzyxm }}</el-descriptions-item>
          <el-descriptions-item label="助理" label-width="90px">{{ detail.caseInfo.zlxm }}</el-descriptions-item>
          <el-descriptions-item label="受理日期" label-width="90px">{{ detail.caseInfo.slrq }}</el-descriptions-item>
          <el-descriptions-item label="公证类别" label-width="90px">
            {{ dictMapFormat(gz_gzlb, detail.caseInfo.lb) }}
          </el-descriptions-item>
          <el-descriptions-item label="当事人" label-width="90px">{{ detail.caseInfo.dsrxm }}</el-descriptions-item>
          <el-descriptions-item label="当事人" label-width="90px">{{ detail.caseInfo.dsrxm }}</el-descriptions-item>
          <el-descriptions-item label="公证书编号" label-width="90px">{{ detail.gzsbh }}</el-descriptions-item>
          <el-descriptions-item label="决定书编号" label-width="90px"
            v-if="detail.jdsbh">{{ detail.jdsbh }}</el-descriptions-item>
          <el-descriptions-item label="预览/下载决定书" label-width="90px" v-if="detail.jdsbh">
            <el-link type="primary" :href="detail.fileUrl" target="_blank" v-if="detail.fileUrl">预览/下载决定书</el-link>
            <span v-else>{{ detail.jdswj }}</span>
          </el-descriptions-item>
        </el-descriptions>

      </div>

      <!-- 类型差异区：表格 -->
      <div class="block-card">
        <div class="section-title">其他信息</div>
        <component :is="currentTableComp" :items="tableItems" />
      </div>

      <!-- 审批意见 -->
      <div class="block-card">
        <div class="section-title">审批信息</div>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="审批意见" label-width="130px">
            {{ detail.tslcSpyj?detail.tslcSpyj:'--' }}
          </el-descriptions-item>
          <el-descriptions-item label="审批状态">
            {{ dictMapFormat(gz_tslc_zt, detail.tslcZt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="showPrint" @click="() => toPrintSpb()">打印审批表</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<script setup name="PopTslcDetail" lang="ts">
  import { ref, reactive, computed, nextTick } from 'vue';
  import type { VxeModalProps } from 'vxe-table';
  import RefundTable from './detail-sections/RefundTable.vue';
  import FeeReductionTable from './detail-sections/FeeReductionTable.vue';
  import TerminateTable from './detail-sections/TerminateTable.vue';
  import RejectTable from './detail-sections/RejectTable.vue';
  import OtherTable from './detail-sections/OtherTable.vue';
  import * as api from '@/api/gongzheng/tslc/tslcSqb';
  import { getChargeList, getCaseItems } from '@/api/gongzheng/tslc/tslcSqb'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_sf_lb, gz_tslc_zt, gz_gzlb, gz_tslc_lx } = toRefs<any>(proxy?.useDict('gz_tslc_lx', 'gz_sf_lb', 'gz_tslc_zt', 'gz_gzlb'));
  import { dictMapFormat } from '@/utils/ruoyi';
import { queryOssInfo } from '@/api/gongzheng/gongzheng/oss';
import { docOpenEdit } from '../../doc/DocEditor';
  interface Props {
    title ?: string;
    width ?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '详情',
    width: '1000px'
  });

  const emit = defineEmits<{
    (e : 'close') : void;
    (e : 'print', data : any) : void;
  }>();

  const visible = ref(false);
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: props.width,
    height: '80vh',
    escClosable: true,
    resize: true,
    showMaximize: true,
    destroyOnClose: true
  });

  const detail = reactive<any>({});
  const tableItems = ref<any[]>([]);

  const currentTableComp = computed(() => {
    switch (String(detail.tslcLx || '')) {
      case '6':
        return RefundTable;
      case '5':
        return FeeReductionTable;
      case '2':
        return TerminateTable;
      case '3':
        return RejectTable;
      case '1':
        return RejectTable;
      case '4':
        return RejectTable;
      default:
        return OtherTable; // 占位，空数据不显示
    }
  });

  const showPrint = computed(() => ['5', '6'].includes(String(detail.tslcLx || '')));

  const open = async (option : { id ?: string | number; row ?: any } = {}) => {
    reset();
    visible.value = true;
    await nextTick();
    if (option.row) {
      Object.assign(detail, option.row);
      tableItems.value = mapItems(option.row);
      return;
    }
    if (option.id) {
      try {
        const res : any = await api.getTslcSqb(option.id);
        if (res && res.data) {
          const data = res.data
          // 头部字段容错映射
          data.tslcSpr = data.tslcSprName || data.tslcSpr || data.approver
          data.jzh = data.jzh || data.caseNumber
          data.gzsbh = data.gzsbh || data.certificateNumber
          data.jdsbh = data.jdsbh || data.decisionNumber
          if (data.caseInfo) {
            data.caseInfo.lb = data.caseInfo.lb + ''
          }

          // data.caseInfo=data.caseInfo;
          Object.assign(detail, data);
          let items = data.items;
          if (!items || items.length === 0) {
            const lx = String(data.tslcLx || '')
            if (lx === '1' || lx === '4' || lx === '2' || lx === '3') {
              const r : any = await getCaseItems({ gzjzId: data.gzjzId, jzh: data.jzh })
              const rows = r.rows || r.data?.rows || []
              console.log(rows)
              items = rows.map((it : any) => ({
                notarizationItem: it.notarizationItem || it.gzsx,
                certificateNumber: it.certificateNumber || it.gzsbh,
                certificate: it.certificate || it.gzs,
                translation: it.translation || it.yw
              }))
            }
          }
          const lx = String(data.tslcLx || '')
          if (lx == '7' || lx == '8' || lx == '9') {
            tableItems.value = data.caseList;
          } else {
            tableItems.value = items
          }

        }
      } catch (e) {
        console.error('加载详情失败', e);
      }
    }
  };

  const close = () => {
    visible.value = false;
    emit('close');
  };

  const handleClose = () => {
    close();
  };

  const reset = () => {
    for (const k of Object.keys(detail)) delete (detail as any)[k];
    tableItems.value = [];
  };

  const toPrintSpb = async (ossId?: string) => {
    if(!ossId && !detail.tslcSpbwj) {
      ElMessage.error('审批表不存在，请核对后重试')
      return;
    }
    try {
      const ossRes = await queryOssInfo(ossId || detail.tslcSpbwj);
      if(ossRes.code === 200) {
        const { ossId, fileName: path, fileSuffix } = ossRes.data[0];
        docOpenEdit(path)
      }
    } catch (err: any) {
      ElMessage.error('获取文件信息失败');
    }
  }

  function mapItems(data : any) : any[] {
    // 尝试兼容多种字段命名；若后端提供明细数组，优先使用
    const list = data?.detailList || data?.items || data?.sfmxList || [];
    if (!Array.isArray(list)) return [];
    return list.map((it : any) => ({
      feeType: it.fylx || it.feeType || '',
      notarizationItem: it.gzsx || it.notarizationItem || '',
      certificateNumber: it.gzsbh || it.certificateNumber || '',
      amountPayable: it.ys || it.amountPayable || 0,
      amountReceived: it.ysh || it.amountReceived || 0,
      amountRefunded: it.yt || it.amountRefunded || 0,
      amountThisRefund: it.bct || it.amountThisRefund || 0,
      amountReduction: it.jm || it.amountReduction || 0,
      status: it.zt || it.status || '',
      chargeStatus: it.sfsz || it.chargeStatus || '',
      certificate: it.gzs || it.certificate || '',
      translation: it.yw || it.translation || ''
    }));
  }

  defineExpose({ open, close });
</script>

<style scoped>
  .detail-container {
    padding: 16px;
  }

  .block-card {
    background: #fff;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 4px;
  }

  .section-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .info-item {
    display: flex;
    gap: 6px;
    line-height: 28px;
  }

  .info-item .label {
    color: #666;
  }

  .info-item.one-line .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>
