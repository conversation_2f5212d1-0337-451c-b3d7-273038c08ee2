<template>
  <!-- 送达信息对话框 -->
  <el-dialog v-model="dialogIndex.visible" :title="dialogIndex.title" width="1200" @close="cancel">
    <div>
      <div class="tipc">请询问当事人当出证后，期望送达方式</div>
      <div class="mainx">
        <div class="title">公证书信息</div>
        <el-table v-loading="gzsxLoading" :data="gzsxList" style="height: 200px;" border size="small">
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column label="事项" align="center" prop="gzsxMc"  show-overflow-tooltip/>
          <el-table-column label="公证书编号" align="center" prop="gzsBh"  show-overflow-tooltip/>
          <el-table-column label="总份数" align="center" prop="gzsFs" />
          <el-table-column label="可领份数" align="center" prop="gzsFs" />
        </el-table>
        <div class="title">发证信息</div>
        <TableBySdxx ref="TableBySdxxRef" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <slot name="actions" :data="gzjzId"></slot>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SdxxIndex" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx'
  import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
  import { listGzjzSdxx, addGzjzSdxx, updateGzjzSdxx, delGzjzSdxx } from '@/api/gongzheng/gongzheng/gzjzSdxx'
  import type { GzjzSdxxVO, GzjzSdxxForm, GzjzSdxxQuery } from '@/api/gongzheng/gongzheng/gzjzSdxx/types';
  import TableBySdxx from './tableBySdxx.vue';
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
  import { number } from 'vue-types'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_fzxx_sdfs, gz_fzxx_sdlx, gz_gr_zjlx, gz_fzxx_lqxx } = toRefs<any>(proxy?.useDict('gz_fzxx_sdfs', 'gz_fzxx_sdlx', 'gz_gr_zjlx', 'gz_fzxx_lqxx'));

  const props = defineProps({
    viewModule: {
      type: String,
      default: 'normal',
      required: false
    }
  });
  const TableBySdxxRef = ref<InstanceType<typeof TableBySdxx>>(null);
  // const SdxxFormRef = ref<InstanceType<typeof SdxxForm>>(null);

  const { viewModule } = props;

  const gzjzId = ref<string | number>(null);
  const gzsxLoading = ref(false);
  const gzsxList = ref<GzjzGzsxVO[]>([]);

  const dialogIndex = reactive<DialogOption>({
    visible: false,
    title: '送达信息'
  });

  // TODO 获取机构地址
  const gzcAddr = ref('南宁市星湖路北二里1号（司法厅门）');
  const view = ref(false);

  // 提供给子组件的数据和方法
  provide('gzcAddr', gzcAddr);
  provide('view', view);
  provide('gzjzId', gzjzId);
  provide('gzsxList', gzsxList);

  // 获取公证书信息
  const getGzsxList = async () => {
    gzsxLoading.value = true;
    try {
      const res = await listGzjzGzsx({
        gzjzId: gzjzId.value,
        pageNum: 1,
        pageSize: 1000
      });
      gzsxList.value = res.rows || [];
    } catch (error) {
      console.error('获取公证书信息失败:', error);
      proxy?.$modal.msgError('获取公证书信息失败');
    } finally {
      gzsxLoading.value = false;
    }
  };

  const init = () => {
    getGzsxList();
  }

  const open = (_gzjzId: string | number) => {
    console.log('sdxxIndex.gzjzId', _gzjzId);
    if(!_gzjzId){
      proxy?.$modal.msgWarning("卷宗编号为空！");
      return;
    }
    gzjzId.value = _gzjzId;
    init();
    dialogIndex.visible = true;
    view.value = true;
  }

  const cancel = () => {
    dialogIndex.visible = false;
    view.value = false;
    emit('callback', {success: true});
  }

  // 回调方法
  const emit = defineEmits(['callback']);
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  });

  onMounted(() => {
  });

</script>

<style scoped>
.tipc {
  text-align: center;
  font-size: 14px;
  color: red;
}

.mainx {
  margin-top: 15px;
}

.title {
  font-weight: bold;
  font-size: 15px;
  margin: 10px 0px;
  line-height: 32px;
}

.action-wrap {
  /* float: right; */
  margin-bottom: 10px;
}
</style>
