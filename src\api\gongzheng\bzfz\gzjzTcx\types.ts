export interface GzjzTcxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId: string | number;

  /**
   * 提存人ID（当事人）
   */
  tcrId: string | number;

  /**
   * 提存人姓名
   */
  tcrXm: string;

  /**
   * 提存类别
   */
  tclb: string;

  /**
   * 业务类别
   */
  ywlb: string;

  /**
   * 提存日期
   */
  tcrq: string;

  /**
   * 提存标的
   */
  tcbd: string;

  /**
   * 名称/证券名称
   */
  wpMc: string;

  /**
   * 数量/证券数量
   */
  wpSl: number;

  /**
   * 备注
   */
  wpBz: string;

  /**
   * 领取条件
   */
  lqtj: string;

  /**
   * 状态
   */
  zt: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 币种
   */
  hbLx: string;

  /**
   * 金额（应缴纳款项）
   */
  hbJe: number;

  /**
   * 折合人名币
   */
  hbZhrmb: number;

  /**
   * 开户行
   */
  hbKhh: string;

  /**
   * 账号
   */
  hbYhzh: string;

  /**
   * 入账时间
   */
  hbRzsj: string;

  /**
   * 累计实缴
   */
  hbLjsj: number;

  /**
   * 累计领取
   */
  hbLjlq: number;

  /**
   * 余额
   */
  hbYe: number;

  /**
   * 提存原因
   */
  tcyy: string;
  /**
   * 承办人id
   */
  cbrId : string | number;

  /**
   * 承办人姓名
   */
  cbrXm : string;
}

export interface GzjzTcxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 提存人ID（当事人）
   */
  tcrId?: string | number;

  /**
   * 提存人姓名
   */
  tcrXm?: string;

  /**
   * 提存类别
   */
  tclb?: string;

  /**
   * 业务类别
   */
  ywlb?: string;

  /**
   * 提存日期
   */
  tcrq?: string;

  /**
   * 提存标的
   */
  tcbd?: string;

  /**
   * 名称/证券名称
   */
  wpMc?: string;

  /**
   * 数量/证券数量
   */
  wpSl?: number;

  /**
   * 备注
   */
  wpBz?: string;

  /**
   * 领取条件
   */
  lqtj?: string;

  /**
   * 状态
   */
  zt?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 币种
   */
  hbLx?: string;

  /**
   * 金额（应缴纳款项）
   */
  hbJe?: number;

  /**
   * 折合人名币
   */
  hbZhrmb?: number;

  /**
   * 开户行
   */
  hbKhh?: string;

  /**
   * 账号
   */
  hbYhzh?: string;

  /**
   * 入账时间
   */
  hbRzsj?: string;

  /**
   * 累计实缴
   */
  hbLjsj?: number;

  /**
   * 累计领取
   */
  hbLjlq?: number;

  /**
   * 余额
   */
  hbYe?: number;
  /**
   * 提存原因
   */
  tcyy: string;
  /**
   * 承办人id
   */
  cbrId : string | number;

  /**
   * 承办人姓名
   */
  cbrXm : string;
}

export interface GzjzTcxQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 提存人ID（当事人）
   */
  tcrId?: string | number;

  /**
   * 提存人姓名
   */
  tcrXm?: string;

  /**
   * 提存类别
   */
  tclb?: string;

  /**
   * 业务类别
   */
  ywlb?: string;

  /**
   * 提存日期
   */
  tcrq?: string;

  /**
   * 提存标的
   */
  tcbd?: string;

  /**
   * 名称/证券名称
   */
  wpMc?: string;

  /**
   * 数量/证券数量
   */
  wpSl?: number;

  /**
   * 备注
   */
  wpBz?: string;

  /**
   * 领取条件
   */
  lqtj?: string;

  /**
   * 状态
   */
  zt?: string;

  /**
   * 币种
   */
  hbLx?: string;

  /**
   * 金额（应缴纳款项）
   */
  hbJe?: number;

  /**
   * 折合人名币
   */
  hbZhrmb?: number;

  /**
   * 开户行
   */
  hbKhh?: string;

  /**
   * 账号
   */
  hbYhzh?: string;

  /**
   * 入账时间
   */
  hbRzsj?: string;

  /**
   * 累计实缴
   */
  hbLjsj?: number;

  /**
   * 累计领取
   */
  hbLjlq?: number;

  /**
   * 余额
   */
  hbYe?: number;

    /**
     * 日期范围参数
     */
    params?: any;
  /**
   * 提存原因
   */
  tcyy: string;

  /**
   * 承办人id
   */
  cbrId : string | number;

  /**
   * 承办人姓名
   */
  cbrXm : string;
}



