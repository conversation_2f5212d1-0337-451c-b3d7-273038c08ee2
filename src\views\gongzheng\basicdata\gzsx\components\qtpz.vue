<template>
  <div class="p-2">
    <el-card>
      <div slot="header" class="clearfix" style="margin-bottom: 20px;">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <span style="line-height: 24px; display: block; width: 160px;">其 他 配 置 设 置</span>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="primary" @click="handleSave"
              v-hasPermi="['basicdata:gzsxQtpz:add']">保存设置</el-button>
          </el-col>
        </el-row>
        <!-- <span class="item-title">其他配置设置</span><div class="item-title"><el-button size="small" type="primary" @click="handleSave" v-loading="buttonLoading">保存设置</el-button></div> -->
      </div>
      <el-form ref="gzsFormRef" :model="form" :rules="rules" label-width="140px" inline>
        <el-form-item label="档案期限" prop="archivePeriod">
          <el-select v-model="form.archivePeriod" placeholder="请选择档案期限" style="width: 200px;" clearable>
            <el-option v-for="dict in gz_dabh_qx" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="案件级别" prop="caseLevel">
          <el-select v-model="form.caseLevel" placeholder="请选择案件级别" style="width: 200px;" clearable>
            <el-option v-for="dict in gz_sxpz_jb" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标的审核" prop="subjectReview">
          <el-radio-group v-model="form.subjectReview">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="质检员" prop="qualityInspector">
          <el-select v-model="form.qualityInspector" placeholder="请选择质检员" style="width: 200px;" clearable>
            <el-option v-for="item in zjy" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="告知模板" prop="notifyTemplateId">
          <el-button size="small" plain type="primary" @click="handleGz">设置告知</el-button>
          <el-button size="small" plain type="primary" @click="handleQcGz">清除告知</el-button>
        </el-form-item>
        <el-form-item label="申请表" prop="applicationFormId">
          <el-button size="small" plain type="primary" @click="handleSqb">设置</el-button>
        </el-form-item>
        <el-form-item label="电子公证书材料类型" prop="dzgzcllx">
          <el-select v-model="form.dzgzcllx" placeholder="请选择电子公证书材料类型" style="width: 200px;" clearable>
            <el-option v-for="dict in gz_dzgzcl" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最低收费比例" prop="minimumFeeRatio">
          <el-input v-model="form.minimumFeeRatio" style="width: 100px;" />%
        </el-form-item>
        <el-form-item label="最高收费比例" prop="maximumFeeRatio">
          <el-input v-model="form.maximumFeeRatio" style="width: 100px;" />%
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup name="Qtpz" lang="ts">
  import { saveGzsxPz, getByCodeGzsxPz } from '@/api/gongzheng/basicdata/gzsxPz/index';
  import { GzsxPzVO, GzsxPzQuery, GzsxPzForm } from '@/api/gongzheng/basicdata/gzsxPz/types';
  import { listTree } from '@/api/gongzheng/basicdata/gzs';
  import { GzsVO } from '@/api/gongzheng/basicdata/gzs/types';
  interface Props {
    gzsxId : string | Number;
    gzsxCode: string;
    gzlbValue : string;
    selectId : [];
  }
  const props = defineProps<Props>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dabh_qx, gz_sxpz_jb, gz_dzgzcl, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_dabh_qx','gz_sxpz_jb','gz_dzgzcl', 'gz_yes_or_no'));
  const { gzy, zjy } = toRefs<any>(proxy?.useRoleUser('gzy', 'zjy'));

  const gzsFormRef = ref<ElFormInstance>();
  const gzsList = ref<GzsVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxPzForm = {
    id: undefined,
    archivePeriod: undefined,
    caseLevel: undefined,
    subjectReview: '0',
    qualityInspector: undefined,
    notifyTemplateId: undefined,
    applicationFormId: undefined,
    dzgzcllx: undefined,
    minimumFeeRatio: undefined,
    maximumFeeRatio: undefined,
    gzsxId: undefined,
    gzsxCode: undefined,
    gzlbValue: undefined,
    selectId: [],
  }
  const data = reactive<PageData<GzsxPzForm, GzsxPzQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzsxId: undefined,
      gzsxCode: undefined,
      gzlbValue: undefined,
      params: {
      }
    },
    rules: {
      archivePeriod: [
        { required: true, message: "请选择档案期限", trigger: "change" }
      ],
      caseLevel: [
        { required: true, message: "请选择案件级别", trigger: "change" }
      ]
    }
  });

  const { form, rules } = toRefs(data);
  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    form.value.gzsxId = props.gzsxId;
    form.value.gzsxCode = props.gzsxCode;
    form.value.gzlbValue = props.gzlbValue;
    gzsFormRef.value?.resetFields();
  }
  const handleSave = () => {
    gzsFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        const res = await saveGzsxPz(form.value).finally(() => buttonLoading.value = false);
        Object.assign(form.value, res.data);
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
      }
    });
  }
  const getDetailByCode = async (gzsxCode, gzlbValue) => {
    reset();
    const res = await getByCodeGzsxPz(gzsxCode, gzlbValue);
    Object.assign(form.value, res.data);
    form.value.qualityInspector = form.value.qualityInspector;
  }
  const init = (gzsxCode, gzlbValue) => {
    getDetailByCode(gzsxCode, gzlbValue);
  }
  const setGzsxIds = (data) => {
    form.value.selectId = data;
  }
  const handleGz = () => {
    proxy?.$modal.msgError("开发中");
  }
  const handleQcGz = () => {
    proxy?.$modal.msgError("开发中");
  }
  const handleSqb = () => {
    proxy?.$modal.msgError("开发中");
  }

  // 显式暴露方法给父组件
  defineExpose({
    init,
    setGzsxIds
  });
  onMounted(() => {
    getDetailByCode(props.gzsxCode, props.gzlbValue);
  });
</script>

<style>
  .item-title{
    line-height: 22px;
  }
</style>
