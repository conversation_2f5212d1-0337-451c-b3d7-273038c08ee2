export interface SmsRecordVO {
  /**
   * 主键ID
   */
  id : string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId : string | number;

  /**
   * 当事人ID
   */
  dsrId : string | number;

  /**
   * 当事人姓名
   */
  dsrName : string;

  /**
   * 当事人联系电话
   */
  dsrPhone : string;

  /**
   * 短信内容
   */
  smsContent : string;

  /**
   * 发送时间(精确到秒)
   */
  sendTime : string;

  /**
   * 发送状态
   */
  sendStatus : number;

  /**
   * 发送反馈时间(精确到秒)
   */
  feedbackTime : string;

  /**
   * 发送反馈结果(失败原因等)
   */
  feedbackResult : string;

  /**
   * 备注信息
   */
  remark : string;
  gzjzBh : string;
}

export interface SmsRecordForm extends BaseEntity {
  /**
   * 主键ID
   */
  id ?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 当事人姓名
   */
  dsrName ?: string;

  /**
   * 当事人联系电话
   */
  dsrPhone ?: string;

  /**
   * 短信内容
   */
  smsContent ?: string;

  /**
   * 发送时间(精确到秒)
   */
  sendTime ?: string;

  /**
   * 发送状态
   */
  sendStatus ?: number;

  /**
   * 发送反馈时间(精确到秒)
   */
  feedbackTime ?: string;

  /**
   * 发送反馈结果(失败原因等)
   */
  feedbackResult ?: string;

  /**
   * 备注信息
   */
  remark ?: string;

}

export interface SmsRecordQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 当事人姓名
   */
  dsrName ?: string;

  /**
   * 当事人联系电话
   */
  dsrPhone ?: string;

  /**
   * 短信内容
   */
  smsContent ?: string;

  /**
   * 发送时间(精确到秒)
   */
  sendTime ?: string;

  /**
   * 发送状态
   */
  sendStatus ?: number;

  /**
   * 发送反馈时间(精确到秒)
   */
  feedbackTime ?: string;

  /**
   * 发送反馈结果(失败原因等)
   */
  feedbackResult ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
  gzjzBh : string;
}
