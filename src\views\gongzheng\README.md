# 公证审查页面 (sl.vue)

这是一个完整的公证审查页面，基于Vue3 + TypeScript + Element Plus技术栈开发。

## 页面结构

### 主要组件

1. **sl.vue** - 主页面组件
2. **BasicInfoForm.vue** - 基本信息表单组件
3. **PartyPersonList.vue** - 当事人列表组件
4. **PersonForm.vue** - 当事人表单组件
5. **NotaryMatters.vue** - 公证事项组件
6. **NotaryMatterForm.vue** - 公证事项表单组件

### 类型定义

**types/sl.ts** - 包含所有相关的TypeScript类型定义

## 功能特性

### 基本信息表单
- 卷宗号、申请日期、受理日期
- 公证类别、公证员、协办人
- 使用地、译文文种、用途
- 法律提醒、外译种
- 紧急受理、费用减免、电子签名、电子公证书等选项

### 当事人管理
- 当事人列表展示
- 新增、编辑、删除当事人
- 读卡功能
- 支持多种证件类型
- 表单验证

### 公证事项管理
- 公证事项列表展示  
- 费用自动计算
- 收费状态管理
- 新增、编辑、删除公证事项

## 使用方法

1. 将整个gongzheng文件夹复制到你的项目中
2. 确保已安装Element Plus
3. 在路由中配置页面路径
4. 根据实际需要调整API接口

## 路由配置示例

```javascript
{
  path: '/gongzheng/sl',
  name: 'NotaryReview',
  component: () => import('@/views/gongzheng/sl.vue'),
  meta: {
    title: '公证审查',
    requiresAuth: true
  }
}
```

## 依赖

- Vue 3
- TypeScript
- Element Plus
- Vue Router (可选)

## 自定义

所有组件都支持自定义样式和功能扩展，可以根据实际业务需求进行调整。

## 注意事项

1. 确保项目中已正确配置Element Plus
2. 根据实际API接口调整数据结构
3. 可以根据需要添加更多表单验证规则
4. 建议根据实际业务流程调整按钮功能 