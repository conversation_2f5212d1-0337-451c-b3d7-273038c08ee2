<template>
  <div class="w-full h-full flex flex-col">
    <div class="flex-1 flex justify-center items-center bg-gray-500 overflow-hidden">
      <video ref="videoRef" class="h-full bg-black object-contain" autoplay playsinline></video>
    </div>
    <div class="h-36px w-full flex gap-10px justify-center items-center bg-gray-200">
      <el-tooltip v-if="props.control.includes('setup')" :content="cameraState.running ? '关闭' : '启动'" placement="top">
        <el-button type="primary" @click="cameraSwitch" size="small">{{ cameraState.running ? '关闭' : '启动' }}</el-button>
      </el-tooltip>
      <el-tooltip v-if="props.control.includes('camera')" content="摄像头" placement="top">
        <el-select v-model="cameraState.deviceId" @change="cameraChange" placeholder="请选择摄像头" size="small" style="width: 120px;">
          <el-option v-for="device in devices" :key="device.deviceId" :label="device.label" :value="device.deviceId" />
        </el-select>
      </el-tooltip>
      <el-tooltip v-if="props.control.includes('size')" content="分辨率" placement="top">
        <el-select v-model="cameraState.size" @change="sizeChange" placeholder="请选择分辨率" size="small" style="width: 80px;">
          <el-option v-for="size in props.sizes" :key="size.key" :label="size.label" :value="size.key" />
        </el-select>
      </el-tooltip>
      <el-tooltip v-if="props.control.includes('ratio')" content="比例" placement="top">
        <el-select v-model="cameraState.ratio" @change="ratioChange" placeholder="请选择图像比例" size="small" style="width: 80px;">
          <el-option v-for="size in props.ratios" :key="size.key" :label="size.label" :value="size.key" />
        </el-select>
      </el-tooltip>
      <el-tooltip v-if="props.control.includes('photo')" content="拍照" placement="top">
        <el-button type="primary" @click="() => takePhoto()" size="small">拍照</el-button>
      </el-tooltip>
      <el-tooltip v-if="props.control.includes('video')" content="录像" placement="top">
        <el-button type="primary" @click="takeVideo" size="small">{{ cameraState.Recording ? '停止' : '录像' }}</el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, getCurrentInstance, onBeforeUnmount, onUnmounted, onDeactivated } from 'vue';

const DEVICE_KEY = 'CACHE_DEVICE_ID';
const SIZE_KEY = 'CACHE_SIZE_ID';
const RATIO_KEY = 'CACHE_RATIO_ID';
const CAMERA_KEY = '_CACHE_CAMERA_KEY';

type ViewSize = {
  key: string;
  label: string;
  width: number;
  height: number;
}

type ViewRatio = {
  key: string;
  label: string;
  value: [number, number];
}

// 分辨率预案
const viewSizes: Array<ViewSize> = [
  {
    key: '480p',
    label: '480p',
    width: 640,
    height: 480,
  },
  {
    key: '720p',
    label: '720p',
    width: 1280,
    height: 720,
  },
  {
    key: '1080p',
    label: '1080p',
    width: 1920,
    height: 1080,
  },
  {
    key: '1440p',
    label: '1440p',
    width: 2560,
    height: 1440,
  },
  {
    key: '4k',
    label: '4K',
    width: 3840,
    height: 2160,
  }
]

const viewRatios: Array<ViewRatio> = [
  {
    key: '1x1',
    label: '1x1',
    value: [1, 1]
  },
  {
    key: '3x4',
    label: '3x4',
    value: [3, 4]
  },
  {
    key: '4x3',
    label: '4x3',
    value: [4, 3]
  },
  {
    key: '16x9',
    label: '16x9',
    value: [16, 9]
  }
]

const loading = ref(false);

interface CameraEmits {
  (e: 'onTakePhoto', data: string): void;  // 拍照事件，data为base64编码的图片数据
  (e: 'onTakeVideo', stream: MediaStream): void;  // 录像事件，返回MediaStream对象
  (e: 'onStopVideo'): void;  // 停止录像事件，关闭MediaStream
  // (e: 'reset'): void; // 重置事件
}

type Control = 'setup' | 'camera' | 'size' | 'ratio' | 'photo' | 'video';

interface CameraProps  {
  autoStart?: boolean; // 是否自动启动相机
  sizes: Array<ViewSize>;  // 自定义分辨率预案
  defaultSize: string; // 默认分辨率key的值
  ratios: Array<ViewRatio>; // 自定义比例预案
  defaultRatio: string; // 默认比例key的值
  control: Array<Control>; // 控件列表 ['setup', 'camera', 'size', 'ratio', 'photo', 'video']
  deviceCache?: boolean; // 是否缓存设备,记录上次使用的设备
}

interface CameraCache {
  deviceId?: string;
  size?: string;
  ratio?: string;
}

const vm = getCurrentInstance();

const props = withDefaults(defineProps<Partial<CameraProps>>(), {
  autoStart: true,
  sizes: () => [
    {
      key: '320p',
      label: '320p',
      width: 320,
      height: 240,
    },
    {
      key: '480p',
      label: '480p',
      width: 640,
      height: 480,
    },
    {
      key: '720p',
      label: '720p',
      width: 1280,
      height: 720,
    },
    // {
    //   key: '1080p',
    //   label: '1080p',
    //   width: 1920,
    //   height: 1080,
    // },
    // {
    //   key: '1440p',
    //   label: '1440p',
    //   width: 2560,
    //   height: 1440,
    // },
    // {
    //   key: '4k',
    //   label: '4K',
    //   width: 3840,
    //   height: 2160,
    // }
  ],
  defaultSize: '480p',
  ratios: () => [
    {
      key: '1x1',
      label: '1x1',
      value: [1, 1]
    },
    {
      key: '3x4',
      label: '3x4',
      value: [3, 4]
    },
    {
      key: '4x3',
      label: '4x3',
      value: [4, 3]
    },
    // {
    //   key: '16x9',
    //   label: '16x9',
    //   value: [16, 9]
    // }
  ],
  defaultRatio: '1x1',
  control: () => ['camera', 'size', 'ratio', 'photo'],
  deviceCache: false,
});

const emits = defineEmits<CameraEmits>();

const stream = ref<MediaStream>();  // 媒体流对象
const videoRef = ref<HTMLVideoElement>(); // 画面显示video元素

const cameraState = reactive({
  running: false, // 相机是否正在运行
  Recording: false, // 是否正在录像
  size: props.defaultSize || '320p',   // 显示分辨率
  ratio: props.defaultRatio || '3x4',  // 显示比例
  deviceId: undefined, // 选择的摄像头ID
  sizes: props.sizes, // 分辨率预案
  ratios: props.ratios, // 比例预案
  selectedSize: undefined as ViewSize | undefined, // 选中的分辨率
  selectedRatio: undefined as ViewRatio | undefined, // 选中的比例
  dpi: 1, // 屏幕dpi
  width: undefined,
  height: undefined
})

const devices = ref([]);

// 获取屏幕dpi
const getScreenDpi = () => {
  const dpi = window.devicePixelRatio || 1;
  return dpi;
}

// 获取显示器分辨率
const getScreenResolution = () => {
  const width = window.screen.width * getScreenDpi();
  const height = window.screen.height * getScreenDpi();
  return { width, height };
}

// 尝试启动相机
const tryStartCamera = async () => {
  loading.value = true;
  try {
    const { width, height } = cameraState
    stream.value = await navigator.mediaDevices.getUserMedia({
      video: {
        deviceId: cameraState.deviceId || undefined,
        width: { ideal: width },
        height: { ideal: height },
      },
      audio: false
    })

    videoRef.value!.srcObject = stream.value;
    videoRef.value!.play()
    cameraState.running = true;
  } catch (err) {
    console.log('拍照功能不支持', err)
  } finally {
    loading.value = false;
  }
}

// 获取摄像头列表
const getCameraList = async () => {
  try {
    const pc_devices = await navigator.mediaDevices.enumerateDevices();
    const videoDevices = pc_devices.filter(d => d.kind === 'videoinput');
    devices.value = videoDevices;
  } catch (err) {
    ElMessage.error('获取摄像头列表失败');
    console.log('获取摄像头列表失败', err)
  }
}

// 重置摄像头参数
const reset = () => {
  streamOff();
  // cameraState.deviceId = undefined;
  cameraState.size = props.defaultSize || '320p';
  cameraState.ratio = props.defaultRatio || '3x4';
  localStorage.removeItem(CAMERA_KEY);
  initParams();
}

// 初始化参数
const initParams = async () => {
  await getCameraList();
  useCache();
  if (!cameraState.deviceId) {
    cameraState.deviceId = devices.value[0]?.deviceId || undefined;
  }
  cameraState.selectedSize = cameraState.sizes.find(size => size.key === cameraState.size) || cameraState.sizes[0] || viewSizes[0];
  cameraState.selectedRatio = cameraState.ratios.find(ratio => ratio.key === cameraState.ratio) || cameraState.ratios[0] || viewRatios[0];
  cameraState.dpi = getScreenDpi();
  const [r0, r1] = cameraState.selectedRatio.value;
  cameraState.height = cameraState.selectedSize.height;
  cameraState.width = cameraState.height * r0 / r1;
}

const initCamera = async () => {
  if(props.autoStart || !props.control.includes('setup')) {
    tryStartCamera();
  }
}

// 关闭媒体流
const streamOff = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => {
      track.stop();
    });
    stream.value = null;
    videoRef.value!.srcObject = null;
  }
  emits('onStopVideo');
  cameraState.running = false;
}

// 相机开关
const cameraSwitch = () => {
  if (cameraState.running) {
    streamOff();
  } else {
    initParams();
    tryStartCamera();
  }
}

// 拍照
const takePhoto = (cb?: (base64: string) => void) => {
  if (!stream.value) {
    console.warn('请先启动相机');
    return;
  }
  try {
    const canvas = document.createElement('canvas');
    const { width, height } = cameraState;
    canvas.width = width;
    canvas.height = height;
    canvas.getContext('2d')!.drawImage(videoRef.value!, 0, 0, canvas.width, canvas.height);
    const base64 = canvas.toDataURL('image/jpeg');
    if(cb && typeof cb === 'function') cb(base64);
    emits('onTakePhoto', base64);
    return base64;
  } catch (err: any) {
    console.error('拍照失败:', err);
    emits('onTakePhoto', null);
  }
}

// 开始录像
const startVideo = () => {
  if (!stream.value) {
    console.warn('请先启动相机');
    return;
  }
  emits('onTakeVideo', stream.value);
  return stream.value;
}

// 停止录像
const stopVideo = () => {
  streamOff();
}

// 切换录像状态
const takeVideo = () => {
  if (cameraState.Recording) {
    return stopVideo();
  } else {
    startVideo();
    return null;
  }
}

// updateCache
const updateCache = () => {
  // const preCache = JSON.parse(localStorage.getItem(CAMERA_KEY) || '{}') as CameraCache;
  const cache: CameraCache = {
    deviceId: cameraState.deviceId || '',
    size: cameraState.size || '',
    ratio: cameraState.ratio || '',
  }
  sessionStorage.setItem(CAMERA_KEY, JSON.stringify(cache));
}

// 使用缓存
const useCache = (): CameraCache => {
  const cache = JSON.parse(sessionStorage.getItem(CAMERA_KEY) || '{}') as CameraCache;
  if (cache.deviceId) {
    cameraState.deviceId = cache.deviceId;
  }
  if (cache.size) {
    cameraState.size = cache.size;
  }
  if (cache.ratio) {
    cameraState.ratio = cache.ratio;
  }
  return cache;
}

// 摄像头切换
const cameraChange = (deviceId: string) => {
  cameraState.deviceId = deviceId;
  streamOff();
  tryStartCamera();
  updateCache();
}

// 切换分辨率
const sizeChange = (sizeKey: string) => {
  const [r0, r1] = cameraState.selectedRatio.value;
  cameraState.size = sizeKey;
  cameraState.selectedSize = cameraState.sizes.find(size => size.key === sizeKey) || cameraState.sizes[0] || viewSizes[0];
  cameraState.height = cameraState.selectedSize.height;
  cameraState.width = cameraState.height * r0 / r1;
  streamOff();
  tryStartCamera();
  updateCache();
}

// 切换比例
const ratioChange = (ratioKey: string) => {
  const height = cameraState.selectedSize.height;
  cameraState.ratio = ratioKey;
  cameraState.selectedRatio = cameraState.ratios.find(ratio => ratio.key === ratioKey) || cameraState.ratios[0] || viewRatios[0];
  const [r0, r1] = cameraState.selectedRatio.value;
  cameraState.width = height * r0 / r1;
  streamOff();
  tryStartCamera();
  updateCache();
}

// 暴露相机实例方法
const cameraIns = (ins) => {
  // ins.takePhoto = takePhoto;
  // ins.stopVideo = stopVideo;
  // ins.cameraSwitch = cameraSwitch;
  // ins.streamOff = streamOff;
  // ins.reset = reset;
  // vm.exposeProxy = ins || {};
}

defineExpose({
  takePhoto,
  stopVideo,
  cameraSwitch,
  streamOff,
  reset
})

watch(() => props.autoStart, () => {
  cameraSwitch();
})

onMounted(() => {
  initParams();
  initCamera();
})

onBeforeUnmount(() => {
  streamOff();
});

onDeactivated(() => {
  streamOff();
});
</script>
