export interface GzjzFqVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId: string | number;

  /**
   * 债务人（逗号分隔）
   */
  zwrIds: string;

  /**
   * 债权人（逗号分隔）
   */
  zqrIds: string;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds: string;

  /**
   * 担保人（逗号分隔）
   */
  dbrIds: string;

  /**
   * 出借人（字典：gz_cjr_lx）
   */
  cjrLx: string;

  /**
   * 担保方式（字典：gz_dbfs，逗号分隔）
   */
  dbfs: string;

  /**
   * 合同金额币种（字典：gz_tc_bz）
   */
  htJebz: string;

  /**
   * 合同（协议）金额
   */
  htJe: string;

  /**
   * 合同利率类型（字典：gz_ht_lllx）
   */
  htLllx: string;

  /**
   * 借款合同利率
   */
  htJkll: string;

  /**
   * 借款期限起
   */
  jkqxStart: string;

  /**
   * 借款期限止
   */
  jkqxEnd: string;

  /**
   * 借款编号
   */
  jkbh: string;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzFqForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 债务人（逗号分隔）
   */
  zwrIds?: string | number;

  /**
   * 债权人（逗号分隔）
   */
  zqrIds?: string | number;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string | number;

  /**
   * 担保人（逗号分隔）
   */
  dbrIds?: string | number;

  /**
   * 出借人（字典：gz_cjr_lx）
   */
  cjrLx?: string;

  /**
   * 担保方式（字典：gz_dbfs，逗号分隔）
   */
  dbfs?: string;

  /**
   * 合同金额币种（字典：gz_tc_bz）
   */
  htJebz?: string;

  /**
   * 合同（协议）金额
   */
  htJe?: string;

  /**
   * 合同利率类型（字典：gz_ht_lllx）
   */
  htLllx?: string;

  /**
   * 借款合同利率
   */
  htJkll?: string;

  /**
   * 借款期限起
   */
  jkqxStart?: string;

  /**
   * 借款期限止
   */
  jkqxEnd?: string;

  /**
   * 借款编号
   */
  jkbh?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzFqFormEdit extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 债务人（逗号分隔）
   */
  zwrIds?: string[] | number[];

  /**
   * 债权人（逗号分隔）
   */
  zqrIds?: string[] | number[];

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string[] | number[];

  /**
   * 担保人（逗号分隔）
   */
  dbrIds?: string[] | number[];

  /**
   * 出借人（字典：gz_cjr_lx）
   */
  cjrLx?: string;

  /**
   * 担保方式（字典：gz_dbfs，逗号分隔）
   */
  dbfs?: string[];

  /**
   * 合同金额币种（字典：gz_tc_bz）
   */
  htJebz?: string;

  /**
   * 合同（协议）金额
   */
  htJe?: string;

  /**
   * 合同利率类型（字典：gz_ht_lllx）
   */
  htLllx?: string;

  /**
   * 借款合同利率
   */
  htJkll?: string;

  /**
   * 借款期限起
   */
  jkqxStart?: string;

  /**
   * 借款期限止
   */
  jkqxEnd?: string;

  /**
   * 借款编号
   */
  jkbh?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzFqQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 债务人（逗号分隔）
   */
  zwrIds?: string | number;

  /**
   * 债权人（逗号分隔）
   */
  zqrIds?: string | number;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string | number;

  /**
   * 担保人（逗号分隔）
   */
  dbrIds?: string | number;

  /**
   * 出借人（字典：gz_cjr_lx）
   */
  cjrLx?: string;

  /**
   * 担保方式（字典：gz_dbfs，逗号分隔）
   */
  dbfs?: string;

  /**
   * 合同金额币种（字典：gz_tc_bz）
   */
  htJebz?: string;

  /**
   * 合同（协议）金额
   */
  htJe?: number;

  /**
   * 合同利率类型（字典：gz_ht_lllx）
   */
  htLllx?: string;

  /**
   * 借款合同利率
   */
  htJkll?: number;

  /**
   * 借款期限起
   */
  jkqxStart?: string;

  /**
   * 借款期限止
   */
  jkqxEnd?: string;

  /**
   * 借款编号
   */
  jkbh?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



