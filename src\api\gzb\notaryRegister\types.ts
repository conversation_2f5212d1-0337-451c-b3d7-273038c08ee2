export interface NotaryRegisterVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 券案号
   */
  caseNumber: string;

  /**
   * 当事人姓名
   */
  applicantName: string;

  /**
   * 公证事项
   */
  notaryItem: string;

  /**
   * 公证员姓名
   */
  notaryPersonName: string;

  /**
   * 审批人
   */
  auditPerson: string;

  /**
   * 受理日期（毫秒时间戳）
   */
  acceptDate: number;

  /**
   * 出证日期（毫秒时间戳）
   */
  issueDate: number;

  /**
   * 公证类型
   */
  notarizationType: string;

  /**
   * 公证上报状态 (reported: 已上报, unreported: 未上报)
   */
  reportStatus: string;

  /**
   * 省外上报状态 (reported: 已上报, unreported: 未上报)
   */
  provinceReportStatus: string;

  /**
   * 公证书编号
   */
  notaryNum: string;

  /**
   * 实收金额
   */
  itemCharge: number;

  /**
   * 是否完成结案 (completed: 已完成, incomplete: 未完成)
   */
  isCompleted: string;

  /**
   * 证件检验信息 (verified: 已验证, unverified: 未验证)
   */
  certVerifyInfo: string;

  /**
   * 备注
   */
  remark: string;

}

export interface NotaryRegisterForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 券案号
   */
  caseNumber?: string;

  /**
   * 当事人姓名
   */
  applicantName?: string;

  /**
   * 公证事项
   */
  notaryItem?: string;

  /**
   * 公证员姓名
   */
  notaryPersonName?: string;

  /**
   * 审批人
   */
  auditPerson?: string;

  /**
   * 受理日期（毫秒时间戳）
   */
  acceptDate?: number;

  /**
   * 出证日期（毫秒时间戳）
   */
  issueDate?: number;

  /**
   * 公证类型
   */
  notarizationType?: string;

  /**
   * 公证上报状态
   */
  reportStatus?: string;

  /**
   * 省外上报状态
   */
  provinceReportStatus?: string;

  /**
   * 公证书编号
   */
  notaryNum?: string;

  /**
   * 实收金额
   */
  itemCharge?: number;

  /**
   * 是否完成结案
   */
  isCompleted?: string;

  /**
   * 证件检验信息
   */
  certVerifyInfo?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface NotaryRegisterQuery extends PageQuery {
  /**
   * 券案号
   */
  caseNumber?: string;

  /**
   * 公证类型
   */
  notarizationType?: string;

  /**
   * 公证事项
   */
  notaryItem?: string;

  /**
   * 公证书编号类型 (numbered: 已编号, unnumbered: 未编号)
   */
  notaryNumType?: string;

  /**
   * 公证书编号状态 (normal: 正常, abnormal: 异常)
   */
  notaryNumStatus?: string;

  /**
   * 是否完成结案 (completed: 已完成, incomplete: 未完成)
   */
  isCompleted?: string;

  /**
   * 证件检验信息 (verified: 已验证, unverified: 未验证)
   */
  certVerifyInfo?: string;

  /**
   * 公证上报状态 (reported: 已上报, unreported: 未上报)
   */
  reportStatus?: string;

  /**
   * 省外上报状态 (reported: 已上报, unreported: 未上报)
   */
  provinceReportStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 统计信息VO
 */
export interface StatisticsVO {
  /**
   * 总数
   */
  total: number;

  /**
   * 已验证数量
   */
  verified: number;

  /**
   * 正常上报数量
   */
  normalReported: number;

  /**
   * 异常上报数量
   */
  abnormalReported: number;

  /**
   * 未验证数量
   */
  unverified: number;

  /**
   * 已完成数量
   */
  completed: number;

  /**
   * 未完成数量
   */
  incomplete: number;

  /**
   * 已上传数量
   */
  uploaded: number;

  /**
   * 总费用
   */
  totalFee: number;
}



