<template>
  <div class="jz-detail-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" type="border-card">
      <el-tab-pane label="基本信息" name="first">
        <!-- 基本信息区域 -->
        <div class="info-section" v-loading="loading">
          <div class="info-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">卷宗号：</span>
                  <span class="value">{{ basicInfo.jzbh || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">公证员：</span>
                  <span class="value">{{ basicInfo.gzyxm || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">助理：</span>
                  <span class="value">{{ basicInfo.zlxm || '-' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">公证类别：</span>
                  <span class="value">{{ getPublicDictLabel('gz_gzlb', basicInfo.lb) || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">紧急度：</span>
                  <span class="value">{{ getUrgencyLabel(basicInfo.jjd) || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否密卷：</span>
                  <span class="value">{{ getPublicDictLabel('gz_sfmj', basicInfo.sfmj) || '-' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">使用地：</span>
                  <span class="value">{{ dictFindLabel(basicInfo.syd) || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">译文文种：</span>
                  <span class="value">{{ getPublicDictLabel('gz_yw_wz', basicInfo.ywwz) || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">用途：</span>
                  <span class="value">{{ getPublicDictLabel('gz_yt', basicInfo.yt) || '-' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">申请日期：</span>
                  <span class="value">{{ basicInfo.sqsj || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">受理日期：</span>
                  <span class="value">{{ basicInfo.slrq || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否认证：</span>
                  <span class="value">{{ getPublicDictLabel('gz_rz_zt', basicInfo.rz) || '-' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">流程状态：</span>
                  <span class="value">{{ getPublicDictLabel('gz_sl_lczt', basicInfo.lczt) || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">外部订单号：</span>
                  <span class="value">{{ basicInfo.wbddh || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">协办人：</span>
                  <span class="value">{{ basicInfo.xbrxm || '-' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否零接触：</span>
                  <span class="value">{{ basicInfo.sfljc === '1' ? '是' : '否' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否电子签名：</span>
                  <span class="value">{{ basicInfo.sfdzqm === '1' ? '是' : '否' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否电子公证书：</span>
                  <span class="value">{{ basicInfo.sfdzgzs === '1' ? '是' : '否' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">电票领取电话：</span>
                  <span class="value">{{ basicInfo.dplqdh || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">是否外译中：</span>
                  <span class="value">{{ basicInfo.sfwyz === '1' ? '是' : '否' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">法律援助：</span>
                  <span class="value">{{ getPublicDictLabel('gz_flyz', basicInfo.flxz) || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <!-- 基本信息区域结束 -->
        <!-- 当事人列表 -->
        <div class="section-container">
          <div class="section-header">当事人列表</div>
          <div class="section-content">
            <el-table :data="partiesList" border stripe v-loading="partiesLoading">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="js" label="角色" align="center" width="100" />
              <el-table-column label="当事人名称" align="center">
                <template #default="{ row }">
                  {{ formatPartyName(row) }}
                </template>
              </el-table-column>
              <el-table-column label="当事人类型" align="center" width="100">
                <template #default="{ row }">
                  {{ formatPartyType(row) }}
                </template>
              </el-table-column>
              <el-table-column label="证件类型" align="center" width="120">
                <template #default="{ row }">
                  {{ formatIdType(row) }}
                </template>
              </el-table-column>
              <el-table-column label="证件号码" align="center" width="180">
                <template #default="{ row }">
                  {{ formatIdNumber(row) }}
                </template>
              </el-table-column>
              <el-table-column label="住址" align="center">
                <template #default="{ row }">
                  {{ formatAddress(row) }}
                </template>
              </el-table-column>
              <el-table-column label="电话号码" align="center" width="120">
                <template #default="{ row }">
                  {{ formatPhone(row) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 公证事项 -->
        <div class="section-container">
          <div class="section-header">公证事项</div>
          <div class="section-content">
            <el-table :data="notaryMattersList" border stripe>
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="notaryMatter" label="公证事项" align="center" />
              <el-table-column prop="relatedParty" label="关系人" align="center" />
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="notaryDoc" label="公证书" align="center" />
              <el-table-column prop="translation" label="译文" align="center" />
              <el-table-column prop="combinedDoc" label="合成公证书" align="center" />
              <el-table-column prop="operation" label="操作" align="center" />
              <el-table-column prop="copies" label="份数" align="center" width="80" />
              <el-table-column prop="hasBackup" label="备案" align="center" width="80" />
              <el-table-column prop="backupInfo" label="备案信息" align="center">
                <template #default="scope">
                  <el-button type="primary" link v-if="scope.row.hasBackup === '否'">
                    查看
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 收费明细表 -->
        <div class="section-container">
          <div class="section-header">收费明细表</div>
          <div class="section-content">
            <el-table :data="feeList" border stripe>
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="feeType" label="费用类型" align="center" />
              <el-table-column prop="feeStandard" label="收费标准" align="center" />
              <el-table-column prop="notaryType" label="公证事项" align="center" />
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="notaryDoc" label="证件" align="center" />
              <el-table-column prop="partyType" label="当事人" align="center" />
              <el-table-column prop="calculatedFee" label="计价方式" align="center" />
              <el-table-column prop="receivableFee" label="收费金额" align="center" />
              <el-table-column prop="discount" label="优惠" align="center" />
              <el-table-column prop="actualFee" label="实收" align="center" />
              <el-table-column prop="otherFee" label="减免" align="center" />
              <el-table-column prop="totalFee" label="收费状态" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 收费及开票记录 -->
        <div class="section-container">
          <div class="section-header">收费及开票记录</div>
          <div class="section-content">
            <el-table :data="paymentRecords" border stripe>
              <el-table-column prop="paymentType" label="交易凭证" align="center" />
              <el-table-column prop="paymentNo" label="收据编号" align="center" />
              <el-table-column prop="payer" label="当事人" align="center" />
              <el-table-column prop="amount" label="金额" align="center" />
              <el-table-column prop="paymentMethod" label="收支" align="center" />
              <el-table-column prop="bankAccount" label="开票点" align="center" />
              <el-table-column prop="paymentDate" label="开票时间" align="center" />
              <el-table-column prop="operation" label="操作" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 涉外使用水印纸清单 -->
        <div class="section-container">
          <div class="section-header">涉外使用水印纸清单</div>
          <div class="section-content">
            <el-table :data="watermarkList" border stripe>
              <el-table-column prop="watermarkNo" label="水印纸编号" align="center" />
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="useDate" label="使用日期" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 制作副本记录 -->
        <div class="section-container">
          <div class="section-header">制作副本记录</div>
          <div class="section-content">
            <el-table :data="copyRecords" border stripe>
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="notaryType" label="公证事项" align="center" />
              <el-table-column prop="useDate" label="使用日期" align="center" />
              <el-table-column prop="copies" label="份数" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 制作副本记录 -->
        <div class="section-container">
          <div class="section-header">制作副本记录</div>
          <div class="section-content">
            <el-table :data="duplicateRecords" border stripe>
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="notaryType" label="公证事项" align="center" />
              <el-table-column prop="useDate" label="使用日期" align="center" />
              <el-table-column prop="copies" label="份数" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 补证记录 -->
        <div class="section-container">
          <div class="section-header">补证记录</div>
          <div class="section-content">
            <el-table :data="supplementRecords" border stripe>
              <el-table-column prop="notaryNo" label="公证书编号" align="center" />
              <el-table-column prop="notaryType" label="公证事项" align="center" />
              <el-table-column prop="useDate" label="使用日期" align="center" />
              <el-table-column prop="copies" label="份数" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 送达记录 -->
        <div class="section-container">
          <div class="section-header">送达记录</div>
          <div class="section-content">
            <el-table :data="deliveryRecords" border stripe>
              <el-table-column prop="method" label="通知方式" align="center" />
              <el-table-column prop="mailbox" label="送达邮箱" align="center" />
              <el-table-column prop="code" label="送达号码" align="center" />
              <el-table-column prop="date" label="送达日期" align="center" />
              <el-table-column prop="result" label="发送结果" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 评价信息 -->
        <div class="section-container">
          <div class="section-header">评价信息</div>
          <div class="section-content">
            <el-table :data="evaluationInfo" border stripe>
              <el-table-column prop="item" label="标题" align="center" />
              <el-table-column prop="score" label="分值" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 文书 -->
        <div class="section-container">
          <div class="section-header">文书</div>
          <div class="section-content">
            <el-table :data="documentsList" border stripe>
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column prop="name" label="文书名称" align="center" />
              <el-table-column prop="translation" label="译文" align="center" />
              <el-table-column prop="status" label="状态" align="center" />
              <el-table-column prop="operation" label="操作" align="center" />
            </el-table>
          </div>
        </div>

        <!-- 其他文档 -->
        <div class="section-container">
          <div class="section-header">其他文档</div>
          <div class="section-content">
            <el-table :data="otherDocuments" border stripe>
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="name" label="文书名称" align="center">
                <template #default="scope">
                  <el-button type="primary" link>
                    {{ scope.row.name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" align="center" />
              <el-table-column prop="operation" label="操作" align="center" />
            </el-table>
          </div>
        </div>

      </el-tab-pane>
      <el-tab-pane label="发证信息" name="second"></el-tab-pane>
      <el-tab-pane label="日志" name="rz">
        <CzrzList v-if="activeName=='rz'"></CzrzList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, provide, watch, onMounted, getCurrentInstance, toRefs } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { getGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx'
import type { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr'
import type { GzjzDsrVO, GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
import CzrzList from '@/views/gongzheng/gongzheng/components/czrz_list.vue'
import { listAreaname } from '@/api/gongzheng/basicdata/areaname'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { gz_gzlb, gz_sfmj, gz_sl_syd, gz_yw_wz, gz_yt, gz_rz_zt, gz_sl_lczt, gz_flyz, gz_gr_zjlx, gz_jg_zjlx } = toRefs<any>(
  proxy?.useDict('gz_gzlb', 'gz_sfmj', 'gz_sl_syd', 'gz_yw_wz', 'gz_yt', 'gz_rz_zt', 'gz_sl_lczt', 'gz_flyz', 'gz_gr_zjlx', 'gz_jg_zjlx')
)

// 获取当前查看的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

// 向子组件提供当前记录ID
provide('currentRecordId', currentRecordId)

const activeName = ref('first')
const loading = ref(false)
const partiesLoading = ref(false)

const handleClick = () => {}

// 基本信息数据 - 使用真实的API数据结构
const basicInfo = ref<GzjzJbxxVO>({} as GzjzJbxxVO)

// 当事人列表数据 - 使用真实的API数据
const partiesList = ref<GzjzDsrVO[]>([])

// 使用地区域数据
const areas = ref<any[]>([])

// 获取当事人列表
const getPartiesList = async (gzjzId: string | number) => {
  if (!gzjzId) return

  partiesLoading.value = true
  try {
    const query: GzjzDsrQuery = {
      gzjzId: gzjzId,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzjzDsrByGzjz(query)
    if (res.code === 200) {
      partiesList.value = res.rows || []
    } else {
      ElMessage.error('获取当事人列表失败：' + (res.msg || '未知错误'))
      partiesList.value = []
    }
  } catch (error: any) {
    console.error('获取当事人列表失败:', error)
    ElMessage.error('获取当事人列表失败: ' + (error?.message || '未知错误'))
    partiesList.value = []
  } finally {
    partiesLoading.value = false
  }
}

// 格式化当事人姓名 - 兼容多种字段
const formatPartyName = (row: GzjzDsrVO) => {
  // 优先从对象中获取
  if (row.dsrLx === '1' && row.zrrBo?.xm) {
    return row.zrrBo.xm
  }
  if (row.dsrLx === '2' && row.frhzzBo?.dwmc) {
    return row.frhzzBo.dwmc
  }

  // 兼容直接字段
  return row.name || row.xm || '未知'
}

// 格式化当事人类型
const formatPartyType = (row: GzjzDsrVO) => {
  if (row.dsrLx === '1') return '个人'
  if (row.dsrLx === '2') return '单位'
  return row.type || '未知'
}

// 格式化证件类型
const formatIdType = (row: GzjzDsrVO) => {
  let idType = ''

  // 从对象中获取
  if (row.dsrLx === '1' && row.zrrBo?.zjlx) {
    idType = row.zrrBo.zjlx
  } else if (row.dsrLx === '2' && row.frhzzBo?.zjlx) {
    idType = row.frhzzBo.zjlx
  } else {
    // 兼容直接字段
    idType = row.certificateType || row.idType?.toString() || ''
  }

  // 使用字典转换
  if (idType) {
    if (row.dsrLx === '1') {
      const dictItem = gz_gr_zjlx.value?.find((item: any) => item.value === idType)
      return dictItem ? dictItem.label : idType
    } else if (row.dsrLx === '2') {
      const dictItem = gz_jg_zjlx.value?.find((item: any) => item.value === idType)
      return dictItem ? dictItem.label : idType
    }
  }

  return idType || '-'
}

// 格式化证件号码
const formatIdNumber = (row: GzjzDsrVO) => {
  // 从对象中获取
  if (row.dsrLx === '1' && row.zrrBo?.zjhm) {
    return row.zrrBo.zjhm
  }
  if (row.dsrLx === '2' && row.frhzzBo?.zjhm) {
    return row.frhzzBo.zjhm
  }

  // 兼容直接字段
  return row.certificateNo || row.idNumber || row.zjhm || '-'
}

// 格式化住址
const formatAddress = (row: GzjzDsrVO) => {
  // 从对象中获取
  if (row.dsrLx === '1' && row.zrrBo?.zz) {
    return row.zrrBo.zz
  }
  if (row.dsrLx === '2' && row.frhzzBo?.dwszd) {
    return row.frhzzBo.dwszd
  }

  // 兼容直接字段
  return row.address || row.zz || '-'
}

// 格式化联系电话
const formatPhone = (row: GzjzDsrVO) => {
  // 从对象中获取
  if (row.dsrLx === '1' && row.zrrBo?.lxdh) {
    return row.zrrBo.lxdh
  }
  if (row.dsrLx === '2' && row.frhzzBo?.lxdh) {
    return row.frhzzBo.lxdh
  }

  // 兼容直接字段
  return row.contactTel || row.phone || '-'
}

// 获取字典标签的辅助函数
const getPublicDictLabel = (dictType: string, value: string | number | undefined) => {
  if (!value) return '-'

  let dictData: any[] = []
  switch (dictType) {
    case 'gz_gzlb':
      dictData = gz_gzlb.value || []
      break
    case 'gz_sfmj':
      dictData = gz_sfmj.value || []
      break
    case 'gz_sl_syd':
      dictData = gz_sl_syd.value || []
      break
    case 'gz_yw_wz':
      dictData = gz_yw_wz.value || []
      break
    case 'gz_yt':
      dictData = gz_yt.value || []
      break
    case 'gz_rz_zt':
      dictData = gz_rz_zt.value || []
      break
    case 'gz_sl_lczt':
      dictData = gz_sl_lczt.value || []
      break
    case 'gz_flyz':
      dictData = gz_flyz.value || []
      break
    default:
      return value.toString()
  }

  const item = dictData.find(dict => dict.value === value.toString())
  return item ? item.label : value.toString()
}

// 获取紧急度标签
const getUrgencyLabel = (value: string | undefined) => {
  if (!value) return '-'
  const urgencyMap: Record<string, string> = {
    '1': '普通',
    '2': '急',
    '3': '特急'
  }
  return urgencyMap[value] || value
}

// 获取基本信息详情
const getBasicInfo = async (id: string | number) => {
  if (!id) return

  loading.value = true
  try {
    const res = await getGzjzJbxx(id)
    if (res.code === 200) {
      basicInfo.value = res.data

      // 获取基本信息成功后，获取当事人列表
      await getPartiesList(id)
    } else {
      ElMessage.error(res.msg || '获取基本信息失败')
    }
  } catch (error: any) {
    console.error('获取基本信息失败:', error)
    ElMessage.error('获取基本信息失败: ' + (error?.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 监听当前记录ID变化
watch(currentRecordId, (newId) => {
  if (newId) {
    getBasicInfo(newId)
  } else {
    // 清空数据
    basicInfo.value = {} as GzjzJbxxVO
    partiesList.value = []
  }
}, { immediate: true })

// 公证事项列表数据
const notaryMattersList = ref([
  {
    id: '1',
    notaryMatter: '出生',
    relatedParty: '',
    notaryNo: '(2025) 桂东博证字第1251号',
    notaryDoc: '',
    translation: '',
    combinedDoc: '',
    operation: '',
    copies: '2',
    hasBackup: '否',
    backupInfo: ''
  }
])

// 收费明细表数据
const feeList = ref([
  {
    id: '1',
    feeType: '公证费',
    feeStandard: '京',
    notaryType: '出生',
    notaryNo: '(2025) 桂东博证字第1251号',
    notaryDoc: '证件',
    partyType: '个人',
    calculatedFee: '',
    receivableFee: '45.00',
    discount: '0.00',
    actualFee: '45.00',
    otherFee: '0.00',
    totalFee: '已完成'
  },
  {
    id: '2',
    feeType: '证明费',
    feeStandard: '京',
    notaryType: '出生',
    notaryNo: '(2025) 桂东博证字第1251号',
    notaryDoc: '证件',
    partyType: '',
    calculatedFee: '',
    receivableFee: '18.00',
    discount: '0.00',
    actualFee: '18.00',
    otherFee: '0.00',
    totalFee: '已完成'
  },
  {
    id: '3',
    feeType: '合计',
    feeStandard: '',
    notaryType: '',
    notaryNo: '',
    notaryDoc: '',
    partyType: '',
    calculatedFee: '',
    receivableFee: '63.00',
    discount: '0.00',
    actualFee: '63.00',
    otherFee: '0.00',
    totalFee: '63.00'
  }
])

// 收费及开票记录数据
const paymentRecords = ref([
  // 这里是空数据，因为图片中没有显示具体的收费记录
])

// 水印纸使用清单数据
const watermarkList = ref([
  // 这里是空数据，因为图片中没有显示具体的水印纸记录
])

// 制作副本记录数据
const copyRecords = ref([
  // 这里是空数据，因为图片中没有显示具体的副本记录
])

// 制作副本记录数据（重复的表格）
const duplicateRecords = ref([
  // 这里是空数据，因为图片中没有显示具体的副本记录
])

// 补证记录数据
const supplementRecords = ref([
  // 这里是空数据，因为图片中没有显示具体的补证记录
])

// 送达记录数据
const deliveryRecords = ref([
  // 这里是空数据，因为图片中没有显示具体的送达记录
])

// 评价信息数据
const evaluationInfo = ref([
  // 这里是空数据，因为图片中没有显示具体的评价信息
])

// 文书列表数据
const documentsList = ref([
  // 这里是空数据，因为图片中没有显示具体的文书记录
])

// 其他文档数据
const otherDocuments = ref([
  {
    id: '1',
    name: '01-公证申请表',
    status: '-',
    operation: '-'
  },
  {
    id: '2',
    name: '东博告知书',
    status: '-',
    operation: '-'
  },
  {
    id: '3',
    name: '出生 笔录.docx',
    status: '-',
    operation: '-'
  },
  {
    id: '4',
    name: '个人信息表 侯添盛',
    status: '-',
    operation: '-'
  }
])

// 关闭页面
const handleClose = () => {
  ElMessage.info('关闭详情页')
  // 实际关闭逻辑，可能需要调用父组件的方法或使用路由导航
}

const getAreaName = async () => {
  try {
    const params = {
      pageSize: 300,
      pageNum: 1
    }
    const res = await listAreaname(params);
    if (res.code === 200) {
      areas.value = res.rows;
    }
  } catch (err: any) {
    console.error('获取地区名称失败:', err);
  }
}

const dictFindLabel = (code: string) => {
  const res = areas.value.find(item => item.numberCode === code);
  console.log('dictFindLabel', res, code)
  return res ? res.areaName : code;
}

onBeforeMount(() => {
  getAreaName();
})

onMounted(() => {
  // 组件挂载时如果有记录ID就立即获取数据
  if (currentRecordId.value) {
    getBasicInfo(currentRecordId.value)
  }
})
</script>

<style scoped>
.jz-detail-container {
  /* padding: 20px; */
  /* background: #f5f7fa; */
  height: 100%;
  overflow: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.close-icon {
  font-size: 20px;
  cursor: pointer;
}

.tab-container {
  margin-bottom: 20px;
}

.tab-header {
  display: flex;
  border-bottom: 2px solid #ebeef5;
}

.tab-item {
  padding: 10px 20px;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  color: #409eff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409eff;
}

.info-section {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-item {
  margin-bottom: 15px;
  display: flex;
}

.label {
  color: #606266;
  min-width: 120px;
  font-weight: 500;
}

.value {
  color: #303133;
  flex: 1;
}

.section-container {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 15px;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.section-content {
  padding: 0;
}
</style>
