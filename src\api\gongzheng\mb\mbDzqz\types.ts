export interface MbDzqzVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 模板基础信息ID
   */
  mbId: string | number;

  /**
   * 签署方
   */
  qsf: string;

  /**
   * 签章区域
   */
  qzQy: string;

  /**
   * 模板文档ID
   */
  mbWdId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface MbDzqzForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 签署方
   */
  qsf?: string;

  /**
   * 签章区域
   */
  qzQy?: string;

  /**
   * 模板文档ID
   */
  mbWdId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface MbDzqzQuery extends PageQuery {

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 签署方
   */
  qsf?: string;

  /**
   * 签章区域
   */
  qzQy?: string;

  /**
   * 模板文档ID
   */
  mbWdId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



