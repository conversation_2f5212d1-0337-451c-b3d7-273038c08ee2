<template>
  <gz-dialog v-model="visible" :title="title" @closed="closed" append-to-body>
    <ZjclList ref="zjclListRef" :gzjz-id="gzjzId" class="mb-10px" />
    <el-form-item label="备注">
      <el-input v-model="remark" type="textarea" />
    </el-form-item>

    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirm" type="primary" :loading="addLoading" :disabled="addLoading">申请调查</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ZjclList from './ZjclList.vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { addGzjzDchsjl } from '@/api/gongzheng/bzfz/gzjzDchsjl';
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/ruoyi';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '新增调查'
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const zjclListRef = ref(null)

const addLoading = ref(false)

const remark = ref('')

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const comfirm = async () => {
  const list = zjclListRef.value.getSelectionRows() || [];
  if(list.length < 1) {
    ElMessage.error('请选择需要申请调查的信息');
    return;
  }

  try {
    addLoading.value = true;

    let emtyZjcl = []

    const zjclList = list.map((item: any) => {
      if(item.zmclxxMxList.length === 0) {
        emtyZjcl.push(item)
      }
      return {
        nameId: item.id,
        name: item.zmmc,
        remarks: item.remarks,
      }
    })

    if(emtyZjcl.length > 0) {
      ElMessage.warning(`${emtyZjcl[0].zmmc}证据材料为空不能发起审查`)
      return;
    }

    const { userId } = useUserStore()

    let params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      jzbh: curGzjz.value.jzbh || '',
      sqrId: userId,
      zjclList: JSON.stringify(zjclList),
      sqdcrq: formatDate(new Date(), 'YYYY-MM-DD'),
      remark: remark.value
    }
    const res = await addGzjzDchsjl(params);
    if(res.code === 200) {
      ElMessage.success('提交成功');
      emit('success')
      close();
    }
  } catch(err: any) {
    ElMessage.error('提交失败');
    console.error('提交申请调查失败', err);
  } finally {
    addLoading.value = false;
  }
}

const close = () => {
  visible.value = false
}

const closed = () => {}

</script>
