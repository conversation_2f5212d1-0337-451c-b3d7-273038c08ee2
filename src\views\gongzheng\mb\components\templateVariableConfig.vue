<!-- 模板变量配置组件 -->
<template>
  <div class="template-variable-config">
    <!-- 模板选择与变量列表 -->
    <VariableList :blId="props.blId" @select-template="onSelectTemplate" @select-variable="onSelectVariable" @refresh="onRefresh"
      ref="variableListRef" />
    <!-- 数据源配置弹窗 -->
    <DataSourceConfigDialog :blId="props.blId" v-if="showDataSourceDialog" :variable="currentVariable" :visible.sync="showDataSourceDialog"
      @refresh="onRefresh" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import VariableList from './VariableList.vue'
  import DataSourceConfigDialog from './DataSourceConfigDialog.vue'
  
  interface Props {
    blId : string | number
  }
  
  const props = defineProps<Props>();
  const showDataSourceDialog = ref(false)
  const currentVariable = ref(null)
  const variableListRef = ref()

  function onSelectTemplate(template: any) {
    // 可扩展：处理模板选择
    console.log('Selected template:', template)
  }
  
  function onSelectVariable(variable: any) {
    currentVariable.value = variable
    showDataSourceDialog.value = true
  }
  
  function onRefresh() {
    variableListRef.value?.refresh()
  }
</script>

<style scoped>
  .template-variable-config {
    padding: 20px;
  }
</style>
