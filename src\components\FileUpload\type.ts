export interface UploadProps {
  modelValue: boolean;
  title?: string;
  accept?: string;
  limit?: number;
  disabledUpload?: boolean;
  multiple?: boolean;
  maxSize?: number;
}

export interface UploadResult {
  url?: string;
  fileName?: string;
  ossId?: string;
  path?: string;
  [key: string]: any;
}

export interface UploadStatus {
  // 总数量
  total?: number;
  // 已成功上传数量
  count?: number;
  // 上传失败数量
  failCount?: number;
  // 当前文件上传状态
  status?: 'success' | 'error' | 'uploading';
  // 当前文件上传结果
  result?: UploadResult;
}
