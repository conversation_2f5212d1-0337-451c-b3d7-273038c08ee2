<template>
  <div>
    <slot name="trigger">
      <el-button v-if="props.printBtn" type="primary" style="margin-bottom: 10px;">打印</el-button>
    </slot>

    <div ref="printContainer" class="print-container">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { ElMessage } from 'element-plus';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  interface PrintOptions {
    showHeaderFooter ?: boolean;
    margin ?: string;
    printBtn ?: boolean
  }
  const props = defineProps<PrintOptions>();
  const printContainer = ref<HTMLElement | null>(null);

  // 打印方法
  const print = (options : PrintOptions = {}) => {
    if (!printContainer.value) {
      ElMessage.error('未找到打印内容');
      return;
    }

    // 保存当前样式
    const originalStyles = document.styleSheets[0]?.cssRules || [];

    try {
      // 创建打印样式
      const styleElement = document.createElement('style');
      styleElement.setAttribute('type', 'text/css');
      styleElement.innerHTML = `
      @media print {
        body > *:not(.print-container) { display: none; }
        .print-container { width: 100%; margin: 0; padding: 0; }
        ${options.margin ? `@page { margin: ${options.margin}; }` : ''}
      }
    `;
      document.head.appendChild(styleElement);

      // 禁用页眉页脚（IE兼容）
      if (!options.showHeaderFooter) {
        (window as any).printWithoutHeaderFooter = true;
      }

      // 调用打印
      window.print();

    } catch (error) {
      console.error('打印出错:', error);
      ElMessage.error('打印失败，请重试');
    } finally {
      // 恢复样式
      document.head.removeChild(document.head.querySelector('style[type="text/css"]')!);
    }
  };

  // 暴露打印方法
  defineExpose({
    print
  });

  // 事件监听
  onMounted(() => {
    window.addEventListener('beforeprint', () => console.log('打印前'));
    window.addEventListener('afterprint', () => console.log('打印后'));
  });

  onUnmounted(() => {
    window.removeEventListener('beforeprint', () => console.log('打印前'));
    window.removeEventListener('afterprint', () => console.log('打印后'));
  });
</script>

<style scoped>
  .print-container {
    min-height: 100px;
    padding: 20px;
    border: 1px solid #e5e9f2;
  }
</style>
