export interface DocListItem {
  typeName: string;
  typeCode: number;
  typeCodeStr?: string;
  docList?: any[];
  [key: string]: any;
}

// 文档拟定
export const wdndDocTableData: DocListItem[] = [
  {
    typeName: '申请表',
    typeCode: 8,
    singleGen: true
  },
  {
    typeName: '受理通知书',
    typeCode: 9
  },
  {
    typeName: '告知书',
    typeCode: 116
  },
  {
    typeName: '缴费清单',
    typeCode: 77
  },
  {
    typeName: '审批表',
    typeCode: 11
  },
  {
    typeName: '送达回执',
    typeCode: 13
  },
  {
    typeName: '卷宗封皮',
    typeCode: 10
  },
  {
    typeName: '其他证明书',
    typeCode: 85
  }
]

// 笔录
export const blDocTableData: DocListItem[] = [
  {
    typeName: '笔录',
    typeCode: 2
  }
]

// 代书/文书
export const wsDocTableData: DocListItem[] = [
  {
    typeName: '文书',
    typeCode: 1
  }
]

// 拟定公证书
export const ndgzsDocTableData: DocListItem[] = [
    {
    typeName: '申请表',
    typeCode: 8
  },
  {
    typeName: '受理通知书',
    typeCode: 9
  },
  {
    typeName: '告知书',
    typeCode: 116
  },
  {
    typeName: '缴费清单',
    typeCode: 77
  },
  {
    typeName: '笔录',
    typeCode: 2
  },
  {
    typeName: '签发稿',
    typeCode: 20
  },
  {
    typeName: '送达回执',
    typeCode: 13
  },
  {
    typeName: '审批表',
    typeCode: 11
  },
  {
    typeName: '卷宗封皮',
    typeCode: 10
  },
  {
    typeName: '其他证明书',
    typeCode: 85
  }
]

// 签发稿
export const qfgDocTableData: DocListItem[] = [
  {
    typeName: '签发稿',
    typeCode: 20
  },
]

// 当事人拍照时的三个生成按钮
export const picDocTableData: DocListItem[] = [
  {
    typeName: '信息表',
    typeCode: 6,
  },
  {
    typeName: '认证/识别记录',
    typeCode: 94
  },
  {
    typeName: '现场记录',
    typeCode: 23
  }
]
