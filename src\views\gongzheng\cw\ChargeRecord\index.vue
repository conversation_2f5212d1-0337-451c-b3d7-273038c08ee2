<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" width="80%" append-to-body>
    <div v-loading="modelState.loading">
      <RecordList ref="recordRef" :gzjz-id="gzjzId || modelState.gzjzId || curGzjz.id || currentRecordId" />
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting">撤销记录</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import RecordList from './RecordList.vue';
import { RecordListInstance } from './types';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '收费记录'
})

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const modelState = reactive({
  visible: false,
  title: '收费记录',
  gzjzId: undefined,
  loading: false,
  submitting: false,
})

const recordRef = ref<RecordListInstance>(null);

const close = () => {
  modelState.visible = false
}

const closed = () => {
}

const initData = async () => {
  try {
    modelState.loading = true;
    
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.loading = false;
  }
 }

const open = (data?: any) => {
  modelState.visible  = true;
  modelState.gzjzId = data?.gzjzId || undefined;
  modelState.title = data?.title || '';

  initData();
}

const comfirmSave = async () => {
  try {
    ElMessage.warning('功能建设中...')
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.submitting = false;
  }
}

const comfirmDel = () => {
  
}

defineExpose({
  open
})

</script>
