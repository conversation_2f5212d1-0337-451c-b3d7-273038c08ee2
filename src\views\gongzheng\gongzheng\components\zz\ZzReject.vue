<template>
  <el-dialog v-model="visible" title="驳回" show-close destroy-on-close width="600">
    <el-form ref="bhFormIns" :model="bhState" :rules="bhFormRules" label-width="120">
      <el-form-item prop="lczt" label="驳回节点：">
        <el-select v-model="bhState.lczt" style="width: 240px">
          <el-option value="04" label="审查"/>
          <!-- <el-option value="05" label="审批"/> -->
        </el-select>
      </el-form-item>
      <el-form-item prop="lcyj" label="驳回意见：">
          <el-input
            v-model="bhState.lcyj"
            style="min-width: 200px;max-width: 480px;"
            :autosize="{ minRows: 3, maxRows: 6 }"
            type="textarea"
            placeholder="请输入驳回意见."
          />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end items-center">
        <el-button @click="bhComfirm" type="danger">确认驳回</el-button>
        <el-button @click="bhCancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { initiateSigned } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed } from 'vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '驳回'
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['update:modelValue', 'rejected'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const bhFormIns = ref<ElFormInstance>(null);

const bhState = reactive({
  lczt: '05',
  lcyj: ''
})

const bhFormRules = {
  lczt: [
    { required: true, message: '请选择驳回节点', trigger: 'change' }
  ],
  lcyj: [
    { required: true, message: '请输入驳回原因', trigger: 'change' },
    { required: true, message: '请输入驳回原因', trigger: 'blur' }
  ]
}

async function bhSubmit() {
  const loading = ElLoading.service({
    lock: true,
    text: '正在提交制证驳回，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {
    const params = {
      id: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      sftg: '0',
      ...bhState
    }
    const res = await initiateSigned(params);
    if (res.code === 200) {
      ElMessage.success('驳回提交成功')
      emit('rejected');
      bhCancel();
    }
  } catch (err: any) {
    console.error('提交发证驳回失败');
  } finally {
    loading.close()
  }
}

function bhComfirm() {
  if (!bhFormIns.value) return;
  bhFormIns.value.validate((valid, fields) => {
    if (valid) {
      bhSubmit();
    } else {
      const firstField = Object.values(fields)[0];
      if (firstField) {
        ElMessage.error(firstField[0].message);
      }
    }
  })
}
function bhCancel() {
  emit('update:modelValue', false)
}


</script>
