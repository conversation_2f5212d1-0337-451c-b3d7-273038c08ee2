文档模板选择

<template>
  <TempPicker ref="tempPickRef" @pick="tempPick" />
  <el-button @click="openTempPick">打开</el-button>
</template>

<script setup lang="ts">
import TempPicker from '@/views/gongzheng/components/TempPicker/index.vue';

const tempPickRef = ref(null)

// 点击确认按钮回调
const tempPick = (data: any) => {
  const { id, mbId, wdMc, ywId } = data;
  ...
}

const openTempPick = () => {
  
  tempPickRef.value?.open() // 打开
  
  tempPickRef.value?.close() // 关闭
}
</script>

===================================================================================================

{
  id: "1942107563290652674"
  mbId: "1942107543388680194"
  wdMc: "赠与笔录"
  ywId: "1941842457757708291"
}