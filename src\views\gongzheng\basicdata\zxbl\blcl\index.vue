<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
          <el-form-item label="材料名称" prop="materialName">
            <el-input v-model="queryParams.materialName" placeholder="请输入材料名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="类别" prop="category">
            <el-select v-model="queryParams.category" placeholder="请选择" clearable style="width: 240px">
              <el-option label="个人" value="1" />
              <el-option label="企业" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basicdata:zxbl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['basicdata:zxbl:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="materialList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="材料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
        <el-table-column label="类别" align="center" prop="category">
          <template #default="scope">
            <el-tag v-if="scope.row.category === 1" type="primary">个人</el-tag>
            <el-tag v-else-if="scope.row.category === 2" type="success">企业</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="说明" align="center" prop="description" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicdata:zxbl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" style="color:red;" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basicdata:zxbl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改在线办理材料管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="materialFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="材料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入材料名称" />
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select v-model="form.category" placeholder="请选择类别">
            <el-option label="个人" :value="1" />
            <el-option label="企业" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入说明" :rows="4" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ZxblMaterial" lang="ts">
import { listZxblMaterial, getZxblMaterial, delZxblMaterial, addZxblMaterial, updateZxblMaterial } from '@/api/gongzheng/basicdata/zxbl/blcl';
import { ZxblMaterialVO, ZxblMaterialQuery, ZxblMaterialForm } from '@/api/gongzheng/basicdata/zxbl/blcl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const materialList = ref<ZxblMaterialVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const materialFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const buttonLoading = ref(false);

const initFormData: ZxblMaterialForm = {
  id: undefined,
  materialName: undefined,
  category: 1,
  description: undefined,
  remark: undefined
}

const data = reactive<PageData<ZxblMaterialForm, ZxblMaterialQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    materialName: undefined,
    category: undefined,
    params: {}
  },
  rules: {
    materialName: [
      { required: true, message: "材料名称不能为空", trigger: "blur" }
    ],
    category: [
      { required: true, message: "类别不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询在线办理材料管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listZxblMaterial(queryParams.value);
  materialList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  materialFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ZxblMaterialVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加在线办理材料";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ZxblMaterialVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getZxblMaterial(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改在线办理材料";
}

/** 提交按钮 */
const submitForm = () => {
  materialFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateZxblMaterial(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addZxblMaterial(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ZxblMaterialVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除该材料数据项？').finally(() => loading.value = false);
  await delZxblMaterial(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('basicdata/zxbl/material/export', {
    ...queryParams.value
  }, `zxbl_material_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
