<template>
  <div>
    <el-form :model="plfyForm" ref="formRef" label-width="130px" v-loading="loading">
      <el-form-item label="公证员" prop="gzy">
        <el-input v-model="plfyForm.gzyxm" placeholder="公证员" readonly style="width:220px" />
      </el-form-item>
      <el-form-item label="受理日期" prop="slrq" :rules="[{ required: true, message: '请选择受理日期', trigger: 'change' }]">
        <el-date-picker
          v-model="plfyForm.slrq"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          style="width:220px">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="回避当前公证员" prop="hb">
        <el-checkbox v-model="plfyForm.hb">回避当前公证员</el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx'
import { initiateAcceptance } from '@/api/gongzheng/gongzheng/gzjzJbxx'
import type { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import type { FormInstance } from 'element-plus'

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const plfyForm = ref({
  gzy: null,        // 公证员ID
  gzyxm: '',        // 公证员姓名
  slrq: '',         // 受理日期
  hb: false         // 是否回避
})

// 获取卷宗基本信息
const getBasicInfo = async (id: string | number) => {
  if (!id) return

  loading.value = true
  try {
    const res = await getGzjzJbxx(id)
    if (res.code === 200) {
      const data = res.data
      // 设置公证员信息
      plfyForm.value.gzy = data.gzybm;
      plfyForm.value.gzyxm = data.gzyxm || '未设置公证员';
      plfyForm.value.slrq = data.slrq;
      // // 设置默认受理日期为今天
      // const today = new Date()
      // plfyForm.value.slrq = today.toISOString().split('T')[0]
    } else {
      ElMessage.error('获取卷宗信息失败：' + (res.msg || '未知错误'))
    }
  } catch (error: any) {
    console.error('获取卷宗信息失败:', error)
    ElMessage.error('获取卷宗信息失败: ' + (error?.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 提交发起受理 - 暴露给父组件调用
const handleSubmit = async () => {
  if (!formRef.value) return false

  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return false

    if (!currentRecordId.value) {
      ElMessage.error('未获取到卷宗信息')
      return false
    }

    // 调用发起受理接口
    const requestData = {
      id: currentRecordId.value,
      slrq: plfyForm.value.slrq
    }

    const res = await initiateAcceptance(requestData)
    if (res.code === 200) {
      ElMessage.success('发起受理成功')
      return true
    } else {
      ElMessage.error('发起受理失败：' + (res.msg || '未知错误'))
      return false
    }
  } catch (error: any) {
    console.error('发起受理失败:', error)
    ElMessage.error('发起受理失败: ' + (error?.message || '未知错误'))
    return false
  }
}

// 监听当前记录ID变化
watch(
  () => currentRecordId.value,
  (newId) => {
    if (newId) {
      getBasicInfo(newId)
    } else {
      // 清空表单数据
      plfyForm.value = {
        gzy: null,
        gzyxm: '',
        slrq: '',
        hb: false
      }
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  handleSubmit
})

onMounted(() => {
  if (currentRecordId.value) {
    getBasicInfo(currentRecordId.value)
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
