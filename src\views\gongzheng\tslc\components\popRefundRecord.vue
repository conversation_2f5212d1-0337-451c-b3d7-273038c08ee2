<template>
  <!-- 退费记录弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <template #default>
      <div class="refund-record-dialog">
        <h3>退费记录</h3>
        
        <!-- 退费记录表格 -->
        <el-table v-loading="loading" :data="refundList" border stripe style="width: 100%">
          <el-table-column label="费用类型" prop="feeType" align="center" />
          <el-table-column label="公证事项" prop="notarizationItem" align="center" show-overflow-tooltip />
          <el-table-column label="公证书编号" prop="certificateNumber" align="center" />
          <el-table-column label="已收" prop="received" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.received) }}
            </template>
          </el-table-column>
          <el-table-column label="退费" prop="refundAmount" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.refundAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="申请人" prop="applicant" align="center" />
          <el-table-column label="申请时间" prop="applicationTime" align="center" width="120" />
          <el-table-column label="退费状态" prop="refundStatus" align="center" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.refundStatus)">
                {{ scope.row.refundStatus }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="doModalClose">关闭</el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<script setup name="PopRefundRecord" lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router'
import { VxeModalProps } from 'vxe-pc-ui'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 定义 emits 类型
const emits = defineEmits<{
  (event: 'close'): void
}>()

// 弹窗控制
const showPopup = ref(false)
const modalOptions = reactive<VxeModalProps>({
  title: '退费记录',
  width: '1200px',
  height: '80%',
  escClosable: true,
  resize: true,
  showMaximize: true,
  destroyOnClose: true
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 数据状态
const loading = ref(false)
const total = ref(0)
const refundList = ref<any[]>([])

// 方法
const open = (option = {}) => {
  console.log('open-refund-record', option);
  showPopup.value = true;
  // 加载数据
  getRefundList();
}

const close = () => {
  showPopup.value = false;
  emits('close');
}

const doModalClose = () => {
  emits('close');
  showPopup.value = false;
  console.log('退费记录窗口关闭了');
}

// 获取退费记录列表
const getRefundList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据 - 当前为空数据
    refundList.value = [];
    total.value = 0;
  } catch (error) {
    console.error('获取退费记录失败', error);
    ElMessage.error('获取退费记录失败');
  } finally {
    loading.value = false;
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getRefundList();
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  getRefundList();
}

// 格式化金额
const formatMoney = (amount: number) => {
  return amount.toFixed(2);
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success';
    case '处理中':
      return 'warning';
    case '已拒绝':
      return 'danger';
    default:
      return 'info';
  }
}

onMounted(() => {
  console.log('RefundRecordDialog:onMounted');
});

onUnmounted(() => {
  console.log('RefundRecordDialog:onUnmounted');
});

// 暴露方法给父组件
defineExpose({
  open, close
})
</script>

<style scoped>
.refund-record-dialog {
  padding: 20px;
}

.refund-record-dialog h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}
</style>
