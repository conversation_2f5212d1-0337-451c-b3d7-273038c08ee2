<template>
  <el-dialog v-model="visible" :title="title" show-close destroy-on-close>
    <div class="flex flex-col gap-10px">
      <el-card>
        <div class="flex flex-wrap gap-6px mb-6px">
          <span class="flex items-center">
            <el-text>卷宗号：</el-text>
            <el-text type="info">{{ curGzjz.jzbh || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>公证员：</el-text>
            <el-text type="info">{{ curGzjz.gzyxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>助理/受理人：</el-text>
            <el-text type="info">{{ curGzjz.zlxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>收费状态：</el-text>
            <el-text type="info">{{ '--' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>申请日期：</el-text>
            <el-text type="info">{{ formatDate(curGzjz.sqsj) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>受理日期：</el-text>
            <el-text type="info">{{ formatDate(curGzjz.slrq) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text>当事人：</el-text>
            <el-text type="info">{{ curGzjz.gzyxm }}</el-text>
          </span>
        </div>
        <div>
          <span class="flex items-center">
            <el-text>发证提醒：</el-text>
            <el-text type="info">{{ '------' }}</el-text>
          </span>
        </div>
      </el-card>

      <el-card class="here-card">
        <template #header>
          <strong>公证书列表</strong>
        </template>
        <el-table :data="gzsxState.listData" v-loading="gzsxState.loading" max-height="400" border stripe>
          <el-table-column prop="gzsBh" label="公证书编号" />
          <el-table-column prop="gzsxMc" label="公证事项" />
          <el-table-column prop="gzsFs" label="公证书份数" />
        </el-table>
        <template v-if="pageState.total > 0" #footer>
          <pagination v-model:page="pageState.pageNum" v-model:limit="pageState.pageSize" :total="pageState.total" @pagination="loadGzsxList" size="small" style="margin-top: 0;" />
        </template>
      </el-card>

      <el-card>
        <el-form ref="fzFormIns" :model="fzFormState" :rules="fzFormRules" label-width="120">
          <el-form-item prop="lcyj" label="发证意见：" style="margin: 0;">
            <el-input
              v-model="fzFormState.lcyj"
              style="min-width: 200px;max-width: 480px;"
              :autosize="{ minRows: 3, maxRows: 6 }"
              type="textarea"
              placeholder="请输入发证意见."
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <NewZjcl v-model="takePicState.visible" v-if="takePicState.visible" :title="takePicState.title" :gzjz-id="gzjzId" />

    <FzReject v-model="fzRejectVisible" :gzjz-id="gzjzId" />

    <template #footer>
      <div class="flex justify-end items-center gap-10px">
        <el-button @click="fzSubmit" type="primary" v-hasPermi="['gz:fz:edit']">确认发证</el-button>
        <el-button @click="toTakePic" v-hasPermi="['gz:fz:edit']">拍照</el-button>
        <el-button @click="toFzReject" type="danger" v-hasPermi="['gz:fz:edit']">驳回</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed, onMounted } from 'vue';
import { formatDate } from '@/utils/ruoyi';
import NewZjcl from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/index.vue'
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { initiateArchiving, initiateSigned } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import FzReject from './FzReject.vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '发证'
})

const emit = defineEmits(['update:modelValue', 'pass'])

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const pageState = reactive({
  total: 0,
  pageNum: 1,
  pageSize: 10,
})

const takePicState = reactive({
  visible: false,
  title: '拍照'
})

const gzsxState = reactive({
  listData: [],
  loading: false,
})

const fzFormState = reactive({
  lcyj: ''
})

const fzFormIns = ref<ElFormInstance>(null)

const fzFormRules = {
  lcyj: [
    { required: true, message: '请输入发证意见', trigger: 'change' },
    { required: true, message: '请输入发证意见', trigger: 'blur' }
  ]
}

const fzRejectVisible = ref(false)

function toFzReject() {
  fzRejectVisible.value = true
}

function fzSubmit() {
  if(!fzFormIns.value) return;

  fzFormIns.value.validate((valid, fields) => {
    if(valid) {
      ElMessageBox.confirm('确认提交发证吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fzComfirm();
      })
    } else {
      const firstField = Object.values(fields)[0];
      if (firstField) {
        ElMessage.error(firstField[0].message);
      }
    }
  })
}

async function fzComfirm() {
  const loading = ElLoading.service({
    lock: true,
    text: '正在提交发证，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {
    const params = {
      id: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      sftg: '1',
      ...fzFormState
    }
    const res = await initiateArchiving(params);
    if(res.code === 200) {
      ElMessage.success('发证提交成功');
      emit('pass');
      close();
    }
  } catch (err: any) {
    console.error('提交发证失败');
  } finally {
    loading.close()
  }
}

// 加载公证事项列表
async function loadGzsxList() {
  if (!props.gzjzId && !curGzjz.value.id && !currentRecordId.value) {
    gzsxState.listData = []
    return;
  };
  gzsxState.loading = true;
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: pageState.pageNum,
      pageSize: pageState.pageSize
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
      pageState.total = res.total || gzsxState.listData.length || 0;
    }

  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
    gzsxState.loading = false;
  }
}

function toTakePic() {
  takePicState.visible = true;
}

function close() {
  emit('update:modelValue', false)
}

onMounted(() => {
  loadGzsxList();

  // console.log(curGzjz)
})
</script>

<style>
.here-card .el-card__body {
  padding: 0 !important;
}
.here-card .el-card__footer {
  padding: 0;
}
</style>
