import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzSdxxVO, GzjzSdxxQuery, GzjzSdxxForm } from './types';

/**
 * 查询送达信息列表
 */
export const listGzjzSdxx = (query: GzjzSdxxQuery): AxiosPromise<GzjzSdxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzSdxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取送达信息详细信息
 */
export const getGzjzSdxx = (id: string | number): AxiosPromise<GzjzSdxxVO> => {
  return request({
    url: `/gongzheng/gzjzSdxx/${id}`,
    method: 'get'
  });
};

/**
 * 新增送达信息
 */
export const addGzjzSdxx = (data: GzjzSdxxForm): AxiosPromise<void> => {
  return request({
    url: '/gongzheng/gzjzSdxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改送达信息
 */
export const updateGzjzSdxx = (data: GzjzSdxxForm): AxiosPromise<void> => {
  return request({
    url: '/gongzheng/gzjzSdxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除送达信息
 */
export const delGzjzSdxx = (ids: string | number | Array<string | number>): AxiosPromise<void> => {
  return request({
    url: `/gongzheng/gzjzSdxx/${ids}`,
    method: 'delete'
  });
};
