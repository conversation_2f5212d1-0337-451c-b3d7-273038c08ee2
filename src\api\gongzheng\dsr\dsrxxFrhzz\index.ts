import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxFrhzzVO, DsrxxFrhzzForm, DsrxxFrhzzQuery } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';

/**
 * 查询当事人-基本信息-法人或者其他组织信息列表
 * @param query
 * @returns {*}
 */

export const listDsrxxFrhzz = (query?: DsrxxFrhzzQuery): AxiosPromise<DsrxxFrhzzVO[]> => {
  return request({
    url: '/dsr/dsrxxFrhzz/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-基本信息-法人或者其他组织信息详细
 * @param id
 */
export const getDsrxxFrhzz = (id: string | number): AxiosPromise<DsrxxFrhzzVO> => {
  return request({
    url: '/dsr/dsrxxFrhzz/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-基本信息-法人或者其他组织信息
 * @param data
 */
export const addDsrxxFrhzz = (data: DsrxxFrhzzForm) => {
  return request({
    url: '/dsr/dsrxxFrhzz',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-基本信息-法人或者其他组织信息
 * @param data
 */
export const updateDsrxxFrhzz = (data: DsrxxFrhzzForm) => {
  return request({
    url: '/dsr/dsrxxFrhzz',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-基本信息-法人或者其他组织信息
 * @param id
 */
export const delDsrxxFrhzz = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxFrhzz/' + id,
    method: 'delete'
  });
};
