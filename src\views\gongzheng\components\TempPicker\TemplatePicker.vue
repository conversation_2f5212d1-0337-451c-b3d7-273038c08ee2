<template>
  <div v-loading="tempState.loading" class="tree-wrap h-full rounded overflow-hidden">
    <!-- <div class="tree-header h-36px flex flex-nowrap items-center gap-6px border p-x-6px">
      <el-select v-model="selectedGzsxId" size="small" style="width: 100px;">
        <el-option v-for="item in gzsxOptions" :key="item.id" :value="item.id" :label="item.title" />
      </el-select>
      <strong>模版</strong>
      <el-input v-model="filterText" @keyup.enter="handleFilter" @clear="handleFilter" size="small" clearable style="width: 160px;" />
      <el-button @click="handleFilter" type="primary" plain icon="Search" size="small" />
    </div> -->
    <div class="tree-header h-36px flex flex-nowrap items-center gap-6px border p-x-6px">
      <el-text>{{ props.gzsxOptions.length > 0 ? props.gzsxOptions[0].title : '' }}</el-text>
    </div>
    <div class="h-[calc(100%-36px)] p-4px">
      <div class="h-full overflow-auto">
        <el-table :data="tempState.list" @current-change="chooseCurRow" size="small" style="height: 100%;" border stripe highlight-current-row>
          <el-table-column type="index" label="#" width="60" align="center" />
          <el-table-column label="模版名称" prop="wdMc" align="center" />
          <!-- <el-table-column label="模版类型" prop="templateType" align="center">
            <template #default="scope">
              <dict-tag :options="gz_mb_mblx" :value="scope.row.templateType" />
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
    <!-- <div class="tree-footer flex items-center justify-end h-32px">
      <el-pagination
        v-model:current-page="pageState.pageNum"
        v-model:page-size="pageState.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :pager-count="4"
        size="small"
        layout="total, sizes, prev, pager, next"
        :total="pageState.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { listMbJcxx } from '@/api/gongzheng/mb/mbJcxx'
import { MbJcxxQuery } from '@/api/gongzheng/mb/mbJcxx/types'
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_mb_wdlb, gz_mb_mblx, gz_mb_classify } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_mb_wdlb', 'gz_mb_mblx', 'gz_mb_classify'));

interface Props {
  gzsxOptions?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  gzsxOptions: () => [],
})

const emit = defineEmits(['pick'])

const tempState = reactive({
  list: [],
  loading: false,
})

const tempTableRef = ref<ElTableInstance>(null)
const filterText = ref('')

const defaultOptions = [
  {
    id: 'all',
    title: '所有'
  }
]
const selectedGzsxId = ref('all')
const gzsxOptions = ref<Record<string, any>>([...defaultOptions])

const pageState = reactive({
  pageSize: 20,
  pageNum: 1,
  total: 0
})

const handleFilter = () => {}
const handleSizeChange = () => {}
const handleCurrentChange = () => {}

const chooseCurRow = (curRow: any, oldRow: any) => {
  emit('pick', curRow, oldRow);
}

const initData = async () => {
  try {
    tempState.loading = true;
    const { pageNum, pageSize } = pageState
    let params: Record<string, any> = {
      // pageNum,
      // pageSize,
    }
    if(selectedGzsxId.value && selectedGzsxId.value !== 'all') {
      params.ywId = selectedGzsxId.value
    }
    const res = await queryMbFiles(params);
    const { total, code, rows, data } = res;
    if(code === 200) {
      pageState.total = total;
      tempState.list = data || [];
    }
  } catch(err: any) {
    console.error('获取模版列表失败', err)
  } finally {
    tempState.loading = false;
  }
}

watchEffect(() => {
  // gzsxOptions.value = [
  //   ...defaultOptions,
  //   ...props.gzsxOptions
  // ]

  if(props.gzsxOptions.length > 0) {
    selectedGzsxId.value = props.gzsxOptions[0].id
    initData();
  }

})

onMounted(() => {
  // initData()
})
</script>

<style scoped>
.tree-header {
  border-bottom: 1px solid rgba(128, 128, 128, 0.42);
}
.tree-footer {
  border-top: 1px solid rgba(128, 128, 128, 0.42);
}

.tree-wrap {
  /* box-shadow: 0 0 3px 0 #7499d580; */
  border: 1px solid rgba(128, 128, 128, 0.42);
  transition: 0.2s;
}

.tree-wrap:hover {
  box-shadow: 0 0 6px 0 #7396cf80;
}

</style>