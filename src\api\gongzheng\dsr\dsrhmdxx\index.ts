import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrhmdxxVO, DsrhmdxxForm, DsrhmdxxQuery } from '@/api/gongzheng/dsr/dsrhmdxx/types';

/**
 * 查询当事人-黑名单信息（风险信息）列表
 * @param query
 * @returns {*}
 */

export const listDsrhmdxx = (query?: DsrhmdxxQuery): AxiosPromise<DsrhmdxxVO[]> => {
  return request({
    url: '/dsr/dsrhmdxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-黑名单信息（风险信息）详细
 * @param id
 */
export const getDsrhmdxx = (id: string | number): AxiosPromise<DsrhmdxxVO> => {
  return request({
    url: '/dsr/dsrhmdxx/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-黑名单信息（风险信息）
 * @param data
 */
export const addDsrhmdxx = (data: DsrhmdxxForm) => {
  return request({
    url: '/dsr/dsrhmdxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-黑名单信息（风险信息）
 * @param data
 */
export const updateDsrhmdxx = (data: DsrhmdxxForm) => {
  return request({
    url: '/dsr/dsrhmdxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-黑名单信息（风险信息）
 * @param id
 */
export const delDsrhmdxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrhmdxx/' + id,
    method: 'delete'
  });
};
