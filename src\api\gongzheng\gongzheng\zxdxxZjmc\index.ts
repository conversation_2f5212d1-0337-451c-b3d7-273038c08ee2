import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZxdxxZjmcVO, ZxdxxZjmcForm, ZxdxxZjmcQuery } from '@/api/gongzheng/zxdxxZjmc/types';

/**
 * 查询公证-咨询单-证据名称列表
 * @param query
 * @returns {*}
 */

export const listZxdxxZjmc = (query?: ZxdxxZjmcQuery): AxiosPromise<ZxdxxZjmcVO[]> => {
  return request({
    url: '/gongzheng/zxdxxZjmc/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证-咨询单-证据名称详细
 * @param zxzjId
 */
export const getZxdxxZjmc = (zxzjId: string | number): AxiosPromise<ZxdxxZjmcVO> => {
  return request({
    url: '/gongzheng/zxdxxZjmc/' + zxzjId,
    method: 'get'
  });
};

/**
 * 新增公证-咨询单-证据名称
 * @param data
 */
export const addZxdxxZjmc = (data: ZxdxxZjmcForm) => {
  return request({
    url: '/gongzheng/zxdxxZjmc',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证-咨询单-证据名称
 * @param data
 */
export const updateZxdxxZjmc = (data: ZxdxxZjmcForm) => {
  return request({
    url: '/gongzheng/zxdxxZjmc',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证-咨询单-证据名称
 * @param zxzjId
 */
export const delZxdxxZjmc = (zxzjId: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/zxdxxZjmc/' + zxzjId,
    method: 'delete'
  });
};
