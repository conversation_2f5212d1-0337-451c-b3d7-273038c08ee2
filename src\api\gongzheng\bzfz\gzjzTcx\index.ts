import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzTcxVO, GzjzTcxForm, GzjzTcxQuery } from '@/api/gongzheng/bzfz/gzjzTcx/types';

/**
 * 查询提存项列表
 * @param query
 * @returns {*}
 */

export const listGzjzTcx = (query?: GzjzTcxQuery): AxiosPromise<GzjzTcxVO[]> => {
  return request({
    url: '/gongzheng/gzjzTcx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询提存项详细
 * @param id
 */
export const getGzjzTcx = (id: string | number): AxiosPromise<GzjzTcxVO> => {
  return request({
    url: '/gongzheng/gzjzTcx/' + id,
    method: 'get'
  });
};

/**
 * 新增提存项
 * @param data
 */
export const addGzjzTcx = (data: GzjzTcxForm) => {
  return request({
    url: '/gongzheng/gzjzTcx',
    method: 'post',
    data: data
  });
};

/**
 * 修改提存项
 * @param data
 */
export const updateGzjzTcx = (data: GzjzTcxForm) => {
  return request({
    url: '/gongzheng/gzjzTcx',
    method: 'put',
    data: data
  });
};

/**
 * 删除提存项
 * @param id
 */
export const delGzjzTcx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzTcx/' + id,
    method: 'delete'
  });
};
