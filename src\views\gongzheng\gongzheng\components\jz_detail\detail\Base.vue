<template>
  <el-card>
    <template #header>
      <strong class="text-base">基本信息</strong>
    </template>
    <div class="flex flex-col h-full w-full">
      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">卷宗号：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.jzbh || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">公证员：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.gzyxm || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">助理：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.zlxm || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">公证类别：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_gzlb, detail?.lb) || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">紧急度：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_sl_jjcd, detail?.jjd) || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否密卷：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_sfmj, detail?.sfmj) || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">使用地：</span>
            <el-text class="flex-1" type="info" size="large">{{ areaMapName(detail?.syd) || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">译文文种：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_yw_wz, detail?.ywwz) || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">用途：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_yt, detail?.yt) || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">申请日期：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.sqsj || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">受理日期：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.slrq || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否认证：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_rz_zt, detail?.rz) || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">流程状态：</span>
            <el-text class="flex-1" type="primary" size="large">{{ dictMapFormat(gz_sl_lczt, detail?.lczt) || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">外部订单号：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.wbddh || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">协办人：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.xbrxm || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否零接触：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.sfljc === '1' ? '是' : '否' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否电子签名：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.sfdzqm === '1' ? '是' : '否' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否电子公证书：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.sfdzgzs === '1' ? '是' : '否' }}</el-text>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="6">
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">电票领取电话：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.dplqdh || '-' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">是否外译中：</span>
            <el-text class="flex-1" type="info" size="large">{{ detail?.sfwyz === '1' ? '是' : '否' }}</el-text>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center flex-nowrap">
            <span class="text-base leading-4 p-6px min-w-150px flex justify-end">法律援助：</span>
            <el-text class="flex-1" type="info" size="large">{{ dictMapFormat(gz_flyz, detail?.flxz) || '-' }}</el-text>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted } from 'vue';
import { dictMapFormat } from '@/utils/ruoyi';
import { listAreaname } from '@/api/gongzheng/basicdata/areaname';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

interface Detail extends GzjzJbxxVO {
  [k: string]: any;
}

interface Props {
  data?: Detail 
}

const props = defineProps<Props>()

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_sfmj, gz_yw_wz, gz_yt, gz_rz_zt, gz_sl_lczt, gz_flyz, gz_sl_jjcd } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_sfmj', 'gz_yw_wz', 'gz_yt', 'gz_rz_zt', 'gz_sl_lczt', 'gz_flyz', 'gz_sl_jjcd'));

const detail = computed(() => props.data);

const areas = ref([]);

const getAreaName = async () => {
  try {
    const params = {
      pageSize: 300,
      pageNum: 1
    }
    const res = await listAreaname(params);
    if (res.code === 200) {
      areas.value = res.rows;
    }
  } catch (err: any) {
    console.error('获取地区名称失败:', err);
  }
}

const areaMapName = (code: string) => {
  const areaInfo = areas.value.find(area => area.numberCode === code);
  if (areaInfo) {
    return areaInfo.areaName;
  }
  return '-'
}

onBeforeMount(() => {
  getAreaName();
})

onMounted(() => {
  console.log('detail', detail.value)
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(177, 210, 241, 0.623);
}
</style>
