<template>
  <gz-dialog v-model="visible" :title="title" @closed="closed" append-to-body>
    <div class="flex justify-center items-center">
      <video :src="mediaUrl" controls class="w-full h-full object-fit-contain"></video>
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue?: boolean;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '媒体播放器'
})

const emit = defineEmits(['update:modelValue'])

const mediaUrl = ref('')

const visible = ref(false)
//  computed({
//   get: () => props.modelValue,
//   set: (val) => emit('update:modelValue', val)
// })

const close = () => {
  visible.value = false
  mediaUrl.value = ''
}

const closed = () => {}

const show = (url: string) => {
  mediaUrl.value = url
  visible.value = true
}

defineExpose({
  show
})

</script>
