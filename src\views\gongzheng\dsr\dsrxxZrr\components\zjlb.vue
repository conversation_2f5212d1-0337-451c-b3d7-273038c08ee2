<template>
  <div>
    <el-card shadow="never">
      <div slot="header" class="clearfix dateil-card-heard">
        <span>其他证件信息</span>
      </div>
      <el-table v-loading="loading" :data="dsrxxZjxxList">
        <el-table-column label="名称" align="center" prop="xm" />
        <el-table-column label="证件类型" align="center" prop="zjlx">
          <template #default="scope">
            <dict-tag :options="gz_gr_zjlx" :value="scope.row.zjlx" />
          </template>
        </el-table-column>
        <el-table-column label="创建日期" align="center" prop="createTime" />
        <el-table-column label="证件号码" align="center" prop="zjhm" />
        <el-table-column label="是否主要证件" align="center" prop="zjzt">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.zjzt" />
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Zjlb" lang="ts">
  import { listDsrxxZjxx, getDsrxxZjxx, delDsrxxZjxx, addDsrxxZjxx, updateDsrxxZjxx } from '@/api/gongzheng/dsr/dsrxxZjxx';
  import { getGzjzDsr } from '@/api/gongzheng/gongzheng/gzjzDsr';
  import { DsrxxZjxxVO, DsrxxZjxxQuery, DsrxxZjxxForm } from '@/api/gongzheng/dsr/dsrxxZjxx/types';
  import { DsrxxZrrVO } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gr_zjlx, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_gr_zjlx', 'gz_yes_or_no'));
  interface Props {
    vo : DsrxxZrrVO;
    vo2 : DsrxxFrhzzVO;
  }
  const props = defineProps<Partial<Props>>();
  const dsrxxZjxxList = ref<DsrxxZjxxVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const dsrxxZjxxFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrxxZjxxForm = {
    id: undefined,
    dsrId: undefined,
    xm: undefined,
    zjlx: undefined,
    zjzt: undefined,
    bz: undefined,
    zjhm: undefined,
    createTime: undefined,
  }
  const data = reactive<PageData<DsrxxZjxxForm, DsrxxZjxxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      dsrId: undefined,
      xm: undefined,
      zjlx: undefined,
      zjzt: undefined,
      bz: undefined,
      zjhm: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      dsrId: [
        { required: true, message: "当事人不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询当事人-证件列列表 */
  const getList = async () => {
    loading.value = true;
    if (props.vo) {
      queryParams.value.dsrId = props.vo.id;
    } else {
      queryParams.value.dsrId = props.vo2.id;
    }

    console.log('查询参数 >>>>>> :', props.vo);
    const res = await listDsrxxZjxx(queryParams.value);
    dsrxxZjxxList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  onMounted(() => {
    getList();
  });
</script>

<style scoped>
  .no-padding-card .el-card__body {
    padding: 0;
  }

  .dateil-card-main {
    max-height: 500px;
    height: 300px;
    margin-bottom: 10px;
  }

  .dateil-card-heard {
    /* background-color: aliceblue; */
    padding: 10px;
    border-bottom: 1px solid #e7e7e7;
  }
</style>
