export interface GzsxJspzVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 公证事项
   */
  gzsxId: string | number;

  /**
   * 角色名称
   */
  jsmc: string;

  /**
   * 是否有效
   */
  sfyx: string;

  gzsxCode : string;

  gzlbValue : string;
}

export interface GzsxJspzForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 公证事项
   */
  gzsxId?: string | number;

  /**
   * 角色名称
   */
  jsmc?: string;

  /**
   * 是否有效
   */
  sfyx?: string;

  selectId?:[]

  gzsxCode ?: string;

  gzlbValue ?: string;

}

export interface GzsxJspzQuery extends PageQuery {

  /**
   * 公证事项
   */
  gzsxId?: string | number;

  /**
   * 角色名称
   */
  jsmc?: string;

  /**
   * 是否有效
   */
  sfyx?: string;

  gzsxCode ?: string;

  gzlbValue ?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



