import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzGzjzFyglQuerye, GzGzjzFyglVo } from '@/api/gongzheng/fy/types';

/**
 * 查询翻译管理-翻译列表
 * @param query 查询参数
 * @returns
 */
export const listGzjzFygl = (query: GzGzjzFyglQuerye): AxiosPromise<GzGzjzFyglVo[]> => {
  return request({
    url: '/gongzheng/gzjzFygl/list',
    method: 'get',
    params: query
  });
};


/**
 * 翻译管理 - 根据类型列表批量获取文件存储信息
 * @param lxList 类型列表
 * @param gzjzId
 * @param pageNum
 * @param pageSize
 * @returns
 */
export const listWjccxxByLxlist = (lxList: Array<string | number>, gzjzId: string | number, pageNum: string | number, pageSize: string | number) => {
  return request({
    url: '/gongzheng/gzjzFygl/listWjccxxByLxlist',
    method: 'post',
    params: {
      gzjzId: gzjzId,
      pageNum: pageNum,
      pageSize: pageSize,
      lxList: lxList
    }
  });
};
