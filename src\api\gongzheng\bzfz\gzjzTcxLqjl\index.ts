import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzTcxLqjlVO, GzjzTcxLqjlForm, GzjzTcxLqjlQuery } from '@/api/gongzheng/bzfz/gzjzTcxLqjl/types';

/**
 * 查询提存项-领取记录列表
 * @param query
 * @returns {*}
 */

export const listGzjzTcxLqjl = (query?: GzjzTcxLqjlQuery): AxiosPromise<GzjzTcxLqjlVO[]> => {
  return request({
    url: '/gongzheng/gzjzTcxLqjl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询提存项-领取记录详细
 * @param id
 */
export const getGzjzTcxLqjl = (id: string | number): AxiosPromise<GzjzTcxLqjlVO> => {
  return request({
    url: '/gongzheng/gzjzTcxLqjl/' + id,
    method: 'get'
  });
};

/**
 * 新增提存项-领取记录
 * @param data
 */
export const addGzjzTcxLqjl = (data: GzjzTcxLqjlForm) => {
  return request({
    url: '/gongzheng/gzjzTcxLqjl',
    method: 'post',
    data: data
  });
};

/**
 * 修改提存项-领取记录
 * @param data
 */
export const updateGzjzTcxLqjl = (data: GzjzTcxLqjlForm) => {
  return request({
    url: '/gongzheng/gzjzTcxLqjl',
    method: 'put',
    data: data
  });
};

/**
 * 删除提存项-领取记录
 * @param id
 */
export const delGzjzTcxLqjl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzTcxLqjl/' + id,
    method: 'delete'
  });
};
