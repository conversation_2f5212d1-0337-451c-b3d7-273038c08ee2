<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title" draggable append-to-body>
    <el-table ref="dsrTbRef" :data="linkDsrState.dsrList"  height="260" border stripe>
      <el-table-column label="#" type="index" width="60" align="center" />
      <el-table-column type="selection" width="60" align="center" />
      <el-table-column prop="name" label="当事人姓名" width="120" align="center" show-overflow-tooltip/>
      <el-table-column prop="phone" label="电话" width="120" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.phone || row.contactTel }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="住址" align="center" show-overflow-tooltip/>
    </el-table>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="linkDsr" :loading="linkDsrState.loading" :disabled="linkDsrState.loading" type="primary">确定</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';


const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['success'])

const modelState = reactive({
  visible: false,
  title: '引用当事人',
})

const linkDsrState = reactive({
  dsrList: [],
  loading: false,
})

const dsrTbRef = ref<ElTableInstance>(null)

// 加载当事人列表
const loadDsrList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    linkDsrState.dsrList = []
    return;
  };
  linkDsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        linkDsrState.dsrList = (res.rows || []).reduce((doneArr, curItem) => {
          const found = doneArr.find(i => {
            return i.dsrId == curItem.dsrId
          })
          if(!found) {
            doneArr.push(curItem)
          }
          return doneArr;
        }, []);
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    linkDsrState.loading = false;
  }
}

const linkDsr = () => {
  try {
    const selectedRows = dsrTbRef.value.getSelectionRows();
    if (selectedRows.length === 0) {
      ElMessage.warning('请选择要引用的当事人');
      return;
    }
    emit('success', selectedRows);
    close();
  } catch (err: any) {
    console.log('引用当事人失败', err);
  }
}
const close = () => {
  modelState.visible = false;
  linkDsrState.dsrList = [];
}

const open = () => {
  modelState.visible = true
  loadDsrList()
}

defineExpose({
  open
})

</script>
