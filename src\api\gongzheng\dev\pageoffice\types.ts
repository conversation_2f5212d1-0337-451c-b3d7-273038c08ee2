// 统一文档处理参数
export interface UnifiedDocumentProcessParams {
  documentId : string;
  documentName : string;
  documentType : string;
  path : string;
  action : string;
  [key : string] : any;
}
export interface UnifiedDocumentProcessParams2 {
  type : string;
  bizId : number;
  extraParams : {};
}
export interface UnifiedDocumentProcessParams3 {
  path : string;
  action : string;
}

// 编辑文档参数
export interface UnifiedDocumentEditParams {
  documentId : string;
  content : string;
  [key : string] : any;
}

// 根据路径删除参数
export interface UnifiedDocumentDeleteByPathParams {
  path : string;
}

// 文档VO
export interface UnifiedDocumentVO {
  documentId : string;
  name : string;
  path : string;
  status : string;
  [key : string] : any;
}
