import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzsxZjlbVO, GzsxZjlbForm, GzsxZjlbQuery } from '@/api/basicdata/gzsxZjlb/types';

/**
 * 查询证据列表列表
 * @param query
 * @returns {*}
 */

export const listGzsxZjlb = (query?: GzsxZjlbQuery): AxiosPromise<GzsxZjlbVO[]> => {
  return request({
    url: '/basicdata/gzsxZjlb/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询证据列表详细
 * @param id
 */
export const getGzsxZjlb = (id: string | number): AxiosPromise<GzsxZjlbVO> => {
  return request({
    url: '/basicdata/gzsxZjlb/' + id,
    method: 'get'
  });
};

/**
 * 新增证据列表
 * @param data
 */
export const addGzsxZjlb = (data: GzsxZjlbForm) => {
  return request({
    url: '/basicdata/gzsxZjlb',
    method: 'post',
    data: data
  });
};

/**
 * 修改证据列表
 * @param data
 */
export const updateGzsxZjlb = (data: GzsxZjlbForm) => {
  return request({
    url: '/basicdata/gzsxZjlb',
    method: 'put',
    data: data
  });
};

/**
 * 删除证据列表
 * @param id
 */
export const delGzsxZjlb = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsxZjlb/' + id,
    method: 'delete'
  });
};
