export interface DsrxxZrrVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 受理编号
   */
  slbh : string;

  /**
   * 姓名
   */
  xm : string;

  /**
   * 性别
   */
  xb : string;

  /**
   * 联系电话
   */
  lxdh : string;

  /**
   * 住址
   */
  zz : string;

  /**
   * 证件类型
   */
  zjlx : string;

  /**
   * 证件号码
   */
  zjhm : string;

  /**
   * 当事人类别
   */
  dsrlb : string;

  /**
   * 照片
   */
  zp : string;

  /**
   * 国籍
   */
  gj : string;

  /**
   * 民族
   */
  mz : string;

  /**
   * 出生日期
   */
  csrq : string;

  /**
   * $column.columnComment
   */
  remark : string;

  /**
   * 客户号
   */
  khh : string;

  /**
   * 曾用名
   */
  cym : string;

  /**
   * 译文名
   */
  ywm : string;

  /**
   * 电子邮件
   */
  dzyj : string;

  /**
   * 婚姻状态
   */
  hyzk : number;

  /**
   * 工作单位
   */
  gzdw : string;

  /**
   * 微信号
   */
  wxh : string;

  /**
   * 开户行名称
   */
  khhmc : string;

  /**
   * 开户行账号
   */
  khhzh : string;

  /**
   * 评价等级
   */
  pjdj : number;

}

export interface DsrxxZrrForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 受理编号
   */
  slbh ?: string;

  /**
   * 姓名
   */
  xm ?: string;

  /**
   * 性别
   */
  xb ?: string;

  /**
   * 联系电话
   */
  lxdh ?: string;

  /**
   * 住址
   */
  zz ?: string;

  /**
   * 证件类型
   */
  zjlx ?: string;

  /**
   * 证件号码
   */
  zjhm ?: string;

  /**
   * 证件签发机关
   */
  signOffice?: string;

  /**
   * 证件有效期
   */
  cardValidDate?: string;

  /**
   * 当事人类别
   */
  dsrlb ?: string;

  /**
   * 照片
   */
  zp ?: string;

  /**
   * 身份证正面
   */
  cardImage1?: string;

  /**
   * 身份证反面
   */
  cardImage2?: string;

  /**
   * 国籍
   */
  gj ?: string;

  /**
   * 民族
   */
  mz ?: string;

  /**
   * 出生日期
   */
  csrq ?: string;

  /**
   * $column.columnComment
   */
  remark ?: string;

  /**
   * 客户号
   */
  khh ?: string;

  /**
   * 曾用名
   */
  cym ?: string;

  /**
   * 译文名
   */
  ywm ?: string;

  /**
   * 电子邮件
   */
  dzyj ?: string;

  /**
   * 婚姻状态
   */
  hyzk ?: number;

  /**
   * 工作单位
   */
  gzdw ?: string;

  /**
   * 微信号
   */
  wxh ?: string;

  /**
   * 开户行名称
   */
  khhmc ?: string;

  /**
   * 开户行账号
   */
  khhzh ?: string;

  /**
   * 评价等级
   */
  pjdj ?: number;
  lxzz ?: string;
  zjzt ?: boolean;

  sfdk?: string;

}

export interface DsrxxZrrQuery extends PageQuery {

  /**
   * 受理编号
   */
  slbh ?: string;

  /**
   * 姓名
   */
  xm ?: string;

  /**
   * 性别
   */
  xb ?: string;

  /**
   * 联系电话
   */
  lxdh ?: string;

  /**
   * 住址
   */
  zz ?: string;

  /**
   * 证件类型
   */
  zjlx ?: string;

  /**
   * 证件号码
   */
  zjhm ?: string;

  /**
   * 当事人类别
   */
  dsrlb ?: string;

  /**
   * 照片
   */
  zp ?: string;

  /**
   * 国籍
   */
  gj ?: string;

  /**
   * 民族
   */
  mz ?: string;

  /**
   * 出生日期
   */
  csrq ?: string;

  /**
   * 客户号
   */
  khh ?: string;

  /**
   * 曾用名
   */
  cym ?: string;

  /**
   * 译文名
   */
  ywm ?: string;

  /**
   * 电子邮件
   */
  dzyj ?: string;

  /**
   * 婚姻状态
   */
  hyzk ?: number;

  /**
   * 工作单位
   */
  gzdw ?: string;

  /**
   * 微信号
   */
  wxh ?: string;

  /**
   * 开户行名称
   */
  khhmc ?: string;

  /**
   * 开户行账号
   */
  khhzh ?: string;

  /**
   * 评价等级
   */
  pjdj ?: number;

  /**
   * 日期范围参数
   */
  params ?: any;
}
