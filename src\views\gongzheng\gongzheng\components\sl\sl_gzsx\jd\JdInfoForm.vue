<template>
  <el-form :model="jdFormData" :rules="rules" ref="jdFormRef" label-width="120" label-suffix=":">
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="公证书编号">
          <el-text>{{ curGzsxGzsBh || '-' }}</el-text>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="gzyXm" label="公证员">
          <el-text>{{  jdFormData?.gzyXm || '-' }}</el-text>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item prop="dkxh" label="贷款银行">
          <el-select v-model="jdFormData.dkxh">
            <el-option v-for="item in gz_tc_khh" :key="item.value" :value="item.value" :label="item.label" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="jdrq" label="借款日期">
          <el-date-picker
            v-model="jdFormData.jdrq"
            type="date"
            placeholder="借款日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item prop="dqrq" label="到期日期">
          <el-date-picker
            v-model="jdFormData.dqrq"
            type="date"
            placeholder="到期日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="htqsrq" label="合同签署日期">
          <el-date-picker
            v-model="jdFormData.htqsrq"
            type="date"
            placeholder="合同签署日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="10">
        <el-form-item prop="jkje" label="借款金额">
          <el-input v-model="jdFormData.jkje" @input="jkjeOnInput">
            <template #suffix>
              万元
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="7">
        <el-form-item prop="ywdb" label="有无担保">
          <el-select v-model="jdFormData.ywdb">
            <el-option value="0" label="无" />
            <el-option value="1" label="有" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="7">
        <el-form-item prop="hyzk" label="婚姻状况">
          <el-select v-model="jdFormData.hyzk">
            <el-option v-for="item in gz_dsr_hyzt" :key="item.value" :value="item.value" :label="item.label" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="remark" label="备注">
          <el-input v-model="jdFormData.remark" type="textarea" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { GzsxJdVo } from './type';
import { GzjzJdVO } from '@/api/gongzheng/bzfz/gzjzJd/types';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_tc_khh, gz_dsr_hyzt } = toRefs<any>(proxy?.useDict('gz_tc_khh', 'gz_dsr_hyzt'))

interface Props {
  modelValue: GzjzJdVO,
}

const props = defineProps<Props>()

const emit = defineEmits(['update:modelValue'])

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const curGzsxGzsBh = inject<Ref<string>>('curGzsxGzsBh', ref(null))

// const jdFormData = ref<GzsxJdVo>({
//   gzjzId: '',
//   gzjzGzsxId: '',
//   gzyId: '',
//   gzyXm: '',
//   dkxh: '',
//   jdrq: '',
//   dqrq: '',
//   htqsrq: '',
//   jkje: '',
//   ywdb: '',
//   hyzk: '',
//   remark: '',
// })

const jdFormData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const jdFormRef = ref<ElFormInstance>(null)

const rules = {
  dkxh: [
    { required: true, message: '请选择贷款银行', trigger: 'change' }
  ],
  // gzyXm: [],
  jdrq: [
    { required: true, message: '请选择借款日期', trigger: 'change' }
  ],
  dqrq: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ],
  htqsrq: [
    { required: true, message: '请选择合同签署日期', trigger: 'change' }
  ],
  jkje: [
    { required: true, message: '请输入借款金额', trigger: 'blur' }
  ]
}

const oldJkje = ref('')
const jkjeOnInput = (val: any) => {
  const reg = /^\d+\.?\d*$/
  if(val) {
    if(reg.test(val)) {
      jdFormData.value.jkje = val
      oldJkje.value = val
    } else {
      jdFormData.value.jkje = oldJkje.value
    }
  } else {
    oldJkje.value = ''
  }
}

const validate = () => {
  return jdFormRef.value?.validate()
}

defineExpose({
  validate
})

onMounted(() => {
  if(!props.modelValue?.gzyXm) {
    jdFormData.value.gzyXm = curGzjz.value?.gzyxm || ''
  }
})

</script>
