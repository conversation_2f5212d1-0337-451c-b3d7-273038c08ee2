<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="出借人" prop="cjrLx">
              <el-select v-model="queryParams.cjrLx" placeholder="请选择出借人" clearable >
                <el-option v-for="dict in gz_cjr_lx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="合同金额币种" prop="htJebz">
              <el-select v-model="queryParams.htJebz" placeholder="请选择合同金额币种" clearable >
                <el-option v-for="dict in gz_tc_bz" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="合同" prop="htJe">
              <el-input v-model="queryParams.htJe" placeholder="请输入合同" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合同利率类型" prop="htLllx">
              <el-select v-model="queryParams.htLllx" placeholder="请选择合同利率类型" clearable >
                <el-option v-for="dict in gz_ht_lllx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="借款合同利率" prop="htJkll">
              <el-input v-model="queryParams.htJkll" placeholder="请输入借款合同利率" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="借款期限起" prop="jkqxStart">
              <el-date-picker clearable
                v-model="queryParams.jkqxStart"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择借款期限起"
              />
            </el-form-item>
            <el-form-item label="借款期限止" prop="jkqxEnd">
              <el-date-picker clearable
                v-model="queryParams.jkqxEnd"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择借款期限止"
              />
            </el-form-item>
            <el-form-item label="借款编号" prop="jkbh">
              <el-input v-model="queryParams.jkbh" placeholder="请输入借款编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzFq:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzFq:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzFq:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzFq:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzFqList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="公证卷宗公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="债务人" align="center" prop="zwrIds" />
        <el-table-column label="债权人" align="center" prop="zqrIds" />
        <el-table-column label="代理人" align="center" prop="dlrIds" />
        <el-table-column label="担保人" align="center" prop="dbrIds" />
        <el-table-column label="出借人" align="center" prop="cjrLx">
          <template #default="scope">
            <dict-tag :options="gz_cjr_lx" :value="scope.row.cjrLx"/>
          </template>
        </el-table-column>
        <el-table-column label="担保方式" align="center" prop="dbfs">
          <template #default="scope">
            <dict-tag :options="gz_dbfs" :value="scope.row.dbfs ? scope.row.dbfs.split(',') : []"/>
          </template>
        </el-table-column>
        <el-table-column label="合同金额币种" align="center" prop="htJebz">
          <template #default="scope">
            <dict-tag :options="gz_tc_bz" :value="scope.row.htJebz"/>
          </template>
        </el-table-column>
        <el-table-column label="合同" align="center" prop="htJe" />
        <el-table-column label="合同利率类型" align="center" prop="htLllx">
          <template #default="scope">
            <dict-tag :options="gz_ht_lllx" :value="scope.row.htLllx"/>
          </template>
        </el-table-column>
        <el-table-column label="借款合同利率" align="center" prop="htJkll" />
        <el-table-column label="借款期限起" align="center" prop="jkqxStart" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.jkqxStart, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="借款期限止" align="center" prop="jkqxEnd" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.jkqxEnd, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="借款编号" align="center" prop="jkbh" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzFq:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzFq:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-赋强对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzFqFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" />
        </el-form-item>
        <el-form-item label="出借人" prop="cjrLx">
          <el-select v-model="form.cjrLx" placeholder="请选择出借人">
            <el-option
                v-for="dict in gz_cjr_lx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="担保方式" prop="dbfs">
          <el-checkbox-group v-model="form.dbfs">
            <el-checkbox
                v-for="dict in gz_dbfs"
                :key="dict.value"
                :label="dict.value">
                {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="合同金额币种" prop="htJebz">
          <el-select v-model="form.htJebz" placeholder="请选择合同金额币种">
            <el-option
                v-for="dict in gz_tc_bz"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合同" prop="htJe">
          <el-input v-model="form.htJe" placeholder="请输入合同" />
        </el-form-item>
        <el-form-item label="合同利率类型" prop="htLllx">
          <el-select v-model="form.htLllx" placeholder="请选择合同利率类型">
            <el-option
                v-for="dict in gz_ht_lllx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="借款合同利率" prop="htJkll">
          <el-input v-model="form.htJkll" placeholder="请输入借款合同利率" />
        </el-form-item>
        <el-form-item label="借款期限起" prop="jkqxStart">
          <el-date-picker clearable
            v-model="form.jkqxStart"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择借款期限起">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="借款期限止" prop="jkqxEnd">
          <el-date-picker clearable
            v-model="form.jkqxEnd"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择借款期限止">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="借款编号" prop="jkbh">
          <el-input v-model="form.jkbh" placeholder="请输入借款编号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzFq" lang="ts">
import { listGzjzFq, getGzjzFq, delGzjzFq, addGzjzFq, updateGzjzFq } from '@/api/gongzheng/bzfz/gzjzFq';
import { GzjzFqVO, GzjzFqQuery, GzjzFqForm } from '@/api/gongzheng/bzfz/gzjzFq/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_ht_lllx, gz_cjr_lx, gz_dbfs, gz_tc_bz } = toRefs<any>(proxy?.useDict('gz_ht_lllx', 'gz_cjr_lx', 'gz_dbfs', 'gz_tc_bz'));

const gzjzFqList = ref<GzjzFqVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzFqFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzFqForm = {
  id: undefined,
  gzjzId: undefined,
  gzjzGzsxId: undefined,
  zwrIds: undefined,
  zqrIds: undefined,
  dlrIds: undefined,
  dbrIds: undefined,
  cjrLx: undefined,
  dbfs: [],
  htJebz: undefined,
  htJe: undefined,
  htLllx: undefined,
  htJkll: undefined,
  jkqxStart: undefined,
  jkqxEnd: undefined,
  jkbh: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzFqForm, GzjzFqQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    gzjzGzsxId: undefined,
    zwrIds: undefined,
    zqrIds: undefined,
    dlrIds: undefined,
    dbrIds: undefined,
    cjrLx: undefined,
    dbfs: undefined,
    htJebz: undefined,
    htJe: undefined,
    htLllx: undefined,
    htJkll: undefined,
    jkqxStart: undefined,
    jkqxEnd: undefined,
    jkbh: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-赋强列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzFq(queryParams.value);
  gzjzFqList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzFqFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzFqVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-赋强";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzFqVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzFq(_id);
  Object.assign(form.value, res.data);
  form.value.dbfs = form.value.dbfs.split(",");
  dialog.visible = true;
  dialog.title = "修改公证卷宗-赋强";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzFqFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
        form.value.dbfs = form.value.dbfs.join(",");
      if (form.value.id) {
        await updateGzjzFq(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzFq(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzFqVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-赋强编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzFq(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzFq/export', {
    ...queryParams.value
  }, `gzjzFq_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
