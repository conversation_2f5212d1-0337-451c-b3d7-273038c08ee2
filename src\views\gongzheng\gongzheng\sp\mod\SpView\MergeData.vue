<template>
  <el-card class="merge-card">
    <el-tabs v-model="mergeState.activeTab" @tab-change="tabChange" type="border-card" class="h-full">
      <el-tab-pane label="公证书" name="gzs" class="h-full">
        <GzsList :data="mergeState.gzsxList" v-loading="mergeState.gzsLoading" />
      </el-tab-pane>
      <el-tab-pane label="文书" name="ws" class="h-full">
        <WsList :data="mergeState.wsList" v-loading="mergeState.wsLoading" />
      </el-tab-pane>
      <el-tab-pane label="笔录" name="bl" class="h-full">
        <BlList :data="mergeState.blList" v-loading="mergeState.blLoading" />
      </el-tab-pane>
      <!-- <el-tab-pane label="证据材料" name="zjcl" class="h-full">
        <ZjclList :data="mergeState.zjclList" v-loading="mergeState.zjclLoading" />
      </el-tab-pane> -->
      <el-tab-pane label="其他文档" name="qtwd" class="h-full">
        <QtList :data="mergeState.qtList" v-loading="mergeState.qtLoading" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed, onMounted, type Ref } from 'vue';
import GzsList from './GzsList.vue';
import WsList from './WsList.vue';
import BlList from './BlList.vue';
import ZjclList from './ZjclList.vue';
import QtList from './QtList.vue';
import { listGzjzZmclxx, queryGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';
import { listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { nodeFilter } from '@/utils/ruoyi';
import { wdndDocTableData, blDocTableData, wsDocTableData, picDocTableData, qfgDocTableData, type DocListItem } from '@/views/gongzheng/gongzheng/components/sl/preset_data'


interface Props {
  gzjzId?: string | number;
  oneOff?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  gzjzId: '',
});

const mergeState = reactive({
  activeTab: 'gzs',
  gzsLoading: false,
  gzsxList: [],
  gzsList: [],
  wsLoading: false,
  wsList: [],
  blLoading: false,
  blList: [],
  zjclLoading: false,
  zjclList: [],
  qtLoading: false,
  qtList: [
    ...wdndDocTableData,
    ...picDocTableData,
    ...qfgDocTableData
  ],
})

const allDocList = ref([])

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

// 加载公证事项列表
async function loadGzsxList() {
  if (!curGzjz.value.id && !currentRecordId.value) {
    mergeState.gzsxList = []
    return;
  };
  mergeState.gzsLoading = true;
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      mergeState.gzsxList = res.rows || [];
      placeGzsDoc();
    }

  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
    mergeState.gzsLoading = false;
  }
}

// 加载代书文档列表
async function loadDsList() {
  try {
    mergeState.wsLoading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      fjlb: '4',   //  附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      init: true,
    }
    const res = await queryGzjzZmclxx(params);
    if (res.code === 200) {
      mergeState.wsList = res.data || [];
    }
  } catch (err: any) {
    ElMessage.error(`获取代书列表失败: ${err.message}`)
  } finally {
    mergeState.wsLoading = false;
  }
}

// 加载证明材料列表
async function loadZjclList() {
  try {
    mergeState.zjclLoading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      fjlb: '2',   // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      pageNum: 1,
      pageSize: 200
    }
    const res = await listGzjzZmclxx(params);
    if (res.code === 200) {
      mergeState.zjclList = res.data || [];
    }
  } catch (err: any) {
    ElMessage.error(`获取代书列表失败: ${err.message}`)
  } finally {
    mergeState.zjclLoading = false;
  }
}

// 加载代书文档列表
async function loadQtList() {
  try {
    mergeState.qtLoading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      fjlb: '9',   //  附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      init: true,
    }
    const res = await queryGzjzZmclxx(params);
    if (res.code === 200) {
      mergeState.qtList = res.data || [];
    }
  } catch (err: any) {
    ElMessage.error(`获取代书列表失败: ${err.message}`)
  } finally {
    mergeState.qtLoading = false;
  }
}


/**======================================================================= */

const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 200,
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      allDocList.value = res.rows || [];

      filterWsDoc();
      filterBlDoc();
      placeQtDoc();
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

// 其它文档
const otherDocList = () => {
  const otherList = [
    ...wdndDocTableData,
    ...picDocTableData,
    ...qfgDocTableData
  ]
}

// 分配文档文件
const placeDoc = (refList: Ref) => {
  //docList
  refList.value.forEach(item => {
    const { matches, noMatches } = nodeFilter(allDocList.value, (node) => {
      const obj = JSON.parse(node.wblj);
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    allDocList.value = noMatches;
  })
}

const placeQtDoc = () => {
  //docList
  mergeState.qtList.forEach(item => {
    const { matches, noMatches } = nodeFilter(allDocList.value, (node) => {
      const obj = JSON.parse(node.wblj);
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    allDocList.value = noMatches;
  })
}

const placeGzsDoc = () => {
  mergeState.gzsxList = mergeState.gzsxList.map((item: any) => {

    const { matches, noMatches } = nodeFilter(allDocList.value, (node) => {
      const obj = JSON.parse(node.wblj || node.ywlj || '{}');
      return (node.lx == '3') || (item.id === node.gzjzGzsxId);
    });

    if(matches.length > 0) {
      item.gzsFile = matches[0];
    } else {
      item.gzsFile = null;
    }
    allDocList.value = noMatches;

    return item;
  })
}

// 过滤出文书相关文档
const filterWsDoc = () => {
  const { matches, noMatches } = nodeFilter(allDocList.value, (node) => {
    return node.lx == 1;
  })

  mergeState.wsList = matches;
  allDocList.value = noMatches;
}

const filterBlDoc = () => {
  const { matches, noMatches } = nodeFilter(allDocList.value, (node) => {
    return node.lx == 2;
  })

  mergeState.blList = matches;
  allDocList.value = noMatches;
}

async function oneOffReq() {
  loadGzsxList();
  // loadDsList();
  // loadZjclList();
  // loadQtList();
}

function tabSwitch(tabName: string) {
  switch (tabName) {
    case 'gzs':
      loadGzsxList();
    break;

    case 'ws':
      loadDsList();
    break;

    case 'zjcl':
      loadZjclList();
    break;

    case 'qtwd':
      loadQtList();
    break;
  }
}

function tabChange(tabName: string) {
  if (!props.oneOff) {
    tabSwitch(tabName);
  }
}

async function init() {
  await loadDocList();
  oneOffReq();
  // if(props.oneOff) {
  // } else {
  //   tabSwitch(mergeState.activeTab)
  // }
}

onMounted(() => {
  init();
  console.log('mergeState', mergeState)
})

</script>

<style>
.merge-card > .el-card__body {
  height: 100%;
}
</style>
