export interface GzlbpzVO {

  /**
   * 公证事项
   */
  id : string | number;
  /**
   * 公证类别
   */
  gzlbValue : string;

  gzsxCode : string;
  title : string;
  level : string;
  jcsx : string;
  parentCode : string;
  temptreeCode : string;
  childCount : number;
  notarLevel : string;
  remark : string;
}

export interface GzlbpzForm extends BaseEntity {
  /**
   * 公证类别
   */
  gzlbValue ?: string;
  /**
   * 公证事项
   */
  id ?: string | number;
  gzsxCode ?: string;
  gzsxCodeList ?: Array<string | number>;

}

export interface GzlbpzQuery extends PageQuery {

  /**
   * 日期范围参数
   */
  params ?: any;
  /**
   * 公证类别
   */
  gzlbValue ?: string;
}
