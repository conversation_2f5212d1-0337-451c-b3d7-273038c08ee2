<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="110px">
            <el-form-item label="失信人/单位" prop="bdcdjh">
              <el-input v-model="queryParams.bdcdjh" placeholder="请输入失信人/单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号" prop="bdcSf">
              <el-input v-model="queryParams.bdcSf" placeholder="请输入证件号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="失信类型" prop="bdcSf">
              <el-select v-model="queryParams.zt" placeholder="请选择失信类型" clearable>
                <el-option v-for="dict in gz_sxlx" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable>
                <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="dsrxxHmdBdcList" >
        <el-table-column label="查获机构" align="center" prop="dz" />
        <el-table-column label="查获时间" align="center" prop="bdcdjh" />
        <el-table-column label="失信类型" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_sxlx" :value="scope.row.zt" />
          </template>
        </el-table-column>
        <el-table-column label="失信人/单位" align="center" prop="bdcdjh" />
        <el-table-column label="证件类型" align="center" prop="bdcdjh" />
        <el-table-column label="证件号码" align="center" prop="bdcdjh" />
        <el-table-column label="状态" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_hmd_zt" :value="scope.row.zt" />
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

  </div>
</template>

<script setup name="Gqhmd" lang="ts">
  import { listDsrxxHmdBdc, getDsrxxHmdBdc, delDsrxxHmdBdc, addDsrxxHmdBdc, updateDsrxxHmdBdc } from '@/api/gongzheng/dsr/dsrxxHmdBdc';
  import { DsrxxHmdBdcVO, DsrxxHmdBdcQuery, DsrxxHmdBdcForm } from '@/api/gongzheng/dsr/dsrxxHmdBdc/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_hmd_zt,gz_sxlx } = toRefs<any>(proxy?.useDict('gz_hmd_zt','gz_sxlx'));


  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const img = ref(null);
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxHmdBdcFormRef = ref<ElFormInstance>();
  const dsrxxHmdBdcList = ref([])
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initFormData : DsrxxHmdBdcForm = {
    id: undefined,
    bdcdjh: undefined,
    dz: undefined,
    bdcSf: undefined,
    bdcSq: undefined,
    bdcQ: undefined,
    zt: undefined,
    jgbs: undefined,
    jgbm: undefined,
    jgmc: undefined,
    chrq: undefined,
    gzsx: undefined,
    cqr: undefined,
    sqr: undefined,
    sqrsfz: undefined,
    qksm: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<DsrxxHmdBdcForm, DsrxxHmdBdcQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,

      params: {
      }
    },
    rules: {

    }
  });

  const { queryParams, form, rules } = toRefs(data);
  const getList = () => {
    loading.value = false;
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }
  onMounted(() => {
    getList();
  });
</script>

<style>
</style>
