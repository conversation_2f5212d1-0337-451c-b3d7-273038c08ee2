<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px" size="small" v-show="showSearch" class="search-form">
      <el-form-item label="卷宗号：" prop="jzh">
        <el-input v-model="queryParams.jzh" placeholder="请输入卷宗号" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="申请人：" prop="tslcFqr">
        <el-input v-model="queryParams.tslcFqr" placeholder="请输入申请人" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="申请日期：" prop="tslcFqrq">
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 240px" />
      </el-form-item>
     <!-- <el-form-item label="状态：" prop="tslcZt">
        <el-select v-model="queryParams.tslcZt" placeholder="请选择状态" style="width: 100px">
          <el-option v-for="dict in gz_tslc_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="类型：" prop="tslcLx">
        <el-select v-model="queryParams.tslcLx" placeholder="请选择类型" clearable style="width: 140px">
          <el-option v-for="dict in gz_tslc_lx" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" v-has-permi="['tslc:fq:query']">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 审批列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">审批列表</div>
      </div>

      <el-table v-loading="loading" :data="approvalList" border style="width: 100%">
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column label="操作" width="140" align="center">
          <template #default="scope">
            <el-button v-has-permi="['tslc:fq:query']" type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button v-has-permi="['tslc:fq:edit']" v-if="scope.row.tslcZt=='1'" type="danger" link @click="handleReview(scope.row)">审批</el-button>
          </template>
        </el-table-column>
        <el-table-column label="卷宗号" align="center" prop="jzh" />
        <el-table-column label="类型" align="center" prop="tslcLx">
          <template #default="scope">
            <dict-tag :options="gz_tslc_lx" :value="scope.row.tslcLx" />
          </template>
        </el-table-column>
        <el-table-column label="申请人" align="center" prop="tslcFqr" />
        <el-table-column label="申请时间" align="center" prop="tslcFqrq" width="180" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="状态" align="center" prop="tslcZt" width="100">
          <template #default="scope">
            <dict-tag :options="gz_tslc_zt" :value="scope.row.tslcZt" />
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        :total="total" @pagination="getList" />
    </div>

    <!-- 详情弹窗 -->
    <PopTslcDetail v-if="showDetail" ref="detailRef" @close="showDetail=false" />
    <!-- 审批弹窗 -->
    <PopTslcReview v-if="showReview" ref="reviewRef" @close="showReview=false" @success="getList" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import * as api from '@/api/gongzheng/tslc/tslcSqb'
  import PopTslcDetail from './components/popTslcDetail.vue'
  import PopTslcReview from './components/popTslcReview.vue'
  import { TslcSqbQuery, TslcSqbVO } from '@/api/gongzheng/tslc/tslcSqb/types'

  // 显示搜索条件
  const showSearch = ref(true)

  // 加载状态
  const loading = ref(false)

  // 字典
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_tslc_zt, gz_tslc_lx } = toRefs<any>(proxy?.useDict('gz_tslc_zt', 'gz_tslc_lx'))

  // 查询参数（与后端一致）
  const queryParams = reactive<TslcSqbQuery>({
    pageNum: 1,
    pageSize: 10,
    jzh: '',
    tslcFqr: '',
    tslcLx: '',
    tslcZt: '1',
    params: {}
  } as unknown as TslcSqbQuery)

  // 日期范围
  const dateRange = ref<[string, string] | null>(null)

  // 分页总数
  const total = ref(0)

  // 审批列表数据
  const approvalList = ref<TslcSqbVO[]>([])

  // 详情弹窗引用
  const showDetail = ref(false)
  const detailRef = ref<InstanceType<typeof PopTslcDetail> | null>(null)
  const showReview = ref(false)
  const reviewRef = ref<InstanceType<typeof PopTslcReview> | null>(null)

  // 查询审批列表
  const getList = async () => {
    loading.value = true
    try {
      // 固定状态筛选为 1
      queryParams.tslcZt = '1'

      // 处理日期范围
      if (dateRange.value && dateRange.value.length === 2) {
        queryParams.params = {
          beginTslcFqrq: dateRange.value[0],
          endTslcFqrq: dateRange.value[1]
        }
      } else {
        queryParams.params = {}
      }

      const res : any = await api.listTslcSqb(queryParams)
      approvalList.value = res.rows || []
      total.value = res.total || 0
    } catch (error) {
      console.error('获取列表失败', error)
      ElMessage.error('获取列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索按钮操作
  const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
  }

  // 重置按钮操作
  const resetQuery = () => {
    dateRange.value = null
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      jzh: '',
      tslcFqr: '',
      tslcLx: '',
      tslcZt: '1',
      params: {}
    })
    getList()
  }

  // 查看详情
  const handleView = (row : any) => {
    showDetail.value = true
    proxy?.$nextTick(() => {
      detailRef.value?.open({ id: row.id })
    })
  }
  //审查
  const handleReview = (row : any) => {
    showReview.value = true
    proxy?.$nextTick(() => {
      reviewRef.value?.open({ id: row.id })
    })
  }
  // 组件挂载时
  onMounted(() => {
    // 初始加载数据
    handleQuery()
  })
</script>

<style scoped>
  .app-container {
    padding: 15px;
  }

  .search-form {
    background-color: #fff;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 4px;
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .table-title {
    font-size: 16px;
    font-weight: bold;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .pagination-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .page-input {
    width: 50px;
  }

  .page-size-select {
    width: 80px;
    margin-left: 10px;
  }
</style>
