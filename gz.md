# 公证系统前端代码规则与最佳实践

## 📁 目录结构规则

### 1. 组件目录分类

#### `src/views/components/` - 全局业务组件
- **用途**：存放跨业务模块的通用组件
- **特点**：
  - `popIdCardReader.vue` - 身份证读卡器组件（硬件设备交互）
  - `selectors/select-dict.vue` - 数据字典选择组件（表单控件）
- **命名规范**：组件名使用 `camelCase`，文件名使用 `kebab-case`

#### `src/views/temp/` - 临时/演示组件
- **用途**：存放临时开发、测试、演示的组件
- **特点**：
  - `compDemo/` - 组件演示目录
  - 包含完整的组件使用示例和交互流程

### 2. 项目整体目录结构
```
gongzheng-vueadmin/
├─ src/
│  ├─ api/                    # API 接口目录
│  │  └─ gongzheng/          # 公证业务模块
│  │     ├─ basicdata/       # 基础数据
│  │     ├─ gongzheng/       # 公证业务核心
│  │     └─ ...              # 其他业务模块
│  ├─ views/                  # 页面目录
│  │  ├─ components/         # 全局业务组件
│  │  ├─ temp/               # 临时/演示组件
│  │  └─ gongzheng/          # 公证业务页面
│  ├─ components/            # 全局通用组件
│  └─ utils/                 # 工具函数
```

## 🛠️ 技术栈规范

### 1. 核心技术
- **Vue 3** + **TypeScript** + **Composition API**
- **Element Plus** - 基础 UI 组件库
- **VXE Table** - 表格和弹窗组件
- **SignalR** - 实时通信（硬件设备交互）
- **Vite** - 构建工具

### 2. 开发规范
- **ESLint** + **Prettier** - 代码格式化
- **TypeScript** - 严格类型检查
- **Vue 3** - 最新语法和特性

## 📝 组件开发规范

### 1. Vue3 Composition API 标准结构

```typescript
<template>
  <!-- 组件模板 -->
</template>

<script setup name="ComponentName" lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  
  // Props 类型定义
  interface Props {
    title?: string
    width?: string
    disabled?: boolean
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '默认标题',
    width: '100%',
    disabled: false
  });

  // Emits 类型定义
  const emits = defineEmits<{
    (event: 'success', data?: any): void
    (event: 'close'): void
    (event: 'update:modelValue', value: any): void
  }>()

  // 响应式数据
  const showPopup = ref(false)
  const isLoading = ref(false)
  const dataList = ref([])

  // 生命周期
  onMounted(() => {
    console.log('Component mounted');
  });

  onUnmounted(() => {
    console.log('Component unmounted');
  });

  // 组件方法
  const open = (option = {}) => {
    showPopup.value = true;
  }

  const close = () => {
    showPopup.value = false;
  }

  // 暴露方法给父组件
  defineExpose({
    open, close
  })
</script>

<style scoped>
  /* 组件样式 */
</style>
```

### 2. 弹窗组件标准模式
文件命名 以 pop 开头

```typescript
// 弹窗组件标准结构
const showPopup = ref(false)
const modalOptions = reactive<VxeModalProps>({
  title: props.title,
  width: '650px',
  height: '650px',
  escClosable: true,
  resize: true,
  showMaximize: true,
  destroyOnClose: true
})

const open = (option = {}) => {
  console.log('open-dialog', option);
  showPopup.value = true;
}

const close = () => {
  showPopup.value = false;
  emits('close');
}
```

### 3. 表单组件标准模式

```typescript
// 表单组件标准结构
const formData = reactive({
  name: '',
  age: 0,
  email: ''
})

const formRules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    // 提交逻辑
    emits('success', formData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}
```

## 🔄 组件通信规范

### 1. 父子组件通信

#### 父组件调用子组件方法
```typescript
// 父组件
const popDetailRef = ref<InstanceType<typeof popDetail> | null>(null)

const handleShowDetail = () => {
  popDetailRef.value?.open({ id: 1000 });
}
```

#### 子组件向父组件发送事件
```typescript
// 子组件
const emits = defineEmits<{
  (event: 'success', data?: any): void
  (event: 'close'): void
}>()

const handleSave = () => {
  emits('success', formData);
  close();
}
```

### 2. 组件生命周期管理

```typescript
// 按需加载组件
const showIdCardReader = ref(false)

const handleShowIdCardReader = () => {
  // 使用的时候才加载组件，不用的时候卸载组件，释放数据
  showIdCardReader.value = true;
  proxy.$nextTick(() => {
    popIdCardReaderRef.value?.open({});
  })
}
```

## 🎯 业务组件特点

### 1. 硬件交互组件（如身份证读卡器）

```typescript
// SignalR 连接管理
let connection = new signalR.HubConnectionBuilder()
  .withUrl(`http://localhost:32388/device`, {
    accessTokenFactory: () => 'mytoken'
  })
  .configureLogging(signalR.LogLevel.Error)
  .build();

// 自动重连机制
connection.onclose(async () => {
  isConnected.value = false;
  if (autoReconnect.value === true) {
    doConnect();
  }
});

// 设备状态管理
const isConnected = ref(false)
const isConnecting = ref(false)
const isReading = ref(false)
```

### 2. 表单控件组件（如字典选择器）

```typescript
// 支持多种显示模式
const displayType = ref<'select' | 'radio'>('select')

// 数据加载状态管理
const isLoading = ref(false)
const compDataList = ref([])

// 双向数据绑定
const selectedIds = ref(props.modelValue)

watch(() => props.modelValue, (newVal) => {
  selectedIds.value = newVal;
  emits('changed');
})
```

## 🚀 性能优化规范

### 1. 组件懒加载
```typescript
// 按需加载组件
const showComponent = ref(false)

// 使用 v-if 控制组件渲染
<MyComponent v-if="showComponent" />
```

### 2. 资源清理
```typescript
onUnmounted(() => {
  // 清理定时器
  if (timer) {
    clearTimeout(timer);
  }
  
  // 断开连接
  if (connection) {
    connection.stop();
  }
  
  // 清理事件监听
  window.removeEventListener('resize', handleResize);
});
```

### 3. 数据缓存
```typescript
// 缓存数据避免重复请求
const cache = new Map()

const getData = async (key: string) => {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const data = await api.getData(key);
  cache.set(key, data);
  return data;
}
```

## 🛡️ 错误处理规范

### 1. 异步操作错误处理
```typescript
const requestData = async () => {
  isLoading.value = true;
  try {
    const res = await getDicts(proxy.dictType);
    compDataList.value = res.data || [];
    emits('ready');
  } catch (error) {
    console.error('请求失败:', error);
    ElMessage.error('数据加载失败');
  } finally {
    isLoading.value = false;
  }
}
```

### 2. 表单验证错误处理
```typescript
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    // 提交成功逻辑
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单信息');
  }
}
```

## 📋 代码质量规范

### 1. 命名规范
- **组件名**：使用 `PascalCase`（如：`PopIdCardReader`）
- **文件名**：使用 `kebab-case`（如：`pop-id-card-reader.vue`）
- **变量名**：使用 `camelCase`（如：`showPopup`）
- **常量名**：使用 `UPPER_SNAKE_CASE`（如：`API_BASE_URL`）

### 2. 类型定义规范
```typescript
// API 响应类型
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 组件 Props 类型
interface ComponentProps {
  title?: string;
  width?: string;
  disabled?: boolean;
}

// 组件 Emits 类型
interface ComponentEmits {
  (event: 'success', data?: any): void;
  (event: 'close'): void;
}
```

### 3. 注释规范
```typescript
/**
 * 身份证读卡器组件
 * @description 用于读取身份证信息的弹窗组件
 * <AUTHOR>
 * @date 2024-01-01
 */

// 连接读卡器设备
const doConnect = async () => {
  // 防止重复连接
  if (isConnected.value === true || isConnecting.value === true) {
    return;
  }
  
  // 连接逻辑...
}
```

## 🔧 开发工具配置

### 1. ESLint 配置
```json
{
  "extends": [
    "@vue/typescript/recommended",
    "@vue/prettier",
    "@vue/prettier/@typescript-eslint"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off"
  }
}
```

### 2. TypeScript 配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true
  }
}
```

## 📚 最佳实践总结

### 1. 组件设计原则
- **单一职责**：每个组件只负责一个功能
- **可复用性**：通过 Props 配置组件行为
- **可扩展性**：通过 Emits 支持自定义事件
- **类型安全**：完整的 TypeScript 类型定义

### 2. 代码组织原则
- **逻辑分离**：业务逻辑、UI 逻辑、数据逻辑分离
- **命名规范**：语义化的变量和函数命名
- **结构清晰**：template、script、style 结构清晰

### 3. 性能优化原则
- **按需加载**：组件和数据按需加载
- **资源清理**：及时清理定时器、连接等资源
- **缓存策略**：合理使用缓存避免重复请求

### 4. 错误处理原则
- **统一处理**：统一的错误提示和处理机制
- **用户友好**：错误信息对用户友好
- **日志记录**：关键错误需要记录日志

---

*本文档基于公证系统前端代码分析总结，适用于 Vue3 + TypeScript + Element Plus 技术栈的项目开发。*
