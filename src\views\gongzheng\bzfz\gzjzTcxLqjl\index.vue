<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="提存项ID" prop="tcxId">
              <el-input v-model="queryParams.tcxId" placeholder="请输入提存项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeSqsj"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="是否领取孳息" prop="sflqcx">
              <el-select v-model="queryParams.sflqcx" placeholder="请选择是否领取孳息" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="领取方式" prop="lqfs">
              <el-select v-model="queryParams.lqfs" placeholder="请选择领取方式" clearable >
                <el-option v-for="dict in gz_tc_lqfs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="领取人ID" prop="lqrId">
              <el-input v-model="queryParams.lqrId" placeholder="请输入领取人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开户行" prop="khh">
              <el-select v-model="queryParams.khh" placeholder="请选择开户行" clearable >
                <el-option v-for="dict in gz_tc_khh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="银行卡号" prop="yhkh">
              <el-input v-model="queryParams.yhkh" placeholder="请输入银行卡号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请理由" prop="sqly">
              <el-input v-model="queryParams.sqly" placeholder="请输入申请理由" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人ID" prop="sqrId">
              <el-input v-model="queryParams.sqrId" placeholder="请输入申请人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人" prop="sqr">
              <el-input v-model="queryParams.sqr" placeholder="请输入申请人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable >
                <el-option v-for="dict in gz_tc_zt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="代领取人姓名" prop="dlLqrxm">
              <el-input v-model="queryParams.dlLqrxm" placeholder="请输入代领取人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="代领取人联系号码" prop="dlLxhm">
              <el-input v-model="queryParams.dlLxhm" placeholder="请输入代领取人联系号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="代领取人证件类型" prop="dlZjlx">
              <el-input v-model="queryParams.dlZjlx" placeholder="请输入代领取人证件类型" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="代领取人证件号码" prop="dlZjhm">
              <el-input v-model="queryParams.dlZjhm" placeholder="请输入代领取人证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzTcxLqjl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzTcxLqjl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzTcxLqjl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzTcxLqjl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzTcxLqjlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="提存项ID" align="center" prop="tcxId" />
        <el-table-column label="申请时间" align="center" prop="sqsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sqsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请额度" align="center" prop="sqed" />
        <el-table-column label="是否领取孳息" align="center" prop="sflqcx">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sflqcx"/>
          </template>
        </el-table-column>
        <el-table-column label="领取方式" align="center" prop="lqfs">
          <template #default="scope">
            <dict-tag :options="gz_tc_lqfs" :value="scope.row.lqfs"/>
          </template>
        </el-table-column>
        <el-table-column label="领取人ID" align="center" prop="lqrId" />
        <el-table-column label="开户行" align="center" prop="khh">
          <template #default="scope">
            <dict-tag :options="gz_tc_khh" :value="scope.row.khh"/>
          </template>
        </el-table-column>
        <el-table-column label="银行卡号" align="center" prop="yhkh" />
        <el-table-column label="申请理由" align="center" prop="sqly" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="申请人ID" align="center" prop="sqrId" />
        <el-table-column label="申请人" align="center" prop="sqr" />
        <el-table-column label="状态" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_tc_zt" :value="scope.row.zt"/>
          </template>
        </el-table-column>
        <el-table-column label="代领取人姓名" align="center" prop="dlLqrxm" />
        <el-table-column label="代领取人联系号码" align="center" prop="dlLxhm" />
        <el-table-column label="代领取人证件类型" align="center" prop="dlZjlx" />
        <el-table-column label="代领取人证件号码" align="center" prop="dlZjhm" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzTcxLqjl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzTcxLqjl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改提存项-领取记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzTcxLqjlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="提存项ID" prop="tcxId">
          <el-input v-model="form.tcxId" placeholder="请输入提存项ID" />
        </el-form-item>
        <el-form-item label="申请时间" prop="sqsj">
          <el-date-picker clearable
            v-model="form.sqsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择申请时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="申请额度" prop="sqed">
          <el-input v-model="form.sqed" placeholder="请输入申请额度" />
        </el-form-item>
        <el-form-item label="是否领取孳息" prop="sflqcx">
          <el-select v-model="form.sflqcx" placeholder="请选择是否领取孳息">
            <el-option
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="领取方式" prop="lqfs">
          <el-select v-model="form.lqfs" placeholder="请选择领取方式">
            <el-option
                v-for="dict in gz_tc_lqfs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="领取人ID" prop="lqrId">
          <el-input v-model="form.lqrId" placeholder="请输入领取人ID" />
        </el-form-item>
        <el-form-item label="开户行" prop="khh">
          <el-select v-model="form.khh" placeholder="请选择开户行">
            <el-option
                v-for="dict in gz_tc_khh"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="银行卡号" prop="yhkh">
          <el-input v-model="form.yhkh" placeholder="请输入银行卡号" />
        </el-form-item>
        <el-form-item label="申请理由" prop="sqly">
            <el-input v-model="form.sqly" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="申请人ID" prop="sqrId">
          <el-input v-model="form.sqrId" placeholder="请输入申请人ID" />
        </el-form-item>
        <el-form-item label="申请人" prop="sqr">
          <el-input v-model="form.sqr" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="状态" prop="zt">
          <el-select v-model="form.zt" placeholder="请选择状态">
            <el-option
                v-for="dict in gz_tc_zt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="代领取人姓名" prop="dlLqrxm">
          <el-input v-model="form.dlLqrxm" placeholder="请输入代领取人姓名" />
        </el-form-item>
        <el-form-item label="代领取人联系号码" prop="dlLxhm">
          <el-input v-model="form.dlLxhm" placeholder="请输入代领取人联系号码" />
        </el-form-item>
        <el-form-item label="代领取人证件类型" prop="dlZjlx">
          <el-input v-model="form.dlZjlx" placeholder="请输入代领取人证件类型" />
        </el-form-item>
        <el-form-item label="代领取人证件号码" prop="dlZjhm">
          <el-input v-model="form.dlZjhm" placeholder="请输入代领取人证件号码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzTcxLqjl" lang="ts">
import { listGzjzTcxLqjl, getGzjzTcxLqjl, delGzjzTcxLqjl, addGzjzTcxLqjl, updateGzjzTcxLqjl } from '@/api/gongzheng/bzfz/gzjzTcxLqjl';
import { GzjzTcxLqjlVO, GzjzTcxLqjlQuery, GzjzTcxLqjlForm } from '@/api/gongzheng/bzfz/gzjzTcxLqjl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yes_or_no, gz_tc_lqfs, gz_tc_khh, gz_tc_zt } = toRefs<any>(proxy?.useDict('gz_yes_or_no', 'gz_tc_lqfs', 'gz_tc_khh', 'gz_tc_zt'));

const gzjzTcxLqjlList = ref<GzjzTcxLqjlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeSqsj = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const gzjzTcxLqjlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzTcxLqjlForm = {
  id: undefined,
  tcxId: undefined,
  sqsj: undefined,
  sqed: undefined,
  sflqcx: undefined,
  lqfs: undefined,
  lqrId: undefined,
  khh: undefined,
  yhkh: undefined,
  sqly: undefined,
  remark: undefined,
  sqrId: undefined,
  sqr: undefined,
  zt: undefined,
  dlLqrxm: undefined,
  dlLxhm: undefined,
  dlZjlx: undefined,
  dlZjhm: undefined
}
const data = reactive<PageData<GzjzTcxLqjlForm, GzjzTcxLqjlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tcxId: undefined,
    sflqcx: undefined,
    lqfs: undefined,
    lqrId: undefined,
    khh: undefined,
    yhkh: undefined,
    sqly: undefined,
    sqrId: undefined,
    sqr: undefined,
    zt: undefined,
    dlLqrxm: undefined,
    dlLxhm: undefined,
    dlZjlx: undefined,
    dlZjhm: undefined,
    params: {
      sqsj: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    tcxId: [
      { required: true, message: "提存项ID不能为空", trigger: "blur" }
    ],
    sqsj: [
      { required: true, message: "申请时间不能为空", trigger: "blur" }
    ],
    sqed: [
      { required: true, message: "申请额度不能为空", trigger: "blur" }
    ],
    lqfs: [
      { required: true, message: "领取方式不能为空", trigger: "change" }
    ],
    lqrId: [
      { required: true, message: "领取人ID不能为空", trigger: "blur" }
    ],
    sqrId: [
      { required: true, message: "申请人ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询提存项-领取记录列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSqsj.value, 'Sqsj');
  const res = await listGzjzTcxLqjl(queryParams.value);
  gzjzTcxLqjlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzTcxLqjlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeSqsj.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzTcxLqjlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加提存项-领取记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzTcxLqjlVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzTcxLqjl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改提存项-领取记录";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzTcxLqjlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzTcxLqjl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzTcxLqjl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzTcxLqjlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除提存项-领取记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzTcxLqjl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzTcxLqjl/export', {
    ...queryParams.value
  }, `gzjzTcxLqjl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
