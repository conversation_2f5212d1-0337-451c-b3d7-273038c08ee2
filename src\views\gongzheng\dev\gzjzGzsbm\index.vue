<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="queryParams.gzsbh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="年份" prop="nf">
              <el-input v-model="queryParams.nf" placeholder="请输入年份" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="字号" prop="zh">
              <el-select v-model="queryParams.zh" placeholder="请选择字号" clearable >
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="流水号" prop="ls">
              <el-input v-model="queryParams.ls" placeholder="请输入流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否废号" prop="sfZf">
              <el-select v-model="queryParams.sfZf" placeholder="请选择是否废号" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzGzsbm:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzGzsbm:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzGzsbm:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzGzsbm:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzGzsbmList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="年份" align="center" prop="nf" />
        <el-table-column label="字号" align="center" prop="zh">
          <template #default="scope">
            <dict-tag :options="gz_gzs_zh" :value="scope.row.zh"/>
          </template>
        </el-table-column>
        <el-table-column label="流水号" align="center" prop="ls" />
        <el-table-column label="是否废号" align="center" prop="sfZf">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfZf"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzGzsbm:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzGzsbm:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证书编号v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzGzsbmFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsbh">
          <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="年份" prop="nf">
          <el-input v-model="form.nf" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="字号" prop="zh">
          <el-select v-model="form.zh" placeholder="请选择字号">
            <el-option
                v-for="dict in gz_gzs_zh"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流水号" prop="ls">
          <el-input v-model="form.ls" placeholder="请输入流水号" />
        </el-form-item>
        <el-form-item label="是否废号" prop="sfZf">
          <el-radio-group v-model="form.sfZf">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzGzsbm" lang="ts">
import { listGzjzGzsbm, getGzjzGzsbm, delGzjzGzsbm, addGzjzGzsbm, updateGzjzGzsbm } from '@/api/gongzheng/dev/gzjzGzsbm';
import { GzjzGzsbmVO, GzjzGzsbmQuery, GzjzGzsbmForm } from '@/api/gongzheng/dev/gzjzGzsbm/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yes_or_no, gz_gzs_zh } = toRefs<any>(proxy?.useDict('gz_yes_or_no', 'gz_gzs_zh'));

const gzjzGzsbmList = ref<GzjzGzsbmVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzGzsbmFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzGzsbmForm = {
  id: undefined,
  gzjzId: undefined,
  gzsbh: undefined,
  nf: undefined,
  zh: undefined,
  ls: undefined,
  sfZf: undefined,
}
const data = reactive<PageData<GzjzGzsbmForm, GzjzGzsbmQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    gzsbh: undefined,
    nf: undefined,
    zh: undefined,
    ls: undefined,
    sfZf: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证书编号v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzGzsbm(queryParams.value);
  gzjzGzsbmList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzGzsbmFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzGzsbmVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证书编号v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzGzsbmVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzGzsbm(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证书编号v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzGzsbmFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzGzsbm(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzGzsbm(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzGzsbmVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证书编号v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzGzsbm(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzGzsbm/export', {
    ...queryParams.value
  }, `gzjzGzsbm_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
