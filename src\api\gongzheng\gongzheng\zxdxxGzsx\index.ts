import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZxdxxGzsxVO, ZxdxxGzsxForm, ZxdxxGzsxQuery } from '@/api/gongzheng/zxdxxGzsx/types';

/**
 * 查询公证-咨询单-公证事项列表
 * @param query
 * @returns {*}
 */

export const listZxdxxGzsx = (query?: ZxdxxGzsxQuery): AxiosPromise<ZxdxxGzsxVO[]> => {
  return request({
    url: '/gongzheng/zxdxxGzsx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证-咨询单-公证事项详细
 * @param zxsxId
 */
export const getZxdxxGzsx = (zxsxId: string | number): AxiosPromise<ZxdxxGzsxVO> => {
  return request({
    url: '/gongzheng/zxdxxGzsx/' + zxsxId,
    method: 'get'
  });
};

/**
 * 新增公证-咨询单-公证事项
 * @param data
 */
export const addZxdxxGzsx = (data: ZxdxxGzsxForm) => {
  return request({
    url: '/gongzheng/zxdxxGzsx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证-咨询单-公证事项
 * @param data
 */
export const updateZxdxxGzsx = (data: ZxdxxGzsxForm) => {
  return request({
    url: '/gongzheng/zxdxxGzsx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证-咨询单-公证事项
 * @param zxsxId
 */
export const delZxdxxGzsx = (zxsxId: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/zxdxxGzsx/' + zxsxId,
    method: 'delete'
  });
};
