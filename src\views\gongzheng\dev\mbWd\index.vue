<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="模板基础信息ID" prop="mbId">
              <el-input v-model="queryParams.mbId" placeholder="请输入模板基础信息ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文档名称" prop="wdMc">
              <el-input v-model="queryParams.wdMc" placeholder="请输入文档名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文档类别" prop="wdLb">
              <el-input v-model="queryParams.wdLb" placeholder="请输入文档类别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文档地址" prop="wdDz">
              <el-input v-model="queryParams.wdDz" placeholder="请输入文档地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务ID" prop="ywId">
              <el-input v-model="queryParams.ywId" placeholder="请输入业务ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否默认" prop="isDefault">
              <el-input v-model="queryParams.isDefault" placeholder="请输入是否默认" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mb:mbWd:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mb:mbWd:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mb:mbWd:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mb:mbWd:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="mbWdList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="模板基础信息ID" align="center" prop="mbId" />
        <el-table-column label="文档名称" align="center" prop="wdMc" />
        <el-table-column label="文档类别" align="center" prop="wdLb" />
        <el-table-column label="文档地址" align="center" prop="wdDz" />
        <el-table-column label="业务ID" align="center" prop="ywId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="是否默认" align="center" prop="isDefault" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mb:mbWd:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mb:mbWd:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改模板-模板文档对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mbWdFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板基础信息ID" prop="mbId">
          <el-input v-model="form.mbId" placeholder="请输入模板基础信息ID" />
        </el-form-item>
        <el-form-item label="文档名称" prop="wdMc">
          <el-input v-model="form.wdMc" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档类别" prop="wdLb">
          <el-input v-model="form.wdLb" placeholder="请输入文档类别" />
        </el-form-item>
        <el-form-item label="文档地址" prop="wdDz">
          <el-input v-model="form.wdDz" placeholder="请输入文档地址" />
        </el-form-item>
        <el-form-item label="业务ID" prop="ywId">
          <el-input v-model="form.ywId" placeholder="请输入业务ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="是否默认" prop="isDefault">
          <el-input v-model="form.isDefault" placeholder="请输入是否默认" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MbWd" lang="ts">
import { listMbWd, getMbWd, delMbWd, addMbWd, updateMbWd } from '@/api/gongzheng/mb/mbWd';
import { MbWdVO, MbWdQuery, MbWdForm } from '@/api/gongzheng/mb/mbWd/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const mbWdList = ref<MbWdVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const mbWdFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MbWdForm = {
  id: undefined,
  mbId: undefined,
  wdMc: undefined,
  wdLb: undefined,
  wdDz: undefined,
  ywId: undefined,
  remark: undefined,
  isDefault: undefined
}
const data = reactive<PageData<MbWdForm, MbWdQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    mbId: undefined,
    wdMc: undefined,
    wdLb: undefined,
    wdDz: undefined,
    ywId: undefined,
    isDefault: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    mbId: [
      { required: true, message: "模板基础信息ID不能为空", trigger: "blur" }
    ],
    wdMc: [
      { required: true, message: "文档名称不能为空", trigger: "blur" }
    ],
    wdDz: [
      { required: true, message: "文档地址不能为空", trigger: "blur" }
    ],
    ywId: [
      { required: true, message: "业务ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询模板-模板文档列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMbWd(queryParams.value);
  mbWdList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  mbWdFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MbWdVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加模板-模板文档";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MbWdVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMbWd(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改模板-模板文档";
}

/** 提交按钮 */
const submitForm = () => {
  mbWdFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMbWd(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMbWd(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MbWdVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除模板-模板文档编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMbWd(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('mb/mbWd/export', {
    ...queryParams.value
  }, `mbWd_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
