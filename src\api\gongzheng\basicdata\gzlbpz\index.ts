import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzlbpzVO, GzlbpzForm, GzlbpzQuery } from '@/api/gongzheng/basicdata/gzlbpz/types';

/**
 * 查询公证类别配置列表
 * @param query
 * @returns {*}
 */

export const listGzlbpz = (query?: GzlbpzQuery): AxiosPromise<GzlbpzVO[]> => {
  return request({
    url: '/basicdata/gzlbpz/list',
    method: 'get',
    params: query
  });
};

export const treeGzlbpz = (query?: GzlbpzQuery): AxiosPromise<GzlbpzVO[]> => {
  return request({
    url: '/basicdata/gzlbpz/tree',
    method: 'get',
    params: query
  });
};

/**
 * 修改公证类别配置
 * @param data
 */
export const updateGzlbpz = (data: GzlbpzForm) => {
  return request({
    url: '/basicdata/gzlbpz',
    method: 'put',
    data: data
  });
};

/**
 * 获取公证类别配置详细信息
 * @returns
 */
export const getGzlbpz = (id: string | number) => {
  return request({
    url: '/basicdata/gzlbpz/' + id,
    method: 'get'
  });
}

/**
 * 删除公证类别配置
 * @param id
 * @returns
 */
export const delGzlbpz = (id: string | number) => {
  return request({
    url: '/basicdata/gzlbpz/' + id,
    method: 'delete'
  });
}

/**
 * 新增公证类别配置
 */
export const addGzlbpz = (data: GzlbpzForm) => {
  return request({
    url: '/basicdata/gzlbpz',
    method: 'post',
    data
  });
}
