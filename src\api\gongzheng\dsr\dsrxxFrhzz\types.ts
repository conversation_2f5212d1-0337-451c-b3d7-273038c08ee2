export interface DsrxxFrhzzVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 受理编号
   */
  slbh: string;

  /**
   * 单位名称
   */
  dwmc: string;

  /**
   * 单位所在地
   */
  dwszd: string;

  /**
   * 证件类型
   */
  zjlx: string;

  /**
   * 证件号码
   */
  zjhm: string;

  /**
   * 联系电话(格式为固话“区号-号码”或手机号码)
   */
  lxdh: string;

  /**
   * 法定代表人（负责人）
   */
  fddbr: string;

  /**
   * 法定代表人（负责人）性别
   */
  fddbrxb: string;

  /**
   * 法定代表人（负责人）联系电话(格式为固话“区号-号码”或手机号码)
   */
  fddbrlxdh: string;

  /**
   * 法定代表人（负责人）职务
   */
  fddbrzw: string;

  /**
   * 法定代表人（负责人）证件类型
   */
  fddbrzjlx: string;

  /**
   * 法定代表人（负责人）证件号码
   */
  fddbrzjhm: string;

  /**
   * 当事人类别
   */
  dsrlb: string;

  /**
   * 法定代表人（负责人）照片
   */
  fddbrzp: string;

  /**
   * 法定代表人（负责人）住址
   */
  fddbrzz: string;

  /**
   * $column.columnComment
   */
  remark: string;

  /**
   * 会员号码
   */
  hyhm: string;

  /**
   * 英文名称
   */
  ywmc: string;

  /**
   * 负责人证件类型
   */
  fzrzjlx: string;

  /**
   * 负责人证件号码
   */
  fzrzjhm: string;

  /**
   * 负责人姓名
   */
  fzrxm: string;

  /**
   * 负责人出生日期
   */
  fzrcsrq: string;

  /**
   * 负责人微信号
   */
  fzrwxh: string;

  /**
   * 负责人电子邮件
   */
  fzrdzyj: string;

  /**
   * 开户行
   */
  khh: string;

  /**
   * 开户账号
   */
  khzh: string;

}

export interface DsrxxFrhzzForm extends BaseEntity {
  /**
   * 受理编号
   */
  slbh?: string;

  /**
   * 单位名称
   */
  dwmc?: string;

  /**
   * 单位所在地
   */
  dwszd?: string;

  /**
   * 证件类型
   */
  zjlx?: string;

  /**
   * 证件号码
   */
  zjhm?: string;

  /**
   * 联系电话(格式为固话“区号-号码”或手机号码)
   */
  lxdh?: string;

  /**
   * 法定代表人（负责人）
   */
  fddbr?: string;

  /**
   * 法定代表人（负责人）性别
   */
  fddbrxb?: string;

  /**
   * 法定代表人（负责人）联系电话(格式为固话“区号-号码”或手机号码)
   */
  fddbrlxdh?: string;

  /**
   * 法定代表人（负责人）职务
   */
  fddbrzw?: string;

  /**
   * 法定代表人（负责人）证件类型
   */
  fddbrzjlx?: string;

  /**
   * 法定代表人（负责人）证件号码
   */
  fddbrzjhm?: string;

  /**
   * 当事人类别
   */
  dsrlb?: string;

  /**
   * 法定代表人（负责人）照片
   */
  fddbrzp?: string;

  /**
   * 法定代表人（负责人）住址
   */
  fddbrzz?: string;

  /**
   * $column.columnComment
   */
  remark?: string;

  /**
   * 会员号码
   */
  hyhm?: string;

  /**
   * 英文名称
   */
  ywmc?: string;

  /**
   * 负责人证件类型
   */
  fzrzjlx?: string;

  /**
   * 负责人证件号码
   */
  fzrzjhm?: string;

  /**
   * 负责人姓名
   */
  fzrxm?: string;

  /**
   * 负责人出生日期
   */
  fzrcsrq?: string;

  /**
   * 负责人微信号
   */
  fzrwxh?: string;

  /**
   * 负责人电子邮件
   */
  fzrdzyj?: string;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 开户账号
   */
  khzh?: string;
  dz?: string,
  fzrxb?: string,
  dlrzjhm?: string,
  dlrxmzjlx ?: string | number,
  dlrxm?: string,
  fddbrcsrq?: string,

}

export interface DsrxxFrhzzQuery extends PageQuery {

  /**
   * 受理编号
   */
  slbh?: string;

  /**
   * 单位名称
   */
  dwmc?: string;

  /**
   * 单位所在地
   */
  dwszd?: string;

  /**
   * 证件类型
   */
  zjlx?: string;

  /**
   * 证件号码
   */
  zjhm?: string;

  /**
   * 联系电话(格式为固话“区号-号码”或手机号码)
   */
  lxdh?: string;

  /**
   * 法定代表人（负责人）
   */
  fddbr?: string;

  /**
   * 法定代表人（负责人）性别
   */
  fddbrxb?: string;

  /**
   * 法定代表人（负责人）联系电话(格式为固话“区号-号码”或手机号码)
   */
  fddbrlxdh?: string;

  /**
   * 法定代表人（负责人）职务
   */
  fddbrzw?: string;

  /**
   * 法定代表人（负责人）证件类型
   */
  fddbrzjlx?: string;

  /**
   * 法定代表人（负责人）证件号码
   */
  fddbrzjhm?: string;

  /**
   * 当事人类别
   */
  dsrlb?: string;

  /**
   * 法定代表人（负责人）照片
   */
  fddbrzp?: string;

  /**
   * 法定代表人（负责人）住址
   */
  fddbrzz?: string;

  /**
   * 会员号码
   */
  hyhm?: string;

  /**
   * 英文名称
   */
  ywmc?: string;

  /**
   * 负责人证件类型
   */
  fzrzjlx?: string;

  /**
   * 负责人证件号码
   */
  fzrzjhm?: string;

  /**
   * 负责人姓名
   */
  fzrxm?: string;

  /**
   * 负责人出生日期
   */
  fzrcsrq?: string;

  /**
   * 负责人微信号
   */
  fzrwxh?: string;

  /**
   * 负责人电子邮件
   */
  fzrdzyj?: string;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 开户账号
   */
  khzh?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



