<template>
  <gz-dialog v-model="visible" :title="title" @closed="reset" show-close destroy-on-close fullscreen>
    <div class="flex items-center justify-center flex-gap-2 w-full h-full">
      <el-card class="basis-0 grow-2 h-full">
        <template #header>
          <div class="flex items-center justify-between flex-nowrap">
            <strong>证据列表</strong>
            <div v-if="!readOnly">
              <el-button type="primary" size="small" @click="taggleAddZj">新增证据</el-button>
              <!-- <el-button size="small">引用证据</el-button> -->
              <el-button @click="taggleLinkDsr" size="small">关联当事人</el-button>
              <el-button @click="taggleDelZj" type="danger" size="small">删除</el-button>
            </div>
          </div>
        </template>
        <div>
          <el-table :data="zjclState.listData" v-loading="zjclState.loading" ref="zjTableref" border style="width: 100%" size="small" @current-change="handleCurRow" highlight-current-row>
            <el-table-column type="selection" width="40" />
            <el-table-column type="index" width="55" align="center"/>
            <el-table-column prop="zmmc" label="证据名称" align="center"/>
            <el-table-column prop="dsrId" label="当事人" align="center" show-overflow-tooltip>
              <template #default="{ row }">
                {{ mapDsrName(row.dsrId) }}
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center">
              <template #default="{ row }">
                {{ row.zmclxxMxList.length }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card class="basis-0 grow-3 h-full new_zjcl_card_1">
        <template #header>
          <div class="flex items-center gap-1">
            <strong>{{ uploadState.docType || uploadState.zjclObj?.zmmc }}</strong>
            <div v-if="!readOnly" class="flex items-center gap-10px ml-24px">
              <el-checkbox v-model="uploadState.checkAll" :indeterminate="uploadState.isI" size="small" @change="curZjListCheckAll">全选</el-checkbox>
              <el-button v-if="!readOnly" @click="toggleTakePic" type="primary" size="small">拍照</el-button>
              <!-- <el-button type="primary" size="small" @click="taggleUpload">上传</el-button> -->
              <!-- <el-button size="small">分配</el-button>
              <el-button size="small">下载</el-button> -->
              <el-button @click="taggleDelZjcl" type="danger" size="small">删除</el-button>
              <el-text type="danger" size="small">严禁上传存储处理涉密敏感文件信息</el-text>
            </div>
          </div>
        </template>
        <div class="h-full flex flex-col gap-14px overflow-auto">
          <el-checkbox-group v-model="uploadState.curCheckedList" @change="curZjListCheck" style="font-size: 14px;line-height: normal;" class="h-full flex flex-col gap-14px">
            <el-collapse :model-value="['img', 'media', 'doc']">
              <el-collapse-item name="img">
                <template #title>
                  <div class="flex h-32px items-center gap-20px">
                    <strong>照片 ( {{ zjclState.imgs.length }}项 )</strong>
                    <el-button v-if="!readOnly" @click.stop="() => toUploadFile('img')" type="primary" size="small">上传</el-button>
                  </div>
                </template>
                <div class="rounded min-h-100px flex flex-wrap gap-10px p-6px border border-dotted">
                  <div
                    v-for="(item, index) in zjclState.imgs"
                    :key="item.id"
                    class="box-sd object-cover min-w-64px max-w-64px max-h-96px max-w-96px relative flex items-center justify-center bg-gray-100 rounded overflow-hidden p-1px"
                  >
                    <span v-if="!readOnly" class="absolute top-0px left-8px z-50 p-2px">
                      <el-checkbox :value="item.id" />
                    </span>
                    <span v-if="!readOnly" class="absolute top-4px right-8px z-50 p-2px rounded hover:bg-sky-100">
                      <el-button type="danger" link icon="Delete" @click="delCurZjcl(item.id)" />
                    </span>
                    <el-image
                      class="flex-1 rounded"
                      :src="item.visitUrl"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      :preview-src-list="zjclState.imgs.map(item => item.visitUrl)"
                      show-progress
                      :initial-index="index"
                      fit="cover"
                    />
                  </div>
                </div>
              </el-collapse-item>

              <el-collapse-item name="media">
                <template #title>
                  <div class="flex h-32px items-center gap-20px">
                    <strong>音频/视频 ( {{ zjclState.media.length }}项 )</strong>
                    <el-button v-if="!readOnly" @click.stop="() => toUploadFile('media')" type="primary" size="small">上传</el-button>
                  </div>
                </template>
                <div class="rounded min-h-100px flex flex-wrap gap-10px p-6px border border-dotted">
                  <div
                    v-for="(item, index) in zjclState.media"
                    :key="item.id"
                    class="box-sd object-cover min-w-64px max-w-64px max-h-96px max-w-96px relative flex items-center justify-center bg-gray-100 rounded overflow-hidden p-1px"
                  >
                    <span v-if="!readOnly" class="absolute top-0px left-8px z-50 p-2px">
                      <el-checkbox :value="item.id" />
                    </span>
                    <span v-if="!readOnly" class="absolute top-4px right-8px z-50 p-2px rounded hover:bg-sky-100">
                      <el-button type="danger" link icon="Delete" @click="delCurZjcl(item.id)" />
                    </span>
                    <el-tooltip :content="item.xxmc" :show-after="500">
                      <div @click="openMedia(item)" class="flex-1 flex flex-col items-center justify-end gap-4px w-90px h-90px cursor-pointer">
                        <el-icon :size="36">
                          <VideoPlay v-if="isVideo(item.xxmc)" />
                          <Headset v-else />
                        </el-icon>
                        <el-text class="text-ellipsis text-nowrap w-full overflow-hidden">{{ item.xxmc }}</el-text>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </el-collapse-item>

              <el-collapse-item name="doc">
                <template #title>
                  <div class="flex h-32px items-center gap-20px">
                    <strong>文档 ( {{ zjclState.documents.length }}项 )</strong>
                    <el-button v-if="!readOnly" @click.stop="() => toUploadFile('doc')" type="primary" size="small">上传</el-button>
                  </div>
                </template>
                <div class="rounded min-h-100px flex flex-wrap gap-10px p-6px border border-dotted">
                  <div
                    v-for="(item, index) in zjclState.documents"
                    :key="item.id"
                    class="box-sd object-cover min-w-64px max-w-64px max-h-96px max-w-96px relative flex items-center justify-center bg-gray-100 rounded overflow-hidden p-1px"
                  >
                    <span v-if="!readOnly" class="absolute top-0px left-8px z-50 p-2px">
                      <el-checkbox :value="item.id" />
                    </span>
                    <span v-if="!readOnly" class="absolute top-4px right-8px z-50 p-2px rounded hover:bg-sky-100">
                      <el-button type="danger" link icon="Delete" @click="delCurZjcl(item.id)" />
                    </span>
                    <el-tooltip :content="item.xxmc" :show-after="500">
                      <div @click="openPdf(item)" class="flex-1 flex flex-col items-center justify-end gap-4px w-90px h-90px cursor-pointer">
                        <el-icon :size="36"><Document /></el-icon>
                        <el-text class="text-ellipsis text-nowrap w-full overflow-hidden">{{ item.xxmc }}</el-text>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-checkbox-group>
        </div>

      </el-card>
      <!-- <el-card class="basis-0 grow-3 h-full"></el-card> -->
    </div>

    <DragUpload v-if="uploadState.visible" v-model="uploadState.visible" :title="uploadState.title" :accept="uploadState.accept" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" />
    <AddZj v-if="addZjState.visible" v-model="addZjState.visible" @confirm="addZjConfirm" />
    <TakePic v-if="takePicState.visible" v-model="takePicState.visible" @comfirm="takePicConfirm" />
    <PdfShower v-if="pdfState.visible" v-model="pdfState.visible" :url="pdfState.url" />
    <MediaPlayer ref="mediaPlayerRef" />
    <LinkDsr ref="dsrLinkRef" @done="loadZjclList" />
    <Dcjg ref="dcjgRef" />

    <template #footer>
      <div class="flex justify-between items-center gap-10px">
        <div class="flex justify-end basis-0 grow-2">
          <el-button @click="openDcjg">申请调查核实</el-button>
        </div>
        <div class="flex justify-end basis-0 grow-3">
          <el-button @click="visible = false">关闭</el-button>
        </div>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onBeforeMount, onMounted, onUnmounted} from 'vue'
import DragUpload from '@/components/FileUpload/DragUpload.vue'
import AddZj from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/AddZj.vue'
import TakePic from '@/views/gongzheng/components/takePic/index.vue'
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { UploadStatus } from '@/components/FileUpload/type';
import { addZjclFile } from '@/api/gongzheng/gongzheng/gzjzZmclxxMx';
import { queryGzjzZmclxx, addGzjzZmclxx, delGzjzZmclxx, listGzjzZmclxx, updateGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { delEvidenceFile } from '@/api/gongzheng/gongzheng/zjcl';
import { uploadFile } from '@/api/gongzheng/gongzheng/oss';
import PdfShower from '@/components/DocShower/PdfShower.vue';
import MediaPlayer from '@/components/MediaPlayer/index.vue';
import LinkDsr from './LinkDsr.vue';
import Dcjg from '@/views/gongzheng/gongzheng/components/Dcjg/index.vue';

interface Props {
  modelValue?: boolean,
  title?: string,
  gzjzId?: string | number,
  readOnly?: boolean,
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '证据材料',
  readOnly: false,
})

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const zjclState = reactive({
  loading: false,
  listData: [],
  imgs: [],
  media: [],
  documents: []
})

const emit = defineEmits(['update:modelValue']);

const zjTableref = ref(null);
const mediaPlayerRef = ref(null);
const dsrLinkRef = ref(null);
const dcjgRef = ref(null);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
});

const acceptExts = [
  'jpg', 'jpeg', 'png', 'webp',
  'mp4', 'avi', 'mov', 'mp3',
  // 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
  'pdf'
]

// 上传相关状态
const uploadState = reactive({
  loading: false,
  visible: false,
  title: '上传',
  accept: '.jpg,.jpeg,.png',
  docType: '',
  docId: '',
  zjclObj: null,
  checkAll: false,
  isI: false, // 是否为半选状态
  curCheckedList: [],
})

const dsrState = reactive({
  loading: false,
  listData: [],
})

const addZjState = reactive({
  visible: false,
})

const takePicState = reactive({
  visible: false,
})

const pdfState = reactive({
  visible: false,
  url: '',
})

/**======================================= 数据 END =======================================**/

const reset = () => {
  uploadState.docType = ''
  uploadState.docId = ''
  uploadState.zjclObj = null
  uploadState.checkAll = false
  uploadState.curCheckedList = []
}

const openDcjg = () => {
  dcjgRef.value?.open({
    gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
  })
}

const curZjListCheckAll = (val: CheckboxValueType) => {
  if(uploadState.zjclObj) {
    if (val) {
      uploadState.curCheckedList = uploadState.zjclObj?.zmclxxMxList.map(item => item.id);
    } else {
      uploadState.curCheckedList = [];
    }
  } else {
    uploadState.curCheckedList = [];
  }
  uploadState.isI = false;
}

const curZjListCheck = (vals: CheckboxValueType[]) => {
  const checkedCount = vals.length;
  uploadState.checkAll = checkedCount === (uploadState.zjclObj?.zmclxxMxList.length || 0);
  uploadState.isI = checkedCount > 0 && checkedCount < (uploadState.zjclObj?.zmclxxMxList.length || 0);
}

// 选择当前行
const handleCurRow = (row: any) => {
  if (row) {
    uploadState.zjclObj = row;
    uploadState.docId = row.id;
    uploadState.docType = row.zmmc;
    uploadState.checkAll = false
    uploadState.curCheckedList = []

    dealZjcl(row);
  }
}

// 分类证据材料
const dealZjcl = (data: any) => {
  let imgs = [];
  let media = [];
  let documents = [];
  data?.zmclxxMxList.forEach((item: any) => {
    // 获取文件后缀
    const ext = item.xxmc.substring(item.xxmc.lastIndexOf('.') + 1);
    // 如果是图片
    if (['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
      imgs.push(item);
    } else if (['mp4', 'avi', 'mov', 'mp3'].includes(ext)) {
      media.push(item);
    } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'].includes(ext)) {
      documents.push(item);
    }
  })

  zjclState.imgs = imgs;
  zjclState.media = media;
  zjclState.documents = documents;
}

// 判断文件名后缀
const isVideo = (fileName: string) => {
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1);
  return ['mp4', 'avi', 'mov'].includes(ext);
}

// 加载当事人列表
const loadDsrList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    dsrState.listData = []
    return;
  };
  dsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        dsrState.listData = (res.rows || []).reduce((doneArr, curItem) => {
          const found = doneArr.find(i => {
            return i.dsrId == curItem.dsrId
          })
          if(!found) {
            doneArr.push(curItem)
          }
          return doneArr;
        }, []);
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    dsrState.loading = false;
  }
}

const mapDsrName = (dsrId: string) => {
  if(!dsrId) return '';
  console.log('mapDsrName', dsrId)
  const dsrIds = dsrId.split(',');
  const dsrNames = []
  dsrState.listData.forEach((item) => {
    if(dsrIds.includes(String(item.dsrId))) {
      dsrNames.push(item.name);
    }
  });
  return dsrNames.join('，') || '';
}

// 每一个上传完成回调
const uploadEveryDone = (data: UploadStatus) => {
  if (data.status === 'success') {

  } else if (data.status === 'error') {

  }
}

// 全部上传完成回调 data 上传成功的文件信息数组，不含失败
const uploadAllDone = (data: any[]) => {
  const mxList = data.map((item) => {
    return {
      xxmc: item.fileName!,
      bclj: item.path!
    }
  });
  addZmlc(mxList);
}

// 添加证据材料，处理上传成功的文档为卷宗相关文档
const addZmlc = async (zmcl: any[]) => {

  try {
    const params = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      parentId: uploadState.docId || uploadState.zjclObj?.id,
      files: zmcl
    }
    const res = await addZjclFile(params);
    if (res.code === 200) {
      ElMessage.success(`已成功添加 ${zmcl.length} 个 ${uploadState.docType} 文档材料`);
      loadZjclList();
    }
  } catch (err: any) {
    ElMessage.error(`添加 ${uploadState.docType} 文档材料失败: ${err.message}`)
  } finally {

  }
}

// 加载证明材料列表
const loadZjclList = async () => {
  try {
    zjclState.loading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      fjlb: '2',   // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      pageNum: 1,
      pageSize: 200
    }
    const res = await listGzjzZmclxx(params);
    // const res = await queryGzjzZmclxx(params);
    if (res.code === 200) {
      zjclState.listData = res.data;
      if(uploadState.zjclObj) {
        uploadState.zjclObj = zjclState.listData.find((item) => item.id === (uploadState.zjclObj?.id || uploadState.docId)) || null;
        zjTableref.value?.setCurrentRow(uploadState.zjclObj);
      }
    }
  } catch (err: any) {
    ElMessage.error(`获取证据列表失败: ${err.message}`)
  } finally {
    zjclState.loading = false;
    uploadState.checkAll = false
    uploadState.curCheckedList = []
  }
}

// 点击添加证据
const taggleAddZj = () => {
  addZjState.visible = true;
}

// 点击删除证据
const taggleDelZj = () => {
  const rows = zjTableref.value?.getSelectionRows() || [];
  if (rows && rows.length > 0) {
    ElMessageBox.confirm('确定删除选中的证据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      delZjListItem(rows.map(i => i.id));
    }).catch(() => {})
  } else {
    ElMessage.warning('请先勾选要删除的证据');
  }
}

// 关联当事人
const taggleLinkDsr = () => {
  const rows = zjTableref.value?.getSelectionRows() || [];
  if(rows.length === 0) {
    ElMessage.warning('至少选取一个证明材料');
    return;
  }

  dsrLinkRef.value?.show({
    dsrList: dsrState.listData,
    zjclList: rows
  });
}

const delZjListItem = async (ids: string[]) => {
  try {
    const res = await delGzjzZmclxx(ids);
    if (res.code === 200) {
      ElMessage.success(`已成功删除 ${ids.length} 证据`);
      const hasCurRow = ids.includes(uploadState.zjclObj?.id || '');
      if(hasCurRow) {
        uploadState.zjclObj = null;
        uploadState.docId = '';
        uploadState.docType = '';
      }

      loadZjclList();
    }
  } catch (err: any) {
    ElMessage.error(`删除证据失败: ${err.message}`)
  } finally {

  }
}


/**
 * 添加证据确认
 * @param data { names, nodes, customNodes }
 */
const addZjConfirm = (data: any) => {
  const namesStr = data.names.join(',');
  addZjlx(namesStr);
}

const addZjlx = async (zmmc: string) => {
  if(!zmmc) {
    ElMessage.warning('未获取到要添加的证据类型');
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: '正在添加证据类型，请稍候...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  });
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      zmmc,
      fjlb: '2', // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
    }
    const res = await addGzjzZmclxx(params);
    if (res.code === 200) {
      ElMessage.success(`已成功添加 ${zmmc.split(',').length} 证据类型`);
      loadZjclList();
    }
  } catch (err: any) {
    ElMessage.error(`添加证据类型失败`)
    console.error('添加证据类型失败', err);
  } finally {
    loading.close();
  }
}

// 选择上传
const toUploadFile = (type: 'doc' | 'img' | 'media') => {
  if(type === 'doc') {
    uploadState.accept = '.pdf'
  } else if(type === 'img') {
    uploadState.accept = '.jpg,.jpeg,.png'
  } else if(type === 'media') {
    uploadState.accept = '.mp4,.avi,.mov,.wmv,.flv,.mp3'
  }

  taggleUpload();
}

// 点击上传
const taggleUpload = () => {
  if(!uploadState.zjclObj) {
    ElMessage.warning('请先选择要上传材料的证据');
    return;
  }
  uploadState.visible = true;
}

// 选择删除的证据材料
const taggleDelZjcl = () => {
  console.log('删除证据材料', uploadState.curCheckedList)
  if(uploadState.curCheckedList.length < 1) {
    ElMessage.warning('请先勾选要删除的证据材料');
    return;
  }
  delCurZjcl(uploadState.curCheckedList);
}

// 删除当前证据材料
const delCurZjcl = (id: string | number | Array<string | number>) => {
  ElMessageBox.confirm(Array.isArray(id) ? '确定删除选中的证据材料吗？' : '确定删除当前证据材料吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await delEvidenceFile(id);
      if (res.code === 200) {
        ElMessage.success(`已成功删除 ${Array.isArray(id) ? id.length : 1} 个证据材料`);
        loadZjclList();
      }
    } catch (err: any) {
      ElMessage.error(`删除证据材料失败`);
      console.log(`删除证据材料失败`, err)
    }
  }).catch(() => {

  }).finally(() => {

  })
}

const openPdf = (data: any) => {
  pdfState.url = data.visitUrl;
  pdfState.visible = true;
}

const openMedia = (data: any) => {
  // if(isVideo(data.xxmc)) {

  // } else {

  // }
  mediaPlayerRef.value?.show(data.visitUrl);
}

const toggleTakePic = () => {
  if (!uploadState.zjclObj) {
    ElMessage.warning('请先选择要拍照的证据类型');
    return;
  }
  takePicState.visible = true;
}

const takePicConfirm = async (data: any) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在提交照片，请稍候...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {
    const formData = new FormData();
    formData.append('file', data.file);
    const res = await uploadFile(formData);
    if (res.code === 200 && res) {
      const params = [
        {
          xxmc: res.data.fileName,
          bclj: res.data.path!
        }
      ]
      await addZmlc(params);

      takePicState.visible = false;
    }
  } catch (err: any) {
    ElMessage.error('拍照提交失败');
    console.error('拍照提交失败', err);
  } finally {
    loading.close();
  }
}

watch(() => props.modelValue, async (val) => {
  if(val) {
    loadDsrList();
    loadZjclList();
  }
})

onBeforeMount(() => {
})

onMounted(async () => {
  await loadDsrList();
  await loadZjclList();
})

</script>

<style>
.new_zjcl_card_1 .el-card__body {
  height: calc(100% - 40px) !important;
}
.box-sd {
  box-shadow: 0 0 6px 0 rgba(137, 149, 238, 0.4);
}
</style>
