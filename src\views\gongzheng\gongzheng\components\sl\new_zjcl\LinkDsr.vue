<template>
  <gz-dialog v-model="visible" :title="title" @closed="closed" width="580" append-to-body>
    <el-table ref="dsrTbRef" :data="linkDsrState.dsrList" height="260" border stripe>
      <el-table-column type="selection" width="60" align="center" />
      <el-table-column label="#" type="index" width="60" align="center" />
      <el-table-column prop="name" label="当事人姓名" align="center" show-overflow-tooltip/>
    </el-table>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="linkDsr" :loading="linkDsrState.loading" :disabled="linkDsrState.loading" type="primary">确定</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { formatDate, formatGender, dictMapFormat } from '@/utils/ruoyi';
import { updateGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';

interface Props {
  modelValue?: boolean;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '关联当事人'
})

const emit = defineEmits(['update:modelValue', 'done'])

const visible = ref(false)
const dsrTbRef = ref(null)

const linkDsrState = reactive({
  dsrList: [],
  zjclList: [],
  loading: false,
})

const linkDsr = async () => {
  const rows = dsrTbRef.value?.getSelectionRows() || [];
  // if (rows.length === 0) {
  //   ElMessage.warning('至少选一个当事人')
  //   return;
  // }
  try {
    linkDsrState.loading = true;
    const dsrId = rows.map((j: any) => j.dsrId).join(',') || '';
    const zjclLnkReqs = linkDsrState.zjclList.map((i: any) => {
      const params = {
        id: i.id,
        zmmc: i.zmmc,
        gzjzId: i.gzjzId,
        dsrId,
      }
      return updateGzjzZmclxx(params);
    })

    await Promise.all(zjclLnkReqs);
    emit('done');
    close();
  } catch (err: any) {
    console.error('关联当事人异常', err)
  } finally {
    linkDsrState.loading = false;
  }
}

const close = () => {
  visible.value = false
  linkDsrState.dsrList = []
  linkDsrState.zjclList = []
}

const closed = () => {
  linkDsrState.dsrList = []
  linkDsrState.zjclList = []
}

interface LinkParams {
  zjclList: string[] | number[];
  dsrList: any[];
}

const show = (val: LinkParams) => {
  linkDsrState.zjclList = val.zjclList;
  linkDsrState.dsrList = val.dsrList;
  visible.value = true
  nextTick(() => {
    if(linkDsrState.zjclList.length === 1) {
      const dsrIds = (linkDsrState.zjclList[0].dsrId || []).split(',');
      linkDsrState.dsrList.forEach((i: any) => {
        if(dsrIds.includes(String(i.dsrId))) {
          dsrTbRef.value?.toggleRowSelection(i, true);
        }
      });
    }
  })
}

defineExpose({
  show
})

</script>
