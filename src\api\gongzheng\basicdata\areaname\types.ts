export interface AreanameVO {
  /**
   * $column.columnComment
   */
  id: string | number;

  /**
   * 数字代码
   */
  numberCode: string;

  /**
   * 地区名称
   */
  areaName: string;

  /**
   * 中文拼音简写
   */
  areaNameChinese: string;

  /**
   * 地区英文简写
   */
  areaNameEnglish: string;

  /**
   * 是否显示
   */
  showStatus: string | number;

  /**
   * 是否取双号
   */
  evenNumbersStatus: string | number;

  /**
   * 置顶
   */
  topStatus: string | number;

  /**
   * 译文
   */
  translation: string;

  fullName: string;

  twoCharCode: string;

  threeCharCode: string;
}

export interface AreanameForm extends BaseEntity {
  /**
   * $column.columnComment
   */
  id?: string | number;

  /**
   * 数字代码
   */
  numberCode?: string;

  /**
   * 地区名称
   */
  areaName?: string;

  /**
   * 中文拼音简写
   */
  areaNameChinese?: string;

  /**
   * 地区英文简写
   */
  areaNameEnglish?: string;

  /**
   * 是否显示
   */
  showStatus?: string | number;

  /**
   * 是否取双号
   */
  evenNumbersStatus?: string | number;

  /**
   * 置顶
   */
  topStatus?: string | number;

  /**
   * 译文
   */
  translation?: string;

  fullName?: string;

  twoCharCode?: string;

  threeCharCode?: string;
}

export interface AreanameQuery extends PageQuery {

  /**
   * 数字代码
   */
  numberCode?: string;

  /**
   * 地区名称
   */
  areaName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



