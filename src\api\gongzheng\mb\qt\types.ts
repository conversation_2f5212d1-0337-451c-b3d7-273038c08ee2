// 模板列表类型定义
export interface Template {
  id: string;
  title: string;
  templateType: number;
  uploadDate: string;
  updateDate: string;
  defaultStatus: number;
  content: string;
  classify?: number;
  fileUrl: string;
  fileName: string;
  docCategory:number;
  wdOssId:number;
  ywId:number;
  gzlb:number;
}

export interface TemplateQuery {
  title?: string;
  pageNum?: number;
  pageSize?: number;
  classify?: number;
}

export interface TemplateSaveParams {
  id?: string;
  title: string;
  templateType: number;
  classify?: number;
  content: string;
  defaultStatus : number;
  fileUrl: string;
  fileName: string;
  docCategory:number;
  wdOssId:number;
  ywId:number;
  gzlb:number;
}

export interface Role {
  id: string;
  title: string;
}
