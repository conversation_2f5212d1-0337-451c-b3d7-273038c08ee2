export interface GzjzTcxLqjlVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 提存项ID
   */
  tcxId: string | number;

  /**
   * 申请时间
   */
  sqsj: string;

  /**
   * 申请额度
   */
  sqed: number;

  /**
   * 是否领取孳息
   */
  sflqcx: string;

  /**
   * 领取方式
   */
  lqfs: string;

  /**
   * 领取人ID
   */
  lqrId: string | number;

  /**
   * 开户行
   */
  khh: string;

  /**
   * 银行卡号
   */
  yhkh: string;

  /**
   * 申请理由
   */
  sqly: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 申请人ID
   */
  sqrId: string | number;

  /**
   * 申请人
   */
  sqr: string;

  /**
   * 状态
   */
  zt: string;

  /**
   * 代领取人姓名
   */
  dlLqrxm: string;

  /**
   * 代领取人联系号码
   */
  dlLxhm: string;

  /**
   * 代领取人证件类型（gz_gr_zjlx）
   */
  dlZjlx: string;

  /**
   * 代领取人证件号码
   */
  dlZjhm: string;

}

export interface GzjzTcxLqjlForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 申请时间
   */
  sqsj?: string;

  /**
   * 申请额度
   */
  sqed?: number;

  /**
   * 是否领取孳息
   */
  sflqcx?: string;

  /**
   * 领取方式
   */
  lqfs?: string;

  /**
   * 领取人ID
   */
  lqrId?: string | number;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 银行卡号
   */
  yhkh?: string;

  /**
   * 申请理由
   */
  sqly?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 申请人ID
   */
  sqrId?: string | number;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 状态
   */
  zt?: string;

  /**
   * 代领取人姓名
   */
  dlLqrxm?: string;

  /**
   * 代领取人联系号码
   */
  dlLxhm?: string;

  /**
   * 代领取人证件类型（gz_gr_zjlx）
   */
  dlZjlx?: string;

  /**
   * 代领取人证件号码
   */
  dlZjhm?: string;

}

export interface GzjzTcxLqjlQuery extends PageQuery {

  /**
   * 提存项ID
   */
  tcxId?: string | number;

  /**
   * 申请时间
   */
  sqsj?: string;

  /**
   * 是否领取孳息
   */
  sflqcx?: string;

  /**
   * 领取方式
   */
  lqfs?: string;

  /**
   * 领取人ID
   */
  lqrId?: string | number;

  /**
   * 开户行
   */
  khh?: string;

  /**
   * 银行卡号
   */
  yhkh?: string;

  /**
   * 申请理由
   */
  sqly?: string;

  /**
   * 申请人ID
   */
  sqrId?: string | number;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 状态
   */
  zt?: string;

  /**
   * 代领取人姓名
   */
  dlLqrxm?: string;

  /**
   * 代领取人联系号码
   */
  dlLxhm?: string;

  /**
   * 代领取人证件类型（gz_gr_zjlx）
   */
  dlZjlx?: string;

  /**
   * 代领取人证件号码
   */
  dlZjhm?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



