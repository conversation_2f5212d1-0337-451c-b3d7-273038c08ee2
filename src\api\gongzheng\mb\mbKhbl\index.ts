import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbKhblVO, MbKhblForm, MbKhblQuery } from '@/api/mb/mbKhbl/types';

/**
 * 查询模板-客户变量列表
 * @param query
 * @returns {*}
 */

export const listMbKhbl = (query?: MbKhblQuery): AxiosPromise<MbKhblVO[]> => {
  return request({
    url: '/mb/mbKhbl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-客户变量详细
 * @param id
 */
export const getMbKhbl = (id: string | number): AxiosPromise<MbKhblVO> => {
  return request({
    url: '/mb/mbKhbl/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-客户变量
 * @param data
 */
export const addMbKhbl = (data: MbKhblForm) => {
  return request({
    url: '/mb/mbKhbl',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-客户变量
 * @param data
 */
export const updateMbKhbl = (data: MbKhblForm) => {
  return request({
    url: '/mb/mbKhbl',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-客户变量
 * @param id
 */
export const delMbKhbl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbKhbl/' + id,
    method: 'delete'
  });
};
