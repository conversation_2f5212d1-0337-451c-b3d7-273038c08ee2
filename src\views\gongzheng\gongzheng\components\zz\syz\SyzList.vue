<template>
  <div class="h-full overflow-hidden">
    <div class="h-32px flex flex-nowrap gap-10px items-center justify-between">
      <strong>待使用水印纸列表</strong>
      <div class="flex flex-nowrap gap-10px items-center">
        <strong>水印纸编号：</strong>
        <el-input v-model="syzBh" style="width: 180px;" size="small" clearable/>
        <el-input v-model="syzListSize" style="width: 80px;" size="small" placeholder="20" @input="onNumberInput">
          <template #suffix>张</template>
        </el-input>
        <el-button @click="syzSearch" :disabled="loading" type="primary" size="small">查询</el-button>
      </div>
    </div>
    <el-table :data="syzList" v-loading="loading" ref="syzTableRef" style="height: calc(100% - 32px);" size="small" border stripe>
      <el-table-column type="index" label="#" width="55" align="center"/>
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column prop="syzbh" label="水印纸编号" align="center"/>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { listSyzmx } from '@/api/gongzheng/syz/syzmx';
import { SyzmxVO } from '@/api/gongzheng/syz/syzmx/types';

const syzList = ref<SyzmxVO[]>([]);
const loading = ref(false);
const syzBh = ref('');
const syzListSize = ref('20');
const syzTableRef = ref<ElTableInstance>(null);

const oldVal = ref('');
const onNumberInput = (val: string) => {
  const reg = /^\d+$/
  if(!val) {
    syzListSize.value = '20'
  } else {
    if(Number(val) === 0) {
      syzListSize.value = '20'
    } else {
      if(reg.test(val)) {
        oldVal.value = val
      } else {
        syzListSize.value = oldVal.value
      }
    }
  }
}

const syzSearch = () => {
  loadSyzList();
}

const loadSyzList = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: 1,
      pageSize: Number(syzListSize.value),
      syzt: '0',
      syzbh: syzBh.value
    }
    const res = await listSyzmx(params);
    if(res.code === 200) {
      syzList.value = res.rows || [];
    }
  } catch(err: any) {
    console.error('获取水印纸列表异常', err)
  } finally {
    loading.value = false
  }
}

const getSelectedRows = () => {
  return syzTableRef.value?.getSelectionRows() || [];
}

const reload = () => {
  syzTableRef.value?.clearSelection();
  loadSyzList();
}

defineExpose({
  getSelectedRows,
  reload
})

onMounted(() => {
  loadSyzList();
})
</script>