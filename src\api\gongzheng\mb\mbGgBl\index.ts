import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbGgBlVO, MbGgBlForm, MbGgBlQuery } from '@/api/mb/mbGgBl/types';

/**
 * 查询模板-公共-变量列表
 * @param query
 * @returns {*}
 */

export const listMbGgBl = (query?: MbGgBlQuery): AxiosPromise<MbGgBlVO[]> => {
  return request({
    url: '/mb/mbGgBl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-公共-变量详细
 * @param id
 */
export const getMbGgBl = (id: string | number): AxiosPromise<MbGgBlVO> => {
  return request({
    url: '/mb/mbGgBl/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-公共-变量
 * @param data
 */
export const addMbGgBl = (data: MbGgBlForm) => {
  return request({
    url: '/mb/mbGgBl',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-公共-变量
 * @param data
 */
export const updateMbGgBl = (data: MbGgBlForm) => {
  return request({
    url: '/mb/mbGgBl',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-公共-变量
 * @param id
 */
export const delMbGgBl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbGgBl/' + id,
    method: 'delete'
  });
};
