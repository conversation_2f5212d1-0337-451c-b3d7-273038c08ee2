<template>
  <el-table :data="data" border stripe size="small">
    <el-table-column type="index" label="#" align="center"/>
    <el-table-column prop="gzsxMc" label="公证事项" />
    <el-table-column prop="gzsBh" label="公证书编号" show-overflow-tooltip />
    <el-table-column label="公证书" min-width="80" align="center">
      <template #default="{ row }">
        <el-button @click="() => openGzsDoc(row)" type="primary" v-if="row.gzsFile?.wblj" size="small" link>证词</el-button>
      </template>
    </el-table-column>
    <el-table-column label="译文" min-width="80" align="center">
      <template #default="{ row }">
        <el-button @click="() => openGzsDoc(row, true)" type="primary" v-if="row.gzsFile?.ywlj" size="small" link>译文</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { docOpenShow } from '@/views/gongzheng/doc/DocEditor'

interface Props {
  data: any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})


// 打开公证书
const openGzsDoc = (data: any, yw: boolean = false) => {
  let docInfo = null;
  if(yw) {
    // 公证书译文
    docInfo = JSON.parse(data.gzsFile.ywlj || '{}');
  } else {
    // 公证书
    docInfo = JSON.parse(data.gzsFile.wblj || '{}');
  }

  if(docInfo?.path) {
    docOpenShow(docInfo.path)
  }
}

</script>
