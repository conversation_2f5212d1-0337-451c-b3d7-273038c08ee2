<template>
  <!-- 代书 -->
  <gz-dialog v-model="visible" :title="title" @closed="closed" fullscreen show-close destroy-on-close>
    <div class="flex flex-col gap-10px h-full">
      <el-card>
        <div class="flex items-center gap-12px flex-wrap">
          <span class="flex items-center">
            <el-text size="small">卷宗号：</el-text>
            <el-text type="info" size="small">{{ curGzjz.jzbh || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-button @click="openJzDetail" type="primary" size="small" link>【详情】</el-button>
          </span>
          <span class="flex items-center">
            <el-text size="small">公证类别：</el-text>
            <el-text type="info" size="small">{{ dictMapFormat(gz_gzlb, curGzjz.lb) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">公证员：</el-text>
            <el-text type="info" size="small">{{ curGzjz.gzyxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">助理：</el-text>
            <el-text type="info" size="small">{{ curGzjz.zlxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">申请日期：</el-text>
            <el-text type="info" size="small">{{ formatDate(curGzjz.sqsj) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">受理日期：</el-text>
            <el-text type="info" size="small">{{ formatDate(curGzjz.slrq) }}</el-text>
          </span>
        </div>
      </el-card>

      <el-card class="flex-1 overflow-hidden">
        <el-table :data="wsDocTypeList" size="small" style="height: 100%;" border stripe>
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column prop="typeName" label="文档类型" align="center" />
          <el-table-column label="文档名称" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex flex-wrap gap-4px">
                <el-tag v-for="item in row.docList" :key="item.id">
                  <el-button type="primary" link @click="handleOpen(item)">{{item.wbmc}}</el-button>
                  <el-button type="danger" link icon="Delete" @click="handleDelete(item)"></el-button>
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template #default="{ row }">
              <!-- <el-button @click="()=>{}" type="primary" size="small" link>新建</el-button> -->
              <el-button @click="generateDoc(row)" type="primary" size="small" link>生成</el-button>
              <el-button @click="handleUpload(row)" type="primary" size="small" link>上传</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="flex-1 overflow-hidden">
        <SubDataMod ref="SubRef" :gzjz-id="props.gzjzId" :except-pane="['notaryMatters']" />
      </el-card>
    </div>

    <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable :modal="false" show-close destroy-on-close width="400">
      <div class="h-100px flex flex-col items-center justify-center gap-16px">
        <div class="flex items-center justify-center gap-10px">
          <strong>公证事项：</strong>
          <el-select v-model="gzsxState.gzjzGzsxId" @change="gzsxSelectChange" default-first-option filterable style="width: 200px;">
            <el-option v-for="item in gzsxState.listData" :key="item.id" :label="item.gzsxMc + item.gzsBh" :value="item.id" />
          </el-select>
        </div>
        <div class="flex items-center justify-center gap-10px">
          <strong>文档模板：</strong>
          <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;" :disabled="gzsxState.gzsxId === ''">
            <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
        <div v-if="genState.warningShow" class="flex items-center justify-end">
          <el-text type="danger">未查询到模板，请添加模板后重试或上传本地文档文件</el-text>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button type="primary" @click="comfirmGen" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
          <el-button @click="genClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <!-- 证据材料 -->
    <NewZjcl v-model="newZjclState.visible" :title="newZjclState.title" />
    <DragUpload v-model="uploadState.visible" :title="uploadState.title" :disabled-upload="uploadState.disabledUpload" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" >
      <template #header>
        <div class="flex mb-12px">
          <div class="flex gap-6px items-center">
            <strong class="w-100px flex justify-end">公证事项：</strong>
            <el-select v-model="uploadState.gzjzGzsxId" @change="uploadGzsxChange" style="width: 240px;">
              <el-option v-for="item in uploadState.gzsxOptions" :value="item.id" :key="item.id" :label="item.gzsxMc + item.gzsBh" />
            </el-select>
          </div>
        </div>
      </template>
    </DragUpload>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="toZjcl">证据材料</el-button>
        <el-button @click="closeDialog">关 闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed, onMounted } from 'vue';
import { formatDate, dictMapFormat, nodeFilter } from '@/utils/ruoyi';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { UploadStatus } from '@/components/FileUpload/type';
import NewZjcl from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/index.vue'
import SubDataMod from '@/views/gongzheng/gongzheng/components/sl/wdnd/SubDataMod.vue'
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import { docGenerator, docOpenEdit } from '@/views/gongzheng/doc/DocEditor'
import { UserDocGenParams } from '@/views/gongzheng/doc/type';
import { wsDocTableData } from '../preset_data'
import { addGzjzWjccxx, delGzjzWjccxx, listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';

interface Props {
  modelValue?: boolean,
  title?: string,
  gzjzId?: string | number
}

const props = withDefaults(defineProps<Props>(),{
  modelValue: false,
  title: '代书'
})

const emit = defineEmits(['update:modelValue', 'closed'])

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_dsr_jslx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_dsr_jslx', 'gz_dsr_lb'));

/**====================================================================== 数据 START ======================================================================**/
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const dswdState = reactive({
  loading: false,
  listData: [] as any[]
})

const jzDetailState = reactive({
  visible: false
})

const SubRef = ref(null)

const wsDocTypeList = ref(wsDocTableData)

const genState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  typeData: [],
  mbId: '',
  warningShow: false
})

const gzsxState = reactive({
  listData: [],
  gzjzGzsxId: '',
  gzsxId: '',
})

// 上传相关数据
const uploadState = reactive({
  loading: false,
  visible: false,
  title: '上传',
  docType: '',
  docId: '',
  sxRow: null,

  gzsxId: null,
  gzjzGzsxId: '',
  gzsxOptions: [],
  disabledUpload: true,
})

// 证据材料
const newZjclState = reactive({
  visible: false,
  loading: false,
  title: '证据材料'
})

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

/**====================================================================== 数据 END ======================================================================**/

/**====================================================================== 逻辑函数 START ======================================================================**/

const openJzDetail = () => {
  jzDetailState.visible = true;
}

// 文件预览
const handleOpen = (data: any) => {
  // 获取文件名后缀
  const obj = JSON.parse(data.wblj || '{}');
  if (obj.path) {
    docOpenEdit(obj.path)
  }
}

const gzsxSelectChange = (val: any) => {
  gzsxState.gzsxId = gzsxState.listData.find(item => item.id === val).gzsxId

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模板，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  genState.typeData = [];
  genState.mbId = '';

  queryMbFiles({ wdLb: genState.sxRow.typeCode, ywId: gzsxState.gzsxId }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模板为空，请上传模板后重试或选择本地上传文档')
        genState.warningShow = true;
      } else {
        genState.typeData = res.data;
        genState.mbId = res.data[0].id; // 默认选中第一个模板
        genState.warningShow = false;
      }
    }
  }).catch((err: any) => {
    console.log('查询模板文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

const genClose = () => {
  genState.loading = false;
  genState.visible = false;
}

const genClosed = () => {
  genState.sxRow = null
  genState.mbId = ''
  genState.warningShow = false;
  gzsxState.gzsxId = ''
}

// 确认生成文档
const comfirmGen = () => {
  if (!genState.mbId) {
    ElMessage.warning('未选择生成指定模板')
    return;
  }

  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)

  let params: UserDocGenParams = {
    bizId: (curGzjz.value.id || currentRecordId.value) as string,
    // type: genState.sxRow.typeCode,
    // fjlb: EnumDocFileType.QT,
    mbWdId: genState.mbId
  }

  const dsrIds = dsrIdArr.join(',')

  params.extraParams = {
    gzxsId: gzsxState.gzsxId,
    dsrIds
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  genState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      const gzsxInfo = gzsxState.listData.find((item) => item.id === gzsxState.gzjzGzsxId)
      const fileName = `${genState.sxRow.typeName}_${gzsxInfo.gzsxMc}_${gzsxInfo?.gzsBh || formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
      const docInfo: GzjzWjccxxForm = {
        wbmc: fileName,
        wblj: JSON.stringify({
          ossId,
          path,
          fileSuffix,
          fileName,
          typeCode: genState.sxRow.typeCode,
          typeName: genState.sxRow.typeName,
          mbWdId: genState.mbId,
          dsrIds,
          gzsxId: gzsxState.gzsxId,
          gzsx: gzsxInfo.gzsxMc,
        }),
        lx: genState.sxRow.typeCode,
        gzjzGzsxId: gzsxState.gzjzGzsxId,
        gzsx: gzsxInfo.gzsxMc,
        gzsbh: gzsxInfo?.gzsBh || ''
      }
      relateDoc(docInfo);
      genState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败,请检查模板是否完善')
  }).finally(() => {
    genState.loading = false;
    loading.close()
  })
}

// 文档文件列表查询
const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      lx: '1'
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

// 加载公证事项列表
const loadGzsxList = async () => {
  if (!props.gzjzId && !curGzjz.value.id && !currentRecordId.value) {
    gzsxState.listData = []
    return;
  };
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
      // gzsxState.listData = (res.rows || []).reduce((doneArr, curItem) => {
      //   const found = doneArr.find(i => {
      //     console.log('found', i.gzsxId, curItem.gzsxId)
      //     return i.gzsxId === curItem.gzsxId
      //   })
      //   if(!found) {
      //     doneArr.push(curItem)
      //   }
      //   return doneArr;
      // }, []);
      uploadState.gzsxOptions = gzsxState.listData;
      console.log('加载公证事项列表成功', gzsxState.listData);
    }
  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项失败');
  } finally {
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  wsDocTypeList.value.map(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    source = noMatches;
  })
}

// 添加生成后的文档（关联生成文档）
const relateDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    const res = await addGzjzWjccxx(params);
    if(res.code === 200) {
      ElMessage.success('生成文件添加成功')
      loadDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

// 删除关联文档
const delDoc = async (id: number | string) => {
  delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadDocList()
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
}

// 文件删除
const handleDelete = (data: any) => {
  ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDoc(data.id)
  }).catch(() => {
    // ElMessage.info('已取消删除')
  })
}

// 生成
const generateDoc = (row: any) => {
  const subIns = SubRef.value;
  const selectedDsrList = subIns?.getSelectedDsr();
  if(selectedDsrList.length === 0) {
    ElMessage.warning('至少选一个当事人');
    return;
  }

  genState.sxRow = row;
  genState.visible = true;
  genState.title = `${row.typeName} 文档生成`;
}

// 处理上传
const handleUpload = (row: any) => {
  uploadState.visible = true
  uploadState.title = `上传文档 - ${row.typeName}`;
  uploadState.sxRow = row;
}

// 每一个上传完成回调
const uploadEveryDone = (data: UploadStatus) => {
  if (data.status === 'success') {

  } else if (data.status === 'error') {

  }
}

// 全部上传完成回调 data 上传成功的文件信息数组，不含失败
const uploadAllDone = (data: any[]) => {
  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)

  const dsrIds = dsrIdArr.join(',')
  const gzsxInfo = uploadState.gzsxOptions.find(i => i.id === uploadState.gzjzGzsxId)

  const mxList = data.map((item) => {
    const { fileName, ossId, path } = item;
    const docInfo: GzjzWjccxxForm = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      gzjzGzsxId: uploadState.gzjzGzsxId,
      gzsx: gzsxInfo.gzsxMc,
      wbmc: fileName,
      wblj: JSON.stringify({
        ossId,
        path,
        fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
        fileName,
        typeCode: uploadState.sxRow.typeCode,
        typeName: uploadState.sxRow.typeName,
        dsrIds,
        gzsxId: gzsxInfo.gzsxId,
        gzsx: gzsxInfo.gzsxMc,
      }),
      lx: uploadState.sxRow.typeCode
    }

    return addGzjzWjccxx(docInfo)
  });

  Promise.all(mxList).then(() => {

  }).finally(() => {
    loadDocList();
  })
}

const uploadGzsxChange = (val: any) => {
  if(val) {
    uploadState.disabledUpload = false;
  } else {
    uploadState.disabledUpload = true;
  }
}

// 打开证据材料页面
const toZjcl = () => {
  console.log('打开证据材料上传页面');
  newZjclState.visible = true;
}

// 关闭当前主dialog
const closeDialog = () => {
  emit('update:modelValue', false)
  emit('closed')
}

const closed = () => {
  emit('closed')
}

/**====================================================================== 逻辑函数 END ======================================================================**/

watch(() => uploadState.visible, (val) => {
  if(!val) {
    uploadState.gzjzGzsxId = null;
    uploadState.disabledUpload = true;
  }
})

onMounted(() => {
  loadGzsxList();
  loadDocList();
})

</script>

<style scoped>
.gz-ds-wrap {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
}
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.zmcl-tag+.zmcl-tag {
  margin-left: 8px;
}

:deep(.el-card__body) {
  height: 100%;
}
</style>
