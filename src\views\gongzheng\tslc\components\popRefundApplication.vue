<template>
  <!-- 退费申请弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <template #default>
      <div class="refund-application">
        <!-- 申请信息区域 -->
        <div class="application-info">
          <h3>申请信息</h3>
          <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px" size="small">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="申请人：" prop="applicant">
                  <el-input v-model="formData.applicant" placeholder="申请人" readonly disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="申请日期：" prop="applicationDate">
                  <el-date-picker v-model="formData.applicationDate" readonly disabled type="date" placeholder="申请日期"
                    value-format="YYYY-MM-DD" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审批人*：" prop="approver">
                  <el-select v-model="formData.approver" filterable placeholder="请选择">
                  <el-option v-for="item in tsjzspy" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="退费方式*：" prop="refundMethod">
                  <el-select v-model="formData.refundMethod" placeholder="请选择退费方式" style="width: 245px">
                    <el-option v-for="dict in gz_tslc_tffs" :key="dict.value" :label="dict.label"
                      :value="Number(dict.value)" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="申请原因*：" prop="reason">
              <el-input v-model="formData.reason" type="textarea" :rows="3" placeholder="请输入申请原因" />
            </el-form-item>
          </el-form>
        </div>

        <!-- 卷宗信息区域 -->
        <div class="case-info">
          <div class="section-header">
            <h3>卷宗信息</h3>
            <el-button type="primary" @click="handleSelectCase" v-if="formData.id==null">选择卷宗</el-button>
          </div>
          <el-descriptions :column="3" border v-if="formData.caseInfo">
            <el-descriptions-item label="卷宗号">{{ formData.caseInfo.jzbh }}</el-descriptions-item>
            <el-descriptions-item label="公证员">{{ formData.caseInfo.gzyxm }}</el-descriptions-item>
            <el-descriptions-item label="助理">{{ formData.caseInfo.zlxm }}</el-descriptions-item>
            <el-descriptions-item label="受理日期">{{ formData.caseInfo.slrq }}</el-descriptions-item>
            <el-descriptions-item label="公证类别">
              {{ dictMapFormat(gz_gzlb, formData.caseInfo.lb) }}
            </el-descriptions-item>
            <el-descriptions-item label="当事人">{{ formData.caseInfo.dsrxm }}</el-descriptions-item>
          </el-descriptions>
          <div v-if="formData.caseInfo==null" class="no-case">
            <el-empty description="请选择卷宗" />
          </div>
        </div>

        <!-- 收费列表区域 -->
        <div class="charge-list">
          <div class="section-header">
            <h3>收费列表</h3>
            <el-button type="primary" @click="handleBatchModify" v-has-permi="['tslc:fq:edit']">批量修改退费</el-button>
          </div>
          <el-table :data="formData.chargeList" border stripe style="width: 100%">
            <el-table-column label="费用类型" prop="fylx" align="center">
              <template #default="scope">
                {{ dictMapFormat(gz_sf_lb, scope.row.fylx) }}
              </template>
            </el-table-column>
            <el-table-column label="公证事项" prop="gzjzGzsx" align="center" show-overflow-tooltip />
            <el-table-column label="公证书编号" prop="gzsbh" align="center" />
            <el-table-column label="应收" prop="fyys" align="center" width="100">
              <template #default="scope">
                {{ formatMoney(scope.row.fyys) }}
              </template>
            </el-table-column>
            <el-table-column label="已收" prop="fyss" align="center" width="100">
              <template #default="scope">
                {{ formatMoney(scope.row.fyss) }}
              </template>
            </el-table-column>
            <el-table-column label="减免" prop="fyjm" align="center" width="100">
              <template #default="scope">
                {{ formatMoney(scope.row.fyjm) }}
              </template>
            </el-table-column>
            <el-table-column label="已退" prop="fytf" align="center" width="100">
              <template #default="scope">
                {{ formatMoney(scope.row.fytf) }}
              </template>
            </el-table-column>
            <el-table-column label="本次退" align="center" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.thisFytf" type="number" size="small"
                  @change="handleRefundChange(scope.row)" @blur="handleRefundChange(scope.row)" />
                <!-- <div class="editable-tip">可修改</div> -->
              </template>
            </el-table-column>
            <el-table-column label="收费状态" prop="sfzt" align="center" width="100">
              <template #default="scope">
                {{ dictMapFormat(gz_sfzt, scope.row.sfzt) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 合计行 -->
          <div class="total-row">
            <span>合计：</span>
            <span>应收：{{ formatMoney(totalReceivable) }}</span>
            <span>已收：{{ formatMoney(totalReceived) }}</span>
            <span>减免：{{ formatMoney(totalReduction) }}</span>
            <span>已退：{{ formatMoney(totalAlreadyRefunded) }}</span>
            <span>本次退：{{ formatMoney(totalThisRefund) }}</span>
          </div>
        </div>
      </div>

      <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable show-close destroy-on-close width="400">
        <div class="h-100px flex items-center justify-center">
          <div class="flex items-center justify-center gap-10px">
            <strong>文档模版：</strong>
            <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;">
              <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
            </el-select>
          </div>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-10px">
            <el-button type="primary" @click="genSpb" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
            <el-button @click="genClose">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="doModalClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-has-permi="['tslc:fq:edit']">提交审批</el-button>
        <el-button @click="handleRefundRecord" v-has-permi="['tslc:fq:query']">退费记录</el-button>
        <template v-if="formData.id">
          <el-button @click="toGen" v-if="!spbInfo" v-has-permi="['tslc:fq:query']">生成审批表</el-button>
          <el-button @click="() => openSpb()" v-else v-has-permi="['tslc:fq:query']">打开审批表</el-button>
        </template>
        <el-button @click="handleSaveDraft" v-has-permi="['tslc:fq:edit']">保存草稿</el-button>
      </div>
    </template>
  </vxe-modal>

  <!-- 选择卷宗弹窗 -->
  <SelectCaseDialog ref="selectCaseRef" @success="onCaseSelected" />

  <!-- 退费记录弹窗 -->
  <PopRefundRecord ref="refundRecordRef" />
</template>

<script setup name="PopRefundApplication" lang="ts">
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  import { VxeModalProps } from 'vxe-pc-ui'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { Action } from 'element-plus'
  import SelectCaseDialog from './SelectCaseDialog.vue'
  import PopRefundRecord from './popRefundRecord.vue'
  import { addTslcSqb, saveDraftTslcSqb, getChargeList, searchCases, updateTslcSqb } from '@/api/gongzheng/tslc/tslcSqb'
  import { useUserStore } from '@/store/modules/user'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser('tsjzspy'));
  const { gz_sf_lb, gz_sfzt, gz_gzlb, gz_tslc_tffs } = toRefs<any>(proxy?.useDict('gz_tslc_tffs', 'gz_sf_lb', 'gz_sfzt', 'gz_gzlb'));
  import { dictMapFormat } from '@/utils/ruoyi';
import { docGenerator, docOpenEdit } from '../../doc/DocEditor';
import { UserDocGenParams } from '../../doc/type';
import { queryOssInfo } from '@/api/gongzheng/gongzheng/oss';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
  const route = useRoute()
  const userStore = useUserStore()

  // 定义 props 类型
  interface Props {
    title ?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '退费申请'
  });

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'success') : void
    (event : 'close') : void
  }>()

  // 弹窗控制
  const showPopup = ref(false)
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: '1200px',
    height: '90%',
    escClosable: true,
    resize: true,
    showMaximize: true,
    destroyOnClose: true
  })

  // 表单数据
  const formData = reactive({
    id: null,
    applicant: userStore.nickname,
    applicationDate: new Date().toISOString().split('T')[0],
    approver: null,
    refundMethod: 1,
    reason: '',
    caseInfo: null as any,
    chargeList: [] as any[]
  })

  const spbInfo = ref(null)

  // 表单验证规则
  const formRules = {
    approver: [
      { required: true, message: '请输入审批人', trigger: 'blur' }
    ],
    refundMethod: [
      { required: true, message: '请选择退费方式', trigger: 'change' }
    ],
    reason: [
      { required: true, message: '请输入申请原因', trigger: 'blur' }
    ]
  }

  // 表单引用
  const formRef = ref()
  const selectCaseRef = ref<InstanceType<typeof SelectCaseDialog> | null>(null)
  const refundRecordRef = ref<InstanceType<typeof PopRefundRecord> | null>(null)

  // 计算属性
  const totalReceivable = computed(() => {
    return formData.chargeList.reduce((sum, item) => sum + (Number(item.fyys) || 0), 0)
  })

  const totalReceived = computed(() => {
    return formData.chargeList.reduce((sum, item) => sum + (Number(item.fyss) || 0), 0)
  })

  const totalReduction = computed(() => {
    return formData.chargeList.reduce((sum, item) => sum + (Number(item.fyjm) || 0), 0)
  })

  const totalAlreadyRefunded = computed(() => {
    return formData.chargeList.reduce((sum, item) => sum + (Number(item.fytf) || 0), 0)
  })

  const totalThisRefund = computed(() => {
    return formData.chargeList.reduce((sum, item) => sum + (parseFloat(item.thisFytf) || 0), 0)
  })

  const genState = reactive({
    visible: false,
    loading: false,
    title: '',
    typeData: [],
    mbId: '',
  })

  // 方法
  const open = async (option : any = {}) => {
    showPopup.value = true
    resetForm()
    if (option && option.id) {
      await loadDetailForEdit(option.id)
    }
  }

  const close = () => {
    showPopup.value = false;
    emits('close');
  }

  const doModalClose = () => {
    emits('close');
    showPopup.value = false;
    console.log('退费申请窗口关闭了');
  }

  // 选择卷宗
  const handleSelectCase = () => {
    selectCaseRef.value?.open();
  }

  // 卷宗选择成功回调
  const onCaseSelected = async (caseInfo : any) => {
    formData.caseInfo = caseInfo;
    await loadChargeList(caseInfo);
  }

  // 编辑模式加载详情
  const loadDetailForEdit = async (id : string | number) => {
    try {
      const res : any = await (await import('@/api/gongzheng/tslc/tslcSqb')).getTslcSqb(id)
      const data = res.data || {}
      formData.id = data.id;
      formData.applicant = data.tslcFqr || formData.applicant
      formData.applicationDate = data.tslcFqrq || formData.applicationDate
      if (data.tslcSprId) {
        formData.approver = data.tslcSprId
      }
      formData.refundMethod = data.refundMethod ?? formData.refundMethod
      formData.reason = data.tslcSqyy || ''
      formData.caseInfo = data.caseInfo;
      formData.caseInfo.lb = formData.caseInfo.lb + ''

      // 审批表信息
      spbInfo.value = data.tslcSpbwj

      if (Array.isArray(data.items) && data.items.length) {
        formData.chargeList = data.items.map((it : any) => ({
          fylx: it.fylx ?? it.feeType,
          gzjzGzsx: it.gzjzGzsx ?? it.gzsx ?? it.notarizationItem,
          gzjzGzsxId: it.gzjzGzsxId,
          gzsbh: it.gzsbh ?? it.certificateNumber,
          fyys: Number(it.fyys ?? it.receivable ?? 0),
          fyss: Number(it.fyss ?? it.received ?? 0),
          fyjm: Number(it.fyjm ?? it.reduction ?? 0),
          fytf: Number(it.fytf ?? it.alreadyRefunded ?? 0), // 已退
          thisFytf: String(it.thisFytf ?? it.thisRefund ?? 0), // 本次退
          sfzt: it.sfzt ?? it.chargeStatus ?? ''
        }))
      } else if (data.gzjzId || data.jzh) {
        await loadChargeList({ id: data.gzjzId, caseNumber: data.jzh })
      }
    } catch (e) {
      console.error('编辑加载失败', e)
    }
  }

  // 加载收费列表
  const loadChargeList = async (caseInfo : any) => {
    try {
      const res : any = await getChargeList({ gzjzId: caseInfo.gzjzId, jzh: caseInfo.jzbh })
      const rows = res.rows || res.data?.rows || []
      formData.chargeList = rows.map((it : any) => ({
        fylx: it.fylx,
        gzjzGzsx: it.gzjzGzsx || it.gzsx,
        gzjzGzsxId: it.gzjzGzsxId,
        gzsbh: it.gzsbh,
        fyys: Number(it.fyys ?? 0),
        fyss: Number(it.fyss ?? 0),
        fyjm: Number(it.fyjm ?? 0),
        fytf: Number(it.fytf ?? 0), // 已退
        thisFytf: '0', // 本次退默认0
        sfzt: it.sfzt || '',
      }))
    } catch (e) {
      ElMessage.error('加载收费明细失败')
    }
  }

  // 批量修改退费
  const handleBatchModify = () => {
    ElMessage.info('批量修改功能开发中...');
  }

  // 退费金额变化校验：本次退 <= 已收，且不可为负
  const handleRefundChange = (row ?: any) => {
    if (!row) return
    let val = Number(row.thisFytf || 0)
    if (isNaN(val) || val < 0) val = 0
    const max = Number(row.fyss || 0)
    if (val > max) {
      ElMessage.warning('本次退金额不能超过已收金额')
      val = max
    }
    row.thisFytf = String(val)
  }

  // 提交审批
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      if (!formData.caseInfo) {
        ElMessage.warning('请先选择卷宗');
        return;
      }

      if (totalThisRefund.value <= 0) {
        ElMessage.warning('本次退费金额必须大于0');
        return;
      }

      // 行级校验：本次退 <= 已收
      for (let i = 0; i < formData.chargeList.length; i++) {
        const it : any = formData.chargeList[i]
        const val = Number(it.thisFytf || 0)
        const max = Number(it.fyss || 0)
        if (val > max) {
          ElMessage.warning(`第${i + 1}行本次退金额不能超过已收金额`)
          return
        }
      }
console.log(formData.chargeList)
      // 统一提交接口
      const payload : any = {
        id: formData?.id,
        tslcLx: '6',
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        gzsbh: formData.caseInfo?.gzsbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.reason,
        refundMethod: formData.refundMethod,
        items: formData.chargeList.map(it => ({
          fylx: it.fylx,
          gzjzGzsx: it.gzjzGzsx,
          gzsbh: it.gzsbh,
          fyys: Number(it.fyys || 0),
          fyss: Number(it.fyss || 0),
          fyjm: Number(it.fyjm || 0),
          thisFytf: Number(it.thisFytf || 0),
          sfzt: it.sfzt,
          gzjzGzsxId: it.gzjzGzsxId
        }))
      }
      await addTslcSqb(payload)
      ElMessage.success('提交成功')
      emits('success', payload)
      close()
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('请检查表单信息');
    }
  }

  // 退费记录
  const handleRefundRecord = () => {
    refundRecordRef.value?.open();
  }

  const genClose = () => {
    genState.loading = false;
    genState.visible = false;
    genClosed();
  }

  const genClosed = () => {
    genState.mbId = ''
  }

  // 生成模板选择
  const toGen = () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在获取文档模版，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.2)',
      fullscreen: true
    })
    queryMbFiles({ wdLb: 92 }).then((res) => {
      if (res.code === 200) {
        if(!res.data || res.data.length === 0) {
          ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
        } else {
          genState.typeData = res.data;
          genState.mbId = res.data[0].id;
          genState.visible = true;
          genState.title = `退费审批表生成`;
        }
      }
    }).catch((err: any) => {
      console.log('查询模版文件异常', err);
    }).finally(() => {
      loading.close();
    })
  }

    // 生成审批表
  const genSpb = () => {
    if (!genState.mbId) {
      ElMessage.warning('未选择生成指定模版')
      return;
    }

    genState.loading = true;
    console.log('生成审批表:', formData);

    let params : UserDocGenParams = {
      bizId: formData.caseInfo?.gzjzId,
      mbWdId: genState.mbId,
      extraParams: {
        // gzxsId: formData.caseInfo?.gzjzId,
        tslcId: formData.id,
        // item: JSON.stringify(formData.chargeList),
        sqyy: formData.reason
      }
    }

    const loading2 = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    docGenerator(params, {
      success: (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        // const fileName = `费用减免-${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`;

        updateSpb(ossId);
        genClose()
      }
    }).catch((err : any) => {
      console.error('生成审批表异常', err)
      ElMessage.error('生成审批表异常')
    }).finally(() => {
      loading2.close()
      genState.loading = false;
    })
  };

  // 存储审批表ossId
  const updateSpb = async (ossId: string) => {
    try {
      const userStore = useUserStore()
      const params = {
        id: formData.id,
        tslcSpbwj: ossId,
        tslcFqrId: userStore.userId,
        tslcFqr: formData.applicant || userStore.nickname,
        tslcLx: '6',
        gzjzId: formData.caseInfo?.gzjzId,
        gzsbh: formData.caseInfo?.gzsbh,
        jzh: formData.caseInfo?.jzbh,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.reason,
        items: formData.chargeList
      }
      const res = await updateTslcSqb(params);
      if(res.code === 200) {
        spbInfo.value = ossId;
        ElMessageBox.confirm('生成完成，是否打开?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning',
        }).then(() => {
          openSpb(ossId)
        })
      }
    } catch (err: any) {
      console.error('添加审批表失败', err)
      ElMessage.error('添加审批表失败')
    }
  }

  // 打开审批表
  const openSpb = async (ossId?: string) => {
    try {
      const ossRes = await queryOssInfo(ossId || spbInfo.value);
      if(ossRes.code === 200) {
        const { ossId, fileName: path, fileSuffix } = ossRes.data[0];
        docOpenEdit(path)
      }
    } catch (err: any) {
      ElMessage.error('获取文件信息失败');
    }
  }

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      const payload : any = {
        id: formData?.id,
        tslcLx: '6',
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        gzsbh: formData.caseInfo?.gzsbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.reason,
        refundMethod: formData.refundMethod,
        items: formData.chargeList
      }
      await saveDraftTslcSqb(payload)
      ElMessage.success('草稿保存成功')
      emits('success', payload)
      close()
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      applicant: userStore.nickname,
      applicationDate: new Date().toISOString().split('T')[0],
      approver: '',
      refundMethod: 1,
      reason: '',
      caseInfo: null,
      chargeList: []
    });
    formRef.value?.clearValidate();
  }

  // 格式化金额（容错字符串/空值）
  const formatMoney = (amount : unknown) => {
    const n = Number(amount ?? 0)
    return Number.isNaN(n) ? '0.00' : n.toFixed(2)
  }

  onMounted(() => {
    console.log('RefundApplication:onMounted');
  });

  onUnmounted(() => {
    console.log('RefundApplication:onUnmounted');
  });

  // 暴露方法给父组件
  defineExpose({
    open, close
  })
</script>

<style scoped>
  .refund-application {
    padding: 20px;
  }

  .application-info,
  .case-info,
  .charge-list {
    margin-bottom: 30px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
  }

  .no-case {
    text-align: center;
    padding: 40px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .total-row {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    font-weight: bold;
  }

  .editable-tip {
    font-size: 12px;
    color: #f56c6c;
    margin-top: 2px;
  }

  .dialog-footer {
    text-align: right;
    padding-top: 10px;
    border-top: 1px solid #e0e0e0;
  }
</style>
