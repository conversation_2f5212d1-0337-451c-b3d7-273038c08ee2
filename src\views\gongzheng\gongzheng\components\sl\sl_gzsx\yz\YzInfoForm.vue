<template>
  <div v-loading="linkDsrState.loading">
    <el-form :model="yzData" :rules="rules" ref="yzFormRef" label-width="100" label-suffix=":">
      <el-row>
        <el-col :span="24">
          <el-form-item prop="qzrIds" label="遗嘱人">
            <el-select v-model="yzData.qzrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="dlrIds" label="代理人">
            <el-select v-model="yzData.dlrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="gtyz" label="共同遗嘱">
            <el-select v-model="yzData.gtyz">
              <el-option v-for="item in gz_yes_or_no" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { GzjzYzVOEdit } from '@/api/gongzheng/bzfz/gzjzYz/types';
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';


interface Props {
  modelValue: Record<string, any>;
}

const props = defineProps<Props>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yes_or_no'))

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['update:modelValue'])

const yzData = computed<GzjzYzVOEdit>({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const rules = {
  qzrIds: [
    { required: true, message: '请选择遗嘱人', trigger: 'change' }
  ],
  dlrIds: [
    // { required: true, message: '请选择代理人', trigger: 'change' }
  ],
  gtyz: [
    { required: true, message: '请选择是否共同遗嘱', trigger: 'change' }
  ],
}

const linkDsrState = reactive({
  dsrList: [],
  loading: false,
})

const yzFormRef = ref<ElFormInstance>(null)

// 加载当事人列表
const loadDsrList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    linkDsrState.dsrList = []
    return;
  };
  linkDsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        linkDsrState.dsrList = (res.rows || []).reduce((doneArr, curItem) => {
          const found = doneArr.find(i => {
            return i.dsrId == curItem.dsrId
          })
          if(!found) {
            doneArr.push(curItem)
          }
          return doneArr;
        }, []);
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    linkDsrState.loading = false;
  }
}

const validate = async () => {
  return yzFormRef.value?.validate();
}

defineExpose({
  validate
})

onMounted(() => {
  loadDsrList();
})
</script>