import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzFqVO, GzjzFqForm, GzjzFqQuery } from '@/api/gongzheng/bzfz/gzjzFq/types';

/**
 * 查询公证卷宗-赋强列表
 * @param query
 * @returns {*}
 */

export const listGzjzFq = (query?: GzjzFqQuery): AxiosPromise<GzjzFqVO[]> => {
  return request({
    url: '/gongzheng/gzjzFq/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-赋强详细
 * @param id
 */
export const getGzjzFq = (id: string | number): AxiosPromise<GzjzFqVO> => {
  return request({
    url: '/gongzheng/gzjzFq/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-赋强
 * @param data
 */
export const addGzjzFq = (data: GzjzFqForm) => {
  return request({
    url: '/gongzheng/gzjzFq',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-赋强
 * @param data
 */
export const updateGzjzFq = (data: GzjzFqForm) => {
  return request({
    url: '/gongzheng/gzjzFq',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-赋强
 * @param id
 */
export const delGzjzFq = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzFq/' + id,
    method: 'delete'
  });
};

/**
 * 根据gzjzGzsxId查询赋强信息
 * @param params 
 * @returns 
 */
export const getGzjzFqByGzjzGzsxId = (params: { gzjzGzsxId: string }): AxiosPromise<GzjzFqVO> => {
  return request({
    url: '/gongzheng/gzjzFq/getByGzjzGzsxId',
    method: 'get',
    params
  })
}