import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzBdcqVO, GzjzBdcqForm, GzjzBdcqQuery } from '@/api/gongzheng/gzjzBdcq/types';

/**
 * 查询房产信息列表
 * @param query
 * @returns {*}
 */

export const listGzjzBdcq = (query?: GzjzBdcqQuery): AxiosPromise<GzjzBdcqVO[]> => {
  return request({
    url: '/gongzheng/gzjzBdcq/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询房产信息详细
 * @param id
 */
export const getGzjzBdcq = (id: string | number): AxiosPromise<GzjzBdcqVO> => {
  return request({
    url: '/gongzheng/gzjzBdcq/' + id,
    method: 'get'
  });
};

/**
 * 新增房产信息
 * @param data
 */
export const addGzjzBdcq = (data: GzjzBdcqForm) => {
  return request({
    url: '/gongzheng/gzjzBdcq',
    method: 'post',
    data: data
  });
};

/**
 * 修改房产信息
 * @param data
 */
export const updateGzjzBdcq = (data: GzjzBdcqForm) => {
  return request({
    url: '/gongzheng/gzjzBdcq',
    method: 'put',
    data: data
  });
};

/**
 * 删除房产信息
 * @param id
 */
export const delGzjzBdcq = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzBdcq/' + id,
    method: 'delete'
  });
};
