<template>
  <div class="party-person-list">
    <div class="section-header">
      <h3>当事人列表</h3>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增当事人</el-button>
        <el-button @click="handleYyDsr">引用当事人</el-button>
        <el-button @click="toTakePhoto">拍照</el-button>
        <!-- <el-button @click="handleMerge">合照</el-button> -->
        <!-- <el-button @click="handleGenerate">生成信息表</el-button> -->
        <!-- <el-button @click="handleAppCard">app认证卡</el-button> -->
        <el-button @click="handleAddHmd">加入黑名单</el-button>
        <el-button @click="handleSplx">视频连线</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </div>
    </div>

    <el-table :data="partyPersonList" style="width: 100%" border highlight-current-row height="300px"
      @selection-change="handleSelectionChange" @row-dblclick="handleDblclickRow" v-loading="loading" size="small">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作" align="center" width="240" fixed>
        <template #default="scope">
          <el-button link type="primary" size="small" @click="handleBz(scope.row)" style="margin-left: 1px;  padding: 2px 4px;">
            办证({{scope.row.bzTotal}})
          </el-button>
          <el-button v-if="scope.row.dsrLx == '1'" link type="primary" size="small" @click="handleZx(scope.row)" style="margin-left: 1px;  padding: 2px 4px;">
            咨询({{scope.row.zxTotal}})
          </el-button>
          <el-button link type="primary" size="small" @click="handleCopy(scope.row)" style="margin-left: 1px;  padding: 2px 4px;">
            复制
          </el-button>
          <el-button v-if="scope.row.js !== '4' && scope.row.dsrLx == '1'" link type="primary" size="small" @click="handleCopyDlr(scope.row)" style="margin-left: 1px;  padding: 2px 4px;">
            复制为代理人
          </el-button>
          <el-button v-if="scope.row.js === '4' && scope.row.dsrLx == '1'" link type="primary" size="small" @click="handleDl(scope.row)" style="margin-left: 1px;  padding: 2px 4px;">
            代理
          </el-button>
          <!-- <el-button link type="primary" size="small" @click="handleEdit(scope.row)">
            编辑
          </el-button> -->
        </template>
      </el-table-column>
      <el-table-column label="读卡" width="80" align="center">
        <template #default="{ row }">
          <!-- <el-button link type="primary" size="small" @click="() => handleRedCard(scope.row)">读卡</el-button> -->
           <el-tag :type="row.sfdk === '1' ? 'success' : 'info'">{{ row.sfdk === '1' ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" width="120" align="center">
        <template #default="scope">
          {{ scope.row.name || scope.row.xm }}
        </template>
      </el-table-column>
      <el-table-column label="性别" prop="gender" width="80" align="center">
        <template #default="scope">
          {{ formatGender(scope.row.gender || scope.row.xb || scope.row.sex) }}
        </template>
      </el-table-column>
      <el-table-column label="证件类型" prop="certificateType" width="100" align="center">
        <template #default="scope">
          <dict-tag v-if="scope.row.dsrLx==='1'" :options="gz_gr_zjlx" :value="scope.row.certificateType" />
          <dict-tag v-if="scope.row.dsrLx==='2'" :options="gz_jg_zjlx" :value="scope.row.certificateType" />
        </template>
      </el-table-column>
      <el-table-column label="证件号码" prop="idNumber" width="180" align="center">
        <template #default="scope">
          {{ scope.row.idNumber || scope.row.zjhm ||scope.row.certificateNo}}
        </template>
      </el-table-column>
      <el-table-column label="出生日期" prop="birthDate" width="120" align="center">
        <template #default="scope">
          {{ scope.row.birthDate || scope.row.csrq }}
        </template>
      </el-table-column>
      <el-table-column label="住址" prop="address" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.address || scope.row.zz }}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" prop="contactTel" width="140" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <el-text @click="showTelOrType(row, 'tel')" type="primary">{{ row.contactTel || row.lxdh || '---' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="type" width="100" align="center">
        <template #default="{ row }">
          <el-text @click="showTelOrType(row, 'type')" type="primary">{{ formatPersonType(row.js) }}</el-text>
        </template>
      </el-table-column>
    </el-table>

    <!-- 拍照 -->
    <DsrTakePic v-model="takePhotoState.visible" :title="takePhotoState.title" />

    <!-- 办证记录-->
    <el-dialog v-model="dialogVisible2" :title="dialogTitle2" width="50%" @close="handleDialogClose2" destroy-on-close>
      <BzList v-if="dialogVisible2" :dsrInfo="curDsrInfo"></BzList>
      <template #footer>
        <el-button @click="cancel">关 闭</el-button>
      </template>
    </el-dialog>
    <!-- 咨询记录-->
    <el-dialog v-model="dialogVisible3" :title="dialogTitle3" width="50%" @close="handleDialogClose3">
      <ZxList v-if="dialogVisible3"></ZxList>
      <template #footer>
        <el-button @click="cancel2">关 闭</el-button>
      </template>
    </el-dialog>
    <!-- 引用当事人-->
    <el-dialog :title="dialog4.title" v-model="dialog4.visible" append-to-body>
      <Yydsr v-if="dialog4.visible" ref="yydsrRef" :editDlrlb="disabledDlrlb" @update-select="handleUpdateDsrSelectId">
      </Yydsr>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading2" type="primary" @click="submitForm2">确认引用</el-button>
          <el-button @click="cancel3">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增/编辑当事人-->
    <el-dialog :title="dialog5.title" v-model="dialog5.visible" append-to-body style="width:80%">
      <AddDsr v-if="dialog5.visible" ref="addDsrRef" :editData="editDsrData" @submit="handleAddDsrSubmit"></AddDsr>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading3" type="primary" @click="submitForm3">确 定</el-button>
          <el-button @click="cancel4">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!--查看个人客户-->
    <GrDetail :dialog="dialog6.visible" :dialigEdit="true" :title="dialog6.title" :formPro="currentPersonDetail"
      @close="handleCloseGrDetail">
    </GrDetail>
    <DwDetail v-model:dialog="dialog7.visible" :dialigEdit="true" :title="dialog7.title" :formPro="dbCurDsrInfo">
    </DwDetail>
    <!-- 加入黑名单-->
    <!-- <el-dialog :title="dialog8.title" v-model="dialog8.visible" append-to-body width="50%">
      <div v-if="dialog8.visible">
        <el-form label-width="120px">
          <el-form-item label="当事人姓名：">
            <span>{{ selectedPersonForHmd?.name || selectedPersonForHmd?.xm || '' }}</span>
          </el-form-item>
          <el-form-item label="当事人类型：">
            <span>{{ selectedPersonForHmd?.dsrLx === '1' ? '个人' : '单位' }}</span>
          </el-form-item>
          <el-form-item label="证件号码：">
            <span>{{ selectedPersonForHmd?.idNumber || selectedPersonForHmd?.zjhm ||selectedPersonForHmd?.certificateNo || '' }}</span>
          </el-form-item>
          <el-form-item label="不诚信记录：" required>
            <el-input v-model="hmdReason" type="textarea" :rows="4" placeholder="请输入不诚信记录内容" maxlength="500"
              show-word-limit />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading4" type="primary" @click="submitForm4">确 定</el-button>
          <el-button @click="cancel5">关 闭</el-button>
        </div>
      </template>
    </el-dialog> -->
    <DsrBlackList v-model="dialog8.visible" v-if="dialog8.visible" :dsr-info="selectedPersonForHmd" />
    <!-- 视频连线-->
    <el-dialog :title="dialog9.title" v-model="dialog9.visible" append-to-body>
      <Splx v-if="dialog9.visible"></Splx>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading5" type="primary" @click="submitForm5">确 定</el-button>
          <el-button @click="cancel6">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 代理人设置 -->
    <el-dialog title="代理人设置" v-model="dialog10.visible" append-to-body width="700">
      <el-card>
        <template #header>
          <span>请选择当事人</span>
        </template>
        <el-checkbox v-model="dlDsrCheckall" @change="handleSelectAllDsr" :indeterminate="isISelectAllDsr">全选</el-checkbox>
        <el-checkbox-group
          v-model="checkedDlDsrList"
          @change="handleCheckedDsrChange"
        >
          <el-checkbox v-for="p in availableDlrList" :checked="p.selected" :key="p.dsrId" :label="p.dsrxm" :value="p.dsrId" />
        </el-checkbox-group>
      </el-card>
      <el-card style="margin-top: 6px;">
        <template #header>
          <span>当前代理人代理的当事人</span>
        </template>
        <el-text type="danger">提示:若有修改,先点击确认后才会生效</el-text>
        <div style="margin-top: 20px;">
          <template v-if="okDlDsr.length > 0">
            <el-tag v-for="p in okDlDsr" :key="p.dsrId" type="primary" size="large" effect="dark" hit class="dl-dsr-tag">
              {{ p.dsrxm }}
            </el-tag>
          </template>
          <el-text v-else type="info">暂无代理当事人信息</el-text>
        </div>
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="dialog10.loading" type="primary" @click="handleDlSubmit">确 定</el-button>
          <el-button @click="closeDl">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 当事人 电话/角色类型 修改dialog -->
    <el-dialog :title="dsrdhxg.title" v-model="dsrdhxg.visible" append-to-body width="320">
      <div>
        <h4>公证卷宗当事人：{{ dsrdhxg.name }}</h4>
        <el-input v-if="dsrdhxg.type === 'tel'" type="tel" placeholder="请输入联系电话" v-model="dsrdhxg.contactTel" @input="handleTelInput">
          <!-- <template #prepend>
            <el-select v-model="dsrdhxg.gjh" placeholder="Select" style="width: 90px">
              <el-option label="+86" value="+86" />
              <el-option label="+44" value="+44" />
              <el-option label="+81" value="+81" />
              <el-option label="+33" value="+33" />
            </el-select>
          </template> -->
        </el-input>
        <el-select v-if="dsrdhxg.type === 'type'" v-model="dsrdhxg.js" placeholder="请选择角色类型">
          <el-option v-for="dict in gz_dsr_jslx" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="dsrdhxg.loading" :disabled="dsrdhxg.loading" type="primary" @click="telOrTypeUpdate">确 定</el-button>
          <el-button @click="hideTelOrType">关 闭</el-button>
        </div>
      </template>
     </el-dialog>

  </div>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, inject, watch, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import type { GzjzDsrVO, GzjzDsrForm, GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
  import type { DsrxxZrrVO } from '@/api/gongzheng/dsr/dsrxxZrr/types'
  import { listGzjzDsrByGzjz, addGzjzDsr, updateGzjzDsr, delGzjzDsr, getGzjzDsr, getDlrAvailable, updateDlrDlgx, updateDsrTelAndJs, uploadDsrZp, queryDsrZpList, deleteDsrZp } from '@/api/gongzheng/gongzheng/gzjzDsr'
  import Camera from '@/views/gongzheng/components/Camera.vue';
  import 'vue-cropper/dist/index.css';
  import { VueCropper } from 'vue-cropper';
  import { Delete } from '@element-plus/icons-vue';
  import DsrTakePic from '@/views/gongzheng/gongzheng/components/sl/sl_dsr/DsrTakePic.vue';
  import DsrBlackList from './DsrBlackList.vue'

  import BzList from '@/views/gongzheng/gongzheng/components/bz_list.vue'
  import ZxList from '@/views/gongzheng/gongzheng/components/zx_list.vue'

  import DataItem from '@/views/gongzheng/components/DataItem.vue';

  // 定义对话框选项类型
  interface DialogOption {
    visible: boolean
    title: string
  }

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gr_zjlx, gz_jg_zjlx, gz_dsr_jslx } = toRefs<any>(proxy?.useDict('gz_jg_zjlx', 'gz_gr_zjlx', 'gz_dsr_jslx'));

  interface Props {
    gzjzId ?: string | number // 公证卷宗ID
  }

  interface Emits {
    (e : 'update:data', value : GzjzDsrVO[]) : void
    (e : 'add-person') : void
    (e : 'edit-person', person : GzjzDsrVO) : void
    (e : 'delete-person', id : number) : void
    (e : 'read-card', id : number) : void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 获取当前选中的记录ID (从父组件传入)
  const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));

  // 数据状态
  const loading = ref(false)
  const partyPersonList = ref<GzjzDsrVO[]>([])
  const selectedRows = ref<GzjzDsrVO[]>([])
  const availableDlrList = ref([]); // 可用代理人列表
  const okDlDsr = ref([]);
  const curDlrInfo = ref<GzjzDsrVO>(); // 当前代理人

  const dbCurDsrInfo = ref<GzjzDsrVO>(); // 数据库中的当前代理人信息, 用于对比

  // 对话框状态
  const currentPersonDetail = ref<DsrxxZrrVO>({} as DsrxxZrrVO)
  const dialogVisible2 = ref(false)
  const dialogTitle2 = ref('')
  const dialogVisible3 = ref(false)
  const dialogTitle3 = ref('')
  const dialog10 = ref({
    visible: false,
    title: '代理人设置',
    loading: false
  })

  interface Options {
    img: string | any; // 裁剪图片的地址
    autoCrop: boolean; // 是否默认生成截图框
    autoCropWidth: number; // 默认生成截图框宽度
    autoCropHeight: number; // 默认生成截图框高度
    fixedBox: boolean; // 固定截图框大小 不允许改变
    fileName: string;
    previews: any; // 预览数据
    outputType: string;
    visible: boolean;
    preShow: boolean;
    centerBox: boolean;
    original: boolean;
    full: boolean;
  }

  const dlDsrCheckall = ref(false);
  const isISelectAllDsr = ref(true);
  // 已选代理人
  const checkedDlDsrList = ref([]);

  const curDsrInfo = ref<GzjzDsrVO>(null); // 当前当事人

  // 修改当事人电话或角色类型的对话框
  const dsrdhxg = ref({
    id: '',
    name: '',
    js: '',
    gjh: '+86',
    contactTel: '',
    visible: false,
    title: '',
    type: '',  // 'tel' 或 'type'
    loading: false
  })

  // 获取当事人列表
  const getPartyPersonList = async () => {
    if (!props.gzjzId && !currentRecordId.value) {
      return
    }

    try {
      loading.value = true
      const query : GzjzDsrQuery = {
        gzjzId: props.gzjzId || currentRecordId.value!,
        pageNum: 1,
        pageSize: 1000
      }
      const res = await listGzjzDsrByGzjz(query)
      if (res.code === 200) {
        partyPersonList.value = res.rows || []
        emit('update:data', partyPersonList.value)
      }
    } catch (error) {
      console.error('获取当事人列表失败:', error)
      ElMessage.error('获取当事人列表失败')
    } finally {
      loading.value = false
    }
  }

  // 表格选择变化
  const handleSelectionChange = (rows : GzjzDsrVO[]) => {
    selectedRows.value = rows
  }

  // 格式化性别
  const formatGender = (gender : string) => {
    if (gender === '1') return '男'
    if (gender === '2') return '女'
    return gender || ''
  }

  // 格式化当事人类型
  const formatPersonType = (js : string) => {
    // const typeMap : Record<string, string> = {
    //   '1': '申请人',
    //   '2': '当事人',
    //   '3': '关系人',
    //   '4': '代理人',
    //   '5': '证人',
    //   '6': '被继承人'
    // }
    const dict = gz_dsr_jslx.value.find(item => item.value === js);
    return dict ? dict.label : ''; // typeMap[js] || js || ''
  }

  //双击 -- 查看个人客户
  import GrDetail from '@/views/gongzheng/dsr/dsrxxZrr/components/gr_detail.vue';
  import DwDetail from '@/views/gongzheng/dsr/dsrxxZrr/components/dw_detail.vue';
  const dwDetailRef = ref<InstanceType<typeof DwDetail> | null>(null);
  const grDetailRef = ref<InstanceType<typeof GrDetail> | null>(null);
  const dialog6 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog7 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  //双击当事人行
  const handleDblclickRow = (row : GzjzDsrVO) => {
    dbCurDsrInfo.value = row;
    // 转换数据格式以匹配DsrxxZrrVO
    currentPersonDetail.value = {
      id: row.id,
      slbh: '',
      xm: row.name || row.xm || '',
      xb: row.gender || row.xb || '',
      lxdh: row.phone || '',
      zz: row.address || row.zz || '',
      zjlx: row.idType || '',
      zjhm: row.idNumber || row.zjhm || '',
      dsrlb: '',
      zp: '',
      gj: '',
      mz: '',
      csrq: row.birthDate || row.csrq || '',
      remark: row.remark || '',
      khh: '',
      cym: '',
      ywm: '',
      dzyj: '',
      hyzk: 0,
      gzdw: '',
      wxh: '',
      khhmc: '',
      khhzh: '',
      pjdj: 0
    } as DsrxxZrrVO
    console.log('当前个人客户详情:', row)

    if (row.dsrLx === '1') {
      dialog6.visible = true;
      dialog6.title = "查看个人客户";
    } else if (row.dsrLx === '2') {
      dialog7.visible = true;
      dialog7.title = "查看单位客户";
    }
  }

  //关闭-个人客户详情
  const handleCloseGrDetail = () => {
    dialog6.visible = false;
    dialog6.title = "";
    // ElMessage.info('个人客户详情已关闭');
  }

  //单位-个人客户详情
  const handleCloseDwDetail = () => {
    dialog7.visible = false;
    dialog7.title = "";
  }

  // 新增当事人
  const handleAdd = () => {
    editDsrData.value = null // 清空编辑数据，表示新增
    dialog5.visible = true;
    dialog5.title = "新增当事人"
  }

  // 编辑当事人
  const handleEdit = async (row : GzjzDsrVO) => {
    try {
      // 先调用详情接口获取完整的当事人数据
      const res = await getGzjzDsr(row.id!)
      if (res.code === 200) {
        editDsrData.value = res.data // 传递完整的当事人数据给AddDsr组件
        dialog5.visible = true;
        dialog5.title = "编辑当事人"
      } else {
        ElMessage.error('获取当事人详情失败')
      }
    } catch (error) {
      console.error('获取当事人详情失败:', error)
      ElMessage.error('获取当事人详情失败')
    }
  }

  // 删除当事人
  const handleDelete = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要删除的当事人')
      return
    }

    try {
      await ElMessageBox.confirm('确定要删除选中的当事人吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const ids = selectedRows.value.map(row => row.id)
      await delGzjzDsr(ids)
      ElMessage.success('删除成功')
      await getPartyPersonList() // 刷新列表
    } catch (error : any) {
      if (error !== 'cancel') {
        console.error('删除当事人失败:', error)
        ElMessage.error('删除当事人失败')
      }
    }
  }

  // 读卡功能
  const handleReadCard = () => {
    ElMessage.info('批量读卡功能')
  }

  /*** 当事人引用 */
  import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue'
  const yydsrRef = ref<InstanceType<typeof Yydsr> | null>(null);
  const buttonLoading2 = ref(false);
  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dlrInfo = ref(null);
  const disabledDlrlb = ref(false);
  const selectedDsrIds = ref<string[]>([])

  const cancel3 = () => {
    dialog4.title = "";
    dialog4.visible = false;
  }

  const handleYyDsr = () => {
    dialog4.title = "引用当事人";
    dialog4.visible = true;
  }

  const handleUpdateDsrSelectId = (data : any) => {
    if (Array.isArray(data)) {
      selectedDsrIds.value = data
    } else {
      // 兼容传入单个对象的情况
      dlrInfo.value = data
      selectedDsrIds.value = data.id ? [data.id] : []
    }

    console.log('引用选中的当事人信息:', data)
  }

  // 风险信息提示
  const riskTip = async (zjhm: string): Promise<any> => {
    // 风险信息 start
    const hasHmd = await queryDsrxxZrrByIdCard({ IdcardNumber: zjhm });
    const { code, data } = hasHmd || {};
    const { hmdxxVo } = data || {};

    if (code === 200 && hmdxxVo) {
      return ElMessageBox({
        title: '风险信息提示',
        message: h('div', {
          class: 'p-20px'
        }, [
          h(DataItem, { label: '客户名称：', content: hmdxxVo.sxr || '' }),
          h(DataItem, { label: '证件号码：', content: hmdxxVo.sxrzjhm || '' }),
          h(DataItem, { label: '查获时间：', content: formatDate(hmdxxVo.chrq) || ''}),
          h(DataItem, { label: '风险信息：', content: hmdxxVo.qksm || '' }),
        ]),
        showClose: false,
      })
    }
    return Promise.resolve();
    // 风险信息 end
  }

  const submitForm2 = async () => {
    if (selectedDsrIds.value.length === 0) {
      ElMessage.warning('请选择要引用的当事人')
      return
    }

    try {
      buttonLoading2.value = true
      // 获取选中的当事人信息
      const selectedDsrInfo = yydsrRef.value?.getSelectedDsrInfo?.() || dlrInfo.value
      if (!selectedDsrInfo) {
        ElMessage.error('未获取到当事人信息')
        return
      }

      await riskTip(selectedDsrInfo.zjhm); // 风险信息提示

      // 根据当事人类型（dsrlb字段）确定dsrLx
      const dsrLx = selectedDsrInfo.dsrlb === '2' ? '2' : '1' // dsrlb: '1'个人 '2'单位

      // 引用当事人
      for (const dsrId of selectedDsrIds.value) {
        const formData : GzjzDsrForm = {
          dsrId: dsrId,
          dsrLx: dsrLx,
          gzjzId: props.gzjzId || currentRecordId.value!,
          js: '1', // 默认申请人
          sfdk: '0',
          remark: '',
          zrrBo: selectedDsrInfo,
        }
        await addGzjzDsr(formData)
      }
      ElMessage.success('引用当事人成功')
      cancel3()
      await getPartyPersonList() // 刷新列表
      eventBus.emit('sl:list:update', {
        msg: '已引用当事人，更新受理列表'
      })
    } catch (error) {
      console.error('引用当事人失败:', error)
      ElMessage.error('引用当事人失败')
    } finally {
      buttonLoading2.value = false
    }
  }
  /*** 当事人引用 */

  /*** 新增/编辑当事人 */
  import AddDsr from '@/views/gongzheng/dsr/dsrxxZrr/components/add_dsr.vue'
  const addDsrRef = ref<InstanceType<typeof AddDsr> | null>(null);
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const buttonLoading3 = ref(false);
  const editDsrData = ref<GzjzDsrVO | null>(null); // 编辑时的当事人数据

  const cancel4 = () => {
    dialog5.title = "";
    dialog5.visible = false;
    editDsrData.value = null;
  }

  const handleAddDsrSubmit = async (dsrData : any) => {
    try {
      buttonLoading3.value = true;

      console.log('okkkkkk', dsrData)

      // 构建基础表单数据
      const formData : any = {
        dsrLx: dsrData.dsrLx || '1', // 01个人 02单位
        gzjzId: props.gzjzId || currentRecordId.value!,
        js: dsrData.js || '1', // 角色
        sfdk: dsrData.sfdk || '0',
        remark: dsrData.remark || ''
      }

      // 如果是编辑模式，需要添加ID和dsrId
      if (editDsrData.value) {
        // 编辑模式：使用传入的编辑数据
        formData.id = editDsrData.value.id
        formData.dsrId = editDsrData.value.dsrId
      } else {
        // 新增模式
        formData.dsrId = dsrData.dsrId || '' // 新增时可能为空，后台会生成
      }

      // 根据当事人类型添加对应的数据
      if (dsrData.dsrLx === '1') {
        // 个人
        formData.zrrBo = {
          xm: dsrData.xm,
          xb: dsrData.xb,
          zjlx: dsrData.zjlx,
          zjhm: dsrData.zjhm,
          csrq: dsrData.csrq,
          zz: dsrData.zz,
          lxdh: dsrData.lxdh,
          dsrlb: dsrData.dsrlb || '1',
          gj: dsrData.gj || '',
          mz: dsrData.mz || '',
          remark: dsrData.remark || '',
          khh: dsrData.khh || '',
          cym: dsrData.cym || '',
          ywm: dsrData.ywm || '',
          dzyj: dsrData.dzyj || '',
          hyzk: dsrData.hyzk || 0,
          gzdw: dsrData.gzdw || '',
          wxh: dsrData.wxh || '',
          khhmc: dsrData.khhmc || '',
          khhzh: dsrData.khhzh || '',
          pjdj: dsrData.pjdj || 0,
          zp: dsrData.zp || '',
          cardImage1: dsrData.cardImage1 || '',
          cardImage2: dsrData.cardImage2 || '',
          signOffice: dsrData.signOffice || '',
          cardValidDate: dsrData.cardValidDate || '',
        }
      } else if (dsrData.dsrLx === '2') {
        // 单位
        formData.frhzzBo = {
          dwmc: dsrData.dwmc,
          dwszd: dsrData.dwszd,
          zjlx: dsrData.zjlx,
          zjhm: dsrData.zjhm,
          lxdh: dsrData.lxdh,
          fddbr: dsrData.fddbr || '',
          fddbrxb: dsrData.fddbrxb || '',
          fddbrlxdh: dsrData.fddbrlxdh || '',
          fddbrzw: dsrData.fddbrzw || '',
          fddbrzjlx: dsrData.fddbrzjlx || '',
          fddbrzjhm: dsrData.fddbrzjhm || '',
          dsrlb: dsrData.dsrlb || '2',
          fddbrzp: dsrData.fddbrzp || '',
          fddbrzz: dsrData.fddbrzz || '',
          remark: dsrData.remark || '',
          hyhm: dsrData.hyhm || '',
          ywmc: dsrData.ywmc || '',
          fzrzjlx: dsrData.fzrzjlx || '',
          fzrzjhm: dsrData.fzrzjhm || '',
          fzrxm: dsrData.fzrxm || '',
          fzrcsrq: dsrData.fzrcsrq || '',
          fzrwxh: dsrData.fzrwxh || '',
          fzrdzyj: dsrData.fzrdzyj || '',
          khh: dsrData.khh || '',
          khzh: dsrData.khzh || '',
          zjzt: dsrData.zjzt !== undefined ? dsrData.zjzt : true,
          signOffice: dsrData.signOffice || '',
          cardValidDate: dsrData.cardValidDate || '',
        }
      }

      // 调用对应的API
      if (editDsrData.value) {
        // 编辑
        await updateGzjzDsr(formData)
        ElMessage.success('修改当事人成功')
      } else {

        if (dsrData.dsrLx == '1') {
          await riskTip(formData.zrrBo.zjhm); // 风险信息提示
        }

        // 新增
        await addGzjzDsr(formData)
        ElMessage.success('新增当事人成功')
      }

      cancel4()
      await getPartyPersonList() // 刷新列表
      eventBus.emit('sl:list:update', {
        msg: '已新增当事人，更新受理列表'
      })
    } catch (error) {
      console.error('保存当事人失败:', error)
      ElMessage.error('保存当事人失败')
    } finally {
      buttonLoading3.value = false
    }
  }

  const submitForm3 = () => {
    // 调用AddDsr组件的提交方法
    if (addDsrRef.value && typeof (addDsrRef.value as any).submit === 'function') {
      (addDsrRef.value as any).submit()
    }
  }
  /*** 新增/编辑当事人*/

  const handleMerge = () => {
    ElMessage.info('合成功能')
  }

  const handleGenerate = () => {
    ElMessage.info('生成信息表功能')
  }

  const handleAppCard = () => {
    ElMessage.info('app认证卡功能')
  }

  //加入黑名单
  import AddHmd from '@/views/gongzheng/dsr/dsrhmdxx/components/add_hmd.vue'
  import { addDsrxxZrr, queryDsrxxZrrByIdCard } from '@/api/gongzheng/dsr/dsrxxZrr'
  import { addDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz'
  import type { DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types'
  import type { DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types'

  const selectedPersonForHmd = ref<GzjzDsrVO | null>(null)
  const hmdReason = ref('')

  const handleAddHmd = () => {
    if (selectedRows.value.length !== 1) {
      ElMessage.warning('请选择一个当事人加入黑名单')
      return
    }
    selectedPersonForHmd.value = selectedRows.value[0]
    dialog8.visible = true;
    dialog8.title = "加入黑名单";
  }
  const dialog8 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const buttonLoading4 = ref(false);
  const cancel5 = () => {
    dialog8.title = "";
    dialog8.visible = false;
    selectedPersonForHmd.value = null
    hmdReason.value = ''
  }
  const submitForm4 = async () => {
    if (!selectedPersonForHmd.value) {
      ElMessage.error('未选择当事人')
      return
    }

    if (!hmdReason.value.trim()) {
      ElMessage.warning('请输入不诚信记录')
      return
    }

    try {
      buttonLoading4.value = true
      const person = selectedPersonForHmd.value

      // 根据当事人类型调用不同的API
      if (person.dsrLx === '1') {
        // 个人 - 调用新增当事人-基本信息-自然人信息接口
        const formData : DsrxxZrrForm = {
          xm: person.name || person.xm || '',
          xb: person.gender || person.xb || '',
          zjlx: person.idType || '',
          zjhm: person.idNumber || person.zjhm || '',
          csrq: person.birthDate || person.csrq || '',
          zz: person.address || person.zz || '',
          lxdh: person.phone || '',
          dsrlb: '1', // 个人
          remark: `黑名单记录：${hmdReason.value}`,
          gj: '',
          mz: '',
          khh: '',
          cym: '',
          ywm: '',
          dzyj: '',
          hyzk: 0,
          gzdw: '',
          wxh: '',
          khhmc: '',
          khhzh: '',
          pjdj: 0,
          zp: ''
        }
        await addDsrxxZrr(formData)
      } else if (person.dsrLx === '2') {
        // 单位 - 调用新增当事人-基本信息-法人或者其他组织信息接口
        const formData : DsrxxFrhzzForm = {
          dwmc: person.name || person.xm || '',
          dwszd: person.address || person.zz || '',
          zjlx: person.idType || '',
          zjhm: person.idNumber || person.zjhm || '',
          lxdh: person.phone || '',
          fddbr: '',
          fddbrxb: '',
          fddbrlxdh: '',
          fddbrzw: '',
          fddbrzjlx: '',
          fddbrzjhm: '',
          dsrlb: '2', // 单位
          fddbrzp: '',
          fddbrzz: '',
          remark: `黑名单记录：${hmdReason.value}`,
          hyhm: '',
          ywmc: '',
          fzrzjlx: '',
          fzrzjhm: '',
          fzrxm: '',
          fzrcsrq: '',
          fzrwxh: '',
          fzrdzyj: '',
          khh: '',
          khzh: ''
        }
        await addDsrxxFrhzz(formData)
      }

      ElMessage.success('已成功将当事人加入黑名单')
      cancel5()
    } catch (error) {
      console.error('加入黑名单失败:', error)
      ElMessage.error('加入黑名单失败')
    } finally {
      buttonLoading4.value = false
    }
  }
  //加入黑名单

  //视频连线
  import Splx from '@/views/gongzheng/gongzheng/components/sl/sl_dsr/splx.vue'
import eventBus from '@/utils/eventBus'
import { formatDate } from '@/utils/ruoyi'
  const dialog9 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const buttonLoading5 = ref(false);
  const cancel6 = () => {
    dialog9.title = "";
    dialog9.visible = false;
  }
  const submitForm5 = () => {

  }
  const handleSplx = () => {
    dialog9.visible = true;
    dialog9.title = "视频连线配置";
  }
  //视频连线

  // 对话框事件
  const handleDialogClose2 = () => {
    dialogVisible2.value = false
  }
  const handleDialogClose3 = () => {
    dialogVisible3.value = false
  }

  //办证
  const handleBz = (row : GzjzDsrVO) => {
    curDsrInfo.value = row
    dialogVisible2.value = true
    dialogTitle2.value = "办证信息"
  }
  const cancel = () => {
    dialogVisible2.value = false
    dialogTitle2.value = ""
  }

  const handleZx = (row : GzjzDsrVO) => {
    dialogVisible3.value = true
    dialogTitle3.value = "咨询详情"
  }
  const cancel2 = () => {
    dialogVisible3.value = false
    dialogTitle3.value = ""
  }

  //复制
  const handleCopy = async (row : GzjzDsrVO) => {
    // 张三,男,1990-01-01出生,证件号码:123456789012345678,住址:北京市朝阳区

    if (navigator.clipboard) {
      const name = row.name || row.xm || ''
      const gender = formatGender(`${row.gender || row.xb || row.sex || ''}`)
      const birthDate = row.birthDate || row.csrq || ''
      const idNumber = row.idNumber || row.zjhm || row.certificateNo || ''
      const address = row.address || row.zz || ''

      const txt = `${name}，${gender}，${birthDate} 出生，证件号码：${idNumber}，住址：${address}`
      await navigator.clipboard.writeText(txt);
      ElMessage.success("当事人信息已复制到剪切板");
    } else {
      ElMessage.warning("当前浏览器不支持该功能");
    }
  }

  //复制为代理人
  const handleCopyDlr = async (row : GzjzDsrVO) => {
    try {
      // 构建代理人数据，复制当前当事人信息，但角色改为代理人
      const formData : GzjzDsrForm = {
        dsrId: row.dsrId, // 使用相同的当事人ID
        dsrLx: row.dsrLx, // 保持当事人类型不变
        gzjzId: props.gzjzId || currentRecordId.value!,
        js: '4', // 设置为代理人
        sfdk: '0',
        remark: `复制自${formatPersonType(row.js)}，作为代理人`
      }

      await addGzjzDsr(formData)
      ElMessage.success('复制为代理人成功')
      await getPartyPersonList() // 刷新列表
    } catch (error) {
      console.error('复制为代理人失败:', error)
      ElMessage.error('复制为代理人失败')
    }
  }

  // 代理人设置
  const handleDl = async (row : GzjzDsrVO) => {
    // ElMessage.info('代理功能')
    try {
      curDlrInfo.value = row
      const params = {
        dlrId: Number(row.dsrId),
        gzjzId: row.gzjzId as string,
      }
      console.log('代理功能', params);
      const res = await getDlrAvailable(params);
      if(res.code === 200) {
        availableDlrList.value = res.rows || []
        okDlDsr.value = availableDlrList.value.filter((item: any) => item.selected);
        dialog10.value.visible = true;
      }
    } catch (err: any) {
      console.error('获取可代理当事人列表失败:', err);
      ElMessage.error('获取可代理当事人列表失败');
    }
  }

  // 关闭代理人设置对话框
  const closeDl = () => {
    dialog10.value.visible = false;

    console.log('关闭代理人设置对话框')
  }

  // 代理当事人全选
  const handleSelectAllDsr = (val: any) => {
    checkedDlDsrList.value = val ? availableDlrList.value.map(p => p.dsrId) : [];
    isISelectAllDsr.value = false
    console.log('全选代理人:', checkedDlDsrList.value)

  }

  const handleCheckedDsrChange = (vals: any) => {
    // 当选中状态变化时，更新checkedDsrList
    console.log('选中的代理人ID:', vals)
    const counts = vals.length;
    dlDsrCheckall.value = counts === availableDlrList.value.length;
    isISelectAllDsr.value = counts > 0 && counts < availableDlrList.value.length;
  }

  // 代理人设置提交
  const handleDlSubmit = async () => {
    dialog10.value.loading = true;
    try {
      const data = {
        dsrIds: checkedDlDsrList.value,
        dlrId: Number(curDlrInfo.value!.dsrId),
        gzjzId: curDlrInfo.value!.gzjzId as string,
      }
      const res = await updateDlrDlgx(data);
      if (res.code === 200) {
        ElMessage.success('代理人设置成功');
        closeDl();
      }
      // console.log('提交代理人设置:', checkedDlDsrList.value)
    } catch (err: any) {
      console.log('提交代理人设置失败:', err);
      ElMessage.error('代理人设置失败，请重试');
    } finally {
      dialog10.value.loading = false;
    }
  }

  // 触发 当事人电话或类型修改 窗口显示
  const showTelOrType = (row: GzjzDsrVO, showType: string) => {
    const info = dsrdhxg.value;
    dsrdhxg.value = {
      ...info,
      name: row.name || row.xm || '',
      id: row.id as string,
      contactTel: row.contactTel,
      js: row.js,
      visible: true,
      title: showType === 'tel' ? '修改当事人电话' : '修改当事人类型',
      type: showType
    }
  }

  // 触发 当事人电话或类型修改 窗口关闭
  const hideTelOrType = () => {
    dsrdhxg.value = {
      name: '',
      id: '',
      gjh: '+86',
      contactTel: '',
      js: '',
      visible: false,
      title: '',
      type: '',
      loading: false
    }
  }

  const handleTelInput = (val: string) => {
    // 只允许输入数字
    dsrdhxg.value.contactTel = val.replace(/[^\d]/g, '');
    // 限制长度为11位
    if (dsrdhxg.value.contactTel.length > 11) {
      dsrdhxg.value.contactTel = dsrdhxg.value.contactTel.slice(0, 11);
    }
  }

  // 修改当事人电话或类型
  const telOrTypeUpdate = async (row: GzjzDsrVO) => {
    try {
      dsrdhxg.value.loading = true;
      const { id, js, contactTel } = dsrdhxg.value;
      const res = await updateDsrTelAndJs({ id, js, contactTel });
      if (res.code === 200) {
        ElMessage.success('修改成功');
        hideTelOrType();
        // 刷新列表
        await getPartyPersonList();
      }
    } catch (err: any) {
      console.error('修改当事人电话或类型失败:', err);
      ElMessage.error('修改失败，请重试');
    } finally {
      dsrdhxg.value.loading = false;
    }
  }

  //读卡
  const handleRedCard = (row : GzjzDsrVO) => {
    ElMessage.info('读卡功能')
  }

  /**====================================================== 拍照 ======================================================**/
  // 拍照数据
  const takePhotoState = reactive({
    visible: false,
    title: '拍照',
  });

  // 打开拍照
  const toTakePhoto = () => {
    takePhotoState.visible = true;
    takePhotoState.title = '拍照';
  }
  /**====================================================== 拍照 END ======================================================**/

  // 监听公证卷宗ID变化
  watch(() => props.gzjzId, (newVal) => {
    if (newVal) {
      getPartyPersonList()
    }
  }, { immediate: true })

  // 监听当前记录ID变化
  watch(currentRecordId, (newVal) => {
    if (newVal && !props.gzjzId) {
      getPartyPersonList()
    }
  }, { immediate: true })

  // 组件挂载时获取数据
  onMounted(() => {
    if (props.gzjzId || currentRecordId.value) {
      getPartyPersonList()
    }
  })

  // 暴露方法给父组件
  defineExpose({
    refreshList: getPartyPersonList
  })
</script>

<style scoped>
  .party-person-list {
    margin: 20px 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .header-actions .el-button {
    padding: 8px 15px;
    font-size: 12px;
  }

  .dl-dsr-tag {
    margin-bottom: 8px;
  }
  .dl-dsr-tag+.dl-dsr-tag {
    margin-left: 8px;

  }

  .cropper-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 6px auto;
    height: 36px;
    background-color: #a19e9e;
  }

  .cropper-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    overflow: auto;
  }

  .img-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: flex-start;
  }
  .new-img-wrap {
    position: relative;
  }
  .new-img::before {
    content: "new";
    position: absolute;
    top: -10px;
    left: 0;
    width: 26px;
    height: 10px;
    line-height: 10px;
    text-align: center;
    font-size: 8px;
    color: azure;
    background-color: rgb(132, 132, 252);
    border-radius: 2px 2px 0 0;
  }
  .img-del-btn {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 4px;
    border-radius: 4px;
    box-sizing: content-box;
    cursor: pointer;
    background-color: rgba(255, 0, 0, 0.863);
    opacity: 0;
  }
  .img-show:hover>.img-del-btn {
    opacity: 1;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.el-table .el-button--small) {
    padding: 2px 8px;
    font-size: 11px;
  }
</style>
