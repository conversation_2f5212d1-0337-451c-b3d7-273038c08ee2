<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证类别" prop="lb" style="width: 260px;">
              <el-select v-model="queryParams.lb" placeholder="请选择" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable
                style="max-width: 80px; margin-right: 4px;">
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable
                style="max-width: 140px; margin-right: 4px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery"
                style="max-width: 60px" />号
            </el-form-item>
            <el-form-item label="未被重用" prop="" style="width: 260px;">
              <el-checkbox v-model="queryParams.cy" true-value="1" false-value="0"></el-checkbox>
            </el-form-item>
            <el-form-item label="决定书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.jdsNf" placeholder="年份" clearable
                style="max-width: 80px; margin-right: 4px;">
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              <el-select v-model="queryParams.params.jdsZh" placeholder="字号" clearable
                style="max-width: 140px; margin-right: 4px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.params.jdsLs" clearable @keyup.enter="handleQuery"
                style="max-width: 60px" />号
            </el-form-item>
            <el-form-item label="作废日期" style="width: 340px;">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleDateRangeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-row>
        <el-span span="12">
          <el-table v-loading="loading" :data="gzjzGzsbmList">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="公证书编号" align="center">
              <template #default="scope">
                ({{scope.row.nf}}){{scope.row.zh}}第{{scope.row.ls}}号
              </template>
            </el-table-column>
            <el-table-column label="销号时间" align="center" prop="updateTime">
              <template #default="scope">
                {{scope.row.updateTime}}
              </template>
            </el-table-column>
            <el-table-column label="操作人" align="center" prop="updateBy">
              <template #default="scope">
                {{scope.row.updateBy}}
              </template>
            </el-table-column>
            <el-table-column label="原卷宗编号" align="center" prop="gzsbh" />
          </el-table>
          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-span>
        <el-span span="12">
          <el-table v-loading="loading2" :data="gzjzJdsbmList">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="决定书编号" align="center" >
              <template #default="scope">
                ({{scope.row.nf}}){{scope.row.zh}}第{{scope.row.ls}}号
              </template>
            </el-table-column>
            <el-table-column label="销号时间" align="center" prop="updateTime">
              <template #default="scope">
                {{scope.row.updateTime}}
              </template>
            </el-table-column>
            <el-table-column label="原卷宗编号" align="center" prop="jzh" />
          </el-table>
          <pagination v-show="total2 > 0" :total="total2" v-model:page="queryParams2.pageNum"
            v-model:limit="queryParams2.pageSize" @pagination="getList2" />

        </el-span>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="GzjzGzsbm" lang="ts">
  import { listGzjzGzsbm } from '@/api/gongzheng/gongzheng/gzjzGzsbm';
  import { GzjzGzsbmVO, GzjzGzsbmQuery, GzjzGzsbmForm } from '@/api/gongzheng/gongzheng/gzjzGzsbm/types';

  import { listGzjzJdsbm } from '@/api/gongzheng/gongzheng/gzjzJdsbm';
  import { GzjzJdsbmVO, GzjzJdsbmForm, GzjzJdsbmQuery } from '@/api/gongzheng/gongzheng/gzjzJdsbm/types';

  import { genYearOption, genRecentDate } from '@/utils/ruoyi';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb, gz_gzs_zh } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_gzs_zh'))
  const gzsbh_years = genYearOptions(2015);
  const gzjzGzsbmList = ref<GzjzGzsbmVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const rangeDate = genRecentDate('-3M');
  const dateRange = ref<[string, string] | null>(rangeDate);
  const queryFormRef = ref<ElFormInstance>();
  const gzjzGzsbmFormRef = ref<ElFormInstance>();
  const loading2 = ref(true);
  const total2 = ref(0);
  const gzjzJdsbmList = ref<GzjzGzsbmVO[]>([]);

  /** 日期范围变化处理 */
  const handleDateRangeChange = (dates : [string, string] | null) => {
    if (dates && dates.length === 2) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginSqsj: dates[0],
        endSqsj: dates[1]
      };
    } else {
      delete queryParams.value.params?.beginSqsj;
      delete queryParams.value.params?.endSqsj;
    }
  };
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzjzGzsbmForm = {
    id: undefined,
    gzsbh: undefined,
    nf: undefined,
    zh: undefined,
    ls: undefined,
    sfZf: "1",
  }
  const data = reactive<PageData<GzjzGzsbmForm, GzjzGzsbmQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzsbh: undefined,
      lb: undefined,
      cy: undefined,
      sfZf: "1",
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "ID不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);




  const data2 = reactive<PageData<GzjzJdsbmForm, GzjzJdsbmQuery>>({
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      gzsbh: undefined,
      lb: undefined,
      cy: undefined,
      sfZf: "1",
      params: {
      }
    }
  });

  const { queryParams2 } = toRefs(data2);

  /** 查询公证卷宗-公证书编号v1.0列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listGzjzGzsbm(queryParams.value);
    gzjzGzsbmList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  const getList2 = async () => {
    loading2.value = true;
    const res = await gzjzJdsbmList(queryParams2.value);
    gzjzGzsbmList.value = res.rows;
    total2.value = res.total;
    loading2.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzjzGzsbmFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
    getList2();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }



  onMounted(() => {
    getList();
    getList2();
  });
</script>
