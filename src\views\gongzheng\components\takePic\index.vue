<template>
  <gz-dialog v-model="visible" :title="title" @closed="takePicClosed">
    <div class="h-full min-h-600px w-full relative flex items-center justify-center">
      <Transition name="trans">
        <Camera v-if="picState.takePicStatus === '0'" @on-take-photo="onTakePic" default-ratio="4x3" default-size="320p" class="absolute" />
      </Transition>
      <Transition name="trans">
        <Cropper v-if="picState.takePicStatus === '1'" :img="picState.base64Img" @cancel="onCropCancel" @confirm="onCropComfirm" class="absolute" />
      </Transition>
      <Transition name="trans">
        <ImgShow v-if="picState.takePicStatus === '2'" :src="picState.cutedImg" @cancel="onCropCancel" @cancel-to-cut="reCut" class="absolute" />
      </Transition>
    </div>
    <template #footer>
      <el-button type="primary" @click="comfirm" :disabled="picState.takePicStatus !== '2'">确定</el-button>
      <el-button @click="visible = false">取消</el-button>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import Camera from '@/components/Camera/index.vue';
import Cropper from './Cropper.vue'
import ImgShow from './ImgShow.vue'
import { base64ToFile } from '@/utils/ruoyi'

interface Props {
  modelValue: boolean,
  title?: string,
  gzjzId?: string | number,
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '拍照',
  gzjzId: '',
})

const emit = defineEmits(['update:modelValue', 'comfirm'])

const picState = reactive({
  base64Img: '',
  takePicStatus: '0',
  cutedImg: '',
})

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  },
})

const onTakePic = (picStr: string) => {
  picState.base64Img = picStr
  picState.takePicStatus = '1'
}

const onCropComfirm = (picStr: string) => {
  picState.cutedImg = picStr
  picState.takePicStatus = '2'
}

const reCut = () => {
  picState.takePicStatus = '1'
  picState.cutedImg = ''
}

const onCropCancel = () => {
  picState.takePicStatus = '0'
  picState.base64Img = ''
  picState.cutedImg = ''
}

const comfirm = async () => {
  if (!picState.cutedImg) {
    ElMessage.error('请先处理完图片后再确认')
    return;
  }
  try {
    const file = await base64ToFile(picState.cutedImg)
    const fileObj = {
      base64: picState.cutedImg,
      file,
    }
    emit('comfirm', fileObj)
  } catch (err: any) {
    ElMessage.error('图像处理错误')
    console.error(err)
  }
}

const takePicClosed = () => {
  picState.takePicStatus = '0'
  picState.base64Img = ''
}

onMounted(() => {})
</script>

<style scoped>
.trans-enter-active,
.trans-leave-active {
  transition: all 0.5s ease;
}

.trans-enter-from {
  opacity: 0;
  transform: translateX(-100px);
}
.trans-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

</style>
