import request from '@/utils/request';

/**
 * 查询收费标准列表
 * @param query
 * @returns {*}
 */
export function listGzsxSfbz(query?: any) {
  return request({
    url: '/basicdata/gzsxSfbz/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询收费标准详细
 * @param id
 */
export function getGzsxSfbz(id: string | number) {
  return request({
    url: '/basicdata/gzsxSfbz/' + id,
    method: 'get'
  });
}

/**
 * 新增收费标准
 * @param data
 */
export function addGzsxSfbz(data: any) {
  return request({
    url: '/basicdata/gzsxSfbz',
    method: 'post',
    data: data
  });
}

/**
 * 修改收费标准
 * @param data
 */
export function updateGzsxSfbz(data: any) {
  return request({
    url: '/basicdata/gzsxSfbz',
    method: 'put',
    data: data
  });
}

/**
 * 删除收费标准
 * @param id
 */
export function delGzsxSfbz(ids: string) {
  return request({
    url: '/basicdata/gzsxSfbz/' + ids,
    method: 'delete'
  });
}

/**
 * 导出收费标准
 * @param query
 * @returns {*}
 */
export function exportGzsxSfbz(query?: any) {
  return request({
    url: '/basicdata/gzsxSfbz/export',
    method: 'post',
    data: query
  });
}
