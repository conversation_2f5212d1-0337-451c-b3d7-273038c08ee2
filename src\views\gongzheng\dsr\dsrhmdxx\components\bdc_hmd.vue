<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="200px">
            <el-form-item label="房屋所有权号/不动产登记号" prop="bdcdjh">
              <el-input v-model="queryParams.bdcdjh" placeholder="请输入房屋所有权号/不动产登记号" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="不动产地区" prop="bdcSf">
              <el-input v-model="queryParams.bdcSf" placeholder="请输入不动产省份" clearable @keyup.enter="handleQuery" />
              <el-input v-model="queryParams.bdcSq" placeholder="请输入不动产所在市" clearable @keyup.enter="handleQuery" />
              <el-input v-model="queryParams.bdcQ" placeholder="请输入不动产所在区" clearable @keyup.enter="handleQuery" />
              <el-input v-model="queryParams.dz" placeholder="请输入不动产地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable>
                <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['dsr:dsrxxHmdBdc:add']">录入</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxHmdBdcList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="不动产地址" align="center" prop="dz" />
        <el-table-column label="不动产登记号" align="center" prop="bdcdjh" />
        <el-table-column label="名单类型" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_hmd_zt" :value="scope.row.zt" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['dsr:dsrxxHmdBdc:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['dsr:dsrxxHmdBdc:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改当事人-黑名单-不动产对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body>
      <el-row>
        <el-col :span="18">
          <el-form ref="dsrxxHmdBdcFormRef" :model="form" :rules="rules" label-width="130px">
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item>
                  <el-button type="primary">引用</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="不动产地址" prop="dz">
                  <el-input v-model="form.bdcSf" placeholder="请输入不动产省份" />
                  <el-input v-model="form.bdcSq" placeholder="请输入不动产所在市" />
                  <el-input v-model="form.bdcQ" placeholder="请输入不动产所在区" />
                  <el-input v-model="form.dz" placeholder="请输入不动产地址" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="不动产登记号" prop="bdcdjh">
                  <el-input v-model="form.bdcdjh" placeholder="请输入不动产登记号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="状态" prop="zt">
                  <el-select v-model="form.zt" placeholder="请选择状态">
                    <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="查获日期" prop="chrq">
                  <el-date-picker clearable v-model="form.chrq" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择查获日期">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="不动产类型" prop="zt">
                  <el-select v-model="form.zt" placeholder="请选择不动产类型">
                    <el-option v-for="dict in gz_bdclx" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>

                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="公证事项" prop="gzsx">
                  <el-input v-model="form.gzsx" placeholder="请输入公证事项" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="产权人" prop="cqr">
                  <el-input v-model="form.cqr" placeholder="请输入产权人" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="申请人" prop="sqr">
                  <el-input v-model="form.sqr" placeholder="请输入申请人" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="申请人身份证号码" prop="sqrsfz">
                  <el-input v-model="form.sqrsfz" placeholder="请输入申请人身份证号码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <el-form-item label="情况说明" prop="qksm">
                  <el-input v-model="form.qksm" placeholder="请输入情况说明" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="24" :lg="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="6">
          <div class="pc-img-div">
            <el-image :src="img" class="pic-img"></el-image>
            <el-button type="primary" link>拍照</el-button>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BdcHmdd" lang="ts">
  import { listDsrxxHmdBdc, getDsrxxHmdBdc, delDsrxxHmdBdc, addDsrxxHmdBdc, updateDsrxxHmdBdc } from '@/api/gongzheng/dsr/dsrxxHmdBdc';
  import { DsrxxHmdBdcVO, DsrxxHmdBdcQuery, DsrxxHmdBdcForm } from '@/api/gongzheng/dsr/dsrxxHmdBdc/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_hmd_zt,gz_bdclx } = toRefs<any>(proxy?.useDict('gz_hmd_zt','gz_bdclx'));

  const dsrxxHmdBdcList = ref<DsrxxHmdBdcVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const img = ref(null);
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxHmdBdcFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrxxHmdBdcForm = {
    id: undefined,
    bdcdjh: undefined,
    dz: undefined,
    bdcSf: undefined,
    bdcSq: undefined,
    bdcQ: undefined,
    zt: undefined,
    jgbs: undefined,
    jgbm: undefined,
    jgmc: undefined,
    chrq: undefined,
    gzsx: undefined,
    cqr: undefined,
    sqr: undefined,
    sqrsfz: undefined,
    qksm: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<DsrxxHmdBdcForm, DsrxxHmdBdcQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      bdcdjh: undefined,
      dz: undefined,
      bdcSf: undefined,
      bdcSq: undefined,
      bdcQ: undefined,
      zt: undefined,
      jgbs: undefined,
      jgbm: undefined,
      jgmc: undefined,
      chrq: undefined,
      gzsx: undefined,
      cqr: undefined,
      sqr: undefined,
      sqrsfz: undefined,
      qksm: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      bdcdjh: [
        { required: true, message: "不动产登记号不能为空", trigger: "blur" }
      ],
      dz: [
        { required: true, message: "不动产地址不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询当事人-黑名单-不动产列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDsrxxHmdBdc(queryParams.value);
    dsrxxHmdBdcList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxHmdBdcFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxHmdBdcVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加当事人-黑名单-不动产";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrxxHmdBdcVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxHmdBdc(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改当事人-黑名单-不动产";
  }

  /** 提交按钮 */
  const submitForm = () => {
    dsrxxHmdBdcFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateDsrxxHmdBdc(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDsrxxHmdBdc(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DsrxxHmdBdcVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除当事人-黑名单-不动产编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxHmdBdc(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('dsr/dsrxxHmdBdc/export', {
      ...queryParams.value
    }, `dsrxxHmdBdc_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
<style scoped>
  .pc-img-div {
    padding: 10px;
    margin: 10px;
  }

  .pic-img {
    width: 300px;
    height: 250px;
  }
</style>
