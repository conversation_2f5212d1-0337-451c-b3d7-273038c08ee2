<template>
  <el-tabs v-model="curTabName" @tab-change="tabChange" class="w-full h-full overflow-hidden">
    <el-tab-pane v-if="!props.exceptPane?.includes('parties')" label="当事人" name="parties" class="w-full h-full overflow-hidden">
      <div v-if="props.useDsr" class="flex justify-end mb-10px">
        <el-button @click="() => { yydsrState.visible = true }" type="primary" size="small">引用当事人</el-button>
      </div>
      <el-table :data="dsrState.listData" ref="dsrTbRef" v-loading="dsrState.loading" @current-change="dsrCurRow" :style="{ height: props.useDsr ? 'calc(100% - 34px)' : '100%' }" size="small" border stripe highlight-current-row>
        <el-table-column type="index" width="50" align="center"/>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="js" label="角色" width="120" align="center">
          <template #default="{ row }">
            <el-text size="small">{{ dictMapFormat(gz_dsr_jslx, row.js) }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="当事人姓名" align="center" show-overflow-tooltip/>
        <el-table-column prop="dsrLx" label="当事人类别" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <el-text size="small">{{ dictMapFormat(gz_dsr_lb, row.dsrLx) }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="copyParty(scope.row)">
              复制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>

    <el-tab-pane v-if="!props.exceptPane?.includes('notaryMatters')" label="公证事项" name="notaryMatters" class="w-full h-full overflow-hidden">
      <el-table :data="gzsxState.listData" ref="gzsxTbRef" v-loading="gzsxState.loading" @current-change="gzsxCurRow" size="small" border stripe highlight-current-row style="height: calc(100% - 0px) !important;">
        <el-table-column type="index" width="50" align="center"/>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="gzsxMc" label="公证事项" min-width="120" align="center" show-overflow-tooltip/>
        <el-table-column prop="gzsBh" label="公证书编号" min-width="150" align="center" show-overflow-tooltip/>
        <el-table-column prop="gzsFs" label="份数" align="center" />
      </el-table>
    </el-tab-pane>

    <el-tab-pane v-if="!props.exceptPane?.includes('phrases')" label="常用短语" name="phrases" class="w-full h-full overflow-hidden">
      <!-- 常用短语列表 -->
      <div class="flex items-center justify-end gap-10px mb-10px">
        <el-input v-model="cydyState.search" placeholder="搜索常用短语" size="small" clearable @clear="searchPhrase" @keyup.enter="searchPhrase" style="width: 200px;" />
        <el-button type="primary" @click="searchPhrase" size="small">查询</el-button>
      </div>

      <el-table :data="cydyState.listData" ref="cydyTbRef" v-loading="cydyState.loading" @current-change="cydyCurRow" style="height: calc(100% - 34px);" size="small" border stripe highlight-current-row>
        <el-table-column type="index" width="50" align="center"/>
        <el-table-column prop="content" label="内容" show-overflow-tooltip/>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-button type="primary" link @click="usePhrase(scope.row)" size="small">
              复制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>

    <el-dialog v-if="yydsrState.visible" v-model="yydsrState.visible" title="引用当事人" >
      <Yydsr v-if="yydsrState.visible" ref="yydsrRef" :editDlrlb="yydsrState.editable" @update-select="handleUpdateDsrSelectId" />
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="yydsrState.loading" :disabled="yydsrState.loading" type="primary" @click="confirmYydsr">确认引用</el-button>
          <el-button @click="() => { yydsrState.visible = false}">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </el-tabs>
</template>

<script setup lang="ts">
import { addGzjzDsr, listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrForm, GzjzDsrQuery, GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { listMbCydy } from '@/api/gongzheng/mb/mbCydy';
import { formatDate, formatGender, dictMapFormat } from '@/utils/ruoyi';
import { ref, reactive, inject, onMounted, h } from 'vue';
import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue';
import { queryDsrxxZrrByIdCard } from '@/api/gongzheng/dsr/dsrxxZrr';
import DataItem from '@/views/gongzheng/components/DataItem.vue';

type PaneName = 'parties' | 'notaryMatters' | 'phrases'

interface Props {
  gzjzId?: string | number; // 卷宗ID
  exceptPane?: PaneName[];  // 排除的标签页
  useDsr?: boolean;   // 是否使用当事人引用功能
  oneReqAll?: boolean; // 是否一次性加载所有数据
}

const props = withDefaults(defineProps<Partial<Props>>(), {
  oneReqAll: true,
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_dsr_jslx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_dsr_jslx', 'gz_dsr_lb'));

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const curTabName = ref('parties');

const yydsrRef = ref(null);
const yydsrState = reactive({
  visible: false,
  editable: false,
  selectedDsrIds: [] as string[],
  loading: false,
  selectedDsr: null as GzjzDsrVO | null
})

// 当事人
const dsrState = reactive({
  listData: [],
  loading: false,
  curRow: null,
})
const dsrTbRef = ref(null)

// 常用短语
const cydyState = reactive({
  listData: [],
  loading: false,
  curRow: null,
  search: ''
})
const gzsxTbRef = ref(null)

const gzsxState = reactive({
  listData: [],
  loading: false,
  curRow: null,
})
const cydyTbRef = ref(null)

// 加载当事人列表
const loadDsrList = async () => {
  if (!props.gzjzId && !curGzjz.value.id && !currentRecordId.value) {
    dsrState.listData = []
    return;
  };
  dsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        dsrState.listData = res.rows || [];

        nextTick(() => {
          setSelectedDsr(); // 设置申请人自动选中
        })
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    dsrState.loading = false;
  }
}

// 设置申请人自动选中
const setSelectedDsr = () => {
  if (dsrState.listData.length > 0) {
    dsrState.listData.forEach((item: GzjzDsrVO) => {
      if (item.js === '1') { // 申请人
        dsrTbRef.value?.toggleRowSelection(item, true);
      }
      // else {
      //   dsrTbRef.value?.toggleRowSelection(item, false);
      // }
    })
  }
}

const loadCydy = async (content?: string) => {
  try {
    cydyState.loading = true;
    let params = {
      pageNum: 1,
      pageSize: 50,
      ...(content ? {content} : {})
    }
    const res = await listMbCydy(params);
    if (res.code === 200 ) {
      cydyState.listData = res.rows || []
    }
  } catch (err: any) {
    ElMessage.error('常用短语查询失败')
  } finally {
    cydyState.loading = false;
  }
}

// 加载公证事项列表
const loadGzsxList = async () => {
  if (!props.gzjzId && !curGzjz.value.id && !currentRecordId.value) {
    gzsxState.listData = []
    return;
  };
  gzsxState.loading = true;
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
      nextTick(() => {
        setSelectedGzsx(); // 设置公证事项自动选中
      })
    }
  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
    gzsxState.loading = false;
  }
}

// 设置所有公证事项自动选中
const setSelectedGzsx = () => {
  if (gzsxState.listData.length > 0) {
    gzsxState.listData.forEach((item: any) => {
      gzsxTbRef.value?.toggleRowSelection(item, true);
    })
  }
}

const tabChange = (name: string) => {
  if (!props.oneReqAll) {
    tabLoad(name);
  }
}

const tabLoad = (name: string) => {
  switch (name) {
    case 'parties' :
      loadDsrList();
    break;

    case 'notaryMatters' :
      loadGzsxList();
    break;

    case 'phrases' :
      loadCydy();
    break;
  }
}

// 搜索短语
const searchPhrase = () => {
  loadCydy(cydyState.search);
}

// 使用短语
const usePhrase = async (row: any) => {
  if (navigator && navigator.clipboard) {
    await navigator.clipboard.writeText(row.content || '');
    ElMessage.success("常用短语已复制到剪切板");
  } else {
    ElMessage.warning("当前浏览器不支持该功能");
  }
}

const copyParty = async (row: any) => {
  // 张三,男,1990-01-01出生,证件号码:123456789012345678,住址:北京市朝阳区
  if (navigator && navigator.clipboard) {
    const name = row.name || row.xm || ''
    const gender = formatGender(`${row.gender || row.xb || row.sex || ''}`)
    const birthDate = row.birthDate || row.csrq || ''
    const idNumber = row.idNumber || row.zjhm || row.certificateNo || ''
    const address = row.address || row.zz || ''

    const txt = `${name}，${gender}，${birthDate} 出生，证件号码：${idNumber}，住址：${address}`
    await navigator.clipboard.writeText(txt);
    ElMessage.success("当事人信息已复制到剪切板");
  } else {
    ElMessage.warning("当前浏览器不支持该功能");
  }
}

const getDsrTableIns = (cb?: (v?: ElTableInstance) => void): ElTableInstance => {
  if(cb && typeof cb === 'function') {
    cb(dsrTbRef.value)
  }
  return dsrTbRef.value;
}

const getGzsxTableIns = (cb?: (v?: ElTableInstance) => void): ElTableInstance => {
  if(cb && typeof cb === 'function') {
    cb(dsrTbRef.value)
  }
  return dsrTbRef.value;
}

const getCydyTableIns = (cb?: (v?: ElTableInstance) => void): ElTableInstance => {
  if(cb && typeof cb === 'function') {
    cb(dsrTbRef.value)
  }
  return dsrTbRef.value;
}

const handleUpdateDsrSelectId = (data : any) => {
  if (Array.isArray(data)) {
    yydsrState.selectedDsrIds = data
  } else {
    // 兼容传入单个对象的情况
    yydsrState.selectedDsr = data
    yydsrState.selectedDsrIds = data.id ? [data.id] : []
  }

  console.log('引用选中的当事人信息:', data)
}

// 风险信息提示
const riskTip = async (zjhm: string): Promise<any> => {
  // 风险信息 start
  const hasHmd = await queryDsrxxZrrByIdCard({ IdcardNumber: zjhm });
  const { code, data } = hasHmd || {};
  const { hmdxxVo } = data || {};

  if (code === 200 && hmdxxVo) {
    return ElMessageBox({
      title: '风险信息提示',
      message: h('div', {
        class: 'p-20px'
      }, [
        h(DataItem, { label: '客户名称：', content: hmdxxVo.sxr || '' }),
        h(DataItem, { label: '证件号码：', content: hmdxxVo.sxrzjhm || '' }),
        h(DataItem, { label: '查获时间：', content: formatDate(hmdxxVo.chrq) || ''}),
        h(DataItem, { label: '风险信息：', content: hmdxxVo.qksm || '' }),
      ]),
      showClose: false,
    })
  }
  return Promise.resolve();
  // 风险信息 end
}

// 确认引用当事人
const confirmYydsr = async () => {
  const selectedDsrInfo = yydsrRef.value?.getSelectedDsrInfo?.() || yydsrState.selectedDsr
  if (!yydsrState.selectedDsrIds.length || !selectedDsrInfo) {
    ElMessage.warning('请选择要引用的当事人');
    return;
  }

  try {

    await riskTip(selectedDsrInfo.zjhm); // 风险信息提示

    yydsrState.loading = true;

    const formData : GzjzDsrForm = {
      dsrId: selectedDsrInfo.id,
      dsrLx: selectedDsrInfo.dsrlb === '2' ? '2' : '1', // dsrlb: '1'个人 '2'单位,
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      js: '1', // 默认申请人
      sfdk: '0',
      remark: '',
      zrrBo: selectedDsrInfo,
    }
    const res = await addGzjzDsr(formData);
    if (res && res.code === 200) {
      ElMessage.success('引用当事人成功');
      loadDsrList();
      yydsrState.visible = false;
    } else {
      ElMessage.error('引用当事人失败');
    }
  } catch (err: any) {
    console.error('引用当事人失败:', err);
    ElMessage.error('引用当事人失败');
  } finally {
    yydsrState.loading = false;
  }
}

const dsrCurRow = (row: any) => {
  dsrState.curRow = row;
}

const gzsxCurRow = (row: any) => {
  gzsxState.curRow = row;
}

const cydyCurRow = (row: any) => {
  cydyState.curRow = row;
}

const getDsrCurrentRow = (): any => {
  return dsrState.curRow;
}

const getGzsxCurrentRow = (): any => {
  return gzsxState.curRow;
}

const getCydyCurrentRow = (): any => {
  return cydyState.curRow;
}

const getSelectedDsr = (): any[] => {
  return dsrTbRef.value?.getSelectionRows() || [];
}

const getSelectedGzsx = (): any[] => {
  return gzsxTbRef.value?.getSelectionRows() || [];
}

const getSelectedCydy = (): any[] => {
  return cydyTbRef.value?.getSelectionRows() || [];
}

// 暴露方法
defineExpose({
  getDsrTableIns,
  getGzsxTableIns,
  getCydyTableIns,
  getDsrCurrentRow,
  getGzsxCurrentRow,
  getCydyCurrentRow,
  getSelectedDsr,
  getSelectedGzsx,
  getSelectedCydy,
})

const dataInit = () => {
  if (props.oneReqAll) {
    loadDsrList();
    loadGzsxList();
    loadCydy();
  } else if (props.exceptPane?.includes('parties')) {
    loadGzsxList();
    loadCydy();
  } else if (props.exceptPane?.includes('notaryMatters')) {
    loadDsrList();
    loadCydy();
  } else if (props.exceptPane?.includes('phrases')) {
    loadDsrList();
    loadGzsxList();
  } else {
    tabLoad(curTabName.value);
  }
}

onMounted(() => {
  dataInit();
})
</script>

<style scoped>
:deep(.el-tabs) {
  height: calc(100% - 55px) !important; /* 调整标签页内容高度 */
}
</style>
