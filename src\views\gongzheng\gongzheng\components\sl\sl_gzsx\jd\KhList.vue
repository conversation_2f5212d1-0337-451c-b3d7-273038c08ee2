<template>
  <div>
    <div class="flex justify-between items-center mb-10px">
      <span class="text-base font-bold">客户列表</span>
      <div>
        <el-button @click="linkKhList" type="primary" size="small">引用</el-button>
        <el-button @click="delKhList" type="danger" size="small">移除</el-button>
      </div>
    </div>
    <el-table :data="khList" ref="khTableRef" height="260" size="small" border stripe>
      <el-table-column type="index" label="#" width="60" align="center"/>
      <el-table-column type="selection" width="50" align="center"/>
      <el-table-column prop="name" label="姓名" width="120" align="center" show-overflow-tooltip/>
      <el-table-column prop="phone" label="电话号码" width="120" align="center" show-overflow-tooltip/>
      <el-table-column prop="addr" label="住址" align="center" show-overflow-tooltip/>
      <el-table-column label="类型" width="150" align="center">
        <template #default="{ row }">
          <div class="flex flex-wrap">
            <el-checkbox v-model="row.isLender" label="放款人" true-value="1" false-value="0" size="small"/>
            <el-checkbox v-model="row.isMortgagor" label="抵押人" true-value="1" false-value="0" size="small"/>
            <el-checkbox v-model="row.isBorrower" label="借款人" true-value="1" false-value="0" size="small"/>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <LinkDsr ref="linkDsrRef" @success="willLinkDsr"/>
  </div>
</template>

<script setup lang="ts">
import { JdKhVO } from '@/api/gongzheng/bzfz/gzjzJd/types';
import LinkDsr from './LinkDsr.vue';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';


interface Props {
  modelValue: JdKhVO[],
}

const props = defineProps<Props>()

const khTableRef = ref<ElTableInstance>(null);
const linkDsrRef = ref<any>(null);

const emit = defineEmits(['update:modelValue']);

const khList = computed<JdKhVO[]>({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const linkKhList = () => {
  linkDsrRef.value?.open()
}

const willLinkDsr = (list: GzjzDsrVO[]) => {
  const exitsDsrIds = khList.value.map(item => item.dsrId);
  const newDsrList = list.filter(item => !exitsDsrIds.includes(item.dsrId));
  if(newDsrList.length === 0) {
    ElMessage.error('客户列表中已存在这些客户')
    return
  }

  khList.value = [...khList.value, ...newDsrList.map(item => {
    return {
      dsrId: item.dsrId,
      name: item.name,
      phone: item.phone || item.contactTel,
      addr: item.address,
      isLender: '0',
      isMortgagor: '0',
      isBorrower: '0',
    }
  })]
}

const delKhList = () => {
  const selectedKhs = khTableRef.value?.getSelectionRows() || [];
  if(selectedKhs.length === 0) {
    ElMessage.error('请选择要移除的客户')
    return
  }

  const selectedDsrIds = selectedKhs.map(item => item.dsrId);

  ElMessageBox.confirm('确定要移除这些客户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    khList.value = khList.value.filter(item => !selectedDsrIds.includes(item.dsrId))
    ElMessage.success('已移除，需保存后才能生效')
  }).catch(() => {})
}

</script>
