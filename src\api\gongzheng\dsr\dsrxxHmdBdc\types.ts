export interface DsrxxHmdBdcVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 不动产登记号
   */
  bdcdjh: string;

  /**
   * 不动产地址
   */
  dz: string;

  /**
   * 不动产省份
   */
  bdcSf: string;

  /**
   * 不动产所在市
   */
  bdcSq: string;

  /**
   * 不动产所在区
   */
  bdcQ: string;

  /**
   * 状态
   */
  zt: number;

  /**
   * 机构标识
   */
  jgbs: string;

  /**
   * 机构编码
   */
  jgbm: string;

  /**
   * 机构名称
   */
  jgmc: string;

  /**
   * 查获日期
   */
  chrq: string;

  /**
   * 公证事项
   */
  gzsx: string;

  /**
   * 产权人
   */
  cqr: string;

  /**
   * 申请人
   */
  sqr: string;

  /**
   * 申请人身份证号码
   */
  sqrsfz: string;

  /**
   * 情况说明
   */
  qksm: string;

  /**
   * 备注
   */
  remark: string;

}

export interface DsrxxHmdBdcForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 不动产登记号
   */
  bdcdjh?: string;

  /**
   * 不动产地址
   */
  dz?: string;

  /**
   * 不动产省份
   */
  bdcSf?: string;

  /**
   * 不动产所在市
   */
  bdcSq?: string;

  /**
   * 不动产所在区
   */
  bdcQ?: string;

  /**
   * 状态
   */
  zt?: number;

  /**
   * 机构标识
   */
  jgbs?: string;

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 查获日期
   */
  chrq?: string;

  /**
   * 公证事项
   */
  gzsx?: string;

  /**
   * 产权人
   */
  cqr?: string;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 申请人身份证号码
   */
  sqrsfz?: string;

  /**
   * 情况说明
   */
  qksm?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface DsrxxHmdBdcQuery extends PageQuery {

  /**
   * 不动产登记号
   */
  bdcdjh?: string;

  /**
   * 不动产地址
   */
  dz?: string;

  /**
   * 不动产省份
   */
  bdcSf?: string;

  /**
   * 不动产所在市
   */
  bdcSq?: string;

  /**
   * 不动产所在区
   */
  bdcQ?: string;

  /**
   * 状态
   */
  zt?: number;

  /**
   * 机构标识
   */
  jgbs?: string;

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 查获日期
   */
  chrq?: string;

  /**
   * 公证事项
   */
  gzsx?: string;

  /**
   * 产权人
   */
  cqr?: string;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 申请人身份证号码
   */
  sqrsfz?: string;

  /**
   * 情况说明
   */
  qksm?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



