import { StringDecoder } from "string_decoder";

export interface GzjzSdxxVO {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 通知方式
   */
  tzfs?: string;

  /**
   * 送达邮箱
   */
  sdyx?: string;

  /**
   * 送达号码
   */
  sdhm?: string;

  /**
   * 送达日期
   */
  sdrq?: string;

  /**
   * 发送结果
   */
  fsjg?: string;

  /**
   * 送达人ID（来自卷宗当事人）
   */
  sdrId?: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId?: string | number;

  /**
   * 备注
   */
  remark?: string;
  /**
   * 联系电话
   */
  lxdh?: string;
  /**
   * 领取地点
   */
  lqdd?: string;
  /**
   * 领取信息（字典）
   */
  lqxx?: string;
  /**
   * 送达类型（字典：按卷宗、按公证事项）
   */
  sdlx?: string;
  /**
   * 按公证事项时填写{gzsxId,lqfs}
   */
  gzsxJson?: string;
}

export interface GzjzSdxxForm {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 通知方式
   */
  tzfs?: string;

  /**
   * 送达邮箱
   */
  sdyx?: string;

  /**
   * 送达号码
   */
  sdhm?: string;

  /**
   * 送达日期
   */
  sdrq?: string;

  /**
   * 发送结果
   */
  fsjg?: string;

  /**
   * 送达人ID（来自卷宗当事人）
   */
  sdrId?: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId?: string | number;

  /**
   * 备注
   */
  remark?: string;
  /**
   * 联系电话
   */
  lxdh?: string;
  /**
   * 领取地点
   */
  lqdd?: string;
  /**
   * 领取信息（字典）
   */
  lqxx?: string;
  /**
   * 送达类型（字典：按卷宗、按公证事项）
   */
  sdlx?: string;
  /**
   * 按公证事项时填写{gzsxId,lqfs}
   */
  gzsxJson?: string;
}

export interface GzjzSdxxQuery extends PageQuery {
  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 通知方式
   */
  tzfs?: string;

  /**
   * 送达邮箱
   */
  sdyx?: string;

  /**
   * 送达号码
   */
  sdhm?: string;

  /**
   * 送达日期
   */
  sdrq?: string;

  /**
   * 发送结果
   */
  fsjg?: string;

  /**
   * 送达人ID（来自卷宗当事人）
   */
  sdrId?: string | number;

  /**
   * 短信服务日志ID
   */
  dxfwrzId?: string | number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 联系电话
   */
  lxdh?: string;
  /**
   * 领取地点
   */
  lqdd?: string;
  /**
   * 领取信息（字典）
   */
  lqxx?: string;
  /**
   * 送达类型（字典：按卷宗、按公证事项）
   */
  sdlx?: string;
  /**
   * 按公证事项时填写{gzsxId,lqfs}
   */
  gzsxJson?: string;
  /**
    * 日期范围参数
    */
  params?: any;
}



