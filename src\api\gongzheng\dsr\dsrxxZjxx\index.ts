import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxZjxxVO, DsrxxZjxxForm, DsrxxZjxxQuery } from '@/api/gongzheng/dsr/dsrxxZjxx/types';

/**
 * 查询当事人-证件列列表
 * @param query
 * @returns {*}
 */

export const listDsrxxZjxx = (query?: DsrxxZjxxQuery): AxiosPromise<DsrxxZjxxVO[]> => {
  return request({
    url: '/dsr/dsrxxZjxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-证件列详细
 * @param id
 */
export const getDsrxxZjxx = (id: string | number): AxiosPromise<DsrxxZjxxVO> => {
  return request({
    url: '/dsr/dsrxxZjxx/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-证件列
 * @param data
 */
export const addDsrxxZjxx = (data: DsrxxZjxxForm) => {
  return request({
    url: '/dsr/dsrxxZjxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-证件列
 * @param data
 */
export const updateDsrxxZjxx = (data: DsrxxZjxxForm) => {
  return request({
    url: '/dsr/dsrxxZjxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-证件列
 * @param id
 */
export const delDsrxxZjxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxZjxx/' + id,
    method: 'delete'
  });
};
