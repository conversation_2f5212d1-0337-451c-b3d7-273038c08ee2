export interface GzrzLcrzxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

  /**
   * 机构名称
   */
  jgmc: string;

  /**
   * 操作所处环节
   */
  czschj: string;

  /**
   * 日志内容
   */
  rznr: string;

  /**
   * 操作日期
   */
  czrq: string;

  /**
   * 操作人
   */
  czr: string;

  /**
   * 公证书编号
   */
  gzsbh: string;

  /**
   * 流程意见
   */
  lcyj: string;

}

export interface GzrzLcrzxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 操作所处环节
   */
  czschj?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作日期
   */
  czrq?: string;

  /**
   * 操作人
   */
  czr?: string;

  /**
   * 公证书编号
   */
  gzsbh?: string;

  /**
   * 流程意见
   */
  lcyj?: string;

}

export interface GzrzLcrzxxQuery extends PageQuery {

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 操作所处环节
   */
  czschj?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作日期
   */
  czrq?: string;

  /**
   * 操作人
   */
  czr?: string;

  /**
   * 公证书编号
   */
  gzsbh?: string;

  /**
   * 流程意见
   */
  lcyj?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



