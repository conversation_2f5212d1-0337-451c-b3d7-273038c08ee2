<template>
  <el-dialog v-model="visible" :title="props.title" width="700" @closed="handleClosed" show-close destroy-on-close>
    <div class="flex items-center gap-2 mb-3">
      <el-input v-model="zjState.treeFilterStr" placeholder="请输入关键字" @keyup.enter="treeFilter" @clear="resetTreeFilter" style="width: 240px;" clearable/>
      <el-button type="primary" @click="treeFilter">查询</el-button>
      <el-button @click="resetTreeCheck">重置选取</el-button>
    </div>
    <div class="h-260px overflow-auto rounded-md p-2 mb-3 tree-sd">
      <el-tree
        ref="treeRef"
        :data="zjState.treeData"
        :props="{
          label: 'title',
          children: 'children',
          disabled: 'disabled'
        }"
        node-key="id"
        :default-expanded-keys="zjState.treeExpanded"
        :default-checked-keys="zjState.treeSelected"
        show-checkbox
        multiple
        :render-after-expand="false"
        :filter-node-method="filterNodes"
        @check-change="nodeCheckChange"
      />
    </div>
    <div class="h-120px overflow-auto rounded-md p-2 tree-sd">
      <div class="flex flex-wrap gap-1">
        <template v-for="item in zjState.treeSelectedNodes" :key="item.id">
          <el-tag v-if="!item.notMust" @close="removeNode(item)" closable effect="plain">{{ item.title }}</el-tag>
        </template>
        <template v-for="item in zjState.customAddNodes" :key="item.id">
          <el-tag @close="removeCustomNode(item)" type="success" closable effect="plain">{{ item.title }}</el-tag>
        </template>
        <el-input v-if="zjState.customInputShow" ref="customAddInput" v-model="zjState.customNodeName" @keyup.enter="addCustomNode" @blur="cancelAddCustomNode" size="small" style="width: 120px;"/>
        <el-button v-else @click="openCustomAddNode" size="small">添加自定义</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button style="cursor: default;" plain>共 {{ zjlxCount }} 项</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getEvidenceMaterialTree } from '@/api/gongzheng/gongzheng/zjcl';
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { custom } from 'vue-types';

interface Props {
  modelValue?: boolean,
  title?: string,
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '新增证据'
})

const treeRef = ref(null)
const customAddInput = ref(null)

const emit = defineEmits(['update:modelValue', 'confirm'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const zjlxCount = computed(() => {
  return zjState.treeSelectedNodes.length + zjState.customAddNodes.length
})

const zjState = reactive({
  treeExpanded: [],

  treeData: [],
  treeFilterStr: '',

  customInputShow: false,
  customAddNodes: [],
  customNodeName: '',

  treeSelected: [],
  treeSelectedNodes: [],
})

/**====================================================================== 数据 END ======================================================================**/

const handleClosed = () => {
  reset()
}

const reset = () => {
  zjState.treeExpanded = [];
  zjState.treeData = [];
  zjState.treeFilterStr = '';
  zjState.customInputShow = false;
  zjState.customAddNodes = [];
  zjState.customNodeName = '';
  zjState.treeSelected = [];
  zjState.treeSelectedNodes = [];
}

const treeFilter = () => {
  treeRef.value.filter(zjState.treeFilterStr);
}

const resetTreeFilter = () => {
  treeRef.value.filter('');
}

const resetTreeCheck = () => {
  treeRef.value.setCheckedKeys([])
}

const buildTree = (list : any[]) => {
  const map : Record<string, any> = {};
  list.forEach(item => map[item.treeCode] = { ...item, children: [] });
  const tree : any[] = [];
  list.forEach(item => {
    if (item.parentCode && map[item.parentCode]) {
      map[item.parentCode].notMust = true;
      map[item.parentCode].children.push(map[item.treeCode]);
    } else {
      tree.push(map[item.treeCode]);
    }
  });
  return tree;
}

const loadTree = async () => {
  const res = await getEvidenceMaterialTree();
  zjState.treeData = buildTree(res.data || []);
};

// 查询/过滤节点
const filterNodes = (value: string, data: any) => {
  if (!value) return true;
  return data.title.includes(value);
}

const nodeCheckChange = (data: any, checked: boolean, hasCheckedNodes: any) => {
  if(data.notMust) return;
  if(checked) {
    zjState.treeSelected.push(data.id)
    zjState.treeSelectedNodes.push(data)
  } else {
    const index = zjState.treeSelected.indexOf(data.id)
    if(index > -1) {
      zjState.treeSelected.splice(index, 1)
      zjState.treeSelectedNodes.splice(index, 1)
    }
  }
}

const removeNode = (node: any) => {
  treeRef.value.setChecked(node.id, false)
}

const openCustomAddNode = () => {
  zjState.customInputShow = true;
  nextTick(() => {
    customAddInput.value.input.focus()
  })

}

const addCustomNode = () => {
  const timeStamp = new Date().getTime();
  zjState.customAddNodes.push({
    id: `custom_${timeStamp}`,
    title: zjState.customNodeName
  });

  zjState.customInputShow = false;
  zjState.customNodeName = ''
}

const cancelAddCustomNode = () => {
  zjState.customInputShow = false;
  zjState.customNodeName = ''
}

const removeCustomNode = (node: any) => {
  const index = zjState.customAddNodes.indexOf(node)
  if(index > -1) {
    zjState.customAddNodes.splice(index, 1)
  }
}

const confirm = () => {
  const treeSelectedNodeNames = zjState.treeSelectedNodes.map(item => item.title);
  const customAddNodeNames = zjState.customAddNodes.map(item => item.title);
  const concatNodeNames = treeSelectedNodeNames.concat(customAddNodeNames);

  emit('confirm', {
    names: concatNodeNames,
    nodes: [...zjState.treeSelectedNodes],
    customNodes: [...zjState.customAddNodes]
  })
  setTimeout(() => {
    emit('update:modelValue', false);
    reset();
  }, 200)
}

watch(() => props.modelValue, (val) => {
  if(val) {
    loadTree();
  }
})

onMounted(() => {
  loadTree();
})
</script>

<style scoped>
.tree-sd {
  box-shadow: 0 0 4px 0 rgb(51, 51, 51, .3);
}
</style>
