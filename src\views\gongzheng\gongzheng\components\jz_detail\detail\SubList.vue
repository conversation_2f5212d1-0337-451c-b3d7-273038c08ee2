<template>
  <el-card>
    <template #header>
      <strong class="text-base">制作副本记录</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="js" label="公证书编号" align="center" />
      <el-table-column prop="name" label="公证事项" align="center" />
      <el-table-column prop="dsrLx" label="使用日期" align="center" />
      <el-table-column prop="zjlx" label="份数" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
