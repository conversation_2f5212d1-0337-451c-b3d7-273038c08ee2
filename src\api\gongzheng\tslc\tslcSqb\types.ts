export interface TslcSqbVO {
  /**
   * ID
   */
  id : string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId : string | number;

  /**
   * 卷宗号
   */
  jzh : string;

  /**
   * 状态
   */
  tslcZt : string;

  /**
   * 类型
   */
  tslcLx : string;

  /**
   * 发起人ID
   */
  tslcFqrId : string | number;

  /**
   * 发起人
   */
  tslcFqr : string;

  /**
   * 发起日期
   */
  tslcFqrq : string;

  /**
   * 公证书编号
   */
  gzsbh : string;

  /**
   * 决定书编号
   */
  jdsbh : string;

  /**
   * 决定书文件路径
   */
  jdswj : string;

  /**
   * 是否取号
   */
  sfQh : string;

  /**
   * 是否销号
   */
  sfXh : string;

  /**
   * 申请原因
   */
  tslcSqyy : string;

  /**
   * 审批人ID
   */
  tslcSprId : string | number;

  /**
   * 审批人
   */
  tslcSpr : string;

  /**
   * 审批日期
   */
  tslcSprq : string;

  /**
   * 审批意见
   */
  tslcSpyj : string;

  /**
   * 审批表文件路径
   */
  tslcSpbwj : string;

  /**
   * 备注
   */
  remark : string;

  /**
   * 明细列表（按类型返回）
   */
  items?: any[];

  /** 额外扩展数据 */
  extData?: any;
}

export interface TslcSqbForm extends BaseEntity {
  /**
   * ID
   */
  id ?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 卷宗号
   */
  jzh ?: string;

  /**
   * 状态
   */
  tslcZt ?: string;

  /**
   * 类型
   */
  tslcLx ?: string;

  /**
   * 发起人ID
   */
  tslcFqrId ?: string | number;

  /**
   * 发起人
   */
  tslcFqr ?: string;

  /**
   * 发起日期
   */
  tslcFqrq ?: string;

  /**
   * 公证书编号
   */
  gzsbh ?: string;

  /**
   * 决定书编号
   */
  jdsbh ?: string;

  /**
   * 决定书文件路径
   */
  jdswj ?: string;

  /**
   * 是否取号
   */
  sfQh ?: string;

  /**
   * 是否销号
   */
  sfXh ?: string;

  /**
   * 申请原因
   */
  tslcSqyy ?: string;

  /**
   * 审批人ID
   */
  tslcSprId ?: string | number;

  /**
   * 审批人
   */
  tslcSpr ?: string;

  /**
   * 审批日期
   */
  tslcSprq ?: string;

  /**
   * 审批意见
   */
  tslcSpyj ?: string;

  /**
   * 审批表文件路径
   */
  tslcSpbwj ?: string;

  /**
   * 备注
   */
  remark ?: string;

  /**
   * 明细列表（统一提交入口使用）
   */
  items?: any[];

  /** 额外扩展数据 */
  extData?: any;

  /** 以下为常见可选字段（视类型存在） */
  refundMethod?: number; // 退费方式
  decisionNumber?: string; // 决定书编号
  wdOssId?: string | number; // 决定书文件OSS id
  fileUrl?: string;
  fileName?: string;
  caseList?: any[]; // 归档类场景
}

export interface TslcSqbQuery extends PageQuery {

  id : number;
  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 卷宗号
   */
  jzh ?: string;

  /**
   * 状态
   */
  tslcZt ?: string;

  /**
   * 类型
   */
  tslcLx ?: string;

  /**
   * 发起人ID
   */
  tslcFqrId ?: string | number;

  /**
   * 发起人
   */
  tslcFqr ?: string;

  /**
   * 发起日期
   */
  tslcFqrq ?: string;

  /**
   * 公证书编号
   */
  gzsbh ?: string;

  /**
   * 决定书编号
   */
  jdsbh ?: string;

  /**
   * 决定书文件路径
   */
  jdswj ?: string;

  /**
   * 是否取号
   */
  sfQh ?: string;

  /**
   * 是否销号
   */
  sfXh ?: string;

  /**
   * 申请原因
   */
  tslcSqyy ?: string;

  /**
   * 审批人ID
   */
  tslcSprId ?: string | number;

  /**
   * 审批人
   */
  tslcSpr ?: string;

  /**
   * 审批日期
   */
  tslcSprq ?: string;

  /**
   * 审批意见
   */
  tslcSpyj ?: string;

  /**
   * 审批表文件路径
   */
  tslcSpbwj ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}
