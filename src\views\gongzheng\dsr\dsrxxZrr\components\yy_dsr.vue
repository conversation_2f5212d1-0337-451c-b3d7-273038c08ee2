<template>
  <!-- 引用当事人组件-->
  <div>
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="140px">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="当事人名称/证件号" prop="khh">
                  <el-input v-model="queryParams.xm" placeholder="请输入当事人名称/证件号" clearable @keyup.enter="handleQuery" style="width: 160px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="当事人类型" prop="dsrlb">
                  <el-select :disabled="props.editDlrlb" v-model="queryParams.dsrlb" placeholder="请选择当事人类型" @change="resetZjlx" clearable style="width: 160px;">
                    <el-option v-for="dict in gz_dsr_lb" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="证件类型" prop="zjlx">
                  <el-select v-model="queryParams.zjlx" placeholder="请选择证件类型" clearable style="width: 160px;">
                    <template v-if="queryParams.dsrlb==='1'">
                      <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </template>
                    <template v-else-if="queryParams.dsrlb==='2'">
                      <el-option v-for="dict in gz_jg_zjlx" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </template>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码" prop="zjhm">
                  <el-input v-model="queryParams.zjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" style="width: 160px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item> -->
          </el-form>
          <div class="dialog-footer">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </div>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <span>单击选择行</span>
      <el-table v-loading="loading" :data="dsrxxZrrList" border highlight-current-row
        @current-change="handleCurrentChange">
        <!-- <el-table-column type="selection" label="序号" align="center"> </el-table-column> -->
        <el-table-column label="名称" align="center" prop="xm" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.dsrlb==='1'? scope.row.xm : scope.row.dwmc }}
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" prop="dsrlb" show-overflow-tooltip>
          <template #default="scope">
            <el-tag>{{ dictMapFormat(gz_dsr_lb, scope.row.dsrlb) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="证件名称" align="center" prop="zjlx" show-overflow-tooltip>
          <template #default="scope">
            <el-tag>{{ dictMapFormat(scope.row.dsrlb === '1' ? gz_gr_zjlx : gz_jg_zjlx, scope.row.zjlx) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="证件号码" align="center" prop="zjhm" width="160px" show-overflow-tooltip />
        <el-table-column label="联系电话" align="center" prop="lxdh" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button v-if="scope.row.dsrlb==='1'" size="small" type="primary" link icon="InfoFilled"
                @click="handleDetail(scope.row)" v-hasPermi="['dsr:dsrxxZrr:query']">查看</el-button>
              <el-button v-else-if="scope.row.dsrlb==='2'" size="small" type="primary" link icon="InfoFilled"
                @click="handleDetail2(scope.row)" v-hasPermi="['dsr:dsrxxFrhzz:query']">查看</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 查看个人客户详情-->
    <el-drawer :title="dialog.title" v-model="dialog.visible" append-to-body size="90%">
      <el-card class="no-padding-card dateil-card-main" v-if="dialigEdit">
        <div slot="header" class="clearfix dateil-card-heard">
          <span>客户信息</span>
        </div>
        <!-- 当事人照片-->
        <DsrZp v-if="dialog.visible" ref="dsrListRef" :vo="form" :dialigEdit="dialigEdit"></DsrZp>
      </el-card>
      <!--证件列表-->
      <Zjlb v-if="dialog.visible" ref="zjlbRef" :vo="form"></Zjlb>
      <el-tabs style="margin-top:10px" v-model="activeName" type="border-card">
        <el-tab-pane label="基本资料" name="first">
          <Grxx v-if="dialog.visible" ref="grxxRef" :vo="form" :dialigEdit="dialigEdit"></Grxx>
        </el-tab-pane>
        <el-tab-pane label="办证记录" name="second"></el-tab-pane>
        <el-tab-pane label="证据材料" name="third"></el-tab-pane>
        <el-tab-pane label="身份识别记录" name="fourth"></el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 查看单位客户详情-->
    <el-drawer :title="dialog2.title" v-model="dialog2.visible" append-to-body size="90%">
      <Dwxx v-if="dialog2.visible" ref="dwxxRef" :vo="form2" :dialigEdit="dialigEdit2">
      </Dwxx>
      <!--证件列表-->
      <Zjlb v-if="dialog2.visible" ref="zjlbRef" :vo="form2"></Zjlb>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel2">关 闭</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="Yydlr" lang="ts">
  import { listDsrxxZrr, getDsrxxZrr, delDsrxxZrr, addDsrxxZrr, updateDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
  import { DsrxxZrrVO, DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  import { listDsrxxFrhzz, getDsrxxFrhzz, delDsrxxFrhzz, addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import Grxx from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/gr_xx.vue'
  import Dwxx from '@/views/gongzheng/dsr/dsrxxZrr/components/dw/dw_xx.vue'
  import Zjlb from '@/views/gongzheng/dsr/dsrxxZrr/components/zjlb.vue'
  import DsrZp from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/dsr_zp.vue'
  import { dictMapFormat } from '@/utils/ruoyi';
  const grxxRef = ref<InstanceType<typeof Grxx> | null>(null);
  const dwxxRef = ref<InstanceType<typeof Dwxx> | null>(null);
  const zjlbRef = ref<InstanceType<typeof Zjlb> | null>(null);

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_lb, gz_gr_zjlx, gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_jg_zjlx'));
  const dsrxxZrrList = ref<DsrxxZrrVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(false);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const selectedRowId = ref(null); // 存储选中行的id
  const total = ref(0);
  const single = ref(true);
  const multiple = ref(true);
  const queryFormRef = ref<ElFormInstance>();
  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: undefined,
    ywm: undefined,
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined
  }
  const initDwxxFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: "1",
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { queryParams, form } = toRefs(data);
  const lbbj = ref();
  const data2 = reactive<{form2: DsrxxFrhzzForm, queryParams2: DsrxxFrhzzQuery} & Record<string, any>>({
    form2: { ...initDwxxFormData },
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { queryParams2, form2 } = toRefs(data2);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialigEdit = ref(true);
  const dialigEdit2 = ref(true);
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const activeName = ref("first");
  const dsrxxZrrFormRef = ref<ElFormInstance>();


  interface Props {
    editDlrlb : boolean;
  }
  const props = defineProps<Props>();
    // 定义事件
  const emits = defineEmits<{
    (e : 'update-select', []) : void;
  }>();
  // 处理行点击
  const handleCurrentChange = async (row) => {
    selectedRowId.value = row.id;
    var data = null;
    if (queryParams.value.dsrlb === '1') {
      data = await getDsrxxZrr(selectedRowId.value);
    } else if (queryParams.value.dsrlb === '2') {
      data = await getDsrxxFrhzz(selectedRowId.value);
    }
    if (data?.code == 200) {
      //选中id 回传 父组件
      updateSelect(data.data);
      // emits('update-select', data.data);
    }
  };

  // 行点击获取对应行数据
  const handleRowClick = (row: DsrxxZrrVO) => {
    console.log('handleRowClick', row);
  }

  const resetZjlx = () => {
    queryParams.value.zjlx = undefined;
  }

  /** 查询当事人-基本信息-自然人信息列表 */
  const getList = async () => {
    try {
      loading.value = true;
      const res = await listDsrxxZrr(queryParams.value);
      dsrxxZrrList.value = res.rows;
      total.value = res.total;
    } catch (err: any) {
      console.log(err);
    } finally {
      loading.value = false;
    }


  }
  /** 搜索按钮操作 */
  const handleQuery = () => {
    lbbj.value = queryParams.value.dsrlb;
    queryParams.value.pageNum = 1;
    if (queryParams.value.dsrlb === "1") {
      getList();
    } else {
      queryParams2.value.dwmc = queryParams.value.xm;
      queryParams2.value.zjlx = queryParams.value.zjlx;
      queryParams2.value.zjhm = queryParams.value.zjhm;
      getList2();
    }
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    queryParams.value = {
      ...queryParams.value,
      zjhm: undefined,
      xm: undefined,
      dsrlb: '1',
    }
    handleQuery();
  }
  /** 查询单位客户列表 */
  const getList2 = async () => {
    loading.value = true;
    try {
      const res = await listDsrxxFrhzz(queryParams2.value);
      dsrxxZrrList.value = res.rows;
      total.value = res.total;
    } catch (err: any) {
      console.log(err);
    } finally {
      loading.value = false;
    }
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxZrrVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxZrrFormRef.value?.resetFields();
  }
  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 取消按钮 */
  const cancel2 = () => {
    dialog2.visible = false;
  }

  const handleDetail = async (row ?: DsrxxZrrVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZrr(_id);
    Object.assign(form.value, res.data);
    dialigEdit.value = false;
    dialog.visible = true;
    dialog.title = "查看个人客户信息";
  }
  const handleDetail2 = async (row ?: DsrxxFrhzzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxFrhzz(_id);
    Object.assign(form2.value, res.data);
    dialigEdit2.value = false;
    dialog2.visible = true;
    dialog2.title = "查看单位客户信息";
  }

  const updateSelect = (data: any) => {
    emits('update-select', data);
  };
  // 暴露父组件 方法
  defineExpose({
    updateSelect,
  });

  onMounted(() => {
    selectedRowId.value = null;
    console.log('引用当事人', queryParams.value)
    // if (queryParams.value.dsrlb === "1") {
    //   getList();
    // } else {
    //   getList2();
    // }

  });
</script>

<style scoped>
  .selected-row {
    background-color: #f5f7fa !important;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
</style>
