<template>
  <div class="app-container">
    <el-card shadow="never" class="search-wrapper">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <BasicInfoForm
          ref="basicFormRef"
          v-model="basicFormData"
          :is-edit="isEdit"
        >
          <template #header>
            <div class="section-header">
              <h3>基本信息</h3>
              <div class="header-actions">
                <el-button type="primary" @click="handleSaveBasic" :loading="saveLoading">
                  {{ isEdit ? '保存修改' : '保存' }}
                </el-button>
              </div>
            </div>
          </template>
        </BasicInfoForm>
      </div>

      <!-- 当事人列表 -->
      <div class="form-section" v-if="currentRecordId">
        <PartyPersonList
          v-model:data="partyPersonData"
          @add-person="handleAddPerson"
          @edit-person="handleEditPerson"
          @delete-person="handleDeletePerson"
          @read-card="handleReadCard"
        />
      </div>

      <!-- 公证事项 -->
      <div class="form-section" v-if="currentRecordId">
        <NotaryMatters
          v-model:data="notaryMattersData"
          @add-matter="handleAddMatter"
          @edit-matter="handleEditMatter"
          @delete-matter="handleDeleteMatter"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup name="NotaryReview" lang="ts">
import { ref, reactive, onMounted, inject, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Ref } from 'vue'
import BasicInfoForm from '@/views/gongzheng/gongzheng/components/sl/BasicInfoForm.vue'
import PartyPersonList from '@/views/gongzheng/gongzheng/components/sl/sl_dsr/PartyPersonList.vue'
import NotaryMatters from '@/views/gongzheng/gongzheng/components/sl/sl_gzsx/NotaryMatters.vue'
import { addGzjzJbxx, updateGzjzJbxx, getGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx'
import type { GzjzJbxxForm, GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import type { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
import { formatDate } from '@/utils/ruoyi'
import { useUserStore } from '@/store/modules/user'
import eventBus from '@/utils/eventBus'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gzy } = toRefs<any>(proxy?.useRoleUser('gzy'));

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))
const gzlbId = inject<Ref<string | number | null>>('gzlbId', ref(null))
const currentRecord = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const isEdit = computed(() => !!currentRecordId.value)

const basicFormRef = ref()
const saveLoading = ref(false)

// 基本信息表单数据
const basicFormData = ref<GzjzJbxxForm>({
  jzbh: '',
  sqsj: '',
  slrq: '',
  lb: '',
  gzybm: '',
  zlbm: '',
  xbrbh: '',
  syd: '',
  ywwz: undefined,
  jjd: '1',
  sfmj: '0',
  dplqdh: '',
  yt: '',
  flxz: '',
  rz: '0',
  wbddh: '',
  sfwyz: '0',
  sfljc: '0',
  sfdzqm: '0',
  sfdzgzs: '0'
})

// 当事人数据
const partyPersonData = ref<GzjzDsrVO[]>([])

// 公证事项数据
const notaryMattersData = ref<GzjzGzsxVO[]>([])

// 获取基本信息详情
const getBasicInfo = async (id: string | number) => {
  try {
    const res = await getGzjzJbxx(id)
    if (res.code === 200) {
      basicFormData.value = {
        ...basicFormData.value,
        ...res.data
      }
    }
  } catch (error) {
    console.error('获取基本信息失败:', error)
    ElMessage.error('获取基本信息失败')
  }
}

// 保存基本信息
const handleSaveBasic = async () => {
  try {
    // 表单验证
    const isValid = await basicFormRef.value?.validate()
    if (!isValid) {
      return
    }

    saveLoading.value = true

    let result
    if (isEdit.value) {
      // 修改
      result = await updateGzjzJbxx({
        ...basicFormData.value,
        id: currentRecordId.value
      })
    } else {
      // 新增
      result = await addGzjzJbxx(basicFormData.value)
    }

    if (result.code === 200) {
      ElMessage.success(isEdit.value ? '修改成功' : '新增成功')

      // 如果是新增，设置返回的ID
      if (!isEdit.value && result.data?.id) {
        currentRecordId.value = result.data.id
        // 更新表单中的卷宗号等信息
        if (result.data.jzbh) {
          basicFormData.value.jzbh = result.data.jzbh
        }
      }

      gzlbId.value = result.data.lb
      currentRecord.value = result.data

      // 通知父组件刷新列表
      eventBus.emit('sl:list:update', {
        msg: '卷宗已新增或修改，发起更新'
      })
    } else {
      ElMessage.error(result.msg || (isEdit.value ? '修改失败' : '新增失败'))
    }
  } catch (error: any) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败: ' + (error?.message || '请检查表单数据是否填写完整'))
  } finally {
    saveLoading.value = false
  }
}

// 当事人相关操作
const handleAddPerson = () => {
  // 新增当事人逻辑
  console.log('新增当事人')
}

const handleEditPerson = (person: GzjzDsrVO) => {
  // 编辑当事人逻辑
  console.log('编辑当事人:', person)
}

const handleDeletePerson = (id: number) => {
  const index = partyPersonData.value.findIndex(item => item.id === id)
  if (index > -1) {
    partyPersonData.value.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

const handleReadCard = (id: number) => {
  ElMessage.info('读卡功能')
}

// 公证事项相关操作
const handleAddMatter = () => {
  // 新增公证事项逻辑
  console.log('新增公证事项')
}

const handleEditMatter = (matter: GzjzGzsxVO) => {
  // 编辑公证事项逻辑
  console.log('编辑公证事项:', matter)
}

const handleDeleteMatter = (ids: number[]) => {
  // 批量删除公证事项
  ids.forEach(id => {
    const index = notaryMattersData.value.findIndex(item => item.id === id)
    if (index > -1) {
      notaryMattersData.value.splice(index, 1)
    }
  })
  ElMessage.success('删除成功')
}

const initGzy = () => {
  if(!isEdit.value) {
    const gzyArr: any[] = gzy.value
    const findRes = gzyArr.find(i => {
      return i.value === useUserStore().userId
    })
    basicFormData.value.gzybm = (findRes ? useUserStore().userId : '') as string

    basicFormData.value.sqsj = formatDate(new Date(), 'YYYY-MM-DD')
    basicFormData.value.slrq = formatDate(new Date(), 'YYYY-MM-DD')
  }
}

// 监听当前记录ID变化
watch(currentRecordId, (newId) => {
  if (newId) {
    getBasicInfo(newId)
  } else {
    // 清空表单数据，准备新增
    basicFormData.value = {
      jzbh: '',
      sqsj: '',
      slrq: '',
      lb: '',
      gzybm: '',
      zlbm: '',
      xbrbh: '',
      syd: '',
      ywwz: undefined,
      jjd: '1',
      sfmj: '0',
      dplqdh: '',
      yt: '',
      flxz: '',
      rz: '0',
      wbddh: '',
      sfwyz: '0',
      sfljc: '0',
      sfdzqm: '0',
      sfdzgzs: '0'
    }
    partyPersonData.value = []
    notaryMattersData.value = []
  }
}, { immediate: true })

watch(() => basicFormData.value.lb, (val) => {
  if(val === '1' || val === '2') {
    basicFormData.value.syd = '156'
    basicFormData.value.ywwz = '1'
  } else {
    basicFormData.value.syd = ''
    basicFormData.value.ywwz = ''
  }
})

onMounted(() => {
  // 页面初始化逻辑
  initGzy();
})
</script>

<style scoped>
.app-container {
  padding: 0;
}

.search-wrapper {
  margin-bottom: 0;
}

.form-section {
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 2px solid #409eff;
}

.section-title h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions .el-button {
  padding: 8px 15px;
  font-size: 12px;
}

:deep(.party-person-list) {
  margin-bottom: 0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
