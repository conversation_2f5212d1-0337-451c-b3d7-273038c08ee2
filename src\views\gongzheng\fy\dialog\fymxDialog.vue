<template>
  <!-- 翻译明细对话框 -->
  <el-dialog v-model="dialogfymx.visible" top-0 text @close="cancelFymx" width="60%" append-to-body>
    <template #header>
      <div class="text-center" style="font-size: 18px"><strong>{{ dialogfymx.title }}</strong></div>
    </template>
    <!--  明细  -->
    <div>
      <el-card v-loading="loading" class="fymx-card" shadow="never">
        <div class="pd-5">
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>卷宗号:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-text style="padding-top: 2px ;font-size: 16px" line-clamp="2">{{ jbxxVOData.jzbh }}</el-text>
              </div>
            </el-col>

          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>公证事项:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="4">{{ fymxData.gzsx }}</el-text>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>公证书编号:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="2">{{ fymxData.gzsbh }}</el-text>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>文档类型:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <dict-tag :options="wsOptions" :value="fymxData.lx" />
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>文档名称:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-button style="padding-top: 2px;font-size: 16px" type="primary" link @click="handleOpen(fymxData)">
                  {{ fymxData.wbmc }}
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>译文名称:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-button v-if="dialogfymx.type==='translation'" style="padding-top: 2px;font-size: 16px" type="primary" link @click="handleEdit(fymxData)">
                  {{ fymxData.ywmc }}
                </el-button>
                <el-button v-else style="padding-top: 2px;font-size: 16px" type="primary" link @click="handleOpenyw(fymxData)">
                  {{ fymxData.ywmc }}
                </el-button>

              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>公证员:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="2">{{ fymxData.gzyxm }}</el-text>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>译文:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="" style="padding-top: 2px;font-size: 16px">
                <dict-tag :options="gz_yw_wz" :value="jbxxVOData.ywwz" />
                <!--                <el-text line-clamp="2">{{ fymxData.gzsbh }}</el-text>-->
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="fymxData.fyzt==='2'">
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>驳回原因:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="" style="padding-top: 2px;font-size: 16px">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="2">{{ fymxData.remark }}</el-text>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-else >
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>备注:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="" style="padding-top: 2px;font-size: 16px">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="2">{{ fymxData.remark }}</el-text>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" >
            <el-col class="col-border" :span=4 style="text-align:right ;">
              <div class=""><strong>翻译要求:</strong></div>
            </el-col>
            <el-col class="col-border" :span=18>
              <div class="" style="padding-top: 2px;font-size: 16px">
                <el-text style="padding-top: 2px;font-size: 16px" line-clamp="2">{{ yqtxVOData.txFyyq }}</el-text>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

    </div>

    <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable :modal="false" show-close destroy-on-close>
      <div class="h-332px flex flex-col gap-10px">
        <div class="flex flex-nowrap items-center gap-10px">
          <strong>文档模版：</strong>
          <el-select v-model="genState.mbId" default-first-option filterable style="width: 240px;">
            <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
        <div class="flex-1">
          <SubDataMod ref="subRef" :except-pane="['notaryMatters', 'phrases']" />
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button type="primary" @click="comfirmGen" :loading="genState.loading" :disabled="genState.loading">确认</el-button>
          <el-button @click="genClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="flex items-center justify-end gap-10px">
        <el-button type="primary" v-if="dialogfymx.type==='translation'" @click="sqfwBtn">上传范文</el-button>
        <el-button type="primary" v-if="dialogfymx.type==='translation'" @click="tjywBtn">提交译文</el-button>
        <template v-if="dialogfymx.type==='translation'">
          <el-button v-if="fymxData.ywlj" type="primary" @click="() => toReGen('重置译文')">重置译文</el-button>
          <el-button v-else type="primary" @click="() => toGen('生成译文')">生成译文</el-button>
        </template>
        <el-button type="primary" v-if="dialogfymx.type==='proofread'" @click="bhjdBtn">驳回翻译</el-button>
        <el-button type="primary" v-if="dialogfymx.type==='proofread'" @click="tjxdBtn">提交校对</el-button>
        <el-button @click="cancelFymx">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 上传实现 -->
  <DragUpload v-model="uploadState.visible" :title="uploadState.title" :multiple="false" :limit="1" accept=".doc,.docx" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" />

<!--驳回校对弹窗-->
  <el-dialog v-model="bhDialog.visible" top-0 text @close="cancelBh" width="60%" append-to-body>
    <template #header>
      <div class="text-center" style="font-size: 18px"><strong>驳回校对</strong></div>
    </template>
    <div>
      <el-row :gutter="10">
        <el-col class="col-border" :span=4 style="text-align:right ;">
          <div class=""><strong>驳回原因:</strong></div>
        </el-col>
        <el-col class="col-border" :span=18>
          <div class="" style="padding-top: 2px;font-size: 16px">
              <el-input v-model="bhyy" type="textarea" :rows="3"></el-input>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="flex items-center justify-end gap-10px">
        <el-button type="primary" @click="confirmBh">确认驳回</el-button>
        <el-button @click="cancelBh">关闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup name="fymxDialog" lang="ts">

import { inject, onMounted, ref, type Ref } from 'vue';
import { docGenerator, docOpenEdit, docOpenShow } from '@/views/gongzheng/doc/DocEditor';
import { getGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { getGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { getOssConfig } from '@/api/system/ossConfig';
import { updateGzjzWjccxx } from '@/api/gongzheng/dev/gzjzWjccxx';
import DragUpload from '@/components/FileUpload/DragUpload.vue';
import SubDataMod from '@/views/gongzheng/gongzheng/components/sl/wdnd/SubDataMod.vue'
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { UserDocGenParams } from '../../doc/type';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { formatDate } from '@/utils/ruoyi';
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  gz_yw_wz
} = toRefs<any>(proxy?.useDict('gz_yw_wz'));
// 翻译明细对话框
const dialogfymx = ref({
  visible: false,
  title: '翻译明细',
  type: 'view'
});
const bhyy=ref('');
const bhDialog = ref({
  visible: false
});
//文书类型显示
const wsOptions = ref([
  { label: '文书', value: '1' },
  { label: '公证书', value: '3' }
]);

const previewState = reactive({
  pdfVisible: false,
  wordVisible: false,
  imgVisible: false,
  title: '文档预览',
  docUrl: '',
  wordHtml: '',
  zjclImgUrl: ''
});

const pdfState = reactive({
  visible: false,
  url: ''
});

const mediaPlayerRef = ref(null);

const wjccxxId = ref(null);
const wjccxxInfo = ref<any>(null);
const fymxData = ref<Record<string, any>>({});
// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
const jbxxVOData = ref<GzjzJbxxVO | any>({});
const yqtxVOData = ref({ });

const cancelFymx = () => {
  dialogfymx.value.visible = false;

};
const ossPath = ref();

const gzsxList = ref([]);

const subRef = ref(null);

const loading = ref(false)

const getData = async () => {
  try {
    loading.value = true
    const jbxxRes = await getGzjzJbxx(currentRecordId.value);
    if(jbxxRes.code === 200) {
      gzsxList.value = jbxxRes.data?.gzsxVoList || [];
      Object.assign(jbxxVOData.value, jbxxRes.data);
      if (jbxxVOData.value.yqtxVo) {
        yqtxVOData.value = jbxxVOData.value.yqtxVo
      }
      const wjccxxRes = await getGzjzWjccxx(wjccxxId.value);
      Object.assign(fymxData.value, wjccxxRes.data);
      const ossConfig = await getOssConfig(1);
      var bucketName = ossConfig.data.bucketName;
      var endpoint = ossConfig.data.endpoint;
      ossPath.value = endpoint + '/' + bucketName + '/';
    }
  } catch (err: any) {
    console.error('初始化数据异常', err)
  } finally {
    loading.value = false
  }
};

const handleFymx =  (data, type) => {
  dialogfymx.value.visible = true;
  dialogfymx.value.type= type;
  wjccxxId.value = data.id;
  wjccxxInfo.value = data;
  getData();
};

// 原文文件预览
const handleOpen = async (data: any) => {
  if (data.wbmc === '' || data.wbmc === null) {
    ElMessage.error('原文文件查看异常，请联系管理员！');
    return;
  }
  // 获取文件名后缀
  const ext = data.wbmc.substring(data.wbmc.lastIndexOf('.') + 1);
  const obj = JSON.parse(data.wblj || '{}');
  let ywPath = obj.path;
  if (['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
    previewState.zjclImgUrl = ossPath + ywPath;
    previewState.imgVisible = true;
  } else if (['mp4', 'avi', 'mov', 'mp3'].includes(ext)) {
    mediaPlayerRef.value?.show(ossPath + ywPath);
  } else if (['pdf'].includes(ext)) {
    pdfState.url = ossPath + ywPath;
    pdfState.visible = true;
  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
    if (ywPath) {
      docOpenShow(ywPath);
    }
  }
};

// 译文文件预览
const handleOpenyw = async (data: any) => {
  if (data.ywmc === '' || data.ywmc === null) {
    ElMessage.error('译文文件查看异常，请联系管理员！');
    return;
  }
  // 获取文件名后缀
  const ext = data.ywmc.substring(data.ywmc.lastIndexOf('.') + 1);
  const obj = JSON.parse(data.ywlj || '{}');
  let ywPath = obj.path;
  if (['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
    previewState.zjclImgUrl = ossPath + ywPath;
    previewState.imgVisible = true;
  } else if (['mp4', 'avi', 'mov', 'mp3'].includes(ext)) {
    mediaPlayerRef.value?.show(ossPath + ywPath);
  } else if (['pdf'].includes(ext)) {
    pdfState.url = ossPath + ywPath;
    pdfState.visible = true;
  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
    if (ywPath) {
      docOpenShow(ywPath);
    }
  }
};

// 译文文件编辑
const handleEdit = async (data: any) => {
  if (data.ywlj === '' || data.ywlj === null) {
    ElMessage.error('译文文件查看异常，请联系管理员！');
    return;
  }
  const obj = JSON.parse(data.ywlj || '{}');
  if (obj.path) {
    docOpenEdit(obj.path);
  }
};

// 上传对话框相关
const uploadState = reactive({
  title: '文档上传',
  docType: '',
  docId: '',
  visible: false,
  okUploads: [],
  loading: false,
  sxRow: null,
  mbId: '',
})

const genState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  typeData: [],
  mbId: '',
})

const genClose = () => {
  genState.loading = false;
  genState.visible = false;
  genClosed();
}

const genClosed = () => {
  genState.sxRow = null
  genState.mbId = ''
}

const toGen = (title?: string) => {

  const { lx } = wjccxxInfo.value;
  const { gzjzGzsxId, id } = wjccxxInfo.value;
  const gzsx = gzsxList.value.find(i => i.id === gzjzGzsxId);

  const loadService = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })

  let params: any = {
    wdLb: Number(lx),
  }

  // 如果是公证书 需添加ywId
  if(lx == '3') {
    params.ywId = gzsx.gzsxId
  }

  queryMbFiles(params).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        genState.typeData = res.data;
        genState.mbId = res.data[0].id;
        genState.sxRow = gzsx;
        genState.visible = true;
        genState.title = title || `文档生成`;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loadService.close();
  })
}

const toReGen = async (title?: string) => {
  toGen(title)
  // proxy?.$modal.confirm('重置后，译文无法找回，是否继续？').then(() => {
  // });
}

// 确认生成文档
const comfirmGen = () => {
  try {
    if (!genState.mbId) {
      ElMessage.warning('未选择生成指定模版')
      return;
    }

    if(!currentRecordId.value || !jbxxVOData.value?.id) {
      ElMessage.warning('卷宗信息异常，请刷新重试')
      return;
    }

    const { gzjzGzsxId, id } = wjccxxInfo.value;
    const gzsx = gzsxList.value.find(i => i.id === gzjzGzsxId);

    const { lx } = wjccxxInfo.value;
    const { gzsxId } = gzsx

    const subIns = subRef.value;
    const selectedDsrList = (subIns?.getSelectedDsr() || []);

    if(lx == 1 && selectedDsrList.length === 0) {
      ElMessage.warning('至少需要选一个当事人')
      return;
    }

    const dsrIdArr = selectedDsrList.map((dsr: GzjzDsrVO) => dsr.dsrId)
    const dsrIds = dsrIdArr.join(',')
    const dsr = dsrIdArr.map((dsr: GzjzDsrVO) => {
      return {
        dsrId: dsr.dsrId,
        dsrLx: dsr.dsrLx,
        js: dsr.js,
      }
    })

    let extraParams: any = {
      dsrIds,
      gzxsId: gzsxId,
      dsr,
    };

    let params: UserDocGenParams = {
      bizId: (currentRecordId.value || jbxxVOData.value.id) as string,
      mbWdId: genState.mbId,
      extraParams,
    }

    const loadService = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })

    genState.loading = true;

    docGenerator(params, {
      success: async (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        const { gzsxId, id, gzsxMc, gzsBh } = gzsx;
        let fileName: string = `译文_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`;
        let docInfo: GzjzWjccxxForm = {
          lx,
          gzjzGzsxId: id, // gzjzGzsxId
          gzsx: gzsxMc,
          gzsbh: gzsBh,
        }

        docInfo = {
          ...docInfo,
          ywmc: fileName,
          ywlj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            mbWdId: genState.mbId,
            gzsx: gzsxMc,
            gzsxId,
            gzjzGzsxId: id,
            dsrIds,
          }),
        }

        await updateWjccxx({
          id: fymxData.value.id,
          ywmc: docInfo.ywmc,
          ywlj: docInfo.ywlj
        })

        ElMessage.success('操作成功')

        genClose()
      }
    }).catch((err: any) => {
      console.error('译文文档生成失败', err)
      ElMessage.error('处理失败，请重试')
    }).finally(() => {
      genState.loading = false;
      loadService.close()
    })
  } catch (err: any) {
    console.error('执行异常', err)
  }
}

//上传范文
const sqfwBtn =async  () => {
  uploadState.visible = true;
  uploadState.title = "译文范文上传";
};

const genYwBtn = () => {
  const { gzjzGzsxId, id } = wjccxxInfo.value;
  const { gzsxId } = gzsxList.value.find(i => i.id === gzjzGzsxId);
}
//重置翻译
const czfyBtn = async () => {
  await proxy?.$modal.confirm('重置后，译文无法找回，是否继续？');
  fymxData.value.ywlj = '';
  fymxData.value.ywmc = '';
  console.log('重置翻译', fymxData.value)
  // await updateWjccxx(fymxData.value);
};

//提交翻译
const tjywBtn = async  () => {
  await proxy?.$modal.confirm('提交后，译文无法修改，是否继续？');
  if (fymxData.value.ywmc === '' || fymxData.value.ywmc === null) {
    await proxy?.$modal.msgError("请提交翻译译文！")
    return;
  }
  fymxData.value.fyzt = '1';
  dialogfymx.value.type="proofread"
  await updateWjccxx(fymxData.value);


};

//提交校对
const tjxdBtn = async () => {
  await proxy?.$modal.confirm('是否确认提交校对？');
  fymxData.value.fyzt = '3';
  dialogfymx.value.type="view"
  await updateWjccxx(fymxData.value);
};
//驳回翻译
const bhjdBtn = async () => {
  bhDialog.value.visible=true
  bhyy.value=fymxData.value.remark;

};

const updateWjccxx = async (data: any) => {
  await updateGzjzWjccxx(data).finally(() => {
    getData();
  });
};

// 每一个文件上传后的回调 有成功或结束状态
const uploadEveryDone = (res: any) => {
  // console.log('每一个文件上传后的回调', res)
  if (res.status === 'success') {
    // uploadState.okUploads.push(res.result)
  } else if (res.status === 'error') {

  }
}


// 所有文件上传后的回调 res中只有上传成功的文件信息
const uploadAllDone = async (res: any[]) => {
  console.log('所有文件上传后的回调', res);
  const { fileName, ossId, path } = res[0];
  fymxData.value.ywmc = fileName;
  fymxData.value.ywlj = JSON.stringify({
    ossId,
    path,
    fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
    fileName,
    typeCode: 5,
    typeName: '公证书译文',
    dsrId:fymxData.value.dsrId,
    gzsx:fymxData.value.gzsx,
    gzsxId:fymxData.value.gzjzId,
  })

  await updateWjccxx(fymxData.value);

  await proxy?.$modal.confirm('上传成功，是否打开？');
  docOpenEdit(path);
}


const confirmBh = async  () => {
  await proxy?.$modal.confirm('是否确认驳回翻译？');
  fymxData.value.fyzt = '2';
  fymxData.value.remark = bhyy.value;
  dialogfymx.value.type="translation"
  await updateWjccxx(fymxData.value).then(() => {
    cancelBh()
  });

}

const cancelBh = async  () => {
  bhyy.value=''
  bhDialog.value.visible=false
}

// 暴露方法给父组件
defineExpose({
  handleFymx,
  cancelFymx
});

onMounted(() => {
  cancelFymx();
})


</script>

<style scoped>
.fymx-card {
  border: 0;
  padding-bottom: 5px;
  font-size: 16px;
  align-content: center;

}

:deep(.el-card__header) {
  border: 0;
}

:deep(.el-card__body) {
  border: 0;
  padding: 0 5px !important;
}

.pd-5 {
  padding: 5px !important;
}

.col-border {
  //border: 1px solid #dcdcdc;
  padding: 5px;
  margin: 5px 0;
}


</style>
