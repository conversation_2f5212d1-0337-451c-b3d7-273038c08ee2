export interface GzjzZmclxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

  /**
   * 证明名称
   */
  zmmc: string;

  /**
   * 有效期
   */
  yxq: string;

  /**
   * 证明材料来源
   */
  zmclly: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh: number;

  /**
   * 盖章信息
   */
  gzxx: string;

  /**
   * 是否为公证文书(0：否，1：是)
   */
  sfwgzws: number;

}

export interface GzjzZmclxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 证明名称
   */
  zmmc?: string;

  /**
   * 有效期
   */
  yxq?: string;

  /**
   * 证明材料来源
   */
  zmclly?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

  /**
   * 盖章信息
   */
  gzxx?: string;

  /**
   * 是否为公证文书(0：否，1：是)
   */
  sfwgzws?: number;

}

export interface GzjzZmclxxQuery extends PageQuery {

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 证明名称
   */
  zmmc?: string;

  /**
   * 有效期
   */
  yxq?: string;

  /**
   * 证明材料来源
   */
  zmclly?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

  /**
   * 盖章信息
   */
  gzxx?: string;

  /**
   * 是否为公证文书(0：否，1：是)
   */
  sfwgzws?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



