import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzsxJspzVO, GzsxJspzForm, GzsxJspzQuery } from '@/api/basicdata/gzsxJspz/types';

/**
 * 查询角色配置列表
 * @param query
 * @returns {*}
 */

export const listGzsxJspz = (query?: GzsxJspzQuery): AxiosPromise<GzsxJspzVO[]> => {
  return request({
    url: '/basicdata/gzsxJspz/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询角色配置详细
 * @param id
 */
export const getGzsxJspz = (id: string | number): AxiosPromise<GzsxJspzVO> => {
  return request({
    url: '/basicdata/gzsxJspz/' + id,
    method: 'get'
  });
};

/**
 * 新增角色配置
 * @param data
 */
export const addGzsxJspz = (data: GzsxJspzForm) => {
  return request({
    url: '/basicdata/gzsxJspz',
    method: 'post',
    data: data
  });
};

/**
 * 修改角色配置
 * @param data
 */
export const updateGzsxJspz = (data: GzsxJspzForm) => {
  return request({
    url: '/basicdata/gzsxJspz',
    method: 'put',
    data: data
  });
};

/**
 * 删除角色配置
 * @param id
 */
export const delGzsxJspz = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsxJspz/' + id,
    method: 'delete'
  });
};
