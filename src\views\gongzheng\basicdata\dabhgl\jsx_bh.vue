<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:dabhgl:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dabhglList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="号头" align="center" prop="numberStr" />
        <el-table-column label="编号位数" align="center" prop="numberDigits" />
        <el-table-column label="起始编号" align="center" prop="startNumber" />
        <el-table-column label="编号类型" align="center" prop="numberType" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="gz_dzjz_zt" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:dabhgl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:dabhgl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改档案号头对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="dabhglFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="号头" prop="numberStr">
          <el-input v-model="form.numberStr" placeholder="请输入号头" />
        </el-form-item>
        <el-form-item label="编号位数" prop="numberDigits">
          <el-input v-model="form.numberDigits" placeholder="请输入编号位数" />
        </el-form-item>
        <el-form-item label="起始编号" prop="startNumber">
          <el-input v-model="form.startNumber" placeholder="请输入起始编号" />
        </el-form-item>
        <el-form-item label="编号类型" prop="numberType">
          <el-input v-model="form.numberType" placeholder="请输入编号类型" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in gz_dzjz_zt" :key="dict.value"
              :value="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Jsxbh" lang="ts">
  import { listDabhgl, getDabhgl, delDabhgl, addDabhgl, updateDabhgl } from '@/api/gongzheng/basicdata/dabhgl';
  import { DabhglVO, DabhglQuery, DabhglForm } from '@/api/gongzheng/basicdata/dabhgl/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dzjz_zt } = toRefs<any>(proxy?.useDict('gz_dzjz_zt'));
  const dabhglList = ref<DabhglVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const dabhglFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DabhglForm = {
    id: undefined,
    category: 'jsx',
    term: undefined,
    year: undefined,
    numberStr: undefined,
    numberDigits: undefined,
    startNumber: undefined,
    status: undefined,
    remark: undefined,
    numberType: undefined,
    gzCategory: undefined
  }
  const data = reactive<PageData<DabhglForm, DabhglQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      category: 'jsx',
      term: undefined,
      year: undefined,
      numberStr: undefined,
      numberDigits: undefined,
      startNumber: undefined,
      status: undefined,
      numberType: undefined,
      gzCategory: undefined,
      params: {
      }
    },
    rules: {
      numberStr: [
        { required: true, message: "号头不能为空", trigger: "blur" }
      ],
      numberDigits: [
        { required: true, message: "编号位数不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  const getList = async () => {
    loading.value = true;
    const res = await listDabhgl(queryParams.value);
    dabhglList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dabhglFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DabhglVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加介绍信编号";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DabhglVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDabhgl(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改介绍信编号";
  }

  /** 提交按钮 */
  const submitForm = () => {
    dabhglFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        form.value.createTime = null;
        if (form.value.id) {
          await updateDabhgl(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDabhgl(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DabhglVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除介绍信编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDabhgl(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/dabhgl/export', {
      ...queryParams.value
    }, `dabhgl_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
