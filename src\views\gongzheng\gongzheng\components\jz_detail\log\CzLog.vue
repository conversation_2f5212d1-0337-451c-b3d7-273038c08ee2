<template>
  <el-card>
    <template #header>
      <strong class="text-base">操作日志</strong>
    </template>
    <el-table :data="operationLogList" v-loading="loading" border stripe>
      <el-table-column type="index" label="#" width="50" align="center" />
      <el-table-column prop="czhj" label="环节" align="center" width="120" />
      <el-table-column prop="rznr" label="日志内容" align="center" min-width="300" />
      <el-table-column prop="czrxm" label="操作人" align="center" width="120" />
      <el-table-column prop="czsj" label="操作时间" align="center" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.czsj) }}
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { listGzrzCzrzxx } from '@/api/gongzheng/gongzheng/gzrzCzrzxx';
import { GzrzCzrzxxQuery, GzrzCzrzxxVO } from '@/api/gongzheng/gongzheng/gzrzCzrzxx/types';
import { ref, onMounted } from 'vue';

interface Props {
  gzjzId?: string
}

const props = defineProps<Props>()

// 获取当前查看的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

// 操作日志列表数据 - 使用真实API数据
const operationLogList = ref<GzrzCzrzxxVO[]>([])
const loading = ref(false)

// 获取操作日志列表
const getOperationLogList = async () => {
  if (!props.gzjzId && !currentRecordId.value) return

  loading.value = true
  try {
    const query: GzrzCzrzxxQuery = {
      gzjzId: props.gzjzId || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzrzCzrzxx(query)
    if (res.code === 200) {
      operationLogList.value = res.rows || []
    } else {
      ElMessage.error('获取操作日志失败')
      operationLogList.value = []
    }
  } catch (error: any) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
    operationLogList.value = []
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateStr
  }
}



onMounted(() => {
  getOperationLogList();
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>