<template>
  <el-dialog v-model="visible" :title="title" @closed="closed" show-close destroy-on-close>
    <div class="flex flex-col gap-10px">
      <p class="p-0 m-0 text-base font-bold">在核实环节，办证系统应当包括下列核实方式：</p>
      <CheckList ref="listOneRef" :data="ScOneCheckOptions" default-all />
      <p class="bg-gray-200 p-10px m-0 rounded text-base">相应的核实方式应当有对应核实后证明材料的上传功能。</p>
      <p class="p-0 m-0 text-base font-bold">在审查环节，办证系统应当包括对下列内容的确认：</p>
      <CheckList ref="listTwoRef" :data="ScTwoCheckOptions" default-all />
      <p class="bg-gray-200 p-10px m-0 rounded text-base">具体公证事项审查环节应当确认的内容参见《主要公证业务审查要点与证明材料清单》。</p>
    </div>
    <template #footer>
      <div class="flex items-center justify-end gap-10px">
        <el-button @click="confirm" type="primary">审查确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { initiateApproval, initiateProduction } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, computed } from 'vue';
import CheckList from '@/views/gongzheng/components/CheckList.vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '核实审查'
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['update:modelValue', 'comfirm', 'pass', 'close', 'closed'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const ScOneCheckOptions = ref([
  {
    label: '（一）询问当事人、公证事项的利害关系人和证人；',
    value: '1'
  },
  {
    label: '（二）向有关单位或者个人了解相关情况或者核实、收集相关书证、物证、视听资料等证明材料；',
    value: '2'
  },
  {
    label: '（三）现场勘验；',
    value: '3'
  },
  {
    label: '（四）委托专业机构或者专业人员鉴定、检验、翻译；',
    value: '4'
  },
  {
    label: '（五）申请公证的事项真实、合法。',
    value: '5'
  }
])

const ScTwoCheckOptions = ref([
  {
    label: '（一）当事人的人数、身份、申请办理公证的资格及相应的权利情况；',
    value: '1'
  },
  {
    label: '（二）当事人的意思表示真实；',
    value: '2'
  },
  {
    label: '（三）申请公证的文书内容完备，含义清晰，签名、印鉴齐全；',
    value: '3'
  },
  {
    label: '（四）提供的证明材料真实、合法、充分；',
    value: '4'
  },
  {
    label: '（五）申请公证的事项真实、合法。',
    value: '5'
  }
])

const listOneRef = ref<InstanceType<typeof CheckList> | null>(null);
const listTwoRef = ref<InstanceType<typeof CheckList> | null>(null);

// 提交审查
async function confirmSc() {
  const load = ElLoading.service({
    lock: true,
    text: '正在发起审查，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {

    load.close()
  } catch(err: any) {
    console.error('发起审查提交失败', err)
    ElMessage.error('发起审查提交失败');
    load.close()
  } finally {
  }
}

function confirm() {
  const listOne = listOneRef.value;
  const listTwo = listTwoRef.value;
  if (listOne && listTwo) {
    if (listOne.isAllChecked() && listTwo.isAllChecked()) {
      emit('comfirm')
    } else {
      ElMessage.error('请确保各事项完备并勾选再提交确认');
      return;
    }
  }
}

function close() {
  emit('close')
  emit('update:modelValue', false)
}

function closed() {
  emit('closed')
}

</script>
