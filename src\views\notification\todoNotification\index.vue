<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="140px">
            <el-form-item label="消息提醒内容" prop="todoContent">
              <el-input v-model="queryParams.todoContent" placeholder="请输入消息提醒内容" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-table border v-loading="loading" :data="todoNotificationList" @selection-change="handleSelectionChange" @row-click="handleRowClick" style="cursor: pointer;">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="消息提醒内容" align="center" prop="todoContent" />
      <el-table-column label="是否已读" align="center" prop="isRead" width="90">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.isRead==1">已读</el-tag>
          <el-tag type="warning" v-if="scope.row.isRead==0">未读</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="读取时间" align="center" prop="readTime" width="180">
        <template #default="scope">
          <span v-if="scope.row.readTime">{{ parseTime(scope.row.readTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
     <!-- <el-table-column label="截止时间" align="center" prop="dueDate" width="180">
        <template #default="scope">
          <span v-if="scope.row.dueDate">{{ parseTime(scope.row.dueDate, '{y}-{m}-{d}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 消息详情对话框 -->
    <el-dialog v-model="detailDialog.visible" :title="detailDialog.title" width="600px" append-to-body>
      <div class="message-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="消息内容">
            <div class="message-content" style="white-space: pre-wrap; word-break: break-all;">{{ detailForm.todoContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="接收人">
            <span>{{ detailForm.receiverName }}</span>
          </el-descriptions-item>
         <!-- <el-descriptions-item label="截止时间" v-if="detailForm.dueDate">
            <span>{{ parseTime(detailForm.dueDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </el-descriptions-item> -->
          <el-descriptions-item label="阅读状态">
            <el-tag type="success" v-if="detailForm.isRead==1">已读</el-tag>
            <el-tag type="warning" v-if="detailForm.isRead==0">未读</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="阅读时间" v-if="detailForm.readTime">
            <span>{{ parseTime(detailForm.readTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" v-if="detailForm.remark">
            <span>{{ detailForm.remark }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TodoNotification" lang="ts">
  import { listTodoNotification, getTodoNotification, delTodoNotification, addTodoNotification, updateTodoNotification } from '@/api/notification/todoNotification';
  import { TodoNotificationVO, TodoNotificationQuery, TodoNotificationForm } from '@/api/notification/todoNotification/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const todoNotificationList = ref<TodoNotificationVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const todoNotificationFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const detailDialog = reactive<DialogOption>({
    visible: false,
    title: '消息详情'
  });

  const detailForm = ref<TodoNotificationVO>({
    id: '',
    todoTitle: '',
    todoContent: '',
    receiverId: '',
    receiverName: '',
    isRead: 0,
    readTime: '',
    taskId: '',
    dueDate: '',
    extData: '',
    remark: ''
  });

  const initFormData : TodoNotificationForm = {
    id: undefined,
    todoTitle: undefined,
    todoContent: undefined,
    receiverId: undefined,
    receiverName: undefined,
    isRead: undefined,
    readTime: undefined,
    taskId: undefined,
    dueDate: undefined,
    extData: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<TodoNotificationForm, TodoNotificationQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      todoTitle: undefined,
      todoContent: undefined,
      receiverId: undefined,
      receiverName: undefined,
      isRead: undefined,
      readTime: undefined,
      taskId: undefined,
      dueDate: undefined,
      extData: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      todoTitle: [
        { required: true, message: "待办标题不能为空", trigger: "blur" }
      ],
      receiverId: [
        { required: true, message: "接收人用户ID不能为空", trigger: "blur" }
      ],
      receiverName: [
        { required: true, message: "接收人姓名不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询待办事项通知主（站内信）列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listTodoNotification(queryParams.value);
    todoNotificationList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    todoNotificationFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : TodoNotificationVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加待办事项通知主（站内信）";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: TodoNotificationVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getTodoNotification(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改待办事项通知主（站内信）";
  }

  /** 提交按钮 */
  const submitForm = () => {
    todoNotificationFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateTodoNotification(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addTodoNotification(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: TodoNotificationVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除待办事项通知主（站内信）编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delTodoNotification(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('notification/todoNotification/export', {
      ...queryParams.value
    }, `todoNotification_${new Date().getTime()}.xlsx`)
  }

  /** 行点击事件 */
  const handleRowClick = async (row: TodoNotificationVO) => {
    try {
      // 获取详细信息
      const res = await getTodoNotification(row.id);
      detailForm.value = res.data;
      detailDialog.visible = true;

      // 如果未读，则更新为已读状态
      if (row.isRead === 0) {
        const currentTime = new Date();
        const formatTime = currentTime.getFullYear() + '-' +
                          String(currentTime.getMonth() + 1).padStart(2, '0') + '-' +
                          String(currentTime.getDate()).padStart(2, '0') + ' ' +
                          String(currentTime.getHours()).padStart(2, '0') + ':' +
                          String(currentTime.getMinutes()).padStart(2, '0') + ':' +
                          String(currentTime.getSeconds()).padStart(2, '0');

        const updateData: TodoNotificationForm = {
          ...row,
          isRead: 1,
          readTime: formatTime
        };

        await updateTodoNotification(updateData);

        // 更新本地数据
        const index = todoNotificationList.value.findIndex(item => item.id === row.id);
        if (index !== -1) {
          todoNotificationList.value[index].isRead = 1;
          todoNotificationList.value[index].readTime = formatTime;
        }

        // 更新详情对话框中的数据
        detailForm.value.isRead = 1;
        detailForm.value.readTime = formatTime;

        // proxy?.$modal.msgSuccess("消息已标记为已读");
      }
    } catch (error) {
      console.error('处理行点击事件失败:', error);
      proxy?.$modal.msgError("操作失败");
    }
  }

  onMounted(() => {
    getList();
  });
</script>
