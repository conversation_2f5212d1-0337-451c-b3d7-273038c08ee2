<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="费用类型" prop="fylx">
              <el-select v-model="queryParams.fylx" placeholder="请选择费用类型" clearable >
                <el-option v-for="dict in gz_sf_lb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="是否记账" prop="sfjz">
              <el-select v-model="queryParams.sfjz" placeholder="请选择是否记账" clearable >
                <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="计价方式" prop="jjfs">
              <el-select v-model="queryParams.jjfs" placeholder="请选择计价方式" clearable >
                <el-option v-for="dict in gz_jjfs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="收费场景" prop="sfcj">
              <el-select v-model="queryParams.sfcj" placeholder="请选择收费场景" clearable >
                <el-option v-for="dict in gz_sfcj" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="应收金额" prop="fyys">
              <el-input v-model="queryParams.fyys" placeholder="请输入应收金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="减免金额" prop="fyjm">
              <el-input v-model="queryParams.fyjm" placeholder="请输入减免金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="减免申请ID" prop="fyjmId">
              <el-input v-model="queryParams.fyjmId" placeholder="请输入减免申请ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实收" prop="fyss">
              <el-input v-model="queryParams.fyss" placeholder="请输入实收" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退费金额" prop="fytf">
              <el-input v-model="queryParams.fytf" placeholder="请输入退费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退费小计" prop="fytfXj">
              <el-input v-model="queryParams.fytfXj" placeholder="请输入退费小计" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退费审批ID" prop="fytfId">
              <el-input v-model="queryParams.fytfId" placeholder="请输入退费审批ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费状态" prop="sfzt">
              <el-select v-model="queryParams.sfzt" placeholder="请选择收费状态" clearable >
                <el-option v-for="dict in gz_sfzt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费及发票ID" prop="sfjfpId">
              <el-input v-model="queryParams.sfjfpId" placeholder="请输入收费及发票ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收费来源" prop="sfly">
              <el-select v-model="queryParams.sfly" placeholder="请选择收费来源" clearable >
                <el-option v-for="dict in gz_sf_fs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzGzsxSfxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzGzsxSfxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzGzsxSfxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzGzsxSfxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzGzsxSfxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="费用类型" align="center" prop="fylx">
          <template #default="scope">
            <dict-tag :options="gz_sf_lb" :value="scope.row.fylx"/>
          </template>
        </el-table-column>
        <el-table-column label="是否记账" align="center" prop="sfjz">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfjz"/>
          </template>
        </el-table-column>
        <el-table-column label="计价方式" align="center" prop="jjfs">
          <template #default="scope">
            <dict-tag :options="gz_jjfs" :value="scope.row.jjfs"/>
          </template>
        </el-table-column>
        <el-table-column label="收费场景" align="center" prop="sfcj">
          <template #default="scope">
            <dict-tag :options="gz_sfcj" :value="scope.row.sfcj"/>
          </template>
        </el-table-column>
        <el-table-column label="应收金额" align="center" prop="fyys" />
        <el-table-column label="减免金额" align="center" prop="fyjm" />
        <el-table-column label="减免申请ID" align="center" prop="fyjmId" />
        <el-table-column label="实收" align="center" prop="fyss" />
        <el-table-column label="退费金额" align="center" prop="fytf" />
        <el-table-column label="退费小计" align="center" prop="fytfXj" />
        <el-table-column label="退费审批ID" align="center" prop="fytfId" />
        <el-table-column label="收费状态" align="center" prop="sfzt">
          <template #default="scope">
            <dict-tag :options="gz_sfzt" :value="scope.row.sfzt"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="收费及发票ID" align="center" prop="sfjfpId" />
        <el-table-column label="收费来源" align="center" prop="sfly">
          <template #default="scope">
            <dict-tag :options="gz_sf_fs" :value="scope.row.sfly"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzGzsxSfxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzGzsxSfxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证事项-收费明细v1.1对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzGzsxSfxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" />
        </el-form-item>
        <el-form-item label="费用类型" prop="fylx">
          <el-select v-model="form.fylx" placeholder="请选择费用类型">
            <el-option
                v-for="dict in gz_sf_lb"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否记账" prop="sfjz">
          <el-radio-group v-model="form.sfjz">
            <el-radio
              v-for="dict in gz_yes_or_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="计价方式" prop="jjfs">
          <el-select v-model="form.jjfs" placeholder="请选择计价方式">
            <el-option
                v-for="dict in gz_jjfs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收费场景" prop="sfcj">
          <el-select v-model="form.sfcj" placeholder="请选择收费场景">
            <el-option
                v-for="dict in gz_sfcj"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="应收金额" prop="fyys">
          <el-input v-model="form.fyys" placeholder="请输入应收金额" />
        </el-form-item>
        <el-form-item label="减免金额" prop="fyjm">
          <el-input v-model="form.fyjm" placeholder="请输入减免金额" />
        </el-form-item>
        <el-form-item label="减免申请ID" prop="fyjmId">
          <el-input v-model="form.fyjmId" placeholder="请输入减免申请ID" />
        </el-form-item>
        <el-form-item label="实收" prop="fyss">
          <el-input v-model="form.fyss" placeholder="请输入实收" />
        </el-form-item>
        <el-form-item label="退费金额" prop="fytf">
          <el-input v-model="form.fytf" placeholder="请输入退费金额" />
        </el-form-item>
        <el-form-item label="退费小计" prop="fytfXj">
          <el-input v-model="form.fytfXj" placeholder="请输入退费小计" />
        </el-form-item>
        <el-form-item label="退费审批ID" prop="fytfId">
          <el-input v-model="form.fytfId" placeholder="请输入退费审批ID" />
        </el-form-item>
        <el-form-item label="收费状态" prop="sfzt">
          <el-select v-model="form.sfzt" placeholder="请选择收费状态">
            <el-option
                v-for="dict in gz_sfzt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="收费及发票ID" prop="sfjfpId">
          <el-input v-model="form.sfjfpId" placeholder="请输入收费及发票ID" />
        </el-form-item>
        <el-form-item label="收费来源" prop="sfly">
          <el-select v-model="form.sfly" placeholder="请选择收费来源">
            <el-option
                v-for="dict in gz_sf_fs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzGzsxSfxx" lang="ts">
import { listGzjzGzsxSfxx, getGzjzGzsxSfxx, delGzjzGzsxSfxx, addGzjzGzsxSfxx, updateGzjzGzsxSfxx } from '@/api/gongzheng/dev/gzjzGzsxSfxx';
import { GzjzGzsxSfxxVO, GzjzGzsxSfxxQuery, GzjzGzsxSfxxForm } from '@/api/gongzheng/dev/gzjzGzsxSfxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_sf_lb, gz_yes_or_no, gz_jjfs, gz_sfcj, gz_sfzt, gz_sf_fs } = toRefs<any>(proxy?.useDict('gz_sf_lb', 'gz_yes_or_no', 'gz_jjfs', 'gz_sfcj', 'gz_sfzt', 'gz_sf_fs'));

const gzjzGzsxSfxxList = ref<GzjzGzsxSfxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzGzsxSfxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzGzsxSfxxForm = {
  id: undefined,
  gzjzGzsxId: undefined,
  fylx: undefined,
  sfjz: undefined,
  jjfs: undefined,
  sfcj: undefined,
  fyys: undefined,
  fyjm: undefined,
  fyjmId: undefined,
  fyss: undefined,
  fytf: undefined,
  fytfXj: undefined,
  fytfId: undefined,
  sfzt: undefined,
  remark: undefined,
  gzjzId: undefined,
  sfjfpId: undefined,
  sfly: undefined
}
const data = reactive<PageData<GzjzGzsxSfxxForm, GzjzGzsxSfxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzGzsxId: undefined,
    fylx: undefined,
    sfjz: undefined,
    jjfs: undefined,
    sfcj: undefined,
    fyys: undefined,
    fyjm: undefined,
    fyjmId: undefined,
    fyss: undefined,
    fytf: undefined,
    fytfXj: undefined,
    fytfId: undefined,
    sfzt: undefined,
    gzjzId: undefined,
    sfjfpId: undefined,
    sfly: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证事项-收费明细v1.1列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzGzsxSfxx(queryParams.value);
  gzjzGzsxSfxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzGzsxSfxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzGzsxSfxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证事项-收费明细v1.1";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzGzsxSfxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzGzsxSfxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证事项-收费明细v1.1";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzGzsxSfxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzGzsxSfxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzGzsxSfxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzGzsxSfxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证事项-收费明细v1.1编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzGzsxSfxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzGzsxSfxx/export', {
    ...queryParams.value
  }, `gzjzGzsxSfxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
