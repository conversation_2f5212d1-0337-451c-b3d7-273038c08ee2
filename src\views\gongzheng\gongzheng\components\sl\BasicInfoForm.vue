<template>
  <div class="basic-info-form">
    <slot name="header" />
    <el-form :model="formData" ref="formRef" label-width="120px" :rules="isEdit ? rules : newRule" inline style="margin-top: 10px;">
      <el-form-item label="卷宗号：" prop="jzbh" >
        <el-input v-model="formData.jzbh" disabled placeholder="系统自动生成" style="width: 160px;" />
      </el-form-item>
      <el-form-item label="申请日期：" prop="sqsj">
        <el-date-picker
          v-model="formData.sqsj"
          type="date"
          placeholder="选择申请日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 160px;"
        />
      </el-form-item>
      <el-form-item label="受理日期：" prop="slrq">
        <el-date-picker
          v-model="formData.slrq"
          type="date"
          placeholder="选择受理日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 160px;"
        />
      </el-form-item>
      <el-form-item label="公证类别：" prop="lb">
        <el-select v-model="formData.lb" placeholder="请选择公证类别" style="width: 160px;">
          <template v-for="item in gz_gzlb" :key="item.value">
            <el-option v-if="item.value !== '-1'" :label="item.label" :value="item.value" />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="公证员：" prop="gzybm">
        <SelectUserByRole ref="selectGzyRef" :roleKey="'gzy'" v-model="formData.gzybm" :width="'160px'"></SelectUserByRole>
      </el-form-item>
      <el-form-item label="助理：" prop="zlbm">
        <SelectUserByRole ref="selectGzyzlRef" :roleKey="'gzyzl'" v-model="formData.zlbm" :width="'160px'"></SelectUserByRole>
      </el-form-item>
      <el-form-item label="协办人：" prop="xbrbhArray" >
        <SelectUserByRole ref="selectGzyXbrRef" :roleKey="'gzy'" v-model="formData.xbrbhArray" :multiple="true" :width="'240px'"></SelectUserByRole>
      </el-form-item>
      <el-form-item label="使用地：" prop="syd" v-if="!!formData.lb">
        <el-select v-model="formData.syd" placeholder="请选择使用地" @change="sydChange" style="width: 160px;" filterable>
          <el-option v-for="item in areas" :key="item.numberCode" :label="item.areaName" :value="item.numberCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="译文文种：" prop="ywwz" v-if="!!formData.lb">
        <el-select v-model="formData.ywwz" placeholder="请选择译文文种" style="width: 160px;">
          <el-option v-for="item in gz_yw_wz" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急度：" prop="jjd">
        <el-select v-model="formData.jjd" placeholder="请选择紧急度" style="width: 80px;">
          <el-option v-for="item in urgencyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否密卷：" prop="sfmj">
        <el-select v-model="formData.sfmj" placeholder="请选择是否密卷" style="width: 70px;">
          <el-option v-for="item in gz_sfmj" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="电票领取电话：" prop="dplqdh">
        <el-input v-model="formData.dplqdh" placeholder="请输入电票领取电话" style="width: 160px;" />
      </el-form-item>

      <el-form-item label="用途：" prop="yt">
        <el-select v-model="formData.yt" placeholder="请选择用途" filterable style="width: 160px;">
          <el-option v-for="item in gz_yt" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="法律援助：" prop="flxz">
        <el-select v-model="formData.flxz" placeholder="请选择法律援助" style="width: 160px;">
          <el-option v-for="item in gz_flyz" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="外部订单号：" prop="wbddh">
        <el-input v-model="formData.wbddh" placeholder="请输入外部订单号" style="width: 160px;" />
      </el-form-item>
      <el-form-item label="领事认证：" prop="rz" v-if="formData.lb === '3'">
        <el-select v-model="formData.rz" placeholder="请选择是否认证" style="width: 70px;">
          <el-option v-for="item in gz_rz_zt" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-row :gutter="10">
        <!-- 第五行 - 复选框 -->
        <el-col :span="24">
          <el-form-item label="其他选项：">
            <div class="checkbox-group">
              <el-checkbox v-model="formData.sfwyz" true-value="1" false-value="0">是否外译中</el-checkbox>
              <el-checkbox v-model="formData.sfljc" true-value="1" false-value="0">零接触</el-checkbox>
              <el-checkbox v-model="formData.sfdzqm" true-value="1" false-value="0">电子签名</el-checkbox>
              <el-checkbox v-model="formData.sfdzgzs" true-value="1" false-value="0">电子公证书</el-checkbox>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
   import SelectUserByRole from '@/views/gongzheng/components/SelectUserByRole.vue'
import { ref, watch, computed, onBeforeMount, onMounted } from 'vue'
import { ElForm } from 'element-plus'
import type { GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import { listAreaname } from '@/api/gongzheng/basicdata/areaname';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_gzlb', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt'));
const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

interface Props {
  modelValue: GzjzJbxxForm
  isEdit?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: GzjzJbxxForm): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()

const areas = ref([]);

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const getAreaName = async () => {
  try {
    const params = {
      pageSize: 300,
      pageNum: 1
    }
    const res = await listAreaname(params);
    if (res.code === 200) {
      areas.value = res.rows;
    }
  } catch (err: any) {
    console.error('获取地区名称失败:', err);
  }
}

const sydChange = (val: any) => {
  console.log('sydChange', val)
  const areaInfo = areas.value.find(area => area.numberCode === val);
  if (areaInfo) {
    formData.value.ywwz = areaInfo.translation || ''
  }
}

onBeforeMount(() => {
  getAreaName();
})

onMounted(() => {
  console.log('BasicInfoForm mounted', formData.value);
})

// 选项数据
const notaryOfficerOptions = ref([
  { label: '测试公证员', value: '1935515056178139138' },
  { label: '张三', value: '2' },
  { label: '李四', value: '3' }
])

const assistantOptions = ref([
  { label: '助理1', value: '1' },
  { label: '助理2', value: '2' },
  { label: '助理3', value: '3' }
])

// 紧急度选项
const urgencyOptions = ref([
  { label: '普通', value: '1' },
  { label: '急', value: '2' },
  { label: '特急', value: '3' }
])

// 表单验证规则
const rules = {
  sqsj: [
    { required: true, message: '请选择申请日期', trigger: 'change' }
  ],
  slrq: [
    { required: true, message: '请选择受理日期', trigger: 'change' }
  ],
  lb: [
    { required: true, message: '请选择公证类别', trigger: 'change' }
  ],
  gzybm: [
    { required: true, message: '请选择公证员', trigger: 'change' }
  ],
  syd: [
    { required: true, message: '请选择使用地', trigger: 'blur' }
  ],
  ywwz: [
    { required: true, message: '请选择译文文种', trigger: 'change' }
  ]
}

const newRule = {
  sqsj: [
    { required: true, message: '请选择申请日期', trigger: 'change' }
  ],
  slrq: [
    { required: true, message: '请选择受理日期', trigger: 'change' }
  ],
  lb: [
    { required: true, message: '请选择公证类别', trigger: 'change' }
  ],
  gzybm: [
    { required: true, message: '请选择公证员', trigger: 'change' }
  ],
  syd: [
    { required: true, message: '请选择使用地', trigger: 'change' }
  ],
  ywwz: [
    { required: true, message: '请选择译文文种', trigger: 'change' }
  ],
  dplqdh: [
    // { required: true, message: '请输入电票领取电话', trigger: 'blur' }
  ],
  zlbm: [
    // { required: true, message: '请选择助理', trigger: 'change' }
  ],
  xbrbhArray: [
    // { required: true, message: '请选择协办人', trigger: 'change' }
  ],
  yt: [
    // { required: true, message: '请选择用途', trigger: 'change' }
  ],
  flxz: [
    // { required: true, message: '请选择法律援助', trigger: 'change' }
  ]
}

// 验证表单
const validate = async () => {
  return await formRef.value?.validate()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.basic-info-form {
  margin-bottom: 0;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.checkbox-group .el-checkbox {
  margin-right: 0;
}

.form-item-size {
  min-width: 80px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input),
:deep(.el-select) {
  width: 100%;
}
</style>
