<template>
  <div class="pageoffice-demo">
    <el-card style="margin-bottom: 30px;">
      <template #header>
        <span>文档打开 DEMO</span>
      </template>
      <el-form :model="processForm" class="process-form" @submit.prevent>
        <el-form-item label="必填：文档路径">
          <el-input v-model="processForm.path" placeholder="文档路径" />
        </el-form-item>
        <el-form-item label="动作">
          <el-input v-model="processForm.action" placeholder="请输入动作" />
          <span style="font-weight: bold;color:red;">说明： 默认编辑  edit  可选 view</span>
        </el-form-item>
      </el-form>
      <el-button type="success" @click="handleProcess" :loading="processLoading">调用统一文档打开接口</el-button>
      <el-alert v-if="processResultMsg" :title="processResultMsg" type="info" show-icon style="margin: 10px 0;" />
      <el-descriptions v-if="processResultObj" :column="1" border>
        <el-descriptions-item v-for="(val, key) in processResultObj" :key="key"
          :label="key">{{ val }}</el-descriptions-item>
      </el-descriptions>
    </el-card>


  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { POBrowser } from "js-pageoffice";
  import {
    processDocument2
  } from '@/api/gongzheng/dev/pageoffice';
  import type { UnifiedDocumentVO } from '@/api/gongzheng/dev/pageoffice/types';


  // 统一文档处理 DEMO 相关
  const processForm = reactive({
    path: '2025/07/30/19d390667d6c487b8580155ee0994ab5.docx',
    action: 'view'
  });
  const processLoading = ref(false);
  const processResultMsg = ref('');
  const processResultObj = ref<Record<string, any> | null>(null);
  import { getToken } from '@/utils/auth';
  async function handleProcess() {
    processLoading.value = true;
    processResultMsg.value = '';
    processResultObj.value = null;
    // 组装参数
    const params : Record<string, any> = {
      path: processForm.path,
      action: processForm.action
    };
    const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;
    try {
      POBrowser.setProxyBaseAPI(baseUrl)
      POBrowser.setHeader("Authorization", "Bearer " + getToken());
      POBrowser.setStorage("Admin-Token", getToken());
      POBrowser.openWindow("/backoffice/word3", 'width=1300px;height=900px;', JSON.stringify(params));

      // }
    } catch (e : any) {
      processResultMsg.value = e?.message || '接口调用失败';
    } finally {
      processLoading.value = false;
    }
  }
</script>

<style scoped>
  .pageoffice-demo {
    max-width: 800px;
    margin: 40px auto;
  }

  .toolbar {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .result-area {
    margin-top: 20px;
  }

  .process-form {
    margin-bottom: 10px;
  }
</style>
