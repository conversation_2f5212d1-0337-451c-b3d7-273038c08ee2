<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px" >
            <el-form-item label="卷宗号" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入" clearable @keyup.enter="handleQuery"  style="width: 160px;"/>
            </el-form-item>
            <el-form-item label="当事人" prop="dsrxm">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入" clearable @keyup.enter="handleQuery"  style="width: 160px;"/>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb">
              <el-select v-model="queryParams.lb" placeholder="请选择" clearable  style="width: 140px;">
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh" label-width="100px">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable style="max-width: 80px; margin-right: 4px;" >
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable style="max-width: 140px; margin-right: 4px;" >
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery" style="max-width: 60px" />号
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable style="width: 160px;">
                <el-option
                  v-for="item in gzy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="助理" prop="zlbm">
              <el-select v-model="queryParams.zlbm" placeholder="请选择" clearable style="width: 160px;">
                <el-option
                  v-for="item in gzyzl"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="协办人" prop="xbrbh">
              <el-select v-model="queryParams.xbrbh" placeholder="请选择" clearable>
                <el-option
                  v-for="item in gzy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="受理日期" prop="slrq" style="width: 345px">
              <el-date-picker
                clearable
                v-model="dateRangeSlrq"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="出证日期" prop="czsj" style="width: 345px">
              <el-date-picker
                clearable
                v-model="dateRangeCzsj"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <el-form-item label="结案方式" prop="jyfs" label-width="80px">
              <el-select v-model="queryParams.jyfs" placeholder="请选择"  style="width: 160px;" clearable>
                <el-option v-for="dict in gz_gzjz_jafs" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="收费状态" prop="sfsf" label-width="80px">
              <el-select v-model="queryParams.sfsf" placeholder="请选择" style="width: 150px;">
                <el-option v-for="dict in gz_sfzt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="收费日期" prop="sfrq" style="width: 345px" v-if="queryParams.sfsf === '2'">
              <el-date-picker
                clearable
                v-model="dateRangeSfrq"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <!-- <el-form-item label="开票日期" prop="fprq" style="width: 345px" v-if="queryParams.sfsf === '2'">
              <el-date-picker
                clearable
                v-model="dateRangeFprq"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item> -->

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-has-permi="['cwgl:sf:query']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzJbxx:add']">新增</el-button>-->
<!--          </el-col>-->
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="" :disabled="multiple" @click="submitProgress()" v-hasPermi="['temp:gzjzJbxx:edit']">批量收费</el-button>
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="" :disabled="multiple" @click="submitProgress()" v-hasPermi="['temp:gzjzJbxx:edit']">批量完成收费</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzJbxx:export']">导出收费清单</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table border v-loading="loading" :data="gzjzCaiWuList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column fixed="left" label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template #default="{row}">
            <el-button type="primary" link v-if="row.sfsf === '1'" @click="handlePayment(row)"  v-has-permi="['cwgl:sf:edit']">收费</el-button>
            <el-button type="primary" link v-if="row.sfsf === '2'" @click="handlePaymentRecode(row)"  v-has-permi="['cwgl:sf:query']">收费记录</el-button>
            <el-button type="primary" link @click="handleDetail(row)" v-has-permi="['cwgl:sf:query']">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="ajtx" label="案件提醒" width="120" align="center" show-overflow-tooltip>
          <template #default="{row}">
            <el-text type="danger">{{ row.ajtx ? `(${row.ajtx})` : '' }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="卷宗号" align="center" prop="jzbh" min-width="120" />
        <el-table-column label="当事人" align="center" prop="sqrxm" show-overflow-tooltip width="160" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" show-overflow-tooltip width="160" />
        <el-table-column label="公证事项" align="center" prop="gzsx" show-overflow-tooltip width="160" />

        <el-table-column label="应收金额" align="center" prop="sfje" show-overflow-tooltip width="100" />
        <el-table-column label="已收金额" align="center" prop="ysje" show-overflow-tooltip width="100" />
        <el-table-column label="退费金额" align="center" prop="tfje" show-overflow-tooltip width="100" />
        <el-table-column label="减免金额" align="center" prop="jmje" show-overflow-tooltip width="100" />

        <el-table-column label="公证员" align="center" prop="gzyxm" show-overflow-tooltip />
        <el-table-column label="助理" align="center" prop="zlxm" show-overflow-tooltip />
        <el-table-column label="协办人" align="center" prop="xbrxm" show-overflow-tooltip />
        <el-table-column prop="slrq" label="受理日期" width="120" :formatter="formatDate" show-overflow-tooltip />
        <el-table-column label="收费状态" align="center" prop="sfsf">
          <template #default="{row}">
            <dict-tag :options="gz_sfzt" :value="row.sfsf" :showValue="false"/>
          </template>
        </el-table-column>
        <el-table-column label="案件类型" align="center" prop="ywlx">
          <template #default="{row}">
            <dict-tag :options="gz_ywlx" :value="row.ywlx"/>
          </template>
        </el-table-column>
        <el-table-column label="收费时间" align="center" prop="sfrq" width="120" :formatter="formatDate" show-overflow-tooltip/>
        <el-table-column label="收费方式" align="center" prop="zffs">
          <template #default="{row}">
            <dict-tag :options="gz_zffs" :value="row.zffs"/>
          </template>
        </el-table-column>
        <el-table-column label="收费员" align="center" prop="sfr" show-overflow-tooltip />
        <el-table-column label="翻译员" align="center" prop="fyrxm" show-overflow-tooltip />
        <el-table-column label="流程状态" align="center" prop="lczt">
          <template #default="{row}">
            <dict-tag :options="gz_sl_lczt" :value="row.lczt"/>
          </template>
        </el-table-column>
        <el-table-column label="收费来源" align="center" prop="sfly">
          <template #default="{row}">
            <dict-tag :options="gz_sfly" :value="row.sfly"/>
          </template>
        </el-table-column>
        <!-- <el-table-column label="发票号码" align="center" prop="fphm" show-overflow-tooltip />
        <el-table-column label="开票时间" align="center" prop="fprq" width="120" :formatter="formatDate" show-overflow-tooltip/> -->
        <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip width="120" />


      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 收费 -->
    <dialogChargeMain ref="dialogChargeMainRef" @success="chargeSuccess" @error="chargeError"></dialogChargeMain>

    <!-- 详情 -->
    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <ChargeRecord ref="chargeRecordRef" :gzjz-id="gzjzId" />
  </div>
</template>


<script setup name="GzjzSfjfpCw" lang="ts">
  import { getGzjzJbxx, listByCaiWu } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import { GzjzJbxxVO, CaiWuVO, CaiWuQuery, CaiWuForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import { genYearOptions, clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
  import dialogChargeMain from './dialogChargeMain.vue'
  import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
  import ChargeRecord from '@/views/gongzheng/cw/ChargeRecord/index.vue';

  const gzsbh_years = genYearOptions(2015);

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));
  const {
      gz_sl_lczt, gz_ywlx, gz_yes_or_no, gz_gzs_zh, gz_gzjz_jafs,
    gz_sfly, gz_zffs, gz_gzlb, gz_sfzt
  } =
    toRefs<any>(proxy?.useDict(
      'gz_sl_lczt', 'gz_ywlx', 'gz_yes_or_no','gz_gzs_zh','gz_gzjz_jafs',
      'gz_sfly', 'gz_zffs', 'gz_gzlb', 'gz_sfzt'
    ));

  const dialogChargeMainRef = ref<InstanceType<typeof dialogChargeMain> | null>(null);

  const gzjzCaiWuList = ref<CaiWuVO[]>([]);

  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const chargeRecordRef = ref(null);

  const gzjzId = ref(null);

  const dateRangeSlrq = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeSfrq = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeFprq = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeCzsj = ref<[DateModelType, DateModelType]>(['', '']);

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData: CaiWuForm = {
    id: undefined,
    jzbh: undefined,
    dsrxm: undefined,
    lb: undefined,
    gzsbh: undefined,
    gzybm: undefined,
    zlbm: undefined,
    slrq: undefined,
    fprq: undefined,
    czsj: undefined,
    jyfs: undefined,
    sfsf: undefined
  };

  const data = reactive<PageData<CaiWuForm, CaiWuQuery>>({
    form: {...initFormData},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      jzbh: undefined,
      dsrxm: undefined,
      lb: undefined,
      gzsbh: undefined,
      gzybm: undefined,
      zlbm: undefined,
      slrq: undefined,
      fprq: undefined,
      czsj: undefined,
      jyfs: '10',
      sfsf: '1',
      lczt: '04,05,06,07',
      sfzf: '0',
      params: {
        gzsNf: undefined,
        gzsZh: undefined,
        gzsLs: undefined,
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 多选框选中数据 */
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const handleSelectionChange = (selection: GzjzJbxxVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 日期格式化 */
  const formatDate = (row : any, column : any, cellValue : string) => {
    if (cellValue) {
      const date = new Date(cellValue), year = date.getFullYear(),
        month = String(date.getMonth() + 1).padStart(2, '0'),
        day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
    }
    return '';
  };

  /** 查询公证卷宗-基本信息v1.0列表 */
  const getList = async () => {
    loading.value = true;
    //queryParams.value.params = {};
    proxy?.addDateRange(queryParams.value, dateRangeSlrq.value, 'Slrq');
    proxy?.addDateRange(queryParams.value, dateRangeSfrq.value, 'Sfrq');
    proxy?.addDateRange(queryParams.value, dateRangeFprq.value, 'Fprq');
    proxy?.addDateRange(queryParams.value, dateRangeCzsj.value, 'Czsj');
    const res = await listByCaiWu(queryParams.value);
    if(res?.rows){
      gzjzCaiWuList.value = res.rows;
      total.value = res.total;
    }
    loading.value = false;
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryParams.value.params = {};
    dateRangeSlrq.value = ['',''];
    dateRangeSfrq.value = ['',''];
    dateRangeFprq.value = ['',''];
    dateRangeCzsj.value = ['',''];
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 收费  按钮操作 */
  const handlePayment = async (row?: CaiWuVO) => {
    dialogChargeMainRef.value?.open(row);
  }

  /** 收费记录 按钮操作 */
  const handlePaymentRecode = async (row?: CaiWuVO) => {
    gzjzId.value = row.id
    chargeRecordRef.value?.open({
      gzjzId: row.id
    })
  }


  // 当前编辑的记录ID，用于传递给子组件
  const currentRecordId = ref<string | number | null>(null);
  const currentRecord = ref<GzjzJbxxVO>(null);
  provide('currentRecordId', currentRecordId);
  provide('currentRecord', currentRecord);
  // 流程状态
  const lczt = ref<string | number | null>(null);

  const jzDetailState = reactive({
    visible: false
  })

  const handleDetail = (row?: CaiWuVO) => {
    if (row?.id) {
      currentRecordId.value = row.id;
      currentRecord.value = row;
      lczt.value = row.lczt;
    }
    jzDetailState.visible = true
  }

  const chargeSuccess = (_data: any) => {
    getList();
  }
  const chargeError = (_data: any) => {
    console.log('error', _data);
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.$modal.alertWarning("功能建设中...");
    // proxy?.download('gongzheng/gzjzSfjfp/export', {
    //   ...queryParams.value
    // }, `收费清单_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
