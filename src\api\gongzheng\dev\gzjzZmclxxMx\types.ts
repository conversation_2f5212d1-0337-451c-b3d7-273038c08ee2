export interface GzjzZmclxxMxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 主表ID
   */
  parentId: string | number;

  /**
   * 信息名称
   */
  xxmc: string;

  /**
   * 录入方式
   */
  lrfs: string;

  /**
   * 保存路径
   */
  bclj: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh: number;

}

export interface GzjzZmclxxMxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 主表ID
   */
  parentId?: string | number;

  /**
   * 信息名称
   */
  xxmc?: string;

  /**
   * 录入方式
   */
  lrfs?: string;

  /**
   * 保存路径
   */
  bclj?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

}

export interface GzjzZmclxxMxQuery extends PageQuery {

  /**
   * 主表ID
   */
  parentId?: string | number;

  /**
   * 信息名称
   */
  xxmc?: string;

  /**
   * 录入方式
   */
  lrfs?: string;

  /**
   * 保存路径
   */
  bclj?: string;

  /**
   * 排序序号(从 1 开始自动生成)
   */
  pxxh?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



