<template>
  <el-card>
    <template #header>
      <strong class="text-base">公证事项</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="gzsxMc" label="公证事项" width="120" align="center" />
      <el-table-column prop="gxrMc" label="关系人" width="120" align="center" />
      <el-table-column prop="gzsBh" label="公证书编号" align="center" show-overflow-tooltip />
      <el-table-column prop="notaryDoc" label="公证书" align="center">
        <template #default="{ row }">
          <el-button @click="() => openGzsDoc(row)" type="primary" v-if="row.gzsFile?.wblj" size="small" link>证词</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="translation" label="译文" align="center">
        <template #default="{ row }">
          <el-button @click="() => openGzsDoc(row, true)" type="primary" v-if="row.gzsFile?.ywlj" size="small" link>译文</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="combinedDoc" label="合成公证书" align="center" show-overflow-tooltip />
      <el-table-column prop="gzsFs" label="份数" width="60" align="center" />
      <el-table-column prop="hasBackup" label="备案" width="60" align="center" />
      <el-table-column prop="backupInfo" label="备案信息" align="center">
        <template #default="{ row }">
          <el-link type="primary">查看</el-link>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { docOpenShow } from '@/views/gongzheng/doc/DocEditor';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

// 打开公证书
const openGzsDoc = (data: any, yw: boolean = false) => {
  let docInfo = null;
  if(yw) {
    // 公证书译文
    docInfo = JSON.parse(data.gzsFile.ywlj || '{}');
  } else {
    // 公证书
    docInfo = JSON.parse(data.gzsFile.wblj || '{}');
  }

  if(docInfo?.path) {
    docOpenShow(docInfo.path)
  }
}

</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
