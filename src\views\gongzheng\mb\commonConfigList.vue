<template>
  <div class="phrase-container">
    <div class="search-container">
      <div class="search-form">
        <div class="form-item">
          <span class="label">内容：</span>
          <el-input v-model="searchForm.content" placeholder="请输入内容" style="width: 180px;"></el-input>
        </div>
        <!-- <div class="form-item">
          <span class="label">创建人：</span>
          <el-input v-model="searchForm.creator" placeholder="请输入创建人" style="width: 180px;"></el-input>
        </div>
        <div class="form-item">
          <span class="label">创建日期：</span>
          <el-date-picker v-model="searchForm.startDate" type="date" placeholder="开始日期" style="width: 180px;" />
          <span class="date-separator">至</span>
          <el-date-picker v-model="searchForm.endDate" type="date" placeholder="结束日期" style="width: 180px;" />
        </div> -->
        <div class="search-buttons">
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>查询
          </el-button>
          <el-button @click="resetSearch">
            <el-icon>
              <Refresh />
            </el-icon>重置
          </el-button>
        </div>
      </div>
    </div>

    <div class="phrase-list">
      <div class="list-header">
        <div class="header-buttons">
          <el-button type="primary" @click="handleAdd">新增</el-button>
          <el-button @click="handleDelete">删除</el-button>
        </div>
      </div>

      <el-table :data="phraseList" border stripe @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="内容" align="left" show-overflow-tooltip />
        <el-table-column prop="syFw" label="使用范围" width="120" align="center">
          <template #default="scope">
            <el-tag type="info">{{scope.row.syFw===0?'个人':'全站'}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createByStr" label="创建人" width="120" align="center" />
        <el-table-column prop="createTime" label="创建日期" width="180" align="center" />
        <el-table-column prop="updateTime" label="修改日期" width="180" align="center" />
      </el-table>

      <div class="pagination-container">
        <el-pagination background layout="prev, pager, next, sizes, total" :total="total" :current-page="currentPage"
          :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 新增/编辑短语对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增内容' : '编辑内容'" width="50%" destroy-on-close>
      <div class="dialog-content">
        <div class="form-item">
          <span class="label">内容：</span>
          <el-input v-model="phraseForm.content" type="textarea" :rows="8" placeholder="请输入内容"></el-input>
        </div>
        <div class="form-item">
          <span class="label">使用范围：</span>
          <div class="radio-group">
            <el-radio v-model="phraseForm.syFw" label="0">个人</el-radio>
            <el-radio v-model="phraseForm.syFw" label="1">全站</el-radio>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSave">确定</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import * as api from '@/api/gongzheng/mb/list'
  import { Phrase, PhraseQuery, PhraseSaveParams } from '@/api/gongzheng/mb/list/types'

  // 搜索表单
  const searchForm = reactive<PhraseQuery>({
    content: '',
    creator: '',
    startDate: '',
    endDate: '',
    pageNum: 1,
    pageSize: 10
  })

  // 短语列表数据
  const phraseList = ref<Phrase[]>([])

  // 分页相关
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const selectedPhrases = ref<Phrase[]>([])

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const phraseForm = reactive<PhraseSaveParams>({
    id: '',
    content: '',
    syFw: 0
  })

  // 查询短语列表
  const fetchList = async () => {
    const params : PhraseQuery = {
      ...searchForm,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    const res = await api.listPhrases(params)
    phraseList.value = res.rows
    total.value = res.total
  }

  // 处理搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchList()
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.content = ''
    searchForm.creator = ''
    searchForm.startDate = ''
    searchForm.endDate = ''
    currentPage.value = 1
    fetchList()
    ElMessage.info('重置搜索条件')
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection : Phrase[]) => {
    selectedPhrases.value = selection
  }

  // 处理分页变化
  const handleCurrentChange = (val : number) => {
    currentPage.value = val
    fetchList()
  }

  // 处理每页显示数量变化
  const handleSizeChange = (val : number) => {
    pageSize.value = val
    currentPage.value = 1
    fetchList()
  }

  // 处理新增短语
  const handleAdd = () => {
    dialogType.value = 'add'
    phraseForm.id = ''
    phraseForm.content = ''
    phraseForm.syFw = 0
    dialogVisible.value = true
  }

  // 处理编辑短语
  const handleEdit = (row : Phrase) => {
    dialogType.value = 'edit'
    phraseForm.id = row.id
    phraseForm.content = row.content
    phraseForm.syFw = row.syFw+''
    dialogVisible.value = true
  }

  // 处理删除短语
  const handleDelete = () => {
    if (selectedPhrases.value.length === 0) {
      ElMessage.warning('请选择要删除的短语')
      return
    }
    ElMessageBox.confirm('确认删除选中的短语?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = selectedPhrases.value.map(v => v.id).join(',')
      await api.deletePhrase(ids)
      ElMessage.success('删除成功')
      fetchList()
    })
  }

  // 保存短语
  const handleSave = async () => {
    if (!phraseForm.content) {
      ElMessage.warning('请输入内容')
      return
    }
    if (dialogType.value === 'add') {
      await api.addPhrase(phraseForm)
      ElMessage.success('新增成功')
    } else {
      await api.updatePhrase(phraseForm)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    fetchList()
  }

  onMounted(() => {
    fetchList()
  })
</script>

<style scoped>
  .phrase-container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .search-container {
    margin-bottom: 20px;
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .label {
    margin-right: 5px;
    white-space: nowrap;
  }

  .date-separator {
    margin: 0 10px;
  }

  .search-buttons {
    margin-left: auto;
  }

  .phrase-list {
    flex: 1;
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .list-header h2 {
    margin: 0;
    font-size: 18px;
  }

  .header-buttons {
    display: flex;
    gap: 10px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-content {
    padding: 20px;
  }

  .radio-group {
    display: flex;
  }

  .radio-group .el-radio {
    margin-right: 20px;
  }
</style>
