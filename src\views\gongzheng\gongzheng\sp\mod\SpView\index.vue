<template>
  <gz-dialog v-model="visible" @closed="closed" show-close destroy-on-close fullscreen>
    <template #header>
      <span>审批</span>
    </template>
    <div class="flex flex-col gap-10px">
      <el-card>
        <div class="flex items-center gap-12px flex-wrap">
          <span class="flex items-center gap-4px">
            <span>卷宗号：</span>
            <el-text type="info">{{ curGzjz.jzbh || '-' }}</el-text>
          </span>
          <span class="flex items-center gap-4px">
            <el-button @click="openJzDetail" type="primary" size="small" link>【详情】</el-button>
          </span>
          <span class="flex items-center gap-4px">
            <span>公证类别：</span>
            <el-text type="info">{{ dictMapFormat(gz_gzlb, curGzjz.lb) }}</el-text>
          </span>
          <span class="flex items-center gap-4px">
            <span>公证员：</span>
            <el-text type="info">{{ curGzjz.gzyxm }}</el-text>
          </span>
          <span class="flex items-center gap-4px">
            <span>助理：</span>
            <el-text type="info">{{ curGzjz.zlxm }}</el-text>
          </span>
          <span class="flex items-center gap-4px">
            <span>申请日期：</span>
            <el-text type="info">{{ formatDate(curGzjz.sqsj) }}</el-text>
          </span>
          <span class="flex items-center gap-4px">
            <span>受理日期：</span>
            <el-text type="info">{{ formatDate(curGzjz.slrq) }}</el-text>
          </span>
        </div>
      </el-card>

      <MergeData one-off />

      <SfMx />

      <el-card class="min-h-200px">
        <el-form ref="SpFormRef" :model="spForm" :rules="spFormRules">
          <el-form-item prop="spsj" label="审批时间：">
            <el-date-picker v-model="spForm.spsj" style="width: 240px" format="YYYY年MM月DD日"/>
          </el-form-item>
          <el-form-item prop="zzsj" label="出证日期：">
            <el-date-picker v-model="spForm.zzsj" style="width: 240px" format="YYYY年MM月DD日"/>
          </el-form-item>
          <el-form-item prop="lcyj" label="审批意见：">
            <el-input
              v-model="spForm.lcyj"
              style="min-width: 360px;max-width: 720px;"
              :autosize="{ minRows: 3, maxRows: 6 }"
              type="textarea"
              placeholder="请输入审批意见."
            />
          </el-form-item>
        </el-form>
      </el-card>

    </div>

    <NewZjcl v-if="zjclVisible" v-model="zjclVisible" :gzjz-id="gzjzId" read-only/>

    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <SpComfirm v-model="spComfirmVisible" :sp-data="spForm" @pass="close"/>

    <SpReject v-model="spRejectVisible" @rejected="close"/>

    <Dcjg v-model="dcjgShow" :gzjz-id="gzjzId" />

    <template #footer>
      <div class="flex justify-end items-center gap-10px">
        <el-button @click="spStart" type="primary">同意</el-button>
        <!-- <el-button>认证/识别记录</el-button> -->
        <el-button @click="showDcjg">调查核实记录</el-button>
        <el-button @click="showZjcl">证据材料</el-button>
        <el-button @click="bhStart" type="warning">驳回</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { formatDate, dictMapFormat } from '@/utils/ruoyi';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import NewZjcl from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/index.vue'
import SfMx from './SfMx.vue';
import MergeData from './MergeData.vue';
import SpComfirm from './SpComfirm.vue';
import SpReject from './SpReject.vue';
import Dcjg from '@/views/gongzheng/gongzheng/components/Dcjg/index.vue'

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '审批',
  gzjzId: '',
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_dsr_jslx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_dsr_jslx', 'gz_dsr_lb'));

const emit = defineEmits(['update:modelValue', 'close', 'closed', 'sp-pass']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const jzDetailState = reactive({
  visible: false
})

const zjclVisible = ref(false)
const dcjgShow = ref(false)

const SpFormRef = ref<ElFormInstance>(null);

const spForm = reactive({
  spsj: new Date(),   // 审批时间
  zzsj: new Date(),   // 出证日期
  lcyj: '',   // 审批意见
})

const spFormRules = {
  spsj: [
    { required: true, message: '审批时间不能为空', trigger: 'change' }
  ],
  zzsj: [
    { required: true, message: '出证日期不能为空', trigger: 'change' }
  ],
  lcyj: [
    { required: true, message: '审批意见不能为空', trigger: 'blur' }
  ],
}

const spComfirmVisible = ref(false);
const spRejectVisible = ref(false);
/**=================================================== - ===================================================**/

function openJzDetail() {
  jzDetailState.visible = true;
}

function showZjcl() {
  zjclVisible.value = true;
}

function showDcjg() {
  dcjgShow.value = true;
}

function spStart() {
  if (!SpFormRef.value) return;
  SpFormRef.value.validate((valid, fields) => {
    if (valid) {
      spComfirmVisible.value = true;
    } else {
      const firstField = Object.values(fields)[0];
      if (firstField) {
        ElMessage.error(firstField[0].message);
      }
    }
  })
}

function bhStart() {
  spRejectVisible.value = true;
}

function close() {
  emit('update:modelValue', false);
  emit('close');
}

function closed() {
  emit('closed');
}
/**=================================================== + ===================================================**/

onMounted(() => {
  // 初始化逻辑
});
</script>
