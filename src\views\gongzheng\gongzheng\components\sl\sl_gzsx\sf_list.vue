<template>
  <div class="fee-management">

    <!-- 收费列表 -->
    <div class="fee-list-section">
      <div class="section-header">
        <h3>收费明细列表 (费用合计：¥{{ totalFee.toFixed(2) }})</h3>
        <div class="header-actions">
          <!-- <el-button type="primary" @click="loadDefaultFees" :loading="loadingDefault">
            加载收费标准
          </el-button> -->
          <el-button type="primary" icon="Plus" plain @click="handleAdd">新增收费项</el-button>
          <el-button type="danger" icon="Delete" plain @click="handleBatchDelete" :disabled="selectedFees.length === 0">
            批量删除
          </el-button>
        </div>
      </div>

      <el-table
        :data="feeList"
        :summary-method="getSummaries"
        style="width: 100%"
        border
        highlight-current-row
        height="350px"
        size="small"
        show-summary
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="费用类型" prop="fylx" align="center" min-width="80">
          <template #default="scope">
            {{ dictMapFormat(gz_sf_lb, scope.row.fylx) }}
          </template>
        </el-table-column>
        <el-table-column label="收费场景" prop="sfcj" align="center" min-width="80">
          <template #default="{ row, column }">
            {{ dictMapFormat(gz_sfcj, row.sfcj) }}
          </template>
        </el-table-column>
        <el-table-column label="计价方式" prop="jjfs" align="center" min-width="80">
          <template #default="scope">
            {{ dictMapFormat(gz_jjfs, scope.row.jjfs) }}
          </template>
        </el-table-column>
        <el-table-column label="是否记账" prop="sfjz" align="center" min-width="80">
          <template #default="{ row }">
            {{ row.sfjz === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="应收金额" prop="fyys" align="center" min-width="100">
          <template #default="scope">
            <span>¥{{ Number(scope.row.fyys)?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="减免金额" prop="fyjm" align="center" width="100">
          <template #default="scope">
            <span>¥{{ Number(scope.row.fyjm)?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实收金额" prop="fyss" align="center" width="100">
          <template #default="scope">
            <span>¥{{ Number(scope.row.fyss)?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="收费状态" prop="sfzt" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.sfzt === '2' ? 'success' : 'danger'" size="small">
              {{ scope.row.sfzt === '2' ? '已完成' : '未收费' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-button v-if="scope.row.sfzt !== '2'" link type="primary" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
         <!--   <el-button
              link
              type="success"
              size="small"
              @click="handlePayFee(scope.row)"
              :disabled="scope.row.sfzt === '2'"
            >
              收费
            </el-button> -->
           <!-- <el-button link type="danger" size="small" @click="handleDelete(scope.row)">
              删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 收费明细编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editMode === 'add' ? '新增收费项' : '编辑收费项'"
      width="500px"
      @close="handleEditDialogClose"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="公证事项" prop="gzjzGzsxId">
          <el-select v-model="editForm.gzjzGzsxId" placeholder="请选择公证事项" style="width: 100%" filterable disabled>
            <el-option
              v-for="item in gzsxState.listData"
              :key="item.id"
              :label="item.gzsxMc"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="费用类型" prop="fylx">
          <el-select v-model="editForm.fylx" placeholder="请选择费用类型" style="width: 100%" filterable>
            <el-option
              v-for="dict in gz_sf_lb"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收费场景" prop="sfcj">
          <el-select v-model="editForm.sfcj" placeholder="请选择收费场景" style="width: 100%">
            <el-option
              v-for="dict in gz_sfcj"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计价方式" prop="jjfs">
          <el-select v-model="editForm.jjfs" placeholder="请选择计价方式" style="width: 100%">
            <el-option
              v-for="dict in gz_jjfs"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否记账" prop="sfjz">
          <el-select v-model="editForm.sfjz" default-first-option style="width: 120px">
            <el-option label="否" :value="'0'" />
            <el-option label="是" :value="'1'" />
          </el-select>
        </el-form-item>
        <el-form-item label="应收金额" prop="fyys">
          <el-input-number
            v-model="editForm.fyys"
            :min="0"
            :step="1"
            :precision="2"
            placeholder="请输入应收金额"
            style="width: 100%"
            @change="calculateActualAmount"
          />
        </el-form-item>
        <!-- <el-form-item label="减免金额" prop="fyjm">
          <el-input-number
            v-model="editForm.fyjm"
            :min="0"
            :step="1"
            :precision="2"
            placeholder="请输入减免金额"
            style="width: 100%"
            @change="calculateActualAmount"
          />
        </el-form-item> -->
        <!-- <el-form-item label="实收金额" prop="fyss">
          <el-input-number
            v-model="editForm.fyss"
            :min="0"
            :precision="2"
            placeholder="自动计算"
            style="width: 100%"
          />
        </el-form-item> -->
        <!-- <el-form-item label="备注" prop="remark">
          <el-input v-model="editForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditDialogClose">取消</el-button>
          <el-button type="primary" @click="handleEditSave" :loading="editSaving">
            {{ editMode === 'add' ? '新增' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, getCurrentInstance, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { listGzsxSfbz } from '@/api/gongzheng/basicdata/gzsxSfbz'
import {
  listGzjzGzsxSfxx,
  addGzjzGzsxSfxx,
  updateGzjzGzsxSfxx,
  delGzjzGzsxSfxx
} from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx'
import type { GzjzGzsxSfxxVO, GzjzGzsxSfxxForm, GzjzGzsxSfxxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx/types'
import { dictMapFormat } from '@/utils/ruoyi';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx'
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
import { listTree } from '@/api/gongzheng/basicdata/gzsx'

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 获取字典数据
const dictData = proxy?.useDict ? proxy.useDict('gz_sf_lb', 'gz_jjfs', 'gz_sfcj') : {
  gz_sf_lb: ref([]),
  gz_jjfs: ref([]),
  gz_sfcj: ref([])
}
const { gz_sf_lb, gz_jjfs, gz_sfcj } = toRefs<any>(dictData)

interface Props {
  gzsxId?: string | number
  gzjzId?: string | number
  visible?: boolean
}

interface Emits {
  (e: 'confirm', data: any): void
  (e: 'update:visible', value: boolean): void
  (e: 'fee-changed', totalFee: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const editFormRef = ref()

// 收费列表
const feeList = ref<GzjzGzsxSfxxVO[]>([])
const selectedFees = ref<GzjzGzsxSfxxVO[]>([])
const loading = ref(false)
const loadingDefault = ref(false)

// 编辑对话框
const editDialogVisible = ref(false)
const editMode = ref<'add' | 'edit'>('add')
const editSaving = ref(false)
const editForm = reactive<GzjzGzsxSfxxForm>({
  id: undefined,
  fylx: '1',
  jjfs: '1',
  sfcj: '1',
  sfjz: '0',
  fyys: 0,
  fyjm: 0,
  fyss: 0,
  sfzt: '1',
  remark: '',
  gzjzId: props.gzjzId,
  gzjzGzsxId: props.gzsxId,
  gzsxId: ''
})

const gzsxState = reactive({
  listData: []
})

// 表单验证规则
const editRules = {
  // gzjzGzsxId: [{ required: true, message: '请选择公证事项', trigger: 'change' }],
  fylx: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
  // sfcj: [{ required: true, message: '请选择收费场景', trigger: 'change' }],
  jjfs: [{ required: true, message: '请选择计价方式', trigger: 'change' }],
  fyys: [{ required: true, message: '请输入应收金额', trigger: 'blur' }]
}

// 计算总费用
const totalFee = computed(() => {
  return feeList.value.reduce((sum, item) => {
    return sum + ((item.fyys || 0) - (item.fyjm || 0))
  }, 0)
})

// 监听总费用变化，通知父组件
watch(totalFee, (newValue) => {
  emit('fee-changed', newValue)
})

// 字典标签获取方法
const getDictLabel = (dictData: any[], value: string) => {
  const dict = dictData.find(item => item.value === value)
  return dict ? dict.label : value
}

// 加载收费明细数据
const loadFeeData = async () => {
  if (!props.gzjzId) {
    ElMessage.warning('未找到公证卷宗ID')
    return
  }

  try {
    loading.value = true
    console.log('正在加载收费明细数据...')

    // 1.1.1 优先从案卷收费信息获取
    const query: GzjzGzsxSfxxQuery = {
      gzjzId: props.gzjzId,
      gzjzGzsxId: props.gzsxId,
      pageNum: 1,
      pageSize: 1000
    }

    const response = await listGzjzGzsxSfxx(query)

    if (response && response.code === 200 && response.rows?.length > 0) {
      // 有案卷收费数据，直接使用
      feeList.value = response.rows;
      // ElMessage.success(`加载到${response.rows.length}条收费明细`)
    } else {
      feeList.value = [];
      // 1.1.2 如果没有案卷收费数据，从收费标准获取默认配置
      // await loadDefaultFromStandard()
    }
  } catch (error: any) {
    console.error('加载收费明细失败:', error)
    // 如果接口异常，尝试加载默认收费标准
    // await loadDefaultFromStandard()
  } finally {
    loading.value = false
  }
}

// 计算实收金额
const calculateActualAmount = () => {
  // editForm.fyss = (editForm.fyys || 0) - (editForm.fyjm || 0)
  // if (editForm.fyss < 0) {
  //   editForm.fyss = 0
  // }
}

// 新增收费项
const handleAdd = () => {
  editMode.value = 'add'
  Object.assign(editForm, {
    id: undefined,
    fylx: '1',
    jjfs: '1',
    sfcj: '1',
    sfjz: '0',
    fyys: undefined,
    fyjm: undefined,
    fyss: undefined,
    sfzt: '1',
    remark: '',
    gzjzId: props.gzjzId,
    gzjzGzsxId: props.gzsxId
  })
  editDialogVisible.value = true
}

// 编辑收费项
const handleEdit = (row: GzjzGzsxSfxxVO) => {
  editMode.value = 'edit'
  Object.assign(editForm, { ...row })
  editDialogVisible.value = true
}

// 删除收费项
const handleDelete = async (row: GzjzGzsxSfxxVO) => {
  ElMessageBox.confirm('确定删除这条收费记录吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        if (typeof row.id === 'string' && row.id.startsWith('temp_')) {
          // 临时数据，直接从列表删除
          const index = feeList.value.findIndex(item => item.id === row.id)
          if (index > -1) {
            feeList.value.splice(index, 1)
            ElMessage.success('删除成功')
          }
        } else {
          // 调用删除接口
          const response = await delGzjzGzsxSfxx(row.id)
          if (response && response.code === 200) {
            await loadFeeData() // 重新加载数据
            ElMessage.success('删除成功')
          } else {
            ElMessage.error('删除失败：' + (response?.msg || '未知错误'))
          }
        }
      } catch (error: any) {
        console.error('删除收费项失败:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedFees.value.length === 0) {
    ElMessage.warning('请选择要删除的收费项')
    return
  }

  ElMessageBox.confirm(`确定删除选中的${selectedFees.value.length}条收费记录吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 分离临时数据和真实数据
        const tempIds = selectedFees.value.filter(fee =>
          typeof fee.id === 'string' && fee.id.startsWith('temp_')
        ).map(fee => fee.id)

        const realIds = selectedFees.value.filter(fee =>
          !(typeof fee.id === 'string' && fee.id.startsWith('temp_'))
        ).map(fee => fee.id)

        // 删除临时数据
        tempIds.forEach(id => {
          const index = feeList.value.findIndex(item => item.id === id)
          if (index > -1) {
            feeList.value.splice(index, 1)
          }
        })

        console.log('realIds:', realIds)

        // 删除真实数据
        if (realIds.length > 0) {
          const response = await delGzjzGzsxSfxx(realIds.join(','))
          if (response && response.code === 200) {
            await loadFeeData() // 重新加载数据
          } else {
            ElMessage.error('删除失败：' + (response?.msg || '未知错误'))
            return
          }
        }

        ElMessage.success('批量删除成功')
        selectedFees.value = []
      } catch (error: any) {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 收费操作
const handlePayFee = (row: GzjzGzsxSfxxVO) => {
  ElMessageBox.confirm(`确定对该收费项进行收费操作吗？`, '收费确认', {
    confirmButtonText: '确定收费',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const updateData = {
          ...row,
          sfzt: '2', // 已完成
          fyss: (row.fyys || 0) - (row.fyjm || 0)
        }

        const response = await updateGzjzGzsxSfxx(updateData)
        if (response && response.code === 200) {
          await loadFeeData() // 重新加载数据
          ElMessage.success('收费成功')
        } else {
          ElMessage.error('收费失败：' + (response?.msg || '未知错误'))
        }
      } catch (error: any) {
        console.error('收费操作失败:', error)
        ElMessage.error('收费失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消收费')
    })
}

// 选择变化
const handleSelectionChange = (selection: GzjzGzsxSfxxVO[], isSelected: boolean) => {
  console.log('handleSelectionChange', selection)
  selectedFees.value = selection
}

// 编辑对话框关闭
const handleEditDialogClose = () => {
  editDialogVisible.value = false
  editFormRef.value?.resetFields()
}

// 编辑保存
const handleEditSave = async () => {
  try {
    const isValid = await editFormRef.value?.validate()
    if (!isValid) return

    editSaving.value = true

    // 计算实收金额
    calculateActualAmount()

    if (editMode.value === 'add') {
      // 新增
      const response = await addGzjzGzsxSfxx(editForm)
      if (response && response.code === 200) {
        await loadFeeData() // 重新加载数据
        ElMessage.success('新增收费项成功')
      } else {
        ElMessage.error('新增失败：' + (response?.msg || '未知错误'))
        return
      }
    } else {
      // 编辑
      const response = await updateGzjzGzsxSfxx(editForm)
      if (response && response.code === 200) {
        await loadFeeData() // 重新加载数据
        ElMessage.success('修改收费项成功')
      } else {
        ElMessage.error('修改失败：' + (response?.msg || '未知错误'))
        return
      }
    }

    editDialogVisible.value = false
  } catch (error: any) {
    console.error('保存收费项失败:', error)
    ElMessage.error('保存失败')
  } finally {
    editSaving.value = false
  }
}

// 合计计算
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 2) {
      sums[index] = '合计'
      return
    }
    if (column.property === 'fyys' || column.property === 'fyjm' || column.property === 'fyss') {
      const values = data.map((item: any) => Number(item[column.property]))
      if (!values.every((value: number) => Number.isNaN(value))) {
        sums[index] = `¥${values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0).toFixed(2)}`
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })
  return sums
}

// 加载公证事项列表
const loadGzsxList = async () => {
  try {
    // const res = await listTree();
    // if(res && res.code === 200) {
    //   gzsxState.listData = (res.data || []).filter((item: any) => item.childCount === 0);
    // }
     const queryParams : GzjzGzsxQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 200 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
    }
  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
  }
}

// 监听props变化，重新加载数据
// watch([() => props.gzjzId, () => props.gzsxId], () => {
//   if (props.gzjzId) {
//     loadFeeData()
//   }
// }, { immediate: true })

onBeforeMount(() => {
  loadGzsxList()
})

// 组件挂载时加载数据
onMounted(() => {
  if (props.gzjzId) {
    loadFeeData()
  }
})

// 暴露方法
defineExpose({
  loadFeeData,
  handleAdd,
  handleEdit,
  handleDelete
})
</script>

<style scoped>
.fee-management {
  padding: 10px;
}

.fee-list-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions .el-button {
  padding: 8px 15px;
  font-size: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table .el-button--small) {
  padding: 2px 8px;
  font-size: 11px;
}

:deep(.el-tag--small) {
  font-size: 11px;
}

.dialog-footer {
  text-align: center;
}
</style>
