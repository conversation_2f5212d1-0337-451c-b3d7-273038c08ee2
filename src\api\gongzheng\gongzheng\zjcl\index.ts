import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  EvidenceType,
  EvidenceFile,
  AddEvidenceTypeParams,
  UploadEvidenceFileParams
} from './types';

// 查询公证卷宗-公证证明材料信息-主信息v1.0列表
export const listEvidenceTypes = (params: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/list',
    method: 'get',
    params
  });
};

// 新增公证卷宗-公证证明材料信息-主信息v1.0
export const addEvidenceType = (data: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'post',
    data
  });
};
// 删除卷宗证据类型
export const delEvidenceType = (id: string | number | Array<string | number>): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/'+id,
    method: 'delete'
  });
};

// 修改公证卷宗-公证证明材料信息-主信息v1.0
export const updateEvidenceType = (data: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'put',
    data
  });
};

// 查询公证卷宗-公证证明材料信息-明细v1.0列表
export const listEvidenceFiles = (params: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/list',
    method: 'get',
    params
  });
};

// 新增公证卷宗-公证证明材料信息-明细v1.0
export const addEvidenceFile = (data: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'post',
    data
  });
};

// 修改公证卷宗-公证证明材料信息-明细v1.0
export const updateEvidenceFile = (data: any): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'put',
    data
  });
};
export const delEvidenceFile = (id: string | number | Array<string | number>): AxiosPromise<any> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/'+id,
    method: 'delete',
  });
};
// 获取证据材料树（新版接口）
export const getEvidenceMaterialTree = (params?: any) => {
  return request({
    url: '/basicdata/zjclmc/listTree',
    method: 'get',
    params
  });
};
