export interface GzsxdzbVO {
  /**
   * 本地公证事项
   */
  notarizedMattersId: string | number;

  /**
   * 对照公证事项
   */
  compareNotarized: string;

  /**
   * 序号
   */
  id: string | number;

}

export interface GzsxdzbForm extends BaseEntity {
  /**
   * 本地公证事项
   */
  notarizedMatters?: string | number;

  /**
   * 对照公证事项
   */
  compareNotarized?: string;

  /**
   * 序号
   */
  id?: string | number;

}

export interface GzsxdzbQuery extends PageQuery {

  /**
   * 本地公证事项
   */
  notarizedMatters?: string | number;

  /**
   * 对照公证事项
   */
  compareNotarized?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



