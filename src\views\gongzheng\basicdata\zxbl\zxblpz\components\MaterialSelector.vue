<template>
  <div>
    <!-- 已选择的材料列表 -->
    <div class="selected-materials-list" v-if="selectedMaterials.length > 0">
      <div class="list-header mb-3">
        <span class="text-sm font-medium">已配置申办材料：</span>
        <el-button type="primary" plain size="small" icon="Plus" @click="showDialog = true">
          添加材料
        </el-button>
      </div>

      <el-table :data="selectedMaterials" border size="small" max-height="300">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="材料名称" prop="materialName" min-width="200" :show-overflow-tooltip="true" />
        <el-table-column label="类别" align="center" prop="category" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.category === 1" type="primary" size="small">个人</el-tag>
            <el-tag v-else-if="scope.row.category === 2" type="success" size="small">企业</el-tag>
            <el-tag v-else type="info" size="small">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="说明" prop="description" min-width="250" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-tooltip content="移除" placement="top">
              <el-button
                link
                type="danger"
                icon="Delete"
                size="small"
                @click="removeMaterial(scope.row.id)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <el-icon size="48" class="empty-icon"><Document /></el-icon>
        <p class="empty-text">暂无配置申办材料</p>
        <el-button type="primary" plain icon="Plus" @click="showDialog = true">
          添加申办材料
        </el-button>
      </div>
    </div>

    <!-- 材料选择对话框 -->
    <el-dialog
      title="选择申办材料"
      v-model="showDialog"
      width="800px"
      append-to-body
      @close="handleDialogClose"
    >
      <!-- 搜索条件 -->
      <div class="mb-4">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="材料名称">
            <el-input
              v-model="searchForm.materialName"
              placeholder="请输入材料名称"
              clearable
              style="width: 200px"
              @keyup.enter="searchMaterials"
            />
          </el-form-item>
          <el-form-item label="类别">
            <el-select v-model="searchForm.category" placeholder="请选择类别" clearable style="width: 120px">
              <el-option label="个人" :value="1" />
              <el-option label="企业" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchMaterials">搜索</el-button>
            <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 材料列表 -->
      <el-table
        ref="materialTableRef"
        border
        v-loading="loading"
        :data="materialList"
        @selection-change="handleSelectionChange"
        height="400"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="材料名称" prop="materialName" width="150" :show-overflow-tooltip="true" />
        <el-table-column label="类别" align="center" prop="category" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.category === 1" type="primary" size="small">个人</el-tag>
            <el-tag v-else-if="scope.row.category === 2" type="success" size="small">企业</el-tag>
            <el-tag v-else type="info" size="small">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="说明" prop="description" width="100" :show-overflow-tooltip="true" />
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="searchForm.pageNum"
        v-model:limit="searchForm.pageSize"
        @pagination="getMaterialList"
        class="mt-4"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmSelection">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="MaterialSelector">
import { ref, reactive, watch, nextTick } from 'vue';
import { ElTable } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import { listZxblMaterial } from '@/api/gongzheng/basicdata/zxbl/blcl';
import { ZxblMaterialVO } from '@/api/gongzheng/basicdata/zxbl/blcl/types';
import { MaterialOption } from '@/api/gongzheng/basicdata/zxbl/zxblpz/types';

// Props定义
interface Props {
  modelValue?: string; // 选中的材料ID字符串（逗号分隔）
}

// Emits定义
interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', materials: MaterialOption[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
});

const emit = defineEmits<Emits>();

// 响应式数据
const showDialog = ref(false);
const loading = ref(false);
const materialTableRef = ref<InstanceType<typeof ElTable>>();
const total = ref(0);

// 材料列表
const materialList = ref<ZxblMaterialVO[]>([]);
const selectedMaterials = ref<MaterialOption[]>([]);
const tempSelectedMaterials = ref<MaterialOption[]>([]);

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  materialName: '',
  category: undefined as number | undefined
});

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      loadSelectedMaterials(newVal);
    } else {
      selectedMaterials.value = [];
    }
  },
  { immediate: true }
);

// 加载已选择的材料
const loadSelectedMaterials = async (materialIds: string) => {
  if (!materialIds) {
    selectedMaterials.value = [];
    return;
  }

  try {
    loading.value = true;
    // 根据ID获取材料详情
    const res = await listZxblMaterial({ pageNum: 1, pageSize: 1000 });
    const allMaterials = res.rows || [];
    const ids = materialIds.split(',').filter(id => id.trim() !== '');

    selectedMaterials.value = allMaterials
      .filter(item => ids.includes(String(item.id)))
      .map(item => ({
        id: item.id,
        materialName: item.materialName,
        category: item.category,
        description: item.description
      }));
  } catch (error) {
    console.error('加载已选材料失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取材料列表
const getMaterialList = async () => {
  try {
    loading.value = true;
    const res = await listZxblMaterial(searchForm);
    materialList.value = res.rows || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error('获取材料列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索材料
const searchMaterials = () => {
  searchForm.pageNum = 1;
  getMaterialList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.materialName = '';
  searchForm.category = undefined;
  searchMaterials();
};

// 移除材料
const removeMaterial = (materialId: string | number) => {
  selectedMaterials.value = selectedMaterials.value.filter(item => item.id !== materialId);
  updateModelValue();
};

// 表格选择变化
const handleSelectionChange = (selection: ZxblMaterialVO[]) => {
  tempSelectedMaterials.value = selection.map(item => ({
    id: item.id,
    materialName: item.materialName,
    category: item.category,
    description: item.description
  }));
};

// 对话框关闭处理
const handleDialogClose = () => {
  tempSelectedMaterials.value = [];
  // 重置表格选择状态
  nextTick(() => {
    materialTableRef.value?.clearSelection();
  });
};

// 确认选择
const confirmSelection = () => {
  // 合并已选择和新选择的材料，去重
  const existingIds = new Set(selectedMaterials.value.map(item => String(item.id)));
  const newMaterials = tempSelectedMaterials.value.filter(item => !existingIds.has(String(item.id)));

  selectedMaterials.value = [...selectedMaterials.value, ...newMaterials];
  updateModelValue();
  showDialog.value = false;
};

// 更新modelValue
const updateModelValue = () => {
  const materialIds = selectedMaterials.value.map(item => String(item.id)).join(',');
  emit('update:modelValue', materialIds);
  emit('change', selectedMaterials.value);
};

// 打开对话框时加载数据
watch(showDialog, (newVal) => {
  if (newVal) {
    getMaterialList();
  }
});
</script>

<style scoped>
.selected-materials-list {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  background: #fafafa;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  color: #c0c4cc;
}

.empty-text {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.mb-3 {
  margin-bottom: 12px;
}

.text-sm {
  font-size: 14px;
}

.font-medium {
  font-weight: 500;
}
</style>
