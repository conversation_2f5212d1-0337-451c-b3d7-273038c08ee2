<template>
  <div class="tb-wrap h-full rounded">
    <div class="h-[calc(100%-32px)] p-4px">
      <el-table ref="tbRef" :data="recordState.list" v-loading="recordState.loading" @current-change="handleCurRow" highlight-current-row height="460" border stripe>
        <el-table-column type="index" label="#" width="55" align="center" />
        <el-table-column type="selection" label="#" width="55" align="center" />
        <el-table-column prop="sfrq" label="收费日期" align="center" show-overflow-tooltip/>
        <el-table-column prop="sfqdh" label="收费清单号" align="center" show-overflow-tooltip/>
        <el-table-column prop="zffs" label="支付方式" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <dict-tag :options="gz_zffs" :value="row.zffs" />
          </template>
        </el-table-column>
        <el-table-column prop="sfje" label="收费金额" align="center" show-overflow-tooltip/>
        <el-table-column prop="ysje" label="已收金额" align="center" show-overflow-tooltip/>
        <el-table-column prop="tfje" label="退费金额" align="center" show-overflow-tooltip/>
        <el-table-column prop="sfr" label="收费人" align="center" />
        <el-table-column prop="jfr" label="缴费人" align="center" />
        <el-table-column prop="fphm" label="发票号码" align="center" show-overflow-tooltip/>
        <el-table-column label="操作" width="140" fixed="right" align="center">
          <template #default="{ row, column }">
            <el-button type="primary" link size="small">操作</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="tb-footer flex items-center justify-end h-32px">
      <el-pagination
        v-model:current-page="pageState.pageNum"
        v-model:page-size="pageState.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :pager-count="7"
        size="small"
        layout="total, sizes, prev, pager, next"
        :total="pageState.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import { listGzjzSfjfp } from '@/api/gongzheng/gongzheng/gzjzSfjfp'

interface Props {
  gzjzId?: string | number;
}

const props = defineProps<Props>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_zffs } = toRefs<any>(proxy?.useDict('gz_zffs'))

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));

const tbRef = ref<ElTableInstance>(null);

const recordState = reactive({
  list: [],
  loading: false,
  curRow: null,
})

const pageState = reactive({
  pageSize: 20,
  pageNum: 1,
  total: 0
})

const loadList = async () => {
  try {
    recordState.loading = true;
    const { pageNum, pageSize } = pageState;
    const params = {
      pageNum,
      pageSize,
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
    }
    const res = await listGzjzSfjfp(params);
    if(res.code === 200) {
      recordState.list = res.rows || [];
    }

  } catch (err: any) {
    ElMessage.error('获取收费记录异常');
    console.error('获取收费记录异常', err);
  } finally {
    recordState.loading = false;
  }
}

const handleSizeChange = (val: number) => {
  pageState.pageSize = val;
  loadList();
}

const handleCurrentChange = (val: number) => {
  pageState.pageNum = val;
  loadList();
}

const handleCurRow = (cur: any, pre: any) => {
  recordState.curRow = cur;
}

const getSelectionRows = () => {
  return tbRef.value?.getSelectionRows() || [];
}

const getCurrentRow = () => {
  return recordState.curRow;
}

defineExpose({
  getSelectionRows,
  getCurrentRow
})

onMounted(() => {
  loadList();
})

</script>

<style scoped>
.tb-wrap {
  border: 1px solid rgba(128, 128, 128, 0.42);
}
.tb-footer {
  border-top: 1px solid rgba(128, 128, 128, 0.42);
}
</style>