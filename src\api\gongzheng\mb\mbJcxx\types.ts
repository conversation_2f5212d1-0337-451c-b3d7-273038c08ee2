export interface MbJcxxVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 模板名称
   */
  title: string;

  /**
   * 模板类型
   */
  templateType: number;

  /**
   * 业务ID（公证事项ID、告知书ID）
   */
  ywId: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 模板分类(字典 告知书、公证事项、其他)
   */
  classify: number;

}

export interface MbJcxxForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 模板名称
   */
  title?: string;

  /**
   * 模板类型
   */
  templateType?: number;

  /**
   * 业务ID（公证事项ID、告知书ID）
   */
  ywId?: string | number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 模板分类(字典 告知书、公证事项、其他)
   */
  classify?: number;

}

export interface MbJcxxQuery extends PageQuery {

  /**
   * 模板名称
   */
  title?: string;

  /**
   * 模板类型
   */
  templateType?: number;

  /**
   * 业务ID（公证事项ID、告知书ID）
   */
  ywId?: string | number;

  /**
   * 模板分类(字典 告知书、公证事项、其他)
   */
  classify?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



