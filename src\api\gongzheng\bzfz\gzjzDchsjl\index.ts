import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzDchsjlVO, GzjzDchsjlForm, GzjzDchsjlQuery } from '@/api/gongzheng/bzfz/gzjzDchsjl/types';

/**
 * 查询公证卷宗-调查核实记录列表
 * @param query
 * @returns {*}
 */

export const listGzjzDchsjl = (query?: GzjzDchsjlQuery): AxiosPromise<GzjzDchsjlVO[]> => {
  return request({
    url: '/bzfz/gzjzDchsjl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-调查核实记录详细
 * @param id
 */
export const getGzjzDchsjl = (id: string | number): AxiosPromise<GzjzDchsjlVO> => {
  return request({
    url: '/bzfz/gzjzDchsjl/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-调查核实记录
 * @param data
 */
export const addGzjzDchsjl = (data: GzjzDchsjlForm) => {
  return request({
    url: '/bzfz/gzjzDchsjl',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-调查核实记录
 * @param data
 */
export const updateGzjzDchsjl = (data: GzjzDchsjlForm) => {
  return request({
    url: '/bzfz/gzjzDchsjl',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-调查核实记录
 * @param id
 */
export const delGzjzDchsjl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/bzfz/gzjzDchsjl/' + id,
    method: 'delete'
  });
};
