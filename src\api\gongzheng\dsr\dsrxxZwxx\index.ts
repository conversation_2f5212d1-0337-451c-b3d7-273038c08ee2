import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxZwxxVO, DsrxxZwxxForm, DsrxxZwxxQuery } from '@/api/gongzheng/dsr/dsrxxZwxx/types';

/**
 * 查询当事人-指纹信息列表
 * @param query
 * @returns {*}
 */

export const listDsrxxZwxx = (query?: DsrxxZwxxQuery): AxiosPromise<DsrxxZwxxVO[]> => {
  return request({
    url: '/dsr/dsrxxZwxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-指纹信息详细
 * @param id
 */
export const getDsrxxZwxx = (id: string | number): AxiosPromise<DsrxxZwxxVO> => {
  return request({
    url: '/dsr/dsrxxZwxx/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-指纹信息
 * @param data
 */
export const addDsrxxZwxx = (data: DsrxxZwxxForm) => {
  return request({
    url: '/dsr/dsrxxZwxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-指纹信息
 * @param data
 */
export const updateDsrxxZwxx = (data: DsrxxZwxxForm) => {
  return request({
    url: '/dsr/dsrxxZwxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-指纹信息
 * @param id
 */
export const delDsrxxZwxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxZwxx/' + id,
    method: 'delete'
  });
};
