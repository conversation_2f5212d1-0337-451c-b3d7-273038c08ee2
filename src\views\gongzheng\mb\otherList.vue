<!-- 其他模板管理页面 -->
<template>
  <div class="template-container">
    <div v-show="showSearch" class="mb-[10px]">
      <el-card shadow="hover">
        <el-form ref="queryFormRef" :model="searchForm" :inline="true">
          <el-form-item label="模板名称" prop="title">
            <el-input v-model="searchForm.title" placeholder="请输入模板名称" clearable @keyup.enter="handleSearch" />
          </el-form-item>
          <!-- <el-form-item label="业务类型" prop="xm">
              <el-select v-model="searchForm.classify" placeholder="请选择业务类型" clearable>
                <el-option v-for="dict in gz_mb_classify" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <div class="template-list">
      <div class="list-header">
        <div class="header-buttons">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
      </div>
      <el-table :data="templateList" border stripe @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" width="320" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">修改</el-button>

            <el-button type="primary" v-if="scope.row.defaultStatus===0||scope.row.defaultStatus==null" link
              @click="handleDefault(scope.row)">设为默认</el-button>
            <el-button type="primary" link size="small" @click="handleDownload(scope.row)">下载</el-button>
            <el-button type="text" size="small" @click="handleViewWord(scope.row)">查看文档</el-button>
            <el-button type="text" size="small" @click="handleEditWord(scope.row)">编辑文档</el-button>

            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            <!-- <el-button type="primary" link @click="handleSign(scope.row)">电子签章</el-button> -->
          </template>
        </el-table-column>
        <el-table-column prop="title" label="模板名称" align="center" />
        <el-table-column prop="classify" label="模板分类" align="center">
          <template #default="scope">
            <dict-tag :options="gz_mb_classify" :value="scope.row.classify" />
          </template>
        </el-table-column>
        <el-table-column prop="gzlb" label="文档类别" align="center">
          <template #default="scope">
            <dict-tag :options="gz_mb_wdlb" :value="scope.row.gzlb" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传日期" align="center" />
        <el-table-column prop="updateTime" label="修改日期" align="center" />
        <el-table-column prop="defaultStatus" label="是否默认" align="center">
          <template #default="scope">
            <span>{{ scope.row.defaultStatus === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination background layout="prev, pager, next, sizes, total, jumper" :total="total"
          :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange" @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 新增/编辑模板对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增模板' : '编辑模板'" width="800px">
      <el-form :model="templateForm" label-width="100px">
        <!-- <el-form-item label="模板分类">
          <el-select v-model="templateForm.classify" placeholder="模板分类">
            <el-option
              v-for="dict in gz_mb_classify"
              :key="dict.value"
              :label="dict.label"
              :value="Number(dict.value)"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="公证类别" v-if="templateForm.classify===1">
          <el-select v-model="templateForm.gzlb" placeholder="请选择公证类别" style="width: 200px;"
            @change="handleChangeCategory">
            <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label"
              :value="Number(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板业务" v-if="templateForm.classify===2 || templateForm.classify===1">
          <div class="ywDIvCss">
            <Tree v-if="templateForm.classify===2" ref="gzsTreeRef" :selectId="selectId" :showCheckBox="false"
              @onClick="handleClickTree"></Tree>
            <GzTree v-if="templateForm.classify===1" ref="gzsTreeRef" :selectId="selectId" :showCheckBox="false"
              @onClick="handleClickTree"></GzTree>
          </div>
        </el-form-item>
        <el-form-item label="文档类别">
          <el-select filterable v-model="templateForm.docCategory" placeholder="请选择文档类别">
            <el-option v-for="dict in gz_mb_wdlb" :key="dict.value" :label="dict.label" :value="Number(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板名称" required>
          <el-input v-model="templateForm.title" placeholder="请输入模板名称" />
        </el-form-item>
        <!-- <el-form-item label="模板内容">
          <el-input
            v-model="templateForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入模板内容"
          />
        </el-form-item> -->
        <el-form-item label="模板文档">
          <el-upload ref="uploadRef" :action="uploadFileUrl" :headers="headers" :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList" :show-file-list="true"
            :limit="1" class="upload-demo">
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">请上传模板文档</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否默认">
          <el-radio-group v-model="templateForm.defaultStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { ElFormInstance } from 'element-plus'
  import type { ComponentInternalInstance } from 'vue'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import * as api from '@/api/gongzheng/mb/qt'
  import { Template, TemplateQuery, TemplateSaveParams, Role } from '@/api/gongzheng/mb/qt/types'
  import { getToken } from '@/utils/auth'
  import { useAppStore } from '@/store/modules/app'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_mb_wdlb, gz_mb_mblx, gz_mb_classify, gz_gzlb } = toRefs<any>(proxy?.useDict('gz_mb_wdlb', 'gz_mb_mblx', 'gz_mb_classify', 'gz_gzlb'));
  import Tree from '@/views/gongzheng/basicdata/gzs/components/tree.vue';
  import GzTree from '@/views/gongzheng/basicdata/gzsx/components/gzsx_tree2.vue';
import { docOpenEdit, docOpenShow } from '@/views/gongzheng/doc/DocEditor'
  const gzsTreeRef = ref<InstanceType<typeof Tree> | null>(null);
  const gzTreeRef = ref<InstanceType<typeof GzTree> | null>(null);
  const queryFormRef = ref<ElFormInstance>();
  // 上传文件相关
  const appStore = useAppStore()
  const uploadRef = ref()
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload')
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  })
  const fileList = ref([]);
  const selectId = ref('');
  const showSearch = ref(true);
  // 搜索表单
  const searchForm = reactive({
    title: '',
    classify: "99"
  })

  // 模板列表数据
  const templateList = ref<Template[]>([])


  // 分页相关
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const selectedTemplates = ref<Template[]>([])

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const templateForm = reactive<TemplateSaveParams>({
    id: '',
    title: '',
    templateType: 0,
    classify: 99,//其他
    content: '',
    defaultStatus: 0,
    docCategory: null,
    fileUrl: '',
    fileName: '',
    ywId: null,
    wdOssId: null,
    gzlb: null
  })

  // 查询模板列表
  const fetchList = async () => {
    const params : TemplateQuery = {
      title: searchForm.title,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      classify: searchForm.classify
    }
    const res = await api.listTemplates(params)
    templateList.value = res.rows
    total.value = res.total
  }

  // 处理搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchList()
  }

  // 重置搜索
  const resetSearch = () => {
    queryFormRef.value?.resetFields()
    handleSearch()
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection : Template[]) => {
    selectedTemplates.value = selection
  }

  // 处理分页变化
  const handleCurrentChange = (val : number) => {
    currentPage.value = val
    fetchList()
  }

  // 处理每页显示数量变化
  const handleSizeChange = (val : number) => {
    pageSize.value = val
    currentPage.value = 1
    fetchList()
  }

  // 处理新增
  const handleAdd = () => {
    dialogType.value = 'add'
    templateForm.id = ''
    templateForm.title = ''
    templateForm.templateType = 0
    templateForm.classify = 99
    templateForm.content = ''
    templateForm.defaultStatus = 0
    templateForm.fileUrl = ''
    templateForm.fileName = ''
    templateForm.wdOssId = null
    templateForm.docCategory = null
    templateForm.ywId = null
    templateForm.gzlb = null
    fileList.value = []
    dialogVisible.value = true
  }

  // 处理编辑
  const handleEdit = async (row : Template) => {
    try {
      dialogType.value = 'edit'

      // 通过接口获取模板详情
      const res = await api.getTemplate(row.id)
      const templateData = res.data

      // 填充表单数据
      templateForm.id = templateData.id
      templateForm.title = templateData.title
      templateForm.ywId = templateData.ywId
      templateForm.templateType = templateData.templateType
      templateForm.classify = templateData.classify
      templateForm.content = templateData.content
      templateForm.defaultStatus = templateData.defaultStatus
      templateForm.gzlb = templateData.gzlb
      templateForm.wdOssId = templateData.wdOssId
      templateForm.fileUrl = templateData.fileUrl || ''
      templateForm.fileName = templateData.fileName || ''
      templateForm.docCategory = templateData.docCategory

      // 设置选中的业务ID
      selectId.value = templateData.ywId || ''

      // 如果有文件，添加到文件列表中
      fileList.value = []
      if (templateData.fileName && templateData.fileUrl) {
        fileList.value.push({
          name: templateData.fileName,
          url: templateData.fileUrl
        })
      }

      dialogVisible.value = true
    } catch (error) {
      console.error('获取模板详情失败', error)
      ElMessage.error('获取模板详情失败，请重试')
    }
  }

  // 文件上传前的处理
  const handleBeforeUpload = (file) => {
    const isValidSize = file.size / 1024 / 1024 < 10
    if (!isValidSize) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      return false
    }
    return true
  }

  // 文件上传成功的处理
  const handleUploadSuccess = (res, file) => {
    if (res.code === 200) {
      templateForm.fileUrl = res.data.path
      templateForm.fileName = res.data.fileName || file.name
      templateForm.wdOssId = res.data.ossId
      ElMessage.success('文件上传成功')
    } else {
      ElMessage.error('文件上传失败')
    }
  }

  // 文件上传失败的处理
  const handleUploadError = () => {
    ElMessage.error('文件上传失败')
  }

  const handleEditWord = (row) => {
    if (row.wdPath) {
      docOpenEdit(row.wdPath)
    }
  }
  const handleViewWord = (row) => {
    if (row.wdPath) {
      docOpenShow(row.wdPath)
    }
  }
  // 处理下载模板
  const handleDownload = async (row : any) => {
    if (row.wdOssId == null) {
      ElMessage.error('文件不存在')
    } else {
      proxy?.$download.oss(row.wdOssId);
    }

  }
  // 处理删除
  const handleDelete = async (row : Template) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除模板"${row.title}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      await api.deleteTemplate(row.id)
      ElMessage.success('删除模板成功')
      fetchList() // 刷新列表
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除模板失败', error)
        ElMessage.error('删除失败，请重试')
      }
    }
  }

  // 处理设为默认
  const handleDefault = async (row : Template) => {
    try {
      await ElMessageBox.confirm(
        `确定要将模板"${row.title}"设为默认吗？`,
        '设为默认确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        }
      )

      await api.setDefault(row.id)
      ElMessage.success('设为默认成功')
      fetchList() // 刷新列表
    } catch (error) {
      if (error !== 'cancel') {
        console.error('设为默认失败', error)
        ElMessage.error('设为默认失败，请重试')
      }
    }
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!templateForm.title) {
      ElMessage.warning('请输入模板名称')
      return
    }

    try {
      if (dialogType.value === 'add') {
        await api.addTemplate(templateForm)
        ElMessage.success('新增模板成功')
      } else {
        await api.updateTemplate(templateForm)
        ElMessage.success('编辑模板成功')
      }
      dialogVisible.value = false
      fetchList() // 刷新列表
    } catch (error) {
      console.error('保存模板失败', error)
      ElMessage.error('操作失败，请重试')
    }
  }


  const handleClickTree = (data) => {
    console.log("handleClickTree 选中：", data)
    if (data) {
      nextTick(() => {
        selectId.value = data.id;
        templateForm.ywId = data.id;
      })
    }
  }
  const handleChangeCategory = (_value) => {
    //刷新 公证事项树
    gzsTreeRef.value?.getList(_value);
  }

  // 组件挂载时
  onMounted(() => {
    fetchList()
  })
</script>

<style scoped>
  .template-container {
    padding: 20px;
  }

  .search-container {
    margin-bottom: 20px;
  }

  .search-form {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .label {
    font-weight: 500;
    color: #606266;
  }

  .template-list {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .header-buttons {
    display: flex;
    gap: 10px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .ywDIvCss {
    max-height: 300px;
    border: 1px solid #606266;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .ywDIvCss :deep(.el-tree) {
    overflow: visible !important;
    max-height: none !important;
  }

  .ywDIvCss :deep(.el-scrollbar) {
    height: auto !important;
  }

  .ywDIvCss :deep(.el-scrollbar__wrap) {
    overflow: visible !important;
  }
</style>
