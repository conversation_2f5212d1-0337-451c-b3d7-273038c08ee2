<template>
  <el-table :data="items" border stripe style="width: 100%">
 <el-table-column type="index" label="序号" width="60" align="center" />
 <el-table-column label="卷宗号" prop="jzbh" />
 <el-table-column label="公证书编号" prop="gzsbh" />
 <el-table-column label="当事人" prop="dsrxm" />
 <el-table-column label="公证事项" prop="gzsx" />
 <el-table-column label="公证员" prop="gzyxm" />
 <el-table-column label="助理/受理人" prop="zlxm" />
  </el-table>
</template>

<script setup lang="ts">
interface Props {
  items: any[];
}

defineProps<Props>();
</script>
