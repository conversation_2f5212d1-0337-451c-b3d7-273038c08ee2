import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZxdxxVO, ZxdxxForm, ZxdxxQuery } from '@/api/gongzheng/zxdxx/types';

/**
 * 查询公证-咨询单信息列表
 * @param query
 * @returns {*}
 */

export const listZxdxx = (query?: ZxdxxQuery): AxiosPromise<ZxdxxVO[]> => {
  return request({
    url: '/gongzheng/zxdxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证-咨询单信息详细
 * @param zxId
 */
export const getZxdxx = (zxId: string | number): AxiosPromise<ZxdxxVO> => {
  return request({
    url: '/gongzheng/zxdxx/' + zxId,
    method: 'get'
  });
};

/**
 * 新增公证-咨询单信息
 * @param data
 */
export const addZxdxx = (data: ZxdxxForm) => {
  return request({
    url: '/gongzheng/zxdxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证-咨询单信息
 * @param data
 */
export const updateZxdxx = (data: ZxdxxForm) => {
  return request({
    url: '/gongzheng/zxdxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证-咨询单信息
 * @param zxId
 */
export const delZxdxx = (zxId: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/zxdxx/' + zxId,
    method: 'delete'
  });
};
