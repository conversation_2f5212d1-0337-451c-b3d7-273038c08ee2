<template>
  <div>

    <el-table :data="list" style="width: 100%" border highlight-current-row height="300px" size="small">
      <el-table-column label="咨询人" prop="matter" min-width="150" align="center" />
      <el-table-column label="接待人" prop="notaryNumber" width="120" align="center" />
      <el-table-column label="公证类别" prop="copies" width="80" align="center" />
      <el-table-column label="公证事项" prop="copies" width="80" align="center" />
      <el-table-column label="使用地" prop="copies" width="80" align="center" />
      <el-table-column label="咨询时间" prop="notaryFee" width="80" align="center" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup lang="ts">
  import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_nf, gz_gzs_bh_jg } = toRefs<any>(proxy?.useDict('gz_nf', 'gz_gzs_bh_jg'));

  const initFormData : GzjzJbxxForm = {
    id: undefined
  }
  const data = reactive<PageData<GzjzJbxxForm, GzjzJbxxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,

    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const total = ref(0)
  const list = ref([])
  const handleQuery = () => {

  }
  const resetQuery = () => {

  }
  const getList = () => { }
</script>

<style>
</style>
