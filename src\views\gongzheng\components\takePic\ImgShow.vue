<template>
  <div class="w-full h-full flex flex-col items-center justify-center">
    <div class="w-full flex-1 flex justify-center items-center bg-gray-500 overflow-hidden">
      <el-image
        :src="props.src"
        fit="contain"
        class="w-full h-full"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[props.src]"
        show-progress
        :initial-index="0"
      />
    </div>
    <div class="h-36px w-full flex gap-10px justify-center items-center bg-gray-200">
      <el-button @click="cancelToCut" type="primary" size="small">重新剪切</el-button>
      <el-button @click="cancel" size="small">取消重拍</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">

const props = defineProps({
  src: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['cancel', 'cancelToCut'])

const cancelToCut = () => {
  emit('cancelToCut')
}

const cancel = () => {
  emit('cancel')
}
</script>

