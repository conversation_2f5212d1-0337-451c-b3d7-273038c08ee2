<template>
  <div class="p-2">
    <div class="container-wrapper">
      <div class="container">
        <div class=" imgDiv" v-for="(im,index) of dsrxxZrrZpList" :key="index">
          <el-image style="width: 100px; height: 100px;" :src="im.zp" :fit="fit"></el-image>
          <el-checkbox v-model="ids" :value="im.id"></el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="DsrxxZrrZp" lang="ts">
  import { number } from 'vue-types';
  import { listDsrxxZrrZp, delDsrxxZrrZp, addDsrxxZrrZp, updateDsrxxZrrZp } from '@/api/gongzheng/dsr/dsrxxZrrZp';
  import { DsrxxZrrZpVO, DsrxxZrrZpQuery, DsrxxZrrZpForm } from '@/api/gongzheng/dsr/dsrxxZrrZp/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const dsrxxZrrZpList = ref<DsrxxZrrZpVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const ids = ref<Array<string | number>>([]);
  const total = ref(0);
  const fit = ref("cover");
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxZrrZpFormRef = ref<ElFormInstance>();
  interface Props {
    vo : DsrxxZrrZpVO;
    dialigEdit : boolean;
  }
  const props = defineProps<Props>();
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrxxZrrZpForm = {
    id: undefined,
    zp: undefined,
    dsrId: undefined
  }
  const data = reactive<PageData<DsrxxZrrZpForm, DsrxxZrrZpQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 100,
      dsrId: undefined,
      params: {
      }
    }

  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询当事人照片列表 */
  const getList = async () => {
    dsrxxZrrZpList.value = [];
    console.log(props.vo.id)
    if (props.vo.id != null) {
      queryParams.value.dsrId = props.vo.id;
      loading.value = true;
      const res = await listDsrxxZrrZp(queryParams.value);
      dsrxxZrrZpList.value = res.rows;
      total.value = res.total;
    }
    loading.value = false;
  }
  /** 提交按钮 */
  const submitForm = async () => {
    buttonLoading.value = true;
    if (form.value.id) {
      await updateDsrxxZrrZp(form.value).finally(() => buttonLoading.value = false);
    } else {
      await addDsrxxZrrZp(form.value).finally(() => buttonLoading.value = false);
    }
    proxy?.$modal.msgSuccess("上传成功");
    dialog.visible = false;
    await getList();
  }

  // 定义事件
  const emits = defineEmits<{
    (e : 'update-count', []) : void;
  }>();

  const init = (vo : DsrxxZrrZpVO) => {
    if (vo.id) {
      form.value = vo;
    }
  };

  // 拍照
  const savePhoto = (dsrId) => {


  };
  // 上传
  const uploadImg = (dsrId) => {
    form.value.dsrId = dsrId;
    submitForm();
  };
  //删除
  const delPics = async (dsrId) => {
    if (dsrId != null && dsrId.length > 0) {
      await proxy?.$modal.confirm('是否确认删除当事人照片编号为"' + dsrId + '"的数据项？').finally(() => loading.value = false);
      await delDsrxxZrrZp(dsrId);
      proxy?.$modal.msgSuccess("删除成功");
      await getList();
    } else {
      proxy?.$modal.msgError("请选择需要删除的照片");
    }
  };
  const updateForm = () => {
    console.log(ids.value)
    emits('update-count', ids.value);
  };


  watch(ids, (value) => {
    if (value) {
      updateForm();
    }
  },
    { deep: true });
  // 显式暴露方法给父组件
  defineExpose({
    init,
    savePhoto,
    uploadImg,
    delPics,
    getList
  });

  onMounted(() => {
    init(props.vo);
    getList();
  });
</script>
<style scoped>
  .container-wrapper {
    max-height: 300px;
    /* 设置最大高度 */
    overflow-y: auto;
    /* 垂直方向溢出时显示滚动条 */
    margin-bottom: 20px;
  }

  .container {
    display: flex;
    /* 启用Flex布局 */
    justify-content: left;
    /* 水平居中 */
    flex-wrap: wrap;
    /* 允许换行 */
    gap: 20px;
    /* 元素间距 */
  }

  .imgDiv {
    display: flex;
    flex-direction: column;
    /* 垂直排列图片和复选框 */
    align-items: center;
    /* 内部元素居中 */
    text-align: center;
    /* 文本居中 */
  }
</style>
