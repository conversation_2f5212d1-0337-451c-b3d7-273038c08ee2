<template>
  <el-card>
    <template #header>
      <strong class="text-base">涉外使用水印纸清单</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="syzbh" label="水印纸编号" align="center" />
      <el-table-column prop="gzs_bh" label="公证书编号" align="center" />
      <el-table-column prop="sysj" label="使用日期" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
