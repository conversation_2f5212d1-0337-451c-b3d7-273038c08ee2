# popEmpty 组件使用帮助文档

## 1. 组件简介
`popEmpty` 是一个基础的弹出窗口组件，可作为其他弹窗组件的模板使用，包含基本的打开、关闭、保存等功能。

## 2. 调用方式（从 index.vue 中调用）

### 2.1 引入组件
首先需要在父组件（如 index.vue）中引入 `popEmpty` 组件：
```typescript
import popEmpty from './popEmpty'
```

### 2.2 注册组件并声明引用
在模板中注册组件并声明引用变量：
```vue
<template>
  <!-- 其他代码 -->
  <popEmpty ref="popEmptyRef" v-if="showPopEmpty" @close="showPopEmpty=false"></popEmpty>
</template>

<script setup lang="ts">
  // 声明组件引用
  const popEmptyRef = ref(null)
  // 控制组件是否显示的变量
  const showPopEmpty = ref(false)
</script>
```

### 2.3 打开弹窗
通过调用组件的 `open` 方法打开弹窗：
```typescript
// 打开演示窗口
const handleShowPopEmpty = () => {
  // 显示组件
  showPopEmpty.value = true;
  // 等待DOM更新后调用open方法，可以在open中传入一些窗口需要的参数，显式传参数可避免一些参数顺序的问题；
  proxy.$nextTick(() => {
    popEmptyRef.value?.open();
  })
}
```

### 2.4 绑定按钮事件
在模板中添加按钮并绑定打开事件：
```vue
<el-button type="primary" @click="handleShowPopEmpty">打开空的窗口</el-button>
```

## 3. 事件处理

### 3.1 关闭事件
通过 `@close` 事件监听弹窗关闭：
```vue
<popEmpty 
  ref="popEmptyRef" 
  v-if="showPopEmpty" 
  @close="showPopEmpty=false"
></popEmpty>
```

### 3.2 成功事件
可监听 `success` 事件获取保存成功的通知：
```vue
<popEmpty 
  ref="popEmptyRef" 
  v-if="showPopEmpty" 
  @close="showPopEmpty=false"
  @success="handleSuccess"
></popEmpty>
```

```typescript
const handleSuccess = () => {
  // 处理保存成功后的逻辑
  console.log('弹窗保存成功');
}
```

## 4. 注意事项

1. **组件加载时机**：
   - 使用 `v-if="showPopEmpty"` 控制组件是否加载，避免不必要的性能消耗
   - 打开弹窗时，先设置 `showPopEmpty = true`，再在 `$nextTick` 中调用 `open()` 方法，确保组件已挂载

2. **组件通信**：
   - 通过 `defineExpose` 暴露了 `open` 和 `close` 方法供父组件调用
   - 通过 `emits` 定义了 `success` 和 `close` 事件，用于向父组件传递信息

3. **弹窗属性**：
   - 弹窗默认宽度为 `1000px`，高度为 `90%`
   - 支持拖拽（`draggable`）、调整大小（`resize`）和最大化（`showMaximize`）
   - 按 ESC 键可关闭弹窗（`escClosable: true`）

4. **性能优化**：
   - 组件使用 `destroy-on-close` 属性，关闭时会销毁组件，释放资源
   - 建议在不需要使用弹窗时将 `showPopEmpty` 设置为 `false`，完全卸载组件

5. **方法调用**：
   - 调用组件方法时使用可选链操作符（`?.`），避免组件未加载时的错误
   - 例如：`popEmptyRef.value?.open()` 和 `popEmptyRef.value?.close()`