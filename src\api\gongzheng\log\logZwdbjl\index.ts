import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LogZwdbjlVO, LogZwdbjlForm, LogZwdbjlQuery } from '@/api/gongzheng/log/logZwdbjl/types';

/**
 * 查询日志-指纹对比记录列表
 * @param query
 * @returns {*}
 */

export const listLogZwdbjl = (query?: LogZwdbjlQuery): AxiosPromise<LogZwdbjlVO[]> => {
  return request({
    url: '/log/logZwdbjl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询日志-指纹对比记录详细
 * @param id
 */
export const getLogZwdbjl = (id: string | number): AxiosPromise<LogZwdbjlVO> => {
  return request({
    url: '/log/logZwdbjl/' + id,
    method: 'get'
  });
};

/**
 * 新增日志-指纹对比记录
 * @param data
 */
export const addLogZwdbjl = (data: LogZwdbjlForm) => {
  return request({
    url: '/log/logZwdbjl',
    method: 'post',
    data: data
  });
};

/**
 * 修改日志-指纹对比记录
 * @param data
 */
export const updateLogZwdbjl = (data: LogZwdbjlForm) => {
  return request({
    url: '/log/logZwdbjl',
    method: 'put',
    data: data
  });
};

/**
 * 删除日志-指纹对比记录
 * @param id
 */
export const delLogZwdbjl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/log/logZwdbjl/' + id,
    method: 'delete'
  });
};
