<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" size="small">
            <el-form-item label="卷宗号" prop="jzbh" style="width: 260px;">
              <el-input v-model="queryParams.jzbh" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人" prop="dsrxm" style="width: 260px;">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证类别" prop="lb" style="width: 260px;">
              <el-select v-model="queryParams.lb" placeholder="请选择" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable
                style="max-width: 80px; margin-right: 4px;">
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable
                style="max-width: 140px; margin-right: 4px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery"
                style="max-width: 60px" />号
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm" style="width: 260px;">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable>
                <el-option v-for="item in gzy" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="助理" prop="zlbm" style="width: 260px;">
              <el-select v-model="queryParams.zlbm" placeholder="请选择" clearable>
                <el-option v-for="item in gzyzl" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申请日期" style="width: 340px;">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleDateRangeChange" />
            </el-form-item>
            <el-form-item label="受理日期" style="width: 340px;">
              <el-date-picker v-model="slDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleSlDateRangeChange" />
            </el-form-item>
            <el-form-item label="业务来源" prop="ywly" style="width: 260px;">
              <el-select v-model="queryParams.ywly" placeholder="请选择" clearable>
                <el-option v-for="dict in gz_ywly" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="案件来源" prop="ajly" style="width: 260px;">
              <el-select v-model="queryParams.ajly" placeholder="请选择" clearable>
                <el-option v-for="dict in gz_ajly" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="外部订单号" prop="wbddh" style="width: 260px;">
              <el-input v-model="queryParams.wbddh" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="流程状态" prop="lczt">
              <el-select multiple collapse-tags :max-collapse-tags="2" collapse-tags-tooltip v-model="queryParams.lczt"
                placeholder="请选择" clearable>
                <el-option v-for="dict in gz_sl_lczt" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否作废" prop="sfzf">
              <el-select v-model="queryParams.sfzf" placeholder="全部" clearable style="width: 80px;">
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="只显示零接触" prop="sfljc">
              <el-checkbox v-model="queryParams.sfljc" true-value="1" false-value="0"></el-checkbox>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['gz:sl:query']">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gz:sl:edit']">新增卷宗</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" @click="handleAddJzfb"
              v-hasPermi="['gz:sl:edit']">新增副本</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" @click="handleAddJzbz"
              v-hasPermi="['gongzheng:gzjzJbxx:add']">新增补正</el-button>
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="multiple" @click="handleBatchSj">批量上架</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleCopy">复制卷宗</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown>
              <el-button type="primary">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleAddBz">新增补正</el-dropdown-item>
                  <el-dropdown-item @click="handleAddLsjzFb">新增历史卷宗副本</el-dropdown-item>
                  <el-dropdown-item @click="handleAddLsjzBz">新增历史卷宗补正</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['gongzheng:gzjzJbxx:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <GeneralProcessTable ref="generalProcessTableRef" :view-moudle="'sl'" :action-width="150"
        @selection-change="handleSelectionChange" @row-dblclick="handleEdit">
        <template #my-actions="{ row }">
          <el-button type="primary" link @click="() => handleEdit(row)"
            v-if="['01','02','03','04'].indexOf(row.lczt as string) > -1 && row.sfzf == '0'">编辑</el-button>
          <el-button type="primary" link @click="() => handleNdgzs(row)" v-if="row.lczt=='04' && row.sfzf == '0'"
            style="color: #F56C6C;">拟定公证书</el-button>

        </template>
        <template #my-alerts="{ row }">
          <el-text type="danger">{{ row.ajtx ? `(${row.ajtx})` : '' }}</el-text>
        </template>
        <template #my-remarks="{ row }">
          <el-text @click="() => handleBz(row)" :type="row.yqtxVo && row.yqtxVo.remark ? '' : 'primary'"
            style="cursor: pointer;">{{ row.yqtxVo ? row.yqtxVo.remark || '备注' : '备注' }}</el-text>
        </template>
      </GeneralProcessTable>

    </el-card>

    <!-- 申请与初核对话框 -->
    <el-drawer :title="dialog.title" v-model="dialog.visible" :direction="direction" destroy-on-close header-class="drawer-header"
      :before-close="cancel" size="100%">
      <!-- <template #header="scope">
        <div class="drawer-header">
          <span>{{ scope.title }}</span>
        </div>
      </template> -->
      <Sl v-if="dialog.visible" :key="currentRecordId || 'new'"></Sl>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="18" style="text-align: left;">
              <div v-if="currentRecordId">
                <el-button @click="handleEvidenceMaterial">证据材料</el-button>
                <el-button @click="handleDeliveryInfo">送达信息</el-button>
                <el-button @click="handleReminderInfo">提醒信息</el-button>
                <el-button @click="handleShortInfo">短信预约</el-button>
                <el-button type="danger" @click="handleZf">卷宗作废</el-button>
                <el-button v-if="['03', '04'].indexOf(lczt as string) > -1" :loading="buttonLoading" type="primary"
                  @click="handleWdnd">文档拟定</el-button>
                <el-button v-if="['03', '04'].indexOf(lczt as string) > -1" :loading="buttonLoading" type="primary"
                  @click="handleBl">笔录</el-button>
                <el-button v-if="['03', '04'].indexOf(lczt as string) > -1" :loading="buttonLoading" type="primary"
                  @click="handleDs">代书</el-button>
              </div>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-button :loading="buttonLoading" type="primary" @click="handleFqch"
                v-if="currentRecordId && lczt=='01'">发起初核</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="handleFqsl"
                v-if="currentRecordId && lczt=='02'">发起受理</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="handleFqsc"
                v-if="currentRecordId && lczt=='03'">发起审查</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="() => handleNdgzs()"
                v-if="currentRecordId && lczt=='04'">拟定公证书</el-button>
              <el-button @click="cancel">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 证据材料对话框 -->
    <!-- <el-drawer :title="dialog2.title" v-model="dialog2.visible" :direction="direction" :before-close="cancel2"
      size="100%">
      <ZjclAdd v-if="dialog2.visible" :gzjzId="currentRecordId"></ZjclAdd>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="18" style="text-align: left;">
              <el-button @click="handleApplyInvestigation">申请调查核实</el-button>
              <el-button @click="handleMiniProgramDownload">小程序材料下载</el-button>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-button @click="cancel2">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer> -->
    <!-- 新证据材料对话框 -->
    <NewZjcl v-model="dialog2.visible" v-if="dialog2.visible" :gzjz-id="currentRecordId" />

    <!-- 送达信息对话框 -->
    <SdxxIndex ref="SdxxIndexRef" @callback="sdxxCallback"></SdxxIndex>
    <!-- <el-dialog v-model="dialog3.visible" :title="dialog3.title" width="80%" @close="cancel3">
      <Sdxx v-if="dialog3.visible" ref="sdxxRef"></Sdxx>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm5">保存</el-button>
          <el-button @click="cancel3">关 闭</el-button>
        </div>
      </template>
    </el-dialog> -->

    <!-- 提醒消息对话框 -->
    <el-dialog v-model="dialog4.visible" :title="dialog4.title" width="40%" @close="cancel4">
      <TxMsg v-if="dialog4.visible" ref="txMsgRef"></TxMsg>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm6">保存</el-button>
          <el-button @click="cancel4">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 推送列表对话框 -->
    <SetSmsIndex ref="SetSmsIndexRef" @callback="callbackSetSms" />
    <!-- <el-dialog v-model="dialog5.visible" :title="dialog5.title" width="50%" @close="cancel5">
      <Dxys v-if="dialog5.visible"></Dxys>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm7">保存</el-button>
          <el-button @click="cancel5">关 闭</el-button>
        </div>
      </template>
    </el-dialog> -->

    <!-- 发起初核对话框 -->
    <el-dialog v-model="dialog6.visible" :title="dialog6.title" width="50%" @close="cancel6">
      <Fqch v-if="dialog6.visible" ref="fqchRef"></Fqch>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm8">审核通过</el-button>
          <el-button @click="cancel6">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 确认受理对话框 -->
    <el-dialog v-model="dialog7.visible" :title="dialog7.title" width="30%" @close="cancel7">
      <Qrsl v-if="dialog7.visible" ref="qrslRef"></Qrsl>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm9">确 定</el-button>
          <el-button @click="cancel7">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 核实审查对话框 -->
    <!-- <el-dialog v-model="dialog8.visible" :title="dialog8.title" width="60%" @close="cancel8">
      <div class="review-content">
        <div class="review-section">
          <h4>在核实环节，办证系统应当包括下列核实方式：</h4>
          <el-checkbox v-model="reviewOptions.selectAll1" @change="handleSelectAll1">全选</el-checkbox>
          <div class="checkbox-group">
            <el-checkbox v-model="reviewOptions.option1">（一）询问当事人、公证事项的利害关系人和证人；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option2">（二）向有关单位或者个人了解相关情况或者核实、收集相关书证、物证、视听资料等证明材料；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option3">（三）现场勘验；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option4">（四）委托专业机构或者专业人员鉴定、检验、翻译；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option5">（五）委托异地公证机构核实。</el-checkbox>
          </div>
          <p>相应的核实方式应当有对应核实后证明材料的上传功能。</p>
        </div>

        <div class="review-section">
          <h4>在审查环节，办证系统应当包括对下列内容的确认：</h4>
          <el-checkbox v-model="reviewOptions.selectAll2" @change="handleSelectAll2">全选</el-checkbox>
          <div class="checkbox-group">
            <el-checkbox v-model="reviewOptions.option6">（一）当事人的人数、身份、申请办理公证的资格及相应的权利情况；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option7">（二）当事人的意思表示真实；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option8">（三）申请公证的文书内容完备，含义清晰，签名、印鉴齐全；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option9">（四）提供的证明材料真实、合法、充分；</el-checkbox>
            <el-checkbox v-model="reviewOptions.option10">（五）申请公证的事项真实、合法。</el-checkbox>
          </div>
          <p>具体公证事项审查环节应当确认的内容参见《主要公证业务审查要点与证明材料清单》。</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm10" :loading="buttonLoading">确认核实</el-button>
          <el-button @click="cancel8">取 消</el-button>
        </div>
      </template>
    </el-dialog> -->

    <ScComfirm v-model="dialog8.visible" :title="dialog8.title" @comfirm="comfirmSc" />

    <!-- 文档拟定对话框 -->
    <el-drawer :title="dialog9.title" v-model="dialog9.visible" :direction="direction" :before-close="cancel9"
      size="100%">
      <Wdnd v-if="dialog9.visible"></Wdnd>
      <template #footer>
        <div class="flex items-center justify-end">
          <el-button @click="cancel9">关 闭</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 拟定公证书 -->
    <el-drawer v-model="drawerNdgzs.visible" :title="drawerNdgzs.title" size="100%" :before-close="cancelDrawerNdgzs" destroy-on-close>
      <Ndgzs v-if="drawerNdgzs.visible" ref="ndgzsRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleFqsp()">发起审批</el-button>
          <!-- <el-button @click="() => {}">电子卷宗</el-button>
          <el-button @click="() => {}">批量证</el-button>
          <el-button @click="() => {}">打印</el-button> -->
          <el-button @click="genQfgEv">生成签发稿</el-button>
          <el-button @click="handleSqfy()">申请翻译</el-button>
          <!-- <el-button @click="() => {}">卷宗作废</el-button> -->
          <el-button @click="cancelDrawerNdgzs">关 闭</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 发起审批对话框 -->
    <fqsp-dialog ref="fqspDialogRef" @callback="fqspCallback"></fqsp-dialog>

    <!-- 笔录对话框 -->
    <BlDialog v-if="dialog10.visible" :title="dialog10.title" v-model="dialog10.visible" :gzjz-id="currentRecordId" />

    <!-- 代书 -->
    <DsDialog v-if="dsDialogState.visible" v-model="dsDialogState.visible" :title="dsDialogState.title"
      :gzjz-id="currentRecordId" />

    <!-- 申请翻译对话框 -->
    <sqfy-dialog ref="sqfyDialogRef" @callback="sqfyCallback" @closed="sqfyClosed"></sqfy-dialog>

    <!-- 详情对话框 - 使用共用组件 -->
    <!-- <DetailDrawer
      v-model:visible="dialog11.visible"
      :title="dialog11.title"
      :current-record-id="currentRecordId"
      :lczt="lczt"
      page-type="sl"
      @refresh="getList"
      @evidence-material="handleEvidenceMaterial"
      @delivery-info="handleDeliveryInfo"
      @reminder-info="handleReminderInfo"
      @short-info="handleShortInfo"
      @invalidation="handleInvalidationSubmit"
      @document-drafting="handleWdnd"
      @transcript="handleBl"
      @ghostwriting="handleDs"
      @draft-notarization="handleNdgzs"
      @initiate-acceptance="handleFqsl"
      @initiate-review="handleFqsc"
      @initiate-approval="handleSbsp"
      @initiate-production="handleTjZz"
      @initiate-signed="handleTjFz"
      @initiate-archiving="handleTjgd"
      @apply-investigation="handleApplyInvestigation"
      @mini-program-download="handleMiniProgramDownload"
      @submit-delivery="submitForm5"
      @submit-reminder="submitForm6"
      @submit-short-info="submitForm7"
    /> -->

    <!-- 制证详情对话框 -->
    <el-drawer :title="dialog12.title" v-model="dialog12.visible" :direction="direction" :before-close="cancel12"
      size="100%">
      <ZzDetail v-if="dialog12.visible"></ZzDetail>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="24" style="text-align: left;">
              <el-button @click="cancel12">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 卷宗作废对话框 -->
    <el-dialog v-model="dialogZfyy.visible" :title="dialogZfyy.title" width="40%" @close="cancelZfyy">
      <el-form label-width="100px">
        <el-form-item label="作废原因" required>
          <el-input v-model="zfyyForm.zfyy" type="textarea" :rows="4" placeholder="请输入作废原因" maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitZfyy" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelZfyy">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 上报审批对话框 -->
    <el-dialog v-model="dialogSbsp.visible" :title="dialogSbsp.title" width="40%" @close="cancelSbsp">
      <div class="confirmation-content">
        <p>确认要上报审批当前卷宗吗？</p>
        <el-form label-width="100px">
          <el-form-item label="流程意见">
            <el-input v-model="sbspForm.lcyj" type="textarea" :rows="3" placeholder="请输入流程意见" maxlength="500"
              show-word-limit />
          </el-form-item>
          <el-form-item label="是否通过">
            <el-radio-group v-model="sbspForm.sftg">
              <el-radio label="1">通过</el-radio>
              <el-radio label="0">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSbsp" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelSbsp">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提交制证对话框 -->
    <el-dialog v-model="dialogTjZz.visible" :title="dialogTjZz.title" width="40%" @close="cancelTjZz">
      <div class="confirmation-content">
        <p>确认要提交制证当前卷宗吗？</p>
        <el-form label-width="100px">
          <el-form-item label="流程意见">
            <el-input v-model="tjZzForm.lcyj" type="textarea" :rows="3" placeholder="请输入流程意见" maxlength="500"
              show-word-limit />
          </el-form-item>
          <el-form-item label="是否通过">
            <el-radio-group v-model="tjZzForm.sftg">
              <el-radio label="1">通过</el-radio>
              <el-radio label="0">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTjZz" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelTjZz">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提交发证对话框 -->
    <el-dialog v-model="dialogTjFz.visible" :title="dialogTjFz.title" width="30%" @close="cancelTjFz">
      <div class="confirmation-content">
        <p>确认要提交发证当前卷宗吗？</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTjFz" :loading="buttonLoading">确认</el-button>
          <el-button @click="cancelTjFz">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提交归档对话框 -->
    <el-dialog v-model="dialogTjgd.visible" :title="dialogTjgd.title" width="30%" @close="cancelTjgd">
      <div class="confirmation-content">

        <p>确认要提交归档当前卷宗吗？</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTjgd" :loading="buttonLoading">确认</el-button>
          <el-button @click="cancelTjgd">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备注编辑对话框 -->
    <el-dialog v-model="dialogJzbz.visible" :title="dialogJzbz.title" width="30%" @close="cancelJzbz">
      <div>
        <h4>卷宗号：{{ jzbzInfo.jzbh }}</h4>
        <el-input :rows="6" type="textarea" :maxlength="100" :show-word-limit="true" placeholder="请输入备注信息"
          v-model="jzbzInfo.remark" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitJzbz" :loading="buttonLoading">确认</el-button>
          <el-button @click="cancelJzbz">取消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="GzjzJbxx" lang="ts">
  import { provide, nextTick } from 'vue';
  import {
    listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx, updateGzjzBz,
    initiateReview, invalidation, initiateApproval, initiateProduction, initiateSigned, initiateArchiving
  } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import type { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

  import Sl from '@/views/gongzheng/gongzheng/components/sl/sl.vue';
  import ZjclAdd from '@/views/gongzheng/gongzheng/components/sl/zjcl/index.vue';
  import NewZjcl from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/index.vue';
  import SdxxIndex from '@/views/gongzheng/gongzheng/components/sl/sdxx/popIndex.vue';
  import TxMsg from '@/views/gongzheng/gongzheng/components/sl/tx_msg.vue';
  // import Dxys from '@/views/gongzheng/gongzheng/components/sl/dxys.vue';
  import SetSmsIndex from '@/views/gongzheng/gongzheng/components/set_sms/popIndex.vue';
  import Fqch from '@/views/gongzheng/gongzheng/components/sl/fqch.vue';
  import Qrsl from '@/views/gongzheng/gongzheng/components/sl/qrsl.vue';
  import Wdnd from '@/views/gongzheng/gongzheng/components/sl/wdnd/index.vue';
  import SlDetail from '@/views/gongzheng/gongzheng/components/jz/detail.vue';
  import ZzDetail from '@/views/gongzheng/gongzheng/components/zz/index.vue';
  import DetailDrawer from '@/views/gongzheng/gongzheng/components/sl/DetailDrawer.vue';
  import Ndgzs from '@/views/gongzheng/gongzheng/components/sl/ndgzs/index.vue';
  import FqspDialog from '@/views/gongzheng/gongzheng/components/sl/FqspDialog.vue';
  import GeneralProcessTable from '@/views/gongzheng/gongzheng/components/GeneralProcessTable.vue';
  import { genYearOptions, clearEmptyProperty, dictMapFormat, genRecentDate } from '@/utils/ruoyi';
  import DsDialog from '@/views/gongzheng/gongzheng/components/sl/ds/index.vue';
  import BlDialog from '@/views/gongzheng/gongzheng/components/sl/bl/index.vue';
  import ScComfirm from './mod/ScComfirm.vue';
  import SqfyDialog from '@/views/gongzheng/gongzheng/components/sl/SqfyDialog.vue';
  import eventBus from '@/utils/eventBus';

  const gzsbh_years = genYearOptions(2015);

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzs_zh, gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt', 'gz_gzlb'));
  const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

  const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');

  const rangeDate = genRecentDate('-3M');
  const dateRange = ref<[string, string] | null>(rangeDate);
  const slDateRange = ref<[string, string] | null>(rangeDate);

  const currentRecord = ref<GzjzJbxxVO>(null);
  // 当前编辑的记录ID，用于传递给sl组件
  const currentRecordId = ref<string | number | null>(null);
  const gzlbId = ref<string | number | null>(null);
  // 流程状态
  const lczt = ref<string | number | null>(null);
  const queryFormRef = ref<ElFormInstance>();

  const fqspDialogRef = ref<InstanceType<typeof FqspDialog>>(null);
  const generalProcessTableRef = ref<InstanceType<typeof GeneralProcessTable>>(null);
  const SetSmsIndexRef = ref<InstanceType<typeof SetSmsIndex>>(null);

  const sqfyDialogRef = ref<InstanceType<typeof SqfyDialog>>(null);

  const ndgzsRef = ref(null)

  const fqspMark = ref(false);

  // 对话框状态管理
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  // const dialog3 = reactive<DialogOption>({
  //   visible: false,
  //   title: ''
  // });

  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog6 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog7 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog8 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog9 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog10 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dsDialogState = reactive({
    visible: false,
    title: '代书'
  });

  const dialog11 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialog12 = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  // 新增对话框状态
  const dialogZfyy = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialogSbsp = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialogTjZz = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialogTjFz = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialogTjgd = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const dialogJzbz = reactive<DialogOption>({
    visible: false,
    title: ''
  })

  const jzbzInfo = reactive({
    id: undefined,
    jzbh: undefined,
    remark: undefined
  })

  // 查询参数
  const queryParams = ref<GzjzJbxxQuery>({
    pageNum: 1,
    pageSize: 10,
    jzbh: undefined,
    gzsbh: undefined,
    lb: undefined,
    dsrxm: undefined,
    sqrxm: undefined,
    gzyxm: undefined,
    zlxm: undefined,
    lczt: ['01', '02', '03', '04'],
    ywly: undefined,
    ajly: undefined,
    wbddh: undefined,
    sfljc: undefined,
    sqsj: undefined,
    slrq: undefined,
    sfzf: '0',
    params: {
      nf: undefined,
      zh: undefined
    }
  });

  // 核实审查选项
  const reviewOptions = reactive({
    selectAll1: true,
    selectAll2: true,
    option1: true,
    option2: true,
    option3: true,
    option4: true,
    option5: true,
    option6: true,
    option7: true,
    option8: true,
    option9: true,
    option10: true
  });

  // 作废原因表单
  const zfyyForm = reactive({
    zfyy: ''
  });

  // 上报审批表单
  const sbspForm = reactive({
    lcyj: '',
    sftg: '1'
  });

  // 提交制证表单
  const tjZzForm = reactive({
    lcyj: '',
    sftg: '1'
  });

  const genQfgEv = () => {
    ndgzsRef.value?.genQfgDoc();
  }

  /** 查询卷宗基本信息列表 */
  const getList = async () => {
    loading.value = true;
    try {
      // 清理空值参数，避免发送空字符串
      // console.log('发送查询参数queryParams:', queryParams.value);
      const cleanParams = clearEmptyProperty(queryParams.value);

      // 确保基本的分页参数
      cleanParams.pageNum = queryParams.value.pageNum || 1;
      cleanParams.pageSize = queryParams.value.pageSize || 10;

      // 保留params对象用于日期范围查询
      if (queryParams.value.params && Object.keys(queryParams.value.params).length > 0) {
        cleanParams.params = queryParams.value.params;
      }

      // 组合公证书编号
      // const { nf, zh, ls } = clearEmptyProperty(cleanParams.params);
      // const gzsbh = `${nf ? `（${nf}）` : ''}${gz_gzs_zh.value.find(i => i.value === zh)?.label || ''}${ls ? `第${ls}号` : ''}`;
      // if(!!gzsbh) {
      //   cleanParams.gzsbh = gzsbh;
      // }

      const lczt = cleanParams.lczt;
      // 处理流程状态
      if (lczt && Array.isArray(lczt)) {
        cleanParams.lczt = lczt.join(',');
      }
      proxy?.addDateRange(cleanParams, dateRange.value, 'Slrq');
      proxy?.addDateRange(cleanParams, slDateRange.value, 'Sqsj');
      cleanParams.routeCode = 'gz:sl';
      generalProcessTableRef.value?.getList(cleanParams);
    } catch (error : any) {
      console.error('查询失败:', error);
      proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };

  /**
   * 修改/添加卷宗备注
   */
  const gzjzbzUpdater = async () => {
    const { id, gzjzId, remark = '' } = jzbzInfo
    if (!gzjzId) {
      ElMessage.warning('未选择要编辑的卷宗备注');
      return;
    }
    buttonLoading.value = true;
    updateGzjzBz({ id, gzjzId, remark }).then(res => {
      console.log('更新卷宗备注res 》 ', res)
      cancelJzbz();
      getList();
    }).catch((err : any) => {
      ElMessage.error('卷宗备注编辑失败，请重试');
    }).finally(() => {
      buttonLoading.value = false;
    })
  }

  /** 取消按钮 */
  const cancel = () => {
    dialog.visible = false;
    currentRecordId.value = null; // 清空当前记录ID
    gzlbId.value = null; // 清空当前记录ID
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    dateRange.value = rangeDate;
    slDateRange.value = rangeDate;
    queryParams.value.params = {};
    queryParams.lczt = ['01', '02', '03', '04'];
    handleQuery();
  };

  /** 日期范围变化处理 */
  const handleDateRangeChange = (dates : [string, string] | null) => {
    if (dates && dates.length === 2) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginSqsj: dates[0],
        endSqsj: dates[1]
      };
    } else {
      delete queryParams.value.params?.beginSqsj;
      delete queryParams.value.params?.endSqsj;
    }
  };

  /** 受理日期范围变化处理 */
  const handleSlDateRangeChange = (dates : [string, string] | null) => {
    if (dates && dates.length === 2) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginSlrq: dates[0],
        endSlrq: dates[1]
      };
    } else {
      delete queryParams.value.params?.beginSlrq;
      delete queryParams.value.params?.endSlrq;
    }
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzjzJbxxVO[]) => {
    ids.value = selection.map((item : GzjzJbxxVO) => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  };

  /** 日期格式化 */
  const formatDate = (row : any, column : any, cellValue : string) => {
    if (cellValue) {
      const date = new Date(cellValue), year = date.getFullYear(),
        month = String(date.getMonth() + 1).padStart(2, '0'),
        day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
      // return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
    }
    return '';
  };

  /** 新增按钮操作 */
  const handleAdd = () => {
    currentRecordId.value = null; // 清空记录ID，表示新增
    currentRecord.value = null; // 清空记录，表示新增
    gzlbId.value = null; // 清空记录ID，表示新增
    dialog.visible = true;
    dialog.title = "新增卷宗";
  };

  /** 修改按钮操作 */
  const handleEdit = (row ?: GzjzJbxxVO) => {
    if (row?.id) {
      currentRecord.value = row;
      currentRecordId.value = row.id; // 设置记录ID，表示编辑
      gzlbId.value = row.lb; // 设置记录公证类别，表示编辑
      lczt.value = row.lczt;//流程专题
    }
    dialog.visible = true;
    dialog.title = "修改卷宗基本信息";
  };

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzjzJbxxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除选中的卷宗数据？');
    try {
      await delGzjzJbxx(_ids);
      proxy?.$modal.msgSuccess("删除成功");
      await getList();
    } catch (error) {
      proxy?.$modal.msgError("删除失败");
    }
  };

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('/gongzheng/gzjzJbxx/export', {
      ...queryParams.value
    }, `卷宗基本信息_${new Date().getTime()}.xlsx`);
  };

  // 卷宗备注操作
  const handleBz = (row ?: GzjzJbxxVO) => {
    console.log('备注操作', row.jzbh, row.remark);
    if (row.yqtxVo) {
      jzbzInfo.id = row.yqtxVo.id;
      jzbzInfo.gzjzId = row.yqtxVo.gzjzId
      jzbzInfo.jzbh = row.jzbh;
      jzbzInfo.remark = row.yqtxVo.remark;
    } else {
      jzbzInfo.gzjzId = row.id;
      jzbzInfo.jzbh = row.jzbh;
      jzbzInfo.remark = "";
    }
    dialogJzbz.visible = true;
    dialogJzbz.title = '备注编辑';
  };

  const handleAddJzfb = () => {
    ElMessage.warning('新增副本 建设中...');
    console.log('新增副本');
  };
  const handleAddJzbz = () => {
    console.log('新增补正');
  };

  const handleBatchSj = () => {
    console.log('批量上架');
  };

  const handleCopy = () => {
    console.log('复制卷宗');
  };

  const handleAddLsjzFb = () => {
    console.log('新增历史卷宗副本');
  };

  const handleAddLsjzBz = () => {
    console.log('新增历史卷宗补正');
  };

  // 证据材料功能
  const handleEvidenceMaterial = () => {
    dialog2.visible = true;
    dialog2.title = "证据明细";
  };

  const cancel2 = () => {
    dialog2.visible = false;
  };

  const handleApplyInvestigation = () => {
    console.log('申请调查核实');
  };

  const handleMiniProgramDownload = () => {
    console.log('小程序材料下载');
  };

  // 送达信息功能
  const SdxxIndexRef = ref<InstanceType<typeof SdxxIndex>>(null);
  const handleDeliveryInfo = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }
    // dialog3.visible = true;
    // dialog3.title = "送达信息";
    SdxxIndexRef.value.open(currentRecordId.value);
  };
  const sdxxCallback = (options ?: any) => {
    // 没有处理需要处理的
  }

  // const cancel3 = () => {
  //   dialog3.visible = false;
  // };

  // const sdxxRef = ref();
  // const submitForm5 = async () => {
  //   if (sdxxRef.value) {
  //     const result = await sdxxRef.value.saveData();
  //     if (result) {
  //       cancel3();
  //       // 可以选择是否刷新列表
  //       // getList();
  //     }
  //   }
  // };

  // 提醒信息功能
  const handleReminderInfo = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }
    dialog4.visible = true;
    dialog4.title = "提醒信息";
  };

  const cancel4 = () => {
    dialog4.visible = false;
  };

  const txMsgRef = ref();
  const submitForm6 = async () => {
    if (txMsgRef.value) {
      const result = await txMsgRef.value.saveData();
      if (result) {
        cancel4();
        // 可以选择是否刷新列表
        // getList();
      }
    }
  };

  // 短信预约功能
  const handleShortInfo = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }
    SetSmsIndexRef.value.open(currentRecordId.value);
    // dialog5.visible = true;
    // dialog5.title = "推送列表";
  };

  const callbackSetSms = (_options : any) => {
    console.log('短信预约回调：', _options)
  }

  const cancel5 = () => {
    dialog5.visible = false;
  };

  const submitForm7 = () => {
    console.log('保存短信预约');
    cancel5();
  };

  // 卷宗作废
  const handleZf = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    // 重置表单
    zfyyForm.zfyy = '';

    dialogZfyy.visible = true;
    dialogZfyy.title = "卷宗作废";
  };

  // 取消作废对话框
  const cancelZfyy = () => {
    dialogZfyy.visible = false;
  };

  // 提交作废
  const submitZfyy = async () => {
    if (!zfyyForm.zfyy.trim()) {
      ElMessage.warning('请输入作废原因');
      return;
    }

    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value,
        zfyy: zfyyForm.zfyy
      };

      const res = await invalidation(data);
      if (res.code === 200) {
        ElMessage.success('卷宗作废成功');
        cancelZfyy();
        cancel();
        getList(); // 刷新列表
      } else {
        ElMessage.error('卷宗作废失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('卷宗作废失败:', error);
      ElMessage.error('卷宗作废失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  // 处理共用组件的作废提交
  const handleInvalidationSubmit = async (data : { id : string | number; zfyy : string }) => {
    buttonLoading.value = true;
    try {
      const res = await invalidation(data);
      if (res.code === 200) {
        ElMessage.success('卷宗作废成功');
        getList(); // 刷新列表
      } else {
        ElMessage.error('卷宗作废失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('卷宗作废失败:', error);
      ElMessage.error('卷宗作废失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  // 发起初核
  const handleFqch = () => {
    // 从当前选中的行或者第一行获取卷宗ID
    if (!currentRecordId.value && gzjzJbxxList.value.length > 0) {
      currentRecordId.value = gzjzJbxxList.value[0].id;
      gzlbId.value = gzjzJbxxList.value[0].lb;
    }
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }
    dialog6.visible = true;
    dialog6.title = "发起初核";
  };

  const cancel6 = () => {
    dialog6.visible = false;
  };

  const fqchRef = ref();
  const submitForm8 = async () => {
    if (fqchRef.value) {
      const result = await fqchRef.value.handleSubmit();
      console.log(result)
      if (result) {
        cancel6();
        getInfo();
        getList(); // 刷新列表
      }
    }
  };

  // 刷新编辑页内容
  const getInfo = async (id ?: string | number) => {
    let _id = id || currentRecordId.value;
    const info = await getGzjzJbxx(_id);
    currentRecord.value = info.data;
    lczt.value = info.data.lczt;//流程专题
  }

  // 确认受理
  const handleFqsl = () => {
    // 从当前选中的行或者第一行获取卷宗ID
    if (!currentRecordId.value && gzjzJbxxList.value.length > 0) {
      currentRecordId.value = gzjzJbxxList.value[0].id;
      gzlbId.value = gzjzJbxxList.value[0].lb;
    }
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }
    dialog7.visible = true;
    dialog7.title = "确认受理";
  };

  const cancel7 = () => {
    dialog7.visible = false;
  };

  const qrslRef = ref();
  const submitForm9 = async () => {
    console.log('submitForm9 被调用');
    if (qrslRef.value) {
      console.log('qrslRef 存在，开始调用 handleSubmit');
      const result = await qrslRef.value.handleSubmit();
      console.log('handleSubmit 返回结果:', result);
      if (result) {
        console.log('操作成功，准备关闭弹窗');
        // 直接调用 cancel7 方法，确保弹窗关闭
        cancel7();
        console.log('调用 cancel7() 后弹窗状态:', dialog7.visible);
        getInfo();
        getList(); // 刷新列表
      } else {
        console.log('操作失败，不关闭弹窗');
      }
    } else {
      console.log('qrslRef 不存在');
    }
  };

  // 发起审查
  const handleFqsc = () => {
    // 从当前选中的行或者第一行获取卷宗ID
    if (!currentRecordId.value && gzjzJbxxList.value.length > 0) {
      currentRecordId.value = gzjzJbxxList.value[0].id;
      gzlbId.value = gzjzJbxxList.value[0].lb;
    }
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    // 重置选项
    Object.keys(reviewOptions).forEach(key => {
      reviewOptions[key] = false;
    });

    dialog8.visible = true;
    dialog8.title = "核实审查";
  };

  // 处理第一组全选
  const handleSelectAll1 = (checked : boolean) => {
    reviewOptions.option1 = checked;
    reviewOptions.option2 = checked;
    reviewOptions.option3 = checked;
    reviewOptions.option4 = checked;
    reviewOptions.option5 = checked;
  };

  // 处理第二组全选
  const handleSelectAll2 = (checked : boolean) => {
    reviewOptions.option6 = checked;
    reviewOptions.option7 = checked;
    reviewOptions.option8 = checked;
    reviewOptions.option9 = checked;
    reviewOptions.option10 = checked;
  };

  // 取消审查对话框
  const cancel8 = () => {
    dialog8.visible = false;
  };

  // 提交审查
  const submitForm10 = async () => {
    console.log('submitForm10 被调用');
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }
    handleSelectAll1(true);
    handleSelectAll2(true);

    console.log('reviewOptions:', reviewOptions);
    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value
      };

      console.log('发起审查请求数据:', data);
      const res = await initiateReview(data);
      console.log('发起审查响应:', res);
      if (res.code === 200) {
        ElMessage.success('发起审查成功');
        console.log('准备关闭审查弹窗');
        cancel8(); // 调用 cancel8 方法关闭弹窗
        console.log('调用 cancel8() 后弹窗状态:', dialog8.visible);
        getInfo();
        getList(); // 刷新列表
      } else {
        ElMessage.error('发起审查失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('发起审查失败:', error);
      ElMessage.error('发起审查失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  const comfirmSc = async () => {
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在发起审查，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
      fullscreen: true
    });

    try {
      const data = {
        id: currentRecordId.value
      };
      const res = await initiateReview(data);
      if (res.code === 200) {
        ElMessage.success('发起审查成功');
        cancel8(); // 调用 cancel8 方法关闭弹窗
        getInfo();
        getList(); // 刷新列表
      } else {
        ElMessage.error('发起审查失败：' + (res.msg || '未知错误'));
      }
    } catch (err : any) {
      console.error('发起审查失败:', err);
      ElMessage.error('发起审查失败: ' + (err?.message || '未知错误'));
    } finally {
      loading.close();
    }
  }



  const handleFqsp = () => {
    fqspMark.value = true
    // fqspDialogRef.value.handleFqsp(currentRecordId.value);
    sqfyDialogRef.value.handleSqfy(currentRecordId.value, '发起审批-申请翻译确认');
  }

  const fqspCallback = (data : any) => {
    console.log('fqspCallback', data);

    getInfo();// 刷新编辑页

    cancelDrawerNdgzs(); //关闭拟定公证书

    getList(); // 刷新列表


  }

  const handleSqfy = () => {
    fqspMark.value = false;
    sqfyDialogRef.value.handleSqfy(currentRecordId.value);
  }

  const sqfyClosed = () => {
    // 判断是由发起审批按钮触发的
    nextTick(() => {
      if(fqspMark.value) {
        fqspDialogRef.value.handleFqsp(currentRecordId.value);
        fqspMark.value = false
      }
    })
  }

  const sqfyCallback = (data : any) => {
    console.log('sqfyCallback', data);

    getInfo();// 刷新编辑页

    cancelDrawerNdgzs(); //关闭拟定公证书

    getList(); // 刷新列表
  }
  // 文档拟定
  const handleWdnd = () => {
    dialog9.visible = true;
    dialog9.title = "文档拟定";
  };

  const cancel9 = () => {
    dialog9.visible = false;
  };

  // 笔录
  const handleBl = () => {
    dialog10.visible = true;
    dialog10.title = "笔录";
  };

  const cancel10 = () => {
    dialog10.visible = false;
  };

  // 代书
  const handleDs = () => {
    dsDialogState.visible = true;
    dsDialogState.title = "代书";
  };

  const drawerNdgzs = ref({
    visible: false,
    title: '',
  })



  // 拟定公证书
  const handleNdgzs = (row ?: GzjzJbxxVO) => {
    console.log('拟定公证书', row);
    if (row?.id) {
      currentRecordId.value = row.id; // 设置当前查看的记录ID
      lczt.value = row.lczt;
      currentRecord.value = row;
    }
    drawerNdgzs.value.visible = true;
    drawerNdgzs.value.title = "拟定公证书";
  };

  const cancelDrawerNdgzs = () => {
    drawerNdgzs.value.visible = false;
  }

  // 双击行查看详情
  const handleRowDblclick = (row : GzjzJbxxVO) => {
    if (row?.id) {
      currentRecordId.value = row.id; // 设置当前查看的记录ID
      lczt.value = row.lczt;
    }
    dialog11.visible = true;
    dialog11.title = "详情";
  };

  // 制证
  const handleZz = (row ?: GzjzJbxxVO) => {
    dialog12.visible = true;
    dialog12.title = "制证详情";
  };

  const cancel11 = () => {
    dialog11.visible = false;
    // 注意：这里不清空currentRecordId，因为detail组件需要独立管理自己的数据
  };

  const cancel12 = () => {
    dialog12.visible = false;
  };

  // 上报审批
  const handleSbsp = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    // 重置表单
    sbspForm.lcyj = '';
    sbspForm.sftg = '1';

    dialogSbsp.visible = true;
    dialogSbsp.title = "上报审批";
  };

  // 取消上报审批对话框
  const cancelSbsp = async () => {
    // 使用 nextTick 确保响应式更新
    await nextTick(() => {
      dialogSbsp.visible = false;
      console.log('弹窗状态设置为:', dialogSbsp.visible);
    });


  };

  // 提交上报审批
  const submitSbsp = async () => {
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value,
        lcyj: sbspForm.lcyj,
        sftg: sbspForm.sftg
      };

      const res = await initiateApproval(data);
      if (res.code === 200) {
        ElMessage.success('上报审批成功');
        cancelSbsp(); // 调用 cancelSbsp 方法关闭弹窗
        getList(); // 刷新列表
      } else {
        ElMessage.error('上报审批失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('上报审批失败:', error);
      ElMessage.error('上报审批失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  // 提交制证
  const handleTjZz = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    // 重置表单
    tjZzForm.lcyj = '';
    tjZzForm.sftg = '1';

    dialogTjZz.visible = true;
    dialogTjZz.title = "提交制证";
  };

  // 取消提交制证对话框
  const cancelTjZz = () => {
    dialogTjZz.visible = false;
  };

  // 提交制证
  const submitTjZz = async () => {
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value,
        lcyj: tjZzForm.lcyj,
        sftg: tjZzForm.sftg
      };

      const res = await initiateProduction(data);
      if (res.code === 200) {
        ElMessage.success('提交制证成功');
        cancelTjZz(); // 调用 cancelTjZz 方法关闭弹窗
        getList(); // 刷新列表
      } else {
        ElMessage.error('提交制证失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('提交制证失败:', error);
      ElMessage.error('提交制证失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  // 提交发证
  const handleTjFz = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    dialogTjFz.visible = true;
    dialogTjFz.title = "提交发证";
  };

  // 取消提交发证对话框
  const cancelTjFz = () => {
    dialogTjFz.visible = false;
  };

  // 提交发证
  const submitTjFz = async () => {
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value
      };

      const res = await initiateSigned(data);
      if (res.code === 200) {
        ElMessage.success('提交发证成功');
        cancelTjFz(); // 调用 cancelTjFz 方法关闭弹窗
        getList(); // 刷新列表
      } else {
        ElMessage.error('提交发证失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('提交发证失败:', error);
      ElMessage.error('提交发证失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  // 提交归档
  const handleTjgd = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先选择要操作的卷宗');
      return;
    }

    dialogTjgd.visible = true;
    dialogTjgd.title = "提交归档";
  };

  // 取消提交归档对话框
  const cancelTjgd = () => {
    dialogTjgd.visible = false;
  };

  // 卷宗备注提交
  const submitJzbz = () => {
    console.log('卷宗备注编辑提交', jzbzInfo)

    gzjzbzUpdater();
  }

  // 卷宗备注编辑&提交取消
  const cancelJzbz = () => {
    dialogJzbz.visible = false;
    jzbzInfo.id = undefined;
    jzbzInfo.jzbh = undefined;
    jzbzInfo.remark = undefined;
  }

  // 提交归档
  const submitTjgd = async () => {
    if (!currentRecordId.value) {
      ElMessage.warning('未选择卷宗');
      return;
    }

    buttonLoading.value = true;
    try {
      const data = {
        id: currentRecordId.value
      };

      const res = await initiateArchiving(data);
      if (res.code === 200) {
        ElMessage.success('提交归档成功');
        cancelTjgd(); // 调用 cancelTjgd 方法关闭弹窗
        getList(); // 刷新列表
      } else {
        ElMessage.error('提交归档失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('提交归档失败:', error);
      ElMessage.error('提交归档失败: ' + (error?.message || '未知错误'));
    } finally {
      buttonLoading.value = false;
    }
  };

  /**====================================== 事件总线 =======================================**/
  // 触发受理列表更新
  const slListUpdate = (val : any) => {
    getList()
  }
  eventBus.on('sl:list:update', slListUpdate)

  // 提供给子组件的数据和方法
  provide('currentRecordId', currentRecordId);
  provide('gzlbId', gzlbId);
  provide('currentRecord', currentRecord);
  provide('refreshList', getList);

  onMounted(() => {
    getList();
  });

  onBeforeUnmount(() => {
    // 可以考虑组件移除时也移除订阅的事件
    // eventBus.off('sl:list:update', slListUpdate)
  })
</script>

<style scoped>
  .el-dropdown {
    vertical-align: top;
  }

  .el-dropdown+.el-dropdown {
    margin-left: 15px;
  }

  .el-icon-arrow-down {
    font-size: 12px;
  }

  .review-content {
    padding: 20px 0;
  }

  .review-section {
    margin-bottom: 30px;
  }

  .review-section h4 {
    margin-bottom: 15px;
    color: #303133;
    font-weight: 600;
  }

  .checkbox-group {
    margin: 15px 0;
    padding-left: 20px;
  }

  .checkbox-group .el-checkbox {
    display: block;
    margin-bottom: 12px;
    line-height: 1.6;
  }

  .review-section p {
    margin-top: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }

  .confirmation-content {
    padding: 20px 0;
  }

  .confirmation-content p {
    font-size: 16px;
    color: #606266;
    margin-bottom: 20px;
    text-align: center;
  }

  .gzsbh_line {
    display: flex;
    flex-direction: row;
    gap: 2px;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 12px;
  }
</style>
