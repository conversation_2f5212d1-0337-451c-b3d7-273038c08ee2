import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzYqtxVO, GzjzYqtxForm, GzjzYqtxQuery } from './types';

/**
 * 查询公证卷宗-要求和提醒列表
 * @param query 查询参数
 * @returns
 */
export const listGzjzYqtx = (query: GzjzYqtxQuery): AxiosPromise<GzjzYqtxVO[]> => {
  return request({
    url: '/gongzheng/gzjzYqtx/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取公证卷宗-要求和提醒详细信息
 * @param id ID
 * @returns
 */
export const getGzjzYqtx = (id: string | number): AxiosPromise<GzjzYqtxVO> => {
  return request({
    url: `/gongzheng/gzjzYqtx/${id}`,
    method: 'get'
  });
};

/**
 * 获取公证卷宗-要求和提醒详细信息
 * @param gzjzId gzjzId
 * @returns
 */
export const getByGzjzIdGzjzYqtx = (gzjzId: string | number): AxiosPromise<GzjzYqtxVO> => {
  return request({
    url: `/gongzheng/gzjzYqtx/getByGzjzId/${gzjzId}`,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-要求和提醒
 * @param data 表单数据
 * @returns
 */
export const addGzjzYqtx = (data: GzjzYqtxForm) => {
  return request({
    url: '/gongzheng/gzjzYqtx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-要求和提醒
 * @param data 表单数据
 * @returns
 */
export const updateGzjzYqtx = (data: GzjzYqtxForm) => {
  return request({
    url: '/gongzheng/gzjzYqtx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-要求和提醒
 * @param id ID
 * @returns
 */
export const delGzjzYqtx = (id: string | number | Array<string | number>) => {
  return request({
    url: `/gongzheng/gzjzYqtx/${id}`,
    method: 'delete'
  });
};
