<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px" size="small">
            <el-form-item label="卷宗号" prop="jzbh" style="width: 260px;">
              <el-input v-model="queryParams.jzbh" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人" prop="dsrxm" style="width: 260px;">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm" style="width: 260px;">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable>
                <el-option
                  v-for="item in gzy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb" style="width: 260px;">
              <el-select v-model="queryParams.lb" placeholder="请选择" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable style="max-width: 80px; margin-right: 4px;" >
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable style="max-width: 140px; margin-right: 4px;" >
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery" style="max-width: 60px" />号
            </el-form-item>

            <el-form-item label="审批日期" prop="spsj" style="width: 340px;">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['gz:sp:query']">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!-- <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleBatchApproval">批量审批</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template> -->

      <GeneralProcessTable ref="generalProcessTableRef" :view-moudle="'sp'" :action-width="120" @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
        <template #my-actions="{ row }">
          <el-button type="primary" link @click="handleApproval(row)" size="small"  v-hasPermi="['gz:sp:edit']">审批</el-button>
          <el-button type="primary" link @click="handleRowDblclick(row)" size="small" v-hasPermi="['gz:sp:query']">详情</el-button>
        </template>
        <template #my-alerts="{ row }">
          <el-text type="danger">{{ row.ajtx ? `(${row.ajtx})` : '' }}</el-text>
        </template>
        <template #my-remarks="{ row }">
          <el-text @click="() => handleBz(row)" :type="row.yqtxVo && row.yqtxVo.remark ? '' : 'primary'" style="cursor: pointer;">{{ row.yqtxVo ? row.yqtxVo.remark || '备注' : '备注' }}</el-text>
        </template>
      </GeneralProcessTable>

    </el-card>

    <!-- 详情对话框 - 使用共用组件 -->
    <!-- <DetailDrawer
      v-model:visible="dialog.visible"
      v-if="dialog.visible"
      :title="dialog.title"
      :current-record-id="currentRecordId"
      :lczt="lczt"
      page-type="sp"
      @refresh="getList"
      @evidence-material="handleEvidenceMaterial"
      @delivery-info="handleDeliveryInfo"
      @reminder-info="handleReminderInfo"
      @short-info="handleShortInfo"
      @invalidation="handleInvalidationSubmit"
      @document-drafting="handleWdnd"
      @transcript="handleBl"
      @ghostwriting="handleDs"
      @draft-notarization="handleNdgzs"
      @initiate-production="handleTjZz"
      @apply-investigation="handleApplyInvestigation"
      @mini-program-download="handleMiniProgramDownload"
      @submit-delivery="submitForm5"
      @submit-reminder="submitForm6"
      @submit-short-info="submitForm7"
    /> -->

    <!-- 审批 -->
    <SpView v-model="dialog.visible" v-if="dialog.visible" />

    <!-- 详情 -->
    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <!-- 证据材料对话框 -->
    <el-drawer :title="dialog2.title" v-model="dialog2.visible" :direction="direction" :before-close="cancel2"
      size="100%">
      <ZjclAdd v-if="dialog2.visible"></ZjclAdd>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="18" style="text-align: left;">
              <el-button @click="handleApplyInvestigation">申请调查核实</el-button>
              <el-button @click="handleMiniProgramDownload">小程序材料下载</el-button>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-button @click="cancel2">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 送达信息对话框 -->
    <el-dialog v-model="dialog3.visible" :title="dialog3.title" width="60%" @close="cancel3">
      <Sdxx v-if="dialog3.visible"></Sdxx>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm5">保存</el-button>
          <el-button @click="cancel3">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提醒消息对话框 -->
    <el-dialog v-model="dialog4.visible" :title="dialog4.title" width="40%" @close="cancel4">
      <TxMsg v-if="dialog4.visible"></TxMsg>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm6">保存</el-button>
          <el-button @click="cancel4">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 推送列表对话框 -->
    <el-dialog v-model="dialog5.visible" :title="dialog5.title" width="50%" @close="cancel5">
      <Dxys v-if="dialog5.visible"></Dxys>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm7">保存</el-button>
          <el-button @click="cancel5">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文档拟定对话框 -->
    <el-drawer :title="dialog9.title" v-model="dialog9.visible" :direction="direction" :before-close="cancel9"
      size="100%">
      <Wdnd v-if="dialog9.visible"></Wdnd>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="24" style="text-align: left;">
              <el-button @click="cancel9">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 笔录对话框 -->
    <el-drawer :title="dialog10.title" v-model="dialog10.visible" :direction="direction" :before-close="cancel10"
      size="100%">
      <Bl v-if="dialog10.visible"></Bl>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="24" style="text-align: left;">
              <el-button @click="cancel10">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 卷宗作废对话框 -->
    <el-dialog v-model="dialogZfyy.visible" :title="dialogZfyy.title" width="40%" @close="cancelZfyy">
      <el-form label-width="100px">
        <el-form-item label="作废原因" required>
          <el-input
            v-model="zfyyForm.zfyy"
            type="textarea"
            :rows="4"
            placeholder="请输入作废原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitZfyy" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelZfyy">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提交制证对话框 -->
    <el-dialog v-model="dialogTjZz.visible" :title="dialogTjZz.title" width="40%" @close="cancelTjZz">
      <div class="confirmation-content">
        <p>确认要提交制证当前卷宗吗？</p>
        <el-form label-width="100px">
          <el-form-item label="流程意见">
            <el-input
              v-model="tjZzForm.lcyj"
              type="textarea"
              :rows="3"
              placeholder="请输入流程意见"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="是否通过">
            <el-radio-group v-model="tjZzForm.sftg">
              <el-radio label="1">通过</el-radio>
              <el-radio label="0">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTjZz" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelTjZz">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="spList" lang="ts">
import { provide } from 'vue';
import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx,
  invalidation,
  initiateProduction
} from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import GeneralProcessTable from '@/views/gongzheng/gongzheng/components/GeneralProcessTable.vue';

import SlDetail from '@/views/gongzheng/gongzheng/components/jz/detail.vue';
import ZjclAdd from '@/views/gongzheng/gongzheng/components/sl/zjcl/index.vue';
import Sdxx from '@/views/gongzheng/gongzheng/components/sl/sdxx.vue';
import TxMsg from '@/views/gongzheng/gongzheng/components/sl/tx_msg.vue';
import Dxys from '@/views/gongzheng/gongzheng/components/sl/dxys.vue';
import Wdnd from '@/views/gongzheng/gongzheng/components/sl/wdnd/index.vue';
import Bl from '@/views/gongzheng/gongzheng/components/sl/bl/index.vue';
import DetailDrawer from '@/views/gongzheng/gongzheng/components/sl/DetailDrawer.vue';
import { genYearOptions, clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import SpView from './mod/SpView/index.vue';
import eventBus from '@/utils/eventBus';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzs_zh, gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_gzlb', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt'));
const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

const gzsbh_years = genYearOptions(2015);

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');
const dateRange = ref<[string, string] | null>([null, null]);

// 当前编辑的记录ID，用于传递给子组件
const currentRecordId = ref<string | number | null>(null);
const currentRecord = ref<GzjzJbxxVO>(null);
// 流程状态
const lczt = ref<string | number | null>(null);
const queryFormRef = ref<ElFormInstance>();
const generalProcessTableRef = ref<InstanceType<typeof GeneralProcessTable>>(null);

const jzDetailState = reactive({
  visible: false
})

// 对话框状态管理
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog2 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog3 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog4 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog5 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog9 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog10 = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 新增对话框状态
const dialogZfyy = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogTjZz = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 查询参数
const queryParams = ref<GzjzJbxxQuery>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  gzsbh: undefined,
  lb: undefined,
  dsrxm: undefined,
  gzyxm: undefined,
  lczt: '05', // 固定为审批状态
  syd: undefined,
  params: {}
});

// 作废原因表单
const zfyyForm = reactive({
  zfyy: ''
});

// 提交制证表单
const tjZzForm = reactive({
  lcyj: '',
  sftg: '1'
});

/** 查询公证卷宗-基本信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    proxy?.addDateRange(queryParams.value, dateRange.value, 'Spsj');
    queryParams.value.routeCode = 'gz:sp';
    generalProcessTableRef.value.getList(queryParams.value);
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = [null, null];
  queryParams.value.params = {};
  handleQuery();
}

  /** 多选框选中数据 */
  const handleSelectionChange = (_ids : Array<number|string>, _single : boolean, _multiple: boolean) => {
    ids.value = _ids;
    single.value = _single;
    multiple.value = _multiple;
  };

/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (cellValue) {
    return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
  }
  return '';
}

// 审批
const handleApproval = (row?: GzjzJbxxVO) => {
  if (row?.id) {
    currentRecordId.value = row.id;
    currentRecord.value = row;
    lczt.value = row.lczt;
  }
  dialog.visible = true;
  dialog.title = "审批详情";
}

// 批量审批
const handleBatchApproval = () => {
  console.log('批量审批操作');
}

// 备注
const handleBz = (row?: GzjzJbxxVO) => {
  console.log('备注操作');
}

// 双击行查看详情
const handleRowDblclick = (row: GzjzJbxxVO) => {
  if (row?.id) {
    currentRecordId.value = row.id;
    currentRecord.value = row;
    lczt.value = row.lczt;
  }
  // dialog.visible = true;
  // dialog.title = "详情";

  jzDetailState.visible = true
}

/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  currentRecordId.value = null;
  currentRecord.value = null;
};

// 证据材料功能
const handleEvidenceMaterial = () => {
  dialog2.visible = true;
  dialog2.title = "证据明细";
};

const cancel2 = () => {
  dialog2.visible = false;
};

const handleApplyInvestigation = () => {
  console.log('申请调查核实');
};

const handleMiniProgramDownload = () => {
  console.log('小程序材料下载');
};

// 送达信息功能
const handleDeliveryInfo = () => {
  dialog3.visible = true;
  dialog3.title = "送达信息";
};

const cancel3 = () => {
  dialog3.visible = false;
};

const submitForm5 = () => {
  console.log('保存送达信息');
  cancel3();
};

// 提醒信息功能
const handleReminderInfo = () => {
  dialog4.visible = true;
  dialog4.title = "提醒信息";
};

const cancel4 = () => {
  dialog4.visible = false;
};

const submitForm6 = () => {
  console.log('保存提醒信息');
  cancel4();
};

// 短信预约功能
const handleShortInfo = () => {
  dialog5.visible = true;
  dialog5.title = "推送列表";
};

const cancel5 = () => {
  dialog5.visible = false;
};

const submitForm7 = () => {
  console.log('保存短信预约');
  cancel5();
};

// 卷宗作废
const handleZf = () => {
  if (!currentRecordId.value) {
    ElMessage.warning('请先选择要操作的卷宗');
    return;
  }

  zfyyForm.zfyy = '';
  dialogZfyy.visible = true;
  dialogZfyy.title = "卷宗作废";
};

const cancelZfyy = () => {
  dialogZfyy.visible = false;
};

const submitZfyy = async () => {
  if (!zfyyForm.zfyy.trim()) {
    ElMessage.warning('请输入作废原因');
    return;
  }

  if (!currentRecordId.value) {
    ElMessage.warning('未选择卷宗');
    return;
  }

  buttonLoading.value = true;
  try {
    const data = {
      id: currentRecordId.value,
      zfyy: zfyyForm.zfyy
    };

    const res = await invalidation(data);
    if (res.code === 200) {
      ElMessage.success('卷宗作废成功');
      cancelZfyy();
      getList();
    } else {
      ElMessage.error('卷宗作废失败：' + (res.msg || '未知错误'));
    }
  } catch (error: any) {
    console.error('卷宗作废失败:', error);
    ElMessage.error('卷宗作废失败: ' + (error?.message || '未知错误'));
  } finally {
    buttonLoading.value = false;
  }
};

// 文档拟定
const handleWdnd = () => {
  dialog9.visible = true;
  dialog9.title = "文档拟定";
};

const cancel9 = () => {
  dialog9.visible = false;
};

// 笔录
const handleBl = () => {
  dialog10.visible = true;
  dialog10.title = "笔录";
};

const cancel10 = () => {
  dialog10.visible = false;
};

// 代书
const handleDs = () => {
  dialog10.visible = true;
  dialog10.title = "代书";
};

// 拟定公证书
const handleNdgzs = () => {
  console.log('拟定公证书');
};

// 提交制证
const handleTjZz = () => {
  if (!currentRecordId.value) {
    ElMessage.warning('请先选择要操作的卷宗');
    return;
  }

  tjZzForm.lcyj = '';
  tjZzForm.sftg = '1';

  dialogTjZz.visible = true;
  dialogTjZz.title = "提交制证";
};

const cancelTjZz = () => {
  dialogTjZz.visible = false;
};

const submitTjZz = async () => {
  if (!currentRecordId.value) {
    ElMessage.warning('未选择卷宗');
    return;
  }

  buttonLoading.value = true;
  try {
    const data = {
      id: currentRecordId.value,
      lcyj: tjZzForm.lcyj,
      sftg: tjZzForm.sftg
    };

    const res = await initiateProduction(data);
    if (res.code === 200) {
      ElMessage.success('提交制证成功');
      cancelTjZz();
      getList();
    } else {
      ElMessage.error('提交制证失败：' + (res.msg || '未知错误'));
    }
  } catch (error: any) {
    console.error('提交制证失败:', error);
    ElMessage.error('提交制证失败: ' + (error?.message || '未知错误'));
  } finally {
    buttonLoading.value = false;
  }
};

  /**====================================== 事件总线 =======================================**/
  // 触发受理列表更新
  eventBus.on('sp:list:update', (val: any) => {
    getList()
  })

// 提供给子组件的数据和方法
provide('currentRecordId', currentRecordId);
provide('currentRecord', currentRecord);
provide('refreshList', getList);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.confirmation-content {
  padding: 20px 0;
}

.confirmation-content p {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  text-align: center;
}
</style>
