<template>
  <div class="data-item flex items-start mb-1px">
    <div v-if="label || $slots.label" class="flex items-center justify-end mr-4px py-8px px-6px font-bold" :style="{ width: labelWidth }">
      <slot name="label">{{ label }}</slot>
    </div>
    <div class="flex-1 p-8px flex flex-wrap items-center border-dotted border-1px border-gary-50 rounded">
      <slot name="default">
        {{ content }}
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label?: string;
  content?: string;
  labelWidth?: string; // 标签宽度
}

const props = defineProps<Props>();
</script>

<style scoped>
.data-item + .data-item {
  margin-top: 8px;
}
</style>
