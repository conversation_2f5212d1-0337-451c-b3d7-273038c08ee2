<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="通知方式" prop="tzfs">
              <el-input v-model="queryParams.tzfs" placeholder="请输入通知方式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="送达邮箱" prop="sdyx">
              <el-input v-model="queryParams.sdyx" placeholder="请输入送达邮箱" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="送达号码" prop="sdhm">
              <el-input v-model="queryParams.sdhm" placeholder="请输入送达号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="送达日期" prop="sdrq">
              <el-date-picker clearable
                v-model="queryParams.sdrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择送达日期"
              />
            </el-form-item>
            <el-form-item label="发送结果" prop="fsjg">
              <el-input v-model="queryParams.fsjg" placeholder="请输入发送结果" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="送达信息ID" prop="sdxxId">
              <el-input v-model="queryParams.sdxxId" placeholder="请输入送达信息ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="短信服务日志ID" prop="dxfwrzId">
              <el-input v-model="queryParams.dxfwrzId" placeholder="请输入短信服务日志ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzSdxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzSdxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzSdxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzSdxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzSdxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="通知方式" align="center" prop="tzfs" />
        <el-table-column label="送达邮箱" align="center" prop="sdyx" />
        <el-table-column label="送达号码" align="center" prop="sdhm" />
        <el-table-column label="送达日期" align="center" prop="sdrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sdrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发送结果" align="center" prop="fsjg" />
        <el-table-column label="送达信息ID" align="center" prop="sdxxId" />
        <el-table-column label="短信服务日志ID" align="center" prop="dxfwrzId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzSdxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzSdxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-送达信息v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzSdxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="通知方式" prop="tzfs">
          <el-input v-model="form.tzfs" placeholder="请输入通知方式" />
        </el-form-item>
        <el-form-item label="送达邮箱" prop="sdyx">
          <el-input v-model="form.sdyx" placeholder="请输入送达邮箱" />
        </el-form-item>
        <el-form-item label="送达号码" prop="sdhm">
          <el-input v-model="form.sdhm" placeholder="请输入送达号码" />
        </el-form-item>
        <el-form-item label="送达日期" prop="sdrq">
          <el-date-picker clearable
            v-model="form.sdrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择送达日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发送结果" prop="fsjg">
          <el-input v-model="form.fsjg" placeholder="请输入发送结果" />
        </el-form-item>
        <el-form-item label="送达信息ID" prop="sdxxId">
          <el-input v-model="form.sdxxId" placeholder="请输入送达信息ID" />
        </el-form-item>
        <el-form-item label="短信服务日志ID" prop="dxfwrzId">
          <el-input v-model="form.dxfwrzId" placeholder="请输入短信服务日志ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzSdxx" lang="ts">
import { listGzjzSdxx, getGzjzSdxx, delGzjzSdxx, addGzjzSdxx, updateGzjzSdxx } from '@/api/gongzheng/dev/gzjzSdxx';
import { GzjzSdxxVO, GzjzSdxxQuery, GzjzSdxxForm } from '@/api/gongzheng/dev/gzjzSdxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzSdxxList = ref<GzjzSdxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzSdxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzSdxxForm = {
  id: undefined,
  gzjzId: undefined,
  tzfs: undefined,
  sdyx: undefined,
  sdhm: undefined,
  sdrq: undefined,
  fsjg: undefined,
  sdxxId: undefined,
  dxfwrzId: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzSdxxForm, GzjzSdxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    tzfs: undefined,
    sdyx: undefined,
    sdhm: undefined,
    sdrq: undefined,
    fsjg: undefined,
    sdxxId: undefined,
    dxfwrzId: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-送达信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzSdxx(queryParams.value);
  gzjzSdxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzSdxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzSdxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-送达信息v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzSdxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzSdxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-送达信息v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzSdxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzSdxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzSdxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzSdxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-送达信息v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzSdxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzSdxx/export', {
    ...queryParams.value
  }, `gzjzSdxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
