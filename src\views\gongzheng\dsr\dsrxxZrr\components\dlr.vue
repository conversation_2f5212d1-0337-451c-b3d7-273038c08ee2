<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="当事人" prop="dsrId">
              <el-input v-model="queryParams.dsrId" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="xm">
              <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件类型" prop="zjlx">
              <el-select v-model="queryParams.zjlx" placeholder="请选择证件类型" clearable >
                <el-option v-for="dict in gz_jg_zjlx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="证件号码" prop="zjhm">
              <el-input v-model="queryParams.zjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="代理人身份" prop="dlrsf">
              <el-input v-model="queryParams.dlrsf" placeholder="请输入代理人身份" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['dsr:dsrxxDlrxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['dsr:dsrxxDlrxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['dsr:dsrxxDlrxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['dsr:dsrxxDlrxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxDlrxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="当事人" align="center" prop="dsrId" />
        <el-table-column label="姓名" align="center" prop="xm" />
        <el-table-column label="证件类型" align="center" prop="zjlx">
          <template #default="scope">
            <dict-tag :options="gz_jg_zjlx" :value="scope.row.zjlx"/>
          </template>
        </el-table-column>
        <el-table-column label="证件号码" align="center" prop="zjhm" />
        <el-table-column label="代理人身份" align="center" prop="dlrsf" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['dsr:dsrxxDlrxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['dsr:dsrxxDlrxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改当事人-法人或者其他组织-代理人信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="dsrxxDlrxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="当事人" prop="dsrId">
          <el-input v-model="form.dsrId" placeholder="请输入当事人" />
        </el-form-item>
        <el-form-item label="姓名" prop="xm">
          <el-input v-model="form.xm" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="证件类型" prop="zjlx">
          <el-select v-model="form.zjlx" placeholder="请选择证件类型">
            <el-option
                v-for="dict in gz_jg_zjlx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码" prop="zjhm">
          <el-input v-model="form.zjhm" placeholder="请输入证件号码" />
        </el-form-item>
        <el-form-item label="代理人身份" prop="dlrsf">
          <el-input v-model="form.dlrsf" placeholder="请输入代理人身份" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DsrxxDlrxx" lang="ts">
import { listDsrxxDlrxx, getDsrxxDlrxx, delDsrxxDlrxx, addDsrxxDlrxx, updateDsrxxDlrxx } from '@/api/gongzheng/dsr/dsrxxDlrxx';
import { DsrxxDlrxxVO, DsrxxDlrxxQuery, DsrxxDlrxxForm } from '@/api/gongzheng/dsr/dsrxxDlrxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_jg_zjlx'));

const dsrxxDlrxxList = ref<DsrxxDlrxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const dsrxxDlrxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DsrxxDlrxxForm = {
  id: undefined,
  dsrId: undefined,
  xm: undefined,
  zjlx: undefined,
  zjhm: undefined,
  dlrsf: undefined,
}
const data = reactive<PageData<DsrxxDlrxxForm, DsrxxDlrxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dsrId: undefined,
    xm: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dlrsf: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    dsrId: [
      { required: true, message: "当事人不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询当事人-法人或者其他组织-代理人信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDsrxxDlrxx(queryParams.value);
  dsrxxDlrxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  dsrxxDlrxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DsrxxDlrxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加当事人-法人或者其他组织-代理人信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DsrxxDlrxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDsrxxDlrxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改当事人-法人或者其他组织-代理人信息";
}

/** 提交按钮 */
const submitForm = () => {
  dsrxxDlrxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDsrxxDlrxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addDsrxxDlrxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: DsrxxDlrxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除当事人-法人或者其他组织-代理人信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delDsrxxDlrxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('dsr/dsrxxDlrxx/export', {
    ...queryParams.value
  }, `dsrxxDlrxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
