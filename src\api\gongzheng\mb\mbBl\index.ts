import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbBlVO, MbBlForm, MbBlQuery } from '@/api/mb/mbBl/types';

/**
 * 查询模板-变量列表
 * @param query
 * @returns {*}
 */

export const listMbBl = (query?: MbBlQuery): AxiosPromise<MbBlVO[]> => {
  return request({
    url: '/mb/mbBl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-变量详细
 * @param id
 */
export const getMbBl = (id: string | number): AxiosPromise<MbBlVO> => {
  return request({
    url: '/mb/mbBl/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-变量
 * @param data
 */
export const addMbBl = (data: MbBlForm) => {
  return request({
    url: '/mb/mbBl',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-变量
 * @param data
 */
export const updateMbBl = (data: MbBlForm) => {
  return request({
    url: '/mb/mbBl',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-变量
 * @param id
 */
export const delMbBl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbBl/' + id,
    method: 'delete'
  });
};
