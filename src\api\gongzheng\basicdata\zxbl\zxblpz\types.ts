/**
 * 在线办理配置 - 类型定义
 */

/**
 * 在线办理配置VO
 */
export interface ZxblConfigVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 公证事项名称（冗余字段）
   */
  gzsxName: string;

  /**
   * 事项编号（冗余字段）
   */
  gzsxCode: string;

  /**
   * 公证事项ID（基于GzsxVO中id）
   */
  gzsxId: string | number;

  /**
   * 是否在线办理（1是/0否）
   */
  isOnlineHandle: number;

  /**
   * 是否启用办理（1是/0否）
   */
  isEnableHandle: number;

  /**
   * 申办材料ID（多个以逗号分隔）
   */
  materialIds?: string;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 创建部门
   */
  createDept?: number;

  /**
   * 创建人
   */
  createBy?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 删除标识：0-正常，1-删除
   */
  delFlag?: string;

  /**
   * 申办材料名称列表（前端显示用）
   */
  materialNames?: string[];
}

/**
 * 在线办理配置Form
 */
export interface ZxblConfigForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 公证事项名称（冗余字段）
   */
  gzsxName?: string;

  /**
   * 事项编号（冗余字段）
   */
  gzsxCode?: string;

  /**
   * 公证事项ID（基于GzsxVO中id）
   */
  gzsxId?: string | number;

  /**
   * 是否在线办理（1是/0否）
   */
  isOnlineHandle?: number;

  /**
   * 是否启用办理（1是/0否）
   */
  isEnableHandle?: number;

  /**
   * 申办材料ID（多个以逗号分隔）
   */
  materialIds?: string;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 创建部门
   */
  createDept?: number;

  /**
   * 备注
   */
  remark?: string;
}

/**
 * 在线办理配置Query
 */
export interface ZxblConfigQuery extends PageQuery {
  /**
   * 公证事项名称
   */
  gzsxName?: string;

  /**
   * 事项编号
   */
  gzsxCode?: string;

  /**
   * 公证事项ID
   */
  gzsxId?: string | number;

  /**
   * 是否在线办理（1是/0否）
   */
  isOnlineHandle?: number;

  /**
   * 是否启用办理（1是/0否）
   */
  isEnableHandle?: number;

  /**
   * 租户ID
   */
  tenantId?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 申办材料选项
 */
export interface MaterialOption {
  /**
   * 材料ID
   */
  id: string | number;

  /**
   * 材料名称
   */
  materialName: string;

  /**
   * 类别：1-个人，2-企业
   */
  category: number;

  /**
   * 说明
   */
  description?: string;
}

/**
 * 公证事项选项（用于左侧树形选择）
 */
export interface GzsxOption {
  /**
   * 公证事项ID
   */
  id: string | number;

  /**
   * 事项名称
   */
  title: string;

  /**
   * 事项编号
   */
  code: string;

  /**
   * 父级ID
   */
  parentId?: string | number;

  /**
   * 父级编号
   */
  parentCode?: string;

  /**
   * 子级
   */
  children?: GzsxOption[];

  /**
   * 层级
   */
  level?: number;

  /**
   * 是否基础公证事项
   */
  jcsx?: string | number;
}
