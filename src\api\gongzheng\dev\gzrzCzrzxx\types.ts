export interface GzrzCzrzxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

  /**
   * 操作人姓名
   */
  czrxm: string;

  /**
   * 操作环节
   */
  czhj: string;

  /**
   * 日志内容
   */
  rznr: string;

  /**
   * 操作时间
   */
  czsj: string;

  /**
   * 操作人ID
   */
  czr: string;

}

export interface GzrzCzrzxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 操作人姓名
   */
  czrxm?: string;

  /**
   * 操作环节
   */
  czhj?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作时间
   */
  czsj?: string;

  /**
   * 操作人ID
   */
  czr?: string;

}

export interface GzrzCzrzxxQuery extends PageQuery {

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 操作人姓名
   */
  czrxm?: string;

  /**
   * 操作环节
   */
  czhj?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作时间
   */
  czsj?: string;

  /**
   * 操作人ID
   */
  czr?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



