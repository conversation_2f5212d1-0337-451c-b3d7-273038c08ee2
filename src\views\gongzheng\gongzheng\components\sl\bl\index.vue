<template>
  <!-- 笔录 -->
  <gz-dialog v-model="visible" :title="title" @closed="closed" fullscreen>
    <div class="flex flex-col gap-10px h-full">
      <el-card>
        <div class="flex items-center gap-12px flex-wrap">
          <span class="flex items-center">
            <el-text size="small">卷宗号：</el-text>
            <el-text type="info" size="small">{{ curGzjz.jzbh || '-' }}</el-text>
          </span>
          <span class="flex items-center">
            <el-button @click="openJzDetail" type="primary" size="small" link>【详情】</el-button>
          </span>
          <span class="flex items-center">
            <el-text size="small">公证类别：</el-text>
            <el-text type="info" size="small">{{ dictMapFormat(gz_gzlb, curGzjz.lb) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">公证员：</el-text>
            <el-text type="info" size="small">{{ curGzjz.gzyxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">助理：</el-text>
            <el-text type="info" size="small">{{ curGzjz.zlxm }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">申请日期：</el-text>
            <el-text type="info" size="small">{{ formatDate(curGzjz.sqsj) }}</el-text>
          </span>
          <span class="flex items-center">
            <el-text size="small">受理日期：</el-text>
            <el-text type="info" size="small">{{ formatDate(curGzjz.slrq) }}</el-text>
          </span>
        </div>
      </el-card>

      <el-card class="flex-1">
        <el-table :data="blDocTypeList" v-loading="recordListLoading" style="height: 100%;" border stripe>
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column prop="typeName" label="文档类型" align="center" />
          <el-table-column label="文档名称" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex flex-wrap gap-4px">
                <el-tag v-for="item in row.docList" :key="item.id">
                  <el-button type="primary" link @click="handleOpen(item)">{{item.wbmc}}</el-button>
                  <el-button type="danger" link icon="Delete" @click="handleDelete(item)"></el-button>
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleGenerate(row)" link>
                生成
              </el-button>
              <el-button type="primary" size="small" @click="handleUpload(row)" link>
                上传
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="flex-1 overflow-hidden">
        <SubDataMod ref="SubRef" :gzjz-id="props.gzjzId" :except-pane="['notaryMatters']" />
      </el-card>

      <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable :modal="false" show-close destroy-on-close width="400">
        <div class="h-100px flex flex-col items-center justify-center gap-16px">
          <div class="flex items-center justify-center gap-10px">
            <strong>公证事项：</strong>
            <el-select v-model="gzsxState.gzjzGzsxId" @change="gzsxSelectChange" default-first-option filterable style="width: 200px;">
              <el-option v-for="item in gzsxState.listData" :key="item.id" :label="item.gzsxMc + item.gzsBh" :value="item.id" />
            </el-select>
          </div>
          <div class="flex items-center justify-center gap-10px">
            <strong>文档模板：</strong>
            <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;" :disabled="gzsxState.gzsxId === ''">
              <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
            </el-select>
          </div>
          <div v-if="genState.warningShow" class="flex items-center justify-end">
            <el-text type="danger">未查询到模板，请添加模板后重试或上传本地文档文件</el-text>
          </div>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-10px">
            <el-button type="primary" @click="comfirmGen" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
            <el-button @click="genClose">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

      <DragUpload v-model="uploadState.visible" :title="uploadState.title" :disabled-upload="uploadState.disabledUpload" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" >
        <template #header>
          <div class="flex mb-12px">
            <div class="flex gap-6px items-center">
              <strong class="w-100px flex justify-end">公证事项：</strong>
              <el-select v-model="uploadState.gzjzGzsxId" @change="uploadGzsxChange" style="width: 240px;">
                <el-option v-for="item in uploadState.gzsxOptions" :value="item.id" :key="item.id" :label="item.gzsxMc + item.gzsBh" />
              </el-select>
            </div>
          </div>
        </template>
      </DragUpload>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="close">关 闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadStatus } from '@/components/FileUpload/type'
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import DragUpload from '@/components/FileUpload/DragUpload.vue'
import SubDataMod from '@/views/gongzheng/gongzheng/components/sl/wdnd/SubDataMod.vue'
import { formatDate, dictMapFormat, nodeFilter } from '@/utils/ruoyi';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import { docGenerator, docOpenEdit } from '@/views/gongzheng/doc/DocEditor'
import { UserDocGenParams } from '@/views/gongzheng/doc/type'
import { blDocTableData } from '../preset_data'
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd'
import { addGzjzWjccxx, delGzjzWjccxx, listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx'
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types'
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx'

interface Props {
  modelValue?: boolean,
  title?: string,
  gzjzId?: string | number
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_dsr_jslx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_dsr_jslx', 'gz_dsr_lb'));

const props = withDefaults(defineProps<Props>(),{
  modelValue: false,
  title: '笔录'
})

const emit = defineEmits(['update:modelValue', 'closed'])

// 笔录列表数据
const recordList = ref([]);
const recordListLoading = ref(false);

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

// 当事人列表数据
const partiesList = ref([
  {
    id: '1',
    role: '申请人',
    partyName: '测试当事人',
    partyType: '个人',
    idType: '身份'
  }
])

// 标签页相关
const activeTab = ref('parties')

const SubRef = ref(null)

const jzDetailState = reactive({
  visible: false
})

// 模板对话框相关
const templateDialogVisible = ref(false)
const templateSearchType = ref('all')
const templateSearchKeyword = ref('')
const selectedPartyType = ref('测试当事人')

const blDocTypeList = ref(blDocTableData)

// 模板树形数据
const templateTreeData = ref([
  {
    id: '1',
    label: '公证类别',
    type: 'folder',
    children: [
      {
        id: '1-1',
        label: '涉外',
        type: 'folder',
        children: [
          { id: '1-1-1', label: '涉外房屋所有权证明', type: 'file' },
          { id: '1-1-2', label: '买卖房屋所有权证明', type: 'file' }
        ]
      },
      {
        id: '1-2',
        label: '继承',
        type: 'folder',
        children: [
          { id: '1-2-1', label: '基础性继承权证明', type: 'file' },
          { id: '1-2-2', label: '房屋不动产继承证明', type: 'file' }
        ]
      },
      {
        id: '1-3',
        label: '动产',
        type: 'folder',
        children: [
          { id: '1-3-1', label: '动产抵押证明', type: 'file' }
        ]
      },
      {
        id: '1-4',
        label: '保全',
        type: 'folder',
        children: [
          { id: '1-4-1', label: '保全证明', type: 'file' }
        ]
      },
      {
        id: '1-5',
        label: '民事',
        type: 'folder',
        children: [
          { id: '1-5-1', label: '民事证明', type: 'file' }
        ]
      },
      {
        id: '1-6',
        label: '委托',
        type: 'folder',
        children: [
          { id: '1-6-1', label: '委托公证书', type: 'file' }
        ]
      },
      {
        id: '1-7',
        label: '声明',
        type: 'folder',
        children: [
          { id: '1-7-1', label: '声明公证书', type: 'file' }
        ]
      },
      {
        id: '1-8',
        label: '历史地址变更证明书',
        type: 'file'
      },
      {
        id: '1-9',
        label: '声明书样本',
        type: 'file'
      },
      {
        id: '1-10',
        label: '一般涉外性继承',
        type: 'file'
      }
    ]
  },
  {
    id: '2',
    label: '声明',
    type: 'folder',
    children: [
      { id: '2-1', label: '声明书', type: 'file' }
    ]
  },
  {
    id: '3',
    label: '调解',
    type: 'folder',
    children: [
      { id: '3-1', label: '调解书', type: 'file' }
    ]
  },
  {
    id: '4',
    label: '协议',
    type: 'folder',
    children: [
      { id: '4-1', label: '协议书', type: 'file' }
    ]
  }
])

// 模板列表数据
const templateList = ref([
  {
    id: '1',
    name: 'H-委托声明_委托声明'
  },
  {
    id: '2',
    name: '涉外声明_委托（出国）'
  },
  {
    id: '3',
    name: '委托书模板_委托书模板'
  }
])

// 模板树属性配置
const defaultProps = {
  children: 'children',
  label: 'label'
}

// 当事人选项
const partyOptions = reactive({
  all: false,
  allParties: false,
  selectedParty: true
})

// 模板表单
const templateForm = reactive({
  name: '笔录1.1',
  mergeRule: 1
})

// 上传相关状态
const uploadState = reactive({
  loading: false,
  visible: false,
  title: '上传笔录',
  docType: '',
  docId: '',
  sxRow: null,

  gzsxId: null,
  gzjzGzsxId: '',
  gzsxOptions: [],
  disabledUpload: true,
})

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const genState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  typeData: [],
  mbId: '',
  warningShow: false
})

const gzsxState = reactive({
  listData: [],
  gzjzGzsxId: '',
  gzsxId: '',
})

const openJzDetail = () => {
  jzDetailState.visible = true;
}

// 关闭当前主dialog
const close = () => {
  emit('update:modelValue', false)
  emit('closed')
}

const closed = () => {
  emit('closed')
}

// 处理生成按钮点击
const handleGenerate = (row: any) => {
  const subIns = SubRef.value;
  const selectedDsrList = subIns?.getSelectedDsr();
  // const selectedGzsxList = subIns?.getSelectedGzsx();
  if(selectedDsrList.length === 0) {
    ElMessage.warning('至少选一个当事人');
    return;
  }
  // if(selectedGzsxList.length === 0) {
  //   ElMessage.warning('未选取公证事项，无法生成文档');
  //   return;
  // } else if(selectedGzsxList.length > 1) {
  //   ElMessage.warning('只能选取一个公证事项生成文档');
  //   return;
  // }
  genState.sxRow = row;
  genState.visible = true;
  genState.title = `${row.typeName} 文档生成`;

}

const gzsxSelectChange = (val: any) => {
  gzsxState.gzsxId = gzsxState.listData.find(item => item.id === val).gzsxId

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模板，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  genState.typeData = [];
  genState.mbId = '';

  queryMbFiles({ wdLb: genState.sxRow.typeCode, ywId: gzsxState.gzsxId }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模板为空，请上传模板后重试或选择本地上传文档')
        genState.warningShow = true;
      } else {
        genState.typeData = res.data;
        genState.mbId = res.data[0].id; // 默认选中第一个模板
        genState.warningShow = false;
      }
    }
  }).catch((err: any) => {
    console.log('查询模板文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

const genClose = () => {
  genState.loading = false;
  genState.visible = false;
}

const genClosed = () => {
  genState.sxRow = null
  genState.mbId = ''
  genState.warningShow = false;
  gzsxState.gzsxId = ''
}

// 确认生成文档
const comfirmGen = () => {
  if (!genState.mbId) {
    ElMessage.warning('未选择生成指定模板')
    return;
  }

  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)

  const dsrIds = dsrIdArr.join(',')

  let params: UserDocGenParams = {
    bizId: (curGzjz.value.id || currentRecordId.value) as string,
    // type: genState.sxRow.typeCode,
    // fjlb: EnumDocFileType.QT,
    mbWdId: genState.mbId,
    extraParams: {
      gzxsId: gzsxState.gzsxId,
      dsrIds
    }
    // ,saveDocumentType:"pdf" //生成文档格式 支持 word\pdf
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  genState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      const gzsxInfo = gzsxState.listData.find((item) => item.id === gzsxState.gzjzGzsxId)
      const fileName = `${genState.sxRow.typeName}_${gzsxInfo.gzsxMc}_${gzsxInfo?.gzsBh || formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
      const docInfo: GzjzWjccxxForm = {
        wbmc: fileName,
        wblj: JSON.stringify({
          ossId,
          path,
          fileSuffix,
          fileName,
          typeCode: genState.sxRow.typeCode,
          typeName: genState.sxRow.typeName,
          mbWdId: genState.mbId,
          dsrIds,
          gzsxId: gzsxState.gzsxId,
          gzsx: gzsxInfo.gzsxMc,
        }),
        lx: genState.sxRow.typeCode,
        gzjzGzsxId: gzsxState.gzjzGzsxId,
        gzsx: gzsxInfo.gzsxMc,
        gzsbh: gzsxInfo?.gzsBh || ''
      }
      relateDoc(docInfo);
      genState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败,请检查模板是否完善')
  }).finally(() => {
    genState.loading = false;
    loading.close()
  })
}

// 文档文件列表查询
const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      lx: '2'
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

// 加载公证事项列表
const loadGzsxList = async () => {
  if (!props.gzjzId && !curGzjz.value.id && !currentRecordId.value) {
    gzsxState.listData = []
    return;
  };
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
      // gzsxState.listData = (res.rows || []).reduce((doneArr, curItem) => {
      //   const found = doneArr.find(i => {
      //     console.log('found', i.gzsxId, curItem.gzsxId)
      //     return i.gzsxId === curItem.gzsxId
      //   })
      //   if(!found) {
      //     doneArr.push(curItem)
      //   }
      //   return doneArr;
      // }, []);
      uploadState.gzsxOptions = gzsxState.listData;
      console.log('加载公证事项列表成功', gzsxState.listData);
    }
  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项失败');
  } finally {
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  blDocTypeList.value.map(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    source = noMatches;
  })
}

// 添加生成后的文档（关联生成文档）
const relateDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    const res = await addGzjzWjccxx(params);
    if(res.code === 200) {
      ElMessage.success('生成文件添加成功')
      loadDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

// 删除关联文档
const delDoc = async (id: number | string) => {
  delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadDocList()
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
}

/**============== 上传 START ==============**/
// 处理上传
const handleUpload = (row: any) => {
  uploadState.visible = true
  uploadState.title = `上传文档 - ${row.typeName}`;
  uploadState.sxRow = row;
}

// 每一个上传完成回调
const uploadEveryDone = (data: UploadStatus) => {
  if (data.status === 'success') {

  } else if (data.status === 'error') {

  }
}

// 全部上传完成回调 data 上传成功的文件信息数组，不含失败
const uploadAllDone = (data: any[]) => {
  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)

  const dsrIds = dsrIdArr.join(',')

  const gzsxInfo = uploadState.gzsxOptions.find(i => i.id === uploadState.gzjzGzsxId)

  const mxList = data.map((item) => {
    const { fileName, ossId, path } = item;
    const docInfo: GzjzWjccxxForm = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      gzjzGzsxId: uploadState.gzjzGzsxId,
      gzsx: gzsxInfo.gzsxMc,
      wbmc: fileName,
      wblj: JSON.stringify({
        ossId,
        path,
        fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
        fileName,
        typeCode: uploadState.sxRow.typeCode,
        typeName: uploadState.sxRow.typeName,
        dsrIds,
        gzsxId: gzsxInfo.gzsxId,
        gzsx: gzsxInfo.gzsxMc,
      }),
      lx: uploadState.sxRow.typeCode,
    }

    return addGzjzWjccxx(docInfo)
  });

  Promise.all(mxList).then(() => {

  }).finally(() => {
    loadDocList();
    uploadState.gzsxId = null;
    uploadState.disabledUpload = true;
  })
}

const uploadGzsxChange = (val: any) => {
  if(val) {
    uploadState.disabledUpload = false;
  } else {
    uploadState.disabledUpload = true;
  }
}

/**============== 上传 END ==============**/

// 文件预览
const handleOpen = (data: any) => {
  // 获取文件名后缀
  // const ext = data.xxmc.substring(data.xxmc.lastIndexOf('.') + 1).toLowerCase();
  const obj = JSON.parse(data.wblj || '{}');
  if (obj.path) {
    docOpenEdit(obj.path)
  }
}

// 文件删除
const handleDelete = (data: any) => {
  ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDoc(data.id)
  }).catch(() => {
    // ElMessage.info('已取消删除')
  })
}

// 处理模板树节点点击
const handleNodeClick = (data: any) => {
  if (data.type === 'file') {
    // 如果是文件节点，可以做一些操作
    ElMessage.info(`选择了模板: ${data.label}`)

    // 更新模板列表（这里只是示例，实际应该根据选择的节点从后端获取相应的模板列表）
    if (data.label === '一般涉外性继承') {
      templateList.value = [
        { id: '1', name: 'H-委托声明_委托声明' },
        { id: '2', name: '涉外声明_委托（出国）' },
        { id: '3', name: '委托书模板_委托书模板' }
      ]
    } else {
      templateList.value = [
        { id: '4', name: `${data.label}_模板1` },
        { id: '5', name: `${data.label}_模板2` }
      ]
    }
  }
}

// 搜索模板
const searchTemplate = () => {
  ElMessage.info(`搜索模板: 类型=${templateSearchType.value}, 关键词=${templateSearchKeyword.value}`)
  // 实际搜索模板逻辑
}

// 处理模板选择
const handleTemplateSelect = (row: any) => {
  ElMessage.info(`选择了模板: ${row.name}`)
  // 实际选择模板逻辑
}

// 确认生成
const confirmGenerate = () => {
  if (!templateForm.name) {
    ElMessage.warning('请输入笔录名称')
    return
  }

  ElMessageBox.confirm('确认生成该笔录？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`成功生成笔录: ${templateForm.name}`)
    templateDialogVisible.value = false
    // 实际生成笔录逻辑
  }).catch(() => {
    // 取消操作
  })
}

// 监听全选变化
const watchAllSelection = () => {
  if (partyOptions.all) {
    partyOptions.allParties = true
    partyOptions.selectedParty = true
  } else {
    partyOptions.allParties = false
    partyOptions.selectedParty = false
  }
}

// 监听选项变化
const watchOptionsChange = () => {
  if (partyOptions.allParties && partyOptions.selectedParty) {
    partyOptions.all = true
  } else {
    partyOptions.all = false
  }
}

// 监听选项变化
watch(
  () => partyOptions.all,
  (newVal) => {
    watchAllSelection()
  }
)

watch(
  () => [partyOptions.allParties, partyOptions.selectedParty],
  () => {
    watchOptionsChange()
  },
  { deep: true }
)

watch(() => uploadState.visible, (val) => {
  if(!val) {
    uploadState.gzsxId = null;
    uploadState.disabledUpload = true;
  }
})

onMounted(() => {
  // loadBlList();
  loadGzsxList();
  loadDocList();
})
</script>

<style scoped>
.bl-container {
  /* padding: 20px; */
  height: 100%;
  /* background: #f5f7fa; */
}

.page-header {
  /* margin-bottom: 20px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-table {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.tab-section {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 15px 0;
}

.parties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.parties-table {
  margin-bottom: 15px;
}

/* 模板对话框样式 */
.template-dialog-content {
  display: flex;
  height: 500px;
  gap: 20px;
}

.template-tree-container {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.template-search {
  padding: 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #dcdfe6;
}

.template-tree {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.folder-icon, .file-icon {
  margin-left: 5px;
}

.template-list-container {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.template-list-header {
  padding: 10px;
  font-weight: bold;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.template-list {
  flex: 1;
  overflow: auto;
}

.party-select-container {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.party-select-header {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.party-select-options {
  margin: 15px 0;
}

.option-item {
  margin-bottom: 10px;
}

.template-info-form {
  margin-top: 20px;
}

.form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.form-item label {
  width: 80px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.zmcl-tag+.zmcl-tag {
  margin-left: 8px;
}
:deep(.el-card__body) {
  height: 100%;
}
</style>
