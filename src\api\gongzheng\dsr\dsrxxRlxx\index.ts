import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxRlxxVO, DsrxxRlxxForm, DsrxxRlxxQuery } from '@/api/gongzheng/dsr/dsrxxRlxx/types';

/**
 * 查询当事人-面部(人脸)信息列表
 * @param query
 * @returns {*}
 */

export const listDsrxxRlxx = (query?: DsrxxRlxxQuery): AxiosPromise<DsrxxRlxxVO[]> => {
  return request({
    url: '/dsr/dsrxxRlxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-面部(人脸)信息详细
 * @param id
 */
export const getDsrxxRlxx = (id: string | number): AxiosPromise<DsrxxRlxxVO> => {
  return request({
    url: '/dsr/dsrxxRlxx/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-面部(人脸)信息
 * @param data
 */
export const addDsrxxRlxx = (data: DsrxxRlxxForm) => {
  return request({
    url: '/dsr/dsrxxRlxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-面部(人脸)信息
 * @param data
 */
export const updateDsrxxRlxx = (data: DsrxxRlxxForm) => {
  return request({
    url: '/dsr/dsrxxRlxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-面部(人脸)信息
 * @param id
 */
export const delDsrxxRlxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxRlxx/' + id,
    method: 'delete'
  });
};
