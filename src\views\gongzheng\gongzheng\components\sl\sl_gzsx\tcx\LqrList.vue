<template>
  <div class="flex justify-between items-center mb-10px">
    <span class="text-base font-bold">领取人【双击进入编辑】</span>
    <div>
      <el-button @click="handleAdd" type="primary" size="small">新录领取人</el-button>
      <el-button @click="handleYyDsr" type="primary" size="small">引用领取人</el-button>
      <el-button @click="delLqr" size="small">删除</el-button>
    </div>
  </div>
  <el-table :data="LqrList" ref="LqrTableRef" height="260" size="small" border stripe @row-dblclick="handleEdit">
    <el-table-column type="index" label="#" width="60" align="center" />
    <el-table-column type="selection" width="50" align="center" />
    <el-table-column prop="name" label="姓名" width="120" align="center" show-overflow-tooltip />
    <el-table-column prop="dsrLx" label="类型" width="80" align="center" show-overflow-tooltip>
      <template #default="scope">
        <dict-tag :options="gz_dsr_lb" :value="scope.row.dsrLx" />
      </template>
    </el-table-column>
    <el-table-column prop="zjlx" label="证件类型" width="120" align="center" show-overflow-tooltip>
      <template #default="scope">
        <template v-if="scope.row.dsrLx==='1'">
          <dict-tag :options="gz_gr_zjlx" :value="scope.row.zjlx" />
        </template>
        <template v-if="scope.row.dsrLx==='2'">
          <dict-tag :options="gz_jg_zjlx" :value="scope.row.zjlx" />
        </template>
      </template>
    </el-table-column>
    <el-table-column prop="zjhm" label="证件号码" width="120" align="center" show-overflow-tooltip />
    <el-table-column prop="lxdh" label="联系电话" width="120" align="center" show-overflow-tooltip />
    <el-table-column prop="zz" label="住址" align="center" show-overflow-tooltip />
  </el-table>


  <!-- 引用当事人-->
  <el-dialog :title="dialog4.title" v-model="dialog4.visible" append-to-body>
    <Yydsr v-if="dialog4.visible" ref="yydsrRef" :editDlrlb="disabledDlrlb" @update-select="handleUpdateDsrSelectId">
    </Yydsr>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading2" type="primary" @click="submitForm2">确认引用</el-button>
        <el-button @click="cancel3">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 新增/编辑当事人-->
  <el-dialog :title="dialog5.title" v-model="dialog5.visible" append-to-body style="width:80%">
    <AddDsr v-if="dialog5.visible" ref="addDsrRef" @submit="handleAddDsrSubmit"></AddDsr>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading3" type="primary" @click="submitForm3">确 定</el-button>
        <el-button @click="cancel4">关 闭</el-button>
      </div>
    </template>
  </el-dialog>


  <el-dialog :title="dialog6.title" v-model="dialog6.visible" append-to-body style="width: 50%" @closed="cancel5">
    <el-form :model="lqrInfoForm" :rules="lqrRules" ref="lqrInfoFormRef" label-width="120">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item prop="name" label="姓名：">
            <el-input placeholder="请输入姓名" v-model="lqrInfoForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="dsrLx" label="类型：">
            <dict-tag :options="gz_dsr_lb" :value="lqrInfoForm.dsrLx" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item prop="zjlx" label="证件类型：">
            <template v-if="lqrInfoForm.dsrLx==='1'">
              <el-select v-model="lqrInfoForm.zjlx" placeholder="请选择证件类型">
                <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </template>
            <template v-if="lqrInfoForm.dsrLx==='2'">
              <el-select v-model="lqrInfoForm.zjlx" placeholder="请选择证件类型">
                <el-option v-for="dict in gz_jg_zjlx" :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="zjhm" label="证件号码：">
            <el-input placeholder="请输入证件号码" v-model="lqrInfoForm.zjhm"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item prop="lxdh" label="联系电话：">
            <el-input placeholder="请输入联系电话" v-model="lqrInfoForm.lxdh"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item prop="zz" label="地址：">
            <el-input placeholder="" type="textarea" v-model="lqrInfoForm.zz" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading4" type="primary" @click="submitForm4">确 定</el-button>
        <el-button @click="cancel5">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
import { ref } from '@vue/reactivity';
import { GzjzTcxLqrForm, GzTcxLqrListVo } from '@/api/gongzheng/bzfz/gzjzTcxLqr/types';
/*** 当事人引用 */
import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue';
/*** 新增/编辑当事人 */
import AddDsr from '@/views/gongzheng/dsr/dsrxxZrr/components/add_dsr.vue';
import { GzjzDsrForm, GzjzDsrVO } from '@/api/gongzheng/dev/gzjzDsr/types';
import { inject, reactive, type Ref } from 'vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import eventBus from '@/utils/eventBus';
import { GzjzTcxVO } from '@/api/gongzheng/bzfz/gzjzTcx/types';
import { addDsrxxZrr, getDsrxxZrr, updateDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
import { addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
import { addGzjzTcxLqr, addTqrDsrxx, delGzjzTcxLqr } from '@/api/gongzheng/bzfz/gzjzTcxLqr';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_dsr_lb, gz_gr_zjlx, gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_gr_zjlx', 'gz_jg_zjlx'));
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
const currentRecord = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const currentTcxInfo = inject<Ref<GzjzTcxVO>>('currentTcxInfo', ref(null));

interface Props {
  modelValue: GzTcxLqrListVo;
}

const LqrTableRef = ref<ElFormInstance>(null);
const props = defineProps<Props>();


const LqrList = computed<GzTcxLqrListVo[]>({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});


const delLqr = async () => {
  if (currentTcxInfo.value.id === null || currentTcxInfo.value.id === undefined || currentTcxInfo.value.id === '') {
    return proxy?.$modal.msgError('请先保存提存项信息');
  }
  const rows = LqrTableRef.value?.getSelectionRows() || [];
  console.log('rows', rows);
  if (rows && rows.length > 0) {
    ElMessageBox.confirm('确定删除选中的领取人吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await delGzjzTcxLqr(rows.map(i => i.id));
      eventBus.emit('tcx:lqrList:reloadInfo', {
        msg: '删除领取人，更新领取人列表'
      });
    }).catch(() => {})
  } else {
    ElMessage.warning('请先勾选要删除的领取人');
  }

};

const yydsrRef = ref<InstanceType<typeof Yydsr> | null>(null);
const buttonLoading2 = ref(false);
const dialog4 = reactive<DialogOption>({
  visible: false,
  title: ''
});
const dlrInfo = ref(null);
const disabledDlrlb = ref(false);
const selectedDsrIds = ref<string[]>([]);

const cancel3 = () => {
  dialog4.title = '';
  dialog4.visible = false;
};

const handleYyDsr = () => {
  if (currentTcxInfo.value.id === null || currentTcxInfo.value.id === undefined || currentTcxInfo.value.id === '') {
    return proxy?.$modal.msgError('请先保存提存项信息');
  }
  dialog4.title = '引用当事人';
  dialog4.visible = true;
};

const handleUpdateDsrSelectId = (data: any) => {
  if (Array.isArray(data)) {
    selectedDsrIds.value = data;
  } else {
    // 兼容传入单个对象的情况
    dlrInfo.value = data;
    selectedDsrIds.value = data.id ? [data.id] : [];
  }

  console.log('引用选中的当事人信息:', data);
};

const submitForm2 = async () => {
  if (selectedDsrIds.value.length === 0) {
    ElMessage.warning('请选择要引用的当事人');
    return;
  }
  console.log(currentTcxInfo.value);
  // console.log(currentRecord.value);
  try {
    buttonLoading2.value = true;

    // 获取选中的当事人信息
    const selectedDsrInfo = yydsrRef.value?.getSelectedDsrInfo?.() || dlrInfo.value;
    if (!selectedDsrInfo) {
      ElMessage.error('未获取到当事人信息');
      return;
    }

    // 根据当事人类型（dsrlb字段）确定dsrLx
    const dsrLx = selectedDsrInfo.dsrlb === '2' ? '2' : '1'; // dsrlb: '1'个人 '2'单位

    // 引用当事人
    for (const dsrId of selectedDsrIds.value) {
      const formData: GzjzTcxLqrForm = {
        dsrId: dsrId,
        dsrLx: dsrLx,
        tcxId:currentTcxInfo.value.id
      };
      await addGzjzTcxLqr(formData);
    }
    ElMessage.success('引用当事人成功');
    cancel3();
    // await getPartyPersonList() // 刷新列表
    eventBus.emit('tcx:lqrList:reloadInfo', {
      msg: '已引用当事人，更新领取人列表'
    });
  } catch (error) {
    console.error('引用当事人失败:', error);
    ElMessage.error('引用当事人失败');
  } finally {
    buttonLoading2.value = false;
  }
};
/*** 当事人引用 */

const addDsrRef = ref<InstanceType<typeof AddDsr> | null>(null);
const dialog5 = reactive<DialogOption>({
  visible: false,
  title: ''
});
const buttonLoading3 = ref(false);
const editDsrData = ref<GzjzDsrVO | null>(null); // 编辑时的当事人数据

const cancel4 = () => {
  dialog5.title = '';
  dialog5.visible = false;
  editDsrData.value = null;
};

const handleAddDsrSubmit = async (dsrData: any) => {
  try {
    buttonLoading3.value = true;

    console.log('okkkkkk', dsrData);

    // 构建基础表单数据
    const formData : any = {
      dsrLx: dsrData.dsrLx || '1', // 01个人 02单位
      tcxId:currentTcxInfo.value.id
    }
    // 根据当事人类型添加对应的数据
    if (dsrData.dsrLx === '1') {
      // 个人
      formData.zrrBo = {
        xm: dsrData.xm,
        xb: dsrData.xb,
        zjlx: dsrData.zjlx,
        zjhm: dsrData.zjhm,
        csrq: dsrData.csrq,
        zz: dsrData.zz,
        lxdh: dsrData.lxdh,
        dsrlb: dsrData.dsrlb || '1',
        gj: dsrData.gj || '',
        mz: dsrData.mz || '',
        remark: dsrData.remark || '',
        khh: dsrData.khh || '',
        cym: dsrData.cym || '',
        ywm: dsrData.ywm || '',
        dzyj: dsrData.dzyj || '',
        hyzk: dsrData.hyzk || 0,
        gzdw: dsrData.gzdw || '',
        wxh: dsrData.wxh || '',
        khhmc: dsrData.khhmc || '',
        khhzh: dsrData.khhzh || '',
        pjdj: dsrData.pjdj || 0,
        zp: dsrData.zp || '',
        cardImage1: dsrData.cardImage1 || '',
        cardImage2: dsrData.cardImage2 || '',
        signOffice: dsrData.signOffice || '',
        cardValidDate: dsrData.cardValidDate || ''
      };
    } else if (dsrData.dsrLx === '2') {
      // 单位
      formData.frhzzBo = {
        dwmc: dsrData.dwmc,
        dwszd: dsrData.dwszd,
        zjlx: dsrData.zjlx,
        zjhm: dsrData.zjhm,
        lxdh: dsrData.lxdh,
        fddbr: dsrData.fddbr || '',
        fddbrxb: dsrData.fddbrxb || '',
        fddbrlxdh: dsrData.fddbrlxdh || '',
        fddbrzw: dsrData.fddbrzw || '',
        fddbrzjlx: dsrData.fddbrzjlx || '',
        fddbrzjhm: dsrData.fddbrzjhm || '',
        dsrlb: dsrData.dsrlb || '2',
        fddbrzp: dsrData.fddbrzp || '',
        fddbrzz: dsrData.fddbrzz || '',
        remark: dsrData.remark || '',
        hyhm: dsrData.hyhm || '',
        ywmc: dsrData.ywmc || '',
        fzrzjlx: dsrData.fzrzjlx || '',
        fzrzjhm: dsrData.fzrzjhm || '',
        fzrxm: dsrData.fzrxm || '',
        fzrcsrq: dsrData.fzrcsrq || '',
        fzrwxh: dsrData.fzrwxh || '',
        fzrdzyj: dsrData.fzrdzyj || '',
        khh: dsrData.khh || '',
        khzh: dsrData.khzh || '',
        zjzt: dsrData.zjzt !== undefined ? dsrData.zjzt : true,
        signOffice: dsrData.signOffice || '',
        cardValidDate: dsrData.cardValidDate || ''
      };
    }
    // 新增
    await addTqrDsrxx(formData)
    ElMessage.success('新增当事人成功');
    cancel4();
    eventBus.emit('tcx:lqrList:reloadInfo', {
      msg: '新增领取人，更新领取人列表'
    });
  } catch (error) {
    console.error('保存当事人失败:', error);
    ElMessage.error('保存当事人失败');
  } finally {
    buttonLoading3.value = false;
  }
};

const submitForm3 = () => {
  // 调用AddDsr组件的提交方法
  if (addDsrRef.value && typeof (addDsrRef.value as any).submit === 'function') {
    (addDsrRef.value as any).submit();
  }
};
// 新增当事人
const handleAdd = () => {
  if (currentTcxInfo.value.id === null || currentTcxInfo.value.id === undefined || currentTcxInfo.value.id === '') {
    return proxy?.$modal.msgError('请先保存提存项信息');
  }
  editDsrData.value = null; // 清空编辑数据，表示新增
  dialog5.visible = true;
  dialog5.title = '新增当事人';
};
/*** 新增/编辑当事人*/

/** 编辑领取人*/
const lqrInfoFormRef = ref<ElFormInstance>(null);
const lqrInfoForm = reactive<GzTcxLqrListVo>({
  dsrId: undefined,
  dsrLx: '',
  id: undefined,
  lxdh: undefined,
  name: '',
  tcxId: undefined,
  zjhm: undefined,
  zjlx: undefined,
  zz: ''
});
const buttonLoading4 = ref(false);

const dialog6 = reactive<DialogOption>({
  visible: false,
  title: ''
});
const handleEdit = (row) => {
  Object.assign(lqrInfoForm, { ...row });
  dialog6.visible = true;
  dialog6.title = '编辑领取人';
};
const lqrRules = {
  name: [{ required: true, message: '姓名不能为空', trigger: ['blur'] }],
  lxdh: [{ required: true, message: '联系电话不能为空', trigger: ['blur'] }],
  zjlx: [{ required: true, message: '证件类型不能为空', trigger: ['blur'] }],
  zjhm: [{ required: true, message: '证件号码不能为空', trigger: ['blur'] }]
};


const submitForm4 = async () => {
  try {
    buttonLoading4.value = true;
    if (lqrInfoForm.dsrLx === '1') {
      const res = await getDsrxxZrr(lqrInfoForm.dsrId);
      const form = {
        ...res.data,
        xm: lqrInfoForm.name,
        lxdh: lqrInfoForm.lxdh,
        zz: lqrInfoForm.zz,
        zjlx: lqrInfoForm.zjlx,
        zjhm: lqrInfoForm.zjhm
      };
      await updateDsrxxZrr(form);
    } else if (lqrInfoForm.dsrLx === '2') {
      const form = {
        id: lqrInfoForm.dsrId,
        dwmc: lqrInfoForm.name,
        lxdh: lqrInfoForm.lxdh,
        dwszd: lqrInfoForm.zz,
        zjlx: lqrInfoForm.zjlx,
        zjhm: lqrInfoForm.zjhm
      };
      await updateDsrxxFrhzz(form);
    }
    proxy?.$modal.msgSuccess('操作成功');
    cancel5();
    eventBus.emit('tcx:lqrList:reloadInfo', {
      msg: '编辑领取人，更新领取人列表'
    });
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('保存当事人失败');
  } finally {
    buttonLoading4.value = false;
  }
};

const cancel5 = () => {
  Object.assign(lqrInfoForm, {
    dsrId: undefined,
    dsrLx: '',
    id: undefined,
    lxdh: undefined,
    name: '',
    tcxId: undefined,
    zjhm: undefined,
    zjlx: undefined,
    zz: ''
  });
  dialog6.title = '';
  dialog6.visible = false;
};

</script>
