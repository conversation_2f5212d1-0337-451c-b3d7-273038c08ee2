export interface SyzmxVO {

  id: number;

  /**
   * 水印纸编号
   */
  syzbh: string;

  /**
   * 使用地
   */
  syd: string;

  /**
   * 领用人
   */
  lyr: string;

  /**
   * 使用人
   */
  syr: string;

  /**
   * 领用时间
   */
  lysj: string;

  /**
   * 使用时间
   */
  sysj: string;

  /**
   * 入库时间
   */
  djrlsj: string;

  /**
   * 作废时间
   */
  zfsj: string;

  /**
   * 销毁时间
   */
  xhsj: string;

  /**
   * 使用状态
   */
  syzt: string;

  /**
   * 公证书编号
   */
  gzsBh: string;

}

export interface SyzmxForm extends BaseEntity {

  id?: number;
  /**
   * 水印纸编号
   */
  syzbh?: string;

  /**
   * 水印纸流水
   */
  syzls?: number;

  /**
   * 使用地
   */
  syd?: string;

  /**
   * 领用人ID
   */
  lyrId?: string | number;

  /**
   * 领用人
   */
  lyr?: string;

  /**
   * 使用人ID
   */
  syrId?: string | number;

  /**
   * 使用人
   */
  syr?: string;

  /**
   * 领用时间
   */
  lysj?: string;

  /**
   * 使用时间
   */
  sysj?: string;

  /**
   * 登记信息ID
   */
  djxxId?: string | number;

  /**
   * 入库时间
   */
  djrlsj?: string;

  /**
   * 作废时间
   */
  zfsj?: string;

  /**
   * 销毁时间
   */
  xhsj?: string;

  /**
   * 使用状态
   */
  syzt?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 公证书编号
   */
  gzsBh?: string;

}

export interface SyzmxQuery extends PageQuery {

  /**
   * 水印纸编号
   */
  syzbh?: string;

  /**
   * 领用人ID
   */
  lyrId?: string | number;

  /**
   * 使用人ID
   */
  syrId?: string | number;

  /**
   * 使用时间
   */
  sysj?: string;

  /**
   * 入库时间
   */
  djrlsj?: string;

  /**
   * 作废时间
   */
  zfsj?: string;

  /**
   * 销毁时间
   */
  xhsj?: string;

  /**
   * 使用状态
   */
  syzt?: string;

  /**
   * 公证书编号
   */
  gzsBh?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



