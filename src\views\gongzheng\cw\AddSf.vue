<template>
  <el-dialog v-model="visible" :title="title" width="480" draggable :modal="false">
    <el-form :model="sfForm" :rules="sfRules" ref="addSfForm" label-width="100">
      <el-form-item label="公证事项" prop="gzjzGzsxId">
        <el-select v-model="sfForm.gzjzGzsxId" placeholder="请选择公证事项" style="width: 100%" filterable>
          <el-option
            v-for="item in gzsxList"
            :key="item.id"
            :label="item.gzsxMc"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="费用类型" prop="fylx">
        <el-select v-model="sfForm.fylx" placeholder="请选择费用类型" style="width: 100%" filterable>
          <el-option
            v-for="dict in gz_sf_lb"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="收费场景" prop="sfcj">
        <el-select v-model="sfForm.sfcj" placeholder="请选择收费场景" style="width: 100%">
          <el-option
            v-for="dict in gz_sfcj"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计价方式" prop="jjfs">
        <el-select v-model="sfForm.jjfs" placeholder="请选择计价方式" style="width: 100%">
          <el-option
            v-for="dict in gz_jjfs"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否记账" prop="sfjz">
        <el-select v-model="sfForm.sfjz" default-first-option style="width: 120px">
          <el-option label="否" :value="'0'" />
          <el-option label="是" :value="'1'" />
        </el-select>
      </el-form-item>
      <el-form-item label="应收金额" prop="fyys">
        <el-input-number
          v-model="sfForm.fyys"
          :min="0"
          :step="1"
          :precision="2"
          placeholder="请输入应收金额"
          style="width: 100%"
          @change="calculateActualAmount"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end items-center">
        <el-button @click="comfirm" :loading="submitting" :disabled="submitting" type="primary" v-has-permi="['cwgl:sf:edit']">确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { addGzjzGzsxSfxx } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed, inject, onMounted } from 'vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
  gzsxId?: string | number;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_sf_lb, gz_sfcj, gz_jjfs } = toRefs<any>(proxy?.useDict('gz_sf_lb', 'gz_sfcj', 'gz_jjfs'));

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const props = withDefaults(defineProps<Props>(), {
  title: '添加收费项'
})

const emit = defineEmits(['update:modelValue', 'close', 'comfirm', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const sfForm = ref({
  fylx: '1',
  jjfs: '1',
  sfcj: '1',
  sfjz: '0',
  fyys: 0,
  fyjm: 0,
  fyss: 0,
  sfzt: '1',
  remark: '',
  gzjzId: props.gzjzId,
  gzjzGzsxId: props.gzsxId,
})

const addSfForm = ref<ElFormInstance>(null)

const submitting = ref(false)

const sfRules = {
  gzjzGzsxId: [{ required: true, message: '请选择公证事项', trigger: 'change' }],
  fylx: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
  sfcj: [{ required: true, message: '请选择收费场景', trigger: 'change' }],
  jjfs: [{ required: true, message: '请选择计价方式', trigger: 'change' }],
  fyys: [{ required: true, message: '请输入应收金额', trigger: 'blur' }]
}

const gzsxList = ref([])

const loadGzsxList = async () => {
  try {
     const queryParams : GzjzGzsxQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 200 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxList.value = res.rows || [];
    }
  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
  }
}

const calculateActualAmount = () => {}

const comfirm = async () => {
  const isValid = addSfForm.value?.validate();
  if(!isValid) return;

  submitting.value = true
  addGzjzGzsxSfxx({...sfForm.value}).then((res) => {
    if(res.code === 200) {
      ElMessage.success('添加成功');
      emit('success', res)
      emit('update:modelValue', false)
    }
  }).catch((err: any) => {
    ElMessage.error('添加收费项失败')
    console.log('添加收费项失败', err)
  }).finally(() => {
    submitting.value = false
  })
}

const close = () => {
  emit('update:modelValue', false)
  emit('close')
}

watch(() => props.modelValue, (val) => {
  if(!val) {
    sfForm.value = {
      fylx: '1',
      jjfs: '1',
      sfcj: '1',
      sfjz: '0',
      fyys: 0,
      fyjm: 0,
      fyss: 0,
      sfzt: '1',
      remark: '',
      gzjzId: props.gzjzId,
      gzjzGzsxId: props.gzsxId,
    }
  }
})

onMounted(() => {
  loadGzsxList();
})

</script>
