<template>
  <!-- 详情窗口 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="onModalClose">
    <template #default>
      <div style="height: 1000px; border: 1px solid #e0e0e0;">
        <div>这是弹出的内容 {{new Date()}} !!</div>
        <div class="padding-content">
          <el-space>
            <el-button :plain="true" @click="handleShowElMessage">显示消息</el-button>
            <el-button :plain="true" @click="handleShowElNotification">显示通知</el-button>
            <el-button :plain="true" @click="handleShowPopEdit">打开编辑窗口</el-button>
          </el-space>
        </div>
        <div>
        </div>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #ccc; padding-top: 8px;">
        <el-space>
          <el-button @click="onModalClose()">关 闭</el-button>

          <el-popconfirm title="您确定要删除吗？">
            <template #reference>
              <el-button>删除</el-button>
            </template>
          </el-popconfirm>

          <el-button @click="handleSave" type="primary">保存</el-button>
        </el-space>
      </div>
    </template>
  </vxe-modal>

  <popEdit ref="popEditDialogRef" v-if="showPopEdit" @close="showPopEdit=false" @success="onSaveSuccess"></popEdit>

</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  import { VxeModalProps, VxeFormProps, VxeFormItemPropTypes, VxeFormListeners } from 'vxe-pc-ui'
  import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
  import type { Action } from 'element-plus'
  import popEdit from './popEdit'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;



  const route = useRoute()

  // 定义 props 类型
  const props = defineProps<{
    title ?: '卷宗详情'
  }>()

  // 定义 emits 类型
  const emits = defineEmits<{
    (event : 'success') : void
    (event : 'close') : void
  }>()

  const showPopEdit = ref(false)
  const popEditDialogRef = ref(null)

  const showPopup = ref(false)
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: '1000px',
    height: '90%',
    escClosable: true,
    resize: true,
    showMaximize: true
  })

  const open = (option = {}) => {
    console.log('open-dialog', option);
    showPopup.value = true;
  }
  const close = () => {
    this.onModalClose();
  }

  const handleSave = () => {
    console.log('handleSave')
    emits('success')
    onModalClose();
  }

  const onModalClose = () => {
    emits('close')
    showPopup.value = false;
    console.log('窗口关闭了');
  }

  const handleShowPopEdit = () => {
    showPopEdit.value = true;
    proxy.$nextTick(() => {
      popEditDialogRef.value?.open({
        id: 1000
      })
    });
  }

  const handleShowElMessage = () => {
    ElMessage('消息提示.')
  }

  const handleShowElNotification = () => {
    ElNotification({
      title: '通知',
      message: '您有新的未读消息',
      duration: 1000 * 3,
    })
  }

  const onSaveSuccess = () => {
    ElMessageBox.alert('保存成功！', '保存提示', {
      // if you want to disable its autofocus
      // autofocus: false,
      confirmButtonText: 'OK',
      callback: (action : Action) => {
        ElMessage({
          type: 'info',
          message: `action: ${action}`,
        })
      },
    })
  }

  onMounted(() => {
    console.log('compDemo:onMounted');
  });
  onUnmounted(() => {
    console.log('compDemo:onUnmounted');
  });

  // 暴露方法给父组件
  defineExpose({
    open
  })

  console.log('一些有用的路由参数')
  console.log('路由地址', route.fullPath)
  console.log('页面标题', route.meta.title)
  console.log('路由参数', route.query)
</script>
