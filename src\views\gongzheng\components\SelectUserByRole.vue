<template>
  <el-select :disabled="props.disabled" :loading="loading" filterable clearable
  @change="handleChange" :model-value="ids" placeholder="请选择" :multiple="props.multiple"
    :style="'width:' + props.width ">
    <el-option v-for="item in roleUserList"
    :key="item.value"
    :label="item.label"
    :value="item.value" />
  </el-select>
</template>

<script setup name="SelectUserByRole" lang="ts">
  import { ref, watch } from 'vue'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  interface Props {
    modelValue ?: string | Array<string>,
    width ?: 'auto',
    roleKey ?: 'gzy',
    disabled ?: false,
    multiple ?: false,
  }
  const props = withDefaults(defineProps<Props>(), {});

  // 定义
  const loading = ref(false)
  const ids = ref(props.modelValue)
  const roleUserList = ref<UsersDataOption[]>([]);

  const getUserList = async () => {
    loading.value = true;
    const res = await proxy?.useRoleUser(props.roleKey);
    // console.log('getUserList - ' + props.roleKey, res[props.roleKey])
    roleUserList.value = res[props.roleKey];
    loading.value = false;
  }
  //父组件 调用 子组件方法
  const emits = defineEmits<{
    (event : 'update:modelValue', value : string | Array<string>) : void
    (event : 'onChange') : void
  }>()

  // 监听
  watch(() => props.modelValue, (newVal) => {
    if(newVal){
      if(props.multiple){
        ids.value = newVal;
      }else{
        ids.value = newVal;
      }
    }else{
      ids.value = undefined;
    }
    // console.log('modelValue - ' + props.roleKey, newVal)
    emits('onChange');
  })

  const handleChange = (_value) => {
    // console.log('handleChange - ' + props.roleKey, _value)
    if(_value){
      if(props.multiple){
        ids.value = _value || ids.value;
      }else{
        ids.value = _value || ids.value;
      }
    }else{
      ids.value = undefined;
    }

    // console.log('handleChange - ' + props.roleKey, ids.value)
    emits('update:modelValue', ids.value)
    emits("onChange");
  }
  const init = () => {
    setTimeout(() => {
      getUserList();
    }, 200);

  }
  //子组件暴露方法
  defineExpose({
    init
  });
  onMounted(() => {
    init();
    console.log('SelectUserByRole - init')
  });
</script>

<style scoped>

</style>
