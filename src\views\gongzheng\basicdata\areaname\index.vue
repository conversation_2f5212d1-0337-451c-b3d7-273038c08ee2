<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="数字代码" prop="numberCode">
              <el-input v-model="queryParams.numberCode" placeholder="请输入数字代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="地区名称" prop="areaName">
              <el-input v-model="queryParams.areaName" placeholder="请输入地区名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:areaname:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Edit" :disabled="single" @click="handleUpdateShow"
              v-hasPermi="['basicdata:areaname:edit']">设置显示</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Edit" :disabled="single" @click="handleUpdateNoShow"
              v-hasPermi="['basicdata:areaname:edit']">设置不显示</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['basicdata:areaname:remove']">删除</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleInit"
              v-hasPermi="['basicdata:areaname:add']">初始数据</el-button>
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['basicdata:areaname:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="areanameList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:areaname:edit']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="数字代码" align="center" prop="numberCode" />
        <el-table-column label="地区名称" align="center" prop="areaName" />
        <el-table-column label="中文拼音简写" align="center" prop="areaNameChinese" />
        <el-table-column label="地区英文简写" align="center" prop="areaNameEnglish" />
        <el-table-column label="是否显示" align="center" prop="showStatus">
          <template #default="scope">
            {{scope.row.showStatus=='1'?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column label="是否取双号" align="center" prop="evenNumbersStatus">
          <template #default="scope">
            {{scope.row.evenNumbersStatus=='1'?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column label="是否置顶" align="center" prop="topStatus">
          <template #default="scope">
            {{scope.row.topStatus=='1'?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column label="译文" align="center" prop="translation">
          <template #default="scope">
            <dict-tag :options="gz_yw_wz" :value="scope.row.translation" />
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改地区名称对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="areanameFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="数字代码" prop="numberCode">
          <el-input v-model="form.numberCode" placeholder="请输入数字代码" />
        </el-form-item>
        <el-form-item label="地区名称" prop="areaName">
          <el-input v-model="form.areaName" placeholder="请输入地区名称" />
        </el-form-item>
        <el-form-item label="中文拼音简写" prop="areaNameChinese">
          <el-input v-model="form.areaNameChinese" placeholder="请输入中文拼音简写" />
        </el-form-item>
        <el-form-item label="地区英文简写" prop="areaNameEnglish">
          <el-input v-model="form.areaNameEnglish" placeholder="请输入地区英文简写" />
        </el-form-item>
        <el-form-item label="译文" prop="translation">
          <el-select v-model="form.translation" placeholder="请选择译文" style="width: 200px;">
            <el-option v-for="dict in gz_yw_wz" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否显示" prop="showStatus">
          <el-radio-group v-model="form.showStatus">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否置顶" prop="topStatus">
          <el-radio-group v-model="form.topStatus">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否取双号" prop="evenNumbersStatus">
          <el-radio-group v-model="form.evenNumbersStatus">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Areaname" lang="ts">
  import { listAreaname, getAreaname, delAreaname, addAreaname, updateAreaname, updateShowStatus, initBaseData } from '@/api/gongzheng/basicdata/areaname';
  import { AreanameVO, AreanameQuery, AreanameForm } from '@/api/gongzheng/basicdata/areaname/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_yw_wz, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yw_wz', 'gz_yes_or_no'));
  const areanameList = ref<AreanameVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const areanameFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : AreanameForm = {
    id: undefined,
    numberCode: undefined,
    areaName: undefined,
    areaNameChinese: undefined,
    areaNameEnglish: undefined,
    showStatus: "1",
    evenNumbersStatus: "0",
    topStatus: "0",
    translation: undefined,
  }
  const data = reactive<PageData<AreanameForm, AreanameQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      numberCode: undefined,
      areaName: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "$comment不能为空", trigger: "blur" }
      ],
      numberCode: [
        { required: true, message: "数字代码不能为空", trigger: "blur" }
      ],
      areaName: [
        { required: true, message: "地区名称不能为空", trigger: "blur" }
      ],
      showStatus: [
        { required: true, message: "是否显示不能为空", trigger: "change" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询地区名称列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listAreaname(queryParams.value);
    areanameList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    areanameFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : AreanameVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  const handleInit = async () => {
    await proxy?.$modal.confirm('确认初始数据么？').finally(() => loading.value = false);
    await initBaseData();
    proxy?.$modal.msgSuccess("操作成功");
    await getList();
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加地区名称";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: AreanameVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getAreaname(_id);
    Object.assign(form.value, res.data);
    form.value.showStatus = res.data.showStatus + '';
    form.value.evenNumbersStatus = res.data.evenNumbersStatus + '';
    form.value.topStatus = res.data.topStatus + '';
    dialog.visible = true;
    dialog.title = "修改地区名称";
  }
  const handleUpdateShow = async (row ?: AreanameVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('确认全部显示？').finally(() => loading.value = false);
    await updateShowStatus(_ids, '1');
    proxy?.$modal.msgSuccess("操作成功");
    await getList();
  }
  const handleUpdateNoShow = async (row ?: AreanameVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('确认全部不显示?').finally(() => loading.value = false);
    await updateShowStatus(_ids, '0');
    proxy?.$modal.msgSuccess("操作成功");
    await getList();
  }

  /** 提交按钮 */
  const submitForm = () => {
    areanameFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateAreaname(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addAreaname(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: AreanameVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除地区名称编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delAreaname(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/areaname/export', {
      ...queryParams.value
    }, `areaname_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
