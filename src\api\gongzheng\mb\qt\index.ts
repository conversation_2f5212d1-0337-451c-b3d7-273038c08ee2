// 模板列表API service
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Template, TemplateQuery, TemplateSaveParams, Role } from './types';

// 查询模板列表
export const listTemplates = (query: TemplateQuery): AxiosPromise<{ records: Template[]; total: number }> => {
  return request({
    url: '/mb/mbJcxx/list',
    method: 'get',
    params: query
  });
};

// 新增模板
export const addTemplate = (data: TemplateSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbJcxx',
    method: 'post',
    data
  });
};

// 编辑模板
export const updateTemplate = (data: TemplateSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbJcxx',
    method: 'put',
    data
  });
};

// 删除模板
export const deleteTemplate = (ids: string): AxiosPromise<any> => {
  return request({
    url: `/mb/mbJcxx/${ids}`,
    method: 'delete'
  });
};

export const getTemplate = (id: string): AxiosPromise<any> => {
  return request({
    url: `/mb/mbJcxx/${id}`,
    method: 'get'
  });
};

// 下载模板
export const downloadTemplate = (id: string): AxiosPromise<Blob> => {
  return request({
    url: `/mb/mbJcxx/download/${id}`,
    method: 'get',
    responseType: 'blob'
  });
};

// 获取模板权限角色列表
export const getTemplateRoles = (templateId: string): AxiosPromise<Role[]> => {
  return request({
    url: `/mb/mbJcxx/roles/${templateId}`,
    method: 'get'
  });
};

// 设置模板权限
export const setTemplateRoles = (templateId: string, roleIds: string[]): AxiosPromise<any> => {
  return request({
    url: `/mb/mbJcxx/roles/${templateId}`,
    method: 'post',
    data: { roleIds }
  });
};

//设为默认

export const setDefault = (templateId: string): AxiosPromise<any> => {
  return request({
    url: '/mb/mbJcxx/setDefault',
    method: 'post',
    params: {
      id: templateId
    }
  });
};
