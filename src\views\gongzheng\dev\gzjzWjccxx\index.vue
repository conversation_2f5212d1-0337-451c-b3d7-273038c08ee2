<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文本名称" prop="wbmc">
              <el-input v-model="queryParams.wbmc" placeholder="请输入文本名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文本路径" prop="wblj">
              <el-input v-model="queryParams.wblj" placeholder="请输入文本路径" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="类型" prop="lx">
              <el-input v-model="queryParams.lx" placeholder="请输入类型" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="译文名称" prop="ywmc">
              <el-input v-model="queryParams.ywmc" placeholder="请输入译文名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="译文路径" prop="ywlj">
              <el-input v-model="queryParams.ywlj" placeholder="请输入译文路径" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证事项" prop="gzsx">
              <el-input v-model="queryParams.gzsx" placeholder="请输入公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="queryParams.gzsbh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="其他证书编号" prop="qtzsbh">
              <el-input v-model="queryParams.qtzsbh" placeholder="请输入其他证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区块链hash" prop="qklhash">
              <el-input v-model="queryParams.qklhash" placeholder="请输入区块链hash" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证卷宗-公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入公证卷宗-公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzWjccxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzWjccxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzWjccxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzWjccxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzWjccxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID(关联公证卷宗)" align="center" prop="gzjzId" />
        <el-table-column label="文本名称" align="center" prop="wbmc" />
        <el-table-column label="文本路径" align="center" prop="wblj" />
        <el-table-column label="类型" align="center" prop="lx" />
        <el-table-column label="译文名称" align="center" prop="ywmc" />
        <el-table-column label="译文路径" align="center" prop="ywlj" />
        <el-table-column label="公证事项" align="center" prop="gzsx" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="其他证书编号" align="center" prop="qtzsbh" />
        <el-table-column label="区块链hash" align="center" prop="qklhash" />
        <el-table-column label="是否翻译" align="center" prop="sfFy" />
        <el-table-column label="公证卷宗-公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzWjccxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzWjccxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证书文件存储信息v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzWjccxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" />
        </el-form-item>
        <el-form-item label="文本名称" prop="wbmc">
          <el-input v-model="form.wbmc" placeholder="请输入文本名称" />
        </el-form-item>
        <el-form-item label="文本路径" prop="wblj">
          <el-input v-model="form.wblj" placeholder="请输入文本路径" />
        </el-form-item>
        <el-form-item label="类型" prop="lx">
          <el-input v-model="form.lx" placeholder="请输入类型" />
        </el-form-item>
        <el-form-item label="译文名称" prop="ywmc">
          <el-input v-model="form.ywmc" placeholder="请输入译文名称" />
        </el-form-item>
        <el-form-item label="译文路径" prop="ywlj">
          <el-input v-model="form.ywlj" placeholder="请输入译文路径" />
        </el-form-item>
        <el-form-item label="公证事项" prop="gzsx">
          <el-input v-model="form.gzsx" placeholder="请输入公证事项" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsbh">
          <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="其他证书编号" prop="qtzsbh">
          <el-input v-model="form.qtzsbh" placeholder="请输入其他证书编号" />
        </el-form-item>
        <el-form-item label="区块链hash" prop="qklhash">
          <el-input v-model="form.qklhash" placeholder="请输入区块链hash" />
        </el-form-item>
        <el-form-item label="公证卷宗-公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入公证卷宗-公证事项ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzWjccxx" lang="ts">
import { listGzjzWjccxx, getGzjzWjccxx, delGzjzWjccxx, addGzjzWjccxx, updateGzjzWjccxx } from '@/api/gongzheng/dev/gzjzWjccxx';
import { GzjzWjccxxVO, GzjzWjccxxQuery, GzjzWjccxxForm } from '@/api/gongzheng/dev/gzjzWjccxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzWjccxxList = ref<GzjzWjccxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzWjccxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzWjccxxForm = {
  id: undefined,
  gzjzId: undefined,
  wbmc: undefined,
  wblj: undefined,
  lx: undefined,
  ywmc: undefined,
  ywlj: undefined,
  gzsx: undefined,
  gzsbh: undefined,
  qtzsbh: undefined,
  qklhash: undefined,
  sfFy: undefined,
  gzjzGzsxId: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzWjccxxForm, GzjzWjccxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    wbmc: undefined,
    wblj: undefined,
    lx: undefined,
    ywmc: undefined,
    ywlj: undefined,
    gzsx: undefined,
    gzsbh: undefined,
    qtzsbh: undefined,
    qklhash: undefined,
    sfFy: undefined,
    gzjzGzsxId: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证书文件存储信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzWjccxx(queryParams.value);
  gzjzWjccxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzWjccxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzWjccxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证书文件存储信息v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzWjccxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzWjccxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证书文件存储信息v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzWjccxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzWjccxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzWjccxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzWjccxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证书文件存储信息v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzWjccxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzWjccxx/export', {
    ...queryParams.value
  }, `gzjzWjccxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
