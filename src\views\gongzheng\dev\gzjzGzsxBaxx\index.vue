<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证事项" prop="gzsx">
              <el-input v-model="queryParams.gzsx" placeholder="请输入公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="份数" prop="fs">
              <el-input v-model="queryParams.fs" placeholder="请输入份数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公益法律服务" prop="gyflfw">
              <el-input v-model="queryParams.gyflfw" placeholder="请输入公益法律服务" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="新型公证业务" prop="xxgzyw">
              <el-input v-model="queryParams.xxgzyw" placeholder="请输入新型公证业务" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="涉疫情/灾情公证" prop="sysz">
              <el-input v-model="queryParams.sysz" placeholder="请输入涉疫情/灾情公证" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="服务类型" prop="fwlx">
              <el-input v-model="queryParams.fwlx" placeholder="请输入服务类型" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件哈希值" prop="wjHash">
              <el-input v-model="queryParams.wjHash" placeholder="请输入文件哈希值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="上传时间" prop="scsj">
              <el-date-picker clearable
                v-model="queryParams.scsj"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择上传时间"
              />
            </el-form-item>
            <el-form-item label="上传结果" prop="scjg">
              <el-input v-model="queryParams.scjg" placeholder="请输入上传结果" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="关联卷宗公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入关联卷宗公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzGzsxBaxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzGzsxBaxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzGzsxBaxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzGzsxBaxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzGzsxBaxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证事项" align="center" prop="gzsx" />
        <el-table-column label="份数" align="center" prop="fs" />
        <el-table-column label="公益法律服务" align="center" prop="gyflfw" />
        <el-table-column label="新型公证业务" align="center" prop="xxgzyw" />
        <el-table-column label="涉疫情/灾情公证" align="center" prop="sysz" />
        <el-table-column label="服务类型" align="center" prop="fwlx" />
        <el-table-column label="文件哈希值" align="center" prop="wjHash" />
        <el-table-column label="上传时间" align="center" prop="scsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.scsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传结果" align="center" prop="scjg" />
        <el-table-column label="关联卷宗公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzGzsxBaxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzGzsxBaxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证事项-备案信息v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzGzsxBaxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证事项" prop="gzsx">
          <el-input v-model="form.gzsx" placeholder="请输入公证事项" />
        </el-form-item>
        <el-form-item label="份数" prop="fs">
          <el-input v-model="form.fs" placeholder="请输入份数" />
        </el-form-item>
        <el-form-item label="公益法律服务" prop="gyflfw">
          <el-input v-model="form.gyflfw" placeholder="请输入公益法律服务" />
        </el-form-item>
        <el-form-item label="新型公证业务" prop="xxgzyw">
          <el-input v-model="form.xxgzyw" placeholder="请输入新型公证业务" />
        </el-form-item>
        <el-form-item label="涉疫情/灾情公证" prop="sysz">
          <el-input v-model="form.sysz" placeholder="请输入涉疫情/灾情公证" />
        </el-form-item>
        <el-form-item label="服务类型" prop="fwlx">
          <el-input v-model="form.fwlx" placeholder="请输入服务类型" />
        </el-form-item>
        <el-form-item label="文件哈希值" prop="wjHash">
          <el-input v-model="form.wjHash" placeholder="请输入文件哈希值" />
        </el-form-item>
        <el-form-item label="上传时间" prop="scsj">
          <el-date-picker clearable
            v-model="form.scsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择上传时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上传结果" prop="scjg">
          <el-input v-model="form.scjg" placeholder="请输入上传结果" />
        </el-form-item>
        <el-form-item label="关联卷宗公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入关联卷宗公证事项ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzGzsxBaxx" lang="ts">
import { listGzjzGzsxBaxx, getGzjzGzsxBaxx, delGzjzGzsxBaxx, addGzjzGzsxBaxx, updateGzjzGzsxBaxx } from '@/api/gongzheng/dev/gzjzGzsxBaxx';
import { GzjzGzsxBaxxVO, GzjzGzsxBaxxQuery, GzjzGzsxBaxxForm } from '@/api/gongzheng/dev/gzjzGzsxBaxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzGzsxBaxxList = ref<GzjzGzsxBaxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzGzsxBaxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzGzsxBaxxForm = {
  id: undefined,
  gzsx: undefined,
  fs: undefined,
  gyflfw: undefined,
  xxgzyw: undefined,
  sysz: undefined,
  fwlx: undefined,
  wjHash: undefined,
  scsj: undefined,
  scjg: undefined,
  gzjzGzsxId: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzGzsxBaxxForm, GzjzGzsxBaxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzsx: undefined,
    fs: undefined,
    gyflfw: undefined,
    xxgzyw: undefined,
    sysz: undefined,
    fwlx: undefined,
    wjHash: undefined,
    scsj: undefined,
    scjg: undefined,
    gzjzGzsxId: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证事项-备案信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzGzsxBaxx(queryParams.value);
  gzjzGzsxBaxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzGzsxBaxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzGzsxBaxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证事项-备案信息v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzGzsxBaxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzGzsxBaxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证事项-备案信息v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzGzsxBaxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzGzsxBaxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzGzsxBaxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzGzsxBaxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证事项-备案信息v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzGzsxBaxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzGzsxBaxx/export', {
    ...queryParams.value
  }, `gzjzGzsxBaxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
