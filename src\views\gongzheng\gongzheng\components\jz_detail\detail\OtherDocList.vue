<template>
  <el-card>
    <template #header>
      <strong class="text-base">其它文档</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="typeName" label="文档类型" width="180" align="center" />
      <el-table-column prop="docList" label="文档名称">
        <template #default="{ row, column }">
          <div class="flex flex-wrap gap-6px">
            <el-tag v-for="item in row.docList" :key="item.id">
              <el-button type="primary" link @click="openPreview(item)">{{item.wbmc}}</el-button>
            </el-tag>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { docOpenShow } from '@/views/gongzheng/doc/DocEditor'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

type FileInfo = {
  id: string,
  parentId: string,
  xmmc: string,   // 文件名
  lrfs: any,
  bclj: string,   // 保存路径
  pxxh: number, 
  clType: string,
  sfFy: string,
  fyzt: string,
  ywmc: string,
  ywlj: string,
  dsrId: string,
  visitUrl: string
}

const openPreview = (data: any) => {
  const ossInfo = JSON.parse(data?.wblj || '{}')
  if(ossInfo.path) {
    docOpenShow(ossInfo.path)
  }
}
</script>

<style scoped>
.zmcl-tag+.zmcl-tag {
  margin-left: 4px;
}
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
