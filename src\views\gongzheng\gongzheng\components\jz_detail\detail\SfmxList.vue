<template>
  <el-card>
    <template #header>
      <strong class="text-base">收费明细表</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="fylx" label="费用类型" width="120" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_sf_lb, row.fylx) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="sfjz" label="是否记账" width="120" align="center" />
      <el-table-column prop="gzjzGzsx" label="公证事项" align="center" />
      <el-table-column prop="gzsbh" label="公证书编号" width="120" align="center" show-overflow-tooltip/>
      <el-table-column prop="jjfs" label="计价方式" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_jjfs, row.jjfs) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="sfcj" label="收费场景" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_sfcj, row.sfcj) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="fyys" label="应收" align="center" show-overflow-tooltip />
      <el-table-column prop="fyjm" label="减免" align="center" />
      <el-table-column prop="fyss" label="已收" align="center" />
      <el-table-column prop="fytf" label="退费" align="center" />
      <el-table-column prop="fyss" label="实收" align="center" />
      <el-table-column prop="sfzt" label="收费状态" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.sfzt === '2'" type="primary">已完成</el-tag>
          <el-tag v-else type="danger">未收费</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { dictMapFormat } from '@/utils/ruoyi'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_sf_lb, gz_jjfs, gz_sfcj } = toRefs<any>(proxy?.useDict('gz_sf_lb', 'gz_jjfs', 'gz_sfcj'));


</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
