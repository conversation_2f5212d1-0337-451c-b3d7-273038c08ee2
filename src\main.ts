import { createApp } from 'vue';
// global css
import 'virtual:uno.css';
import '@/assets/styles/index.scss';
import 'element-plus/theme-chalk/dark/css-vars.css';

// App、router、store
import App from './App.vue';
import store from './store';
import router from './router';

// 自定义指令
import directive from './directive';

// 注册插件
import plugins from './plugins/index'; // plugins

// 高亮组件
// import 'highlight.js/styles/a11y-light.css';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
import HighLight from '@highlightjs/vue-plugin';

// svg图标
import 'virtual:svg-icons-register';
import ElementIcons from '@/plugins/svgicon';

// permission control
import './permission';

// 国际化
import i18n from '@/lang/index';

// vxeTable
import domZIndex from 'dom-zindex'
import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
domZIndex.setCurrent(1500)
// VXETable.config({
//   zIndex: 999999
// });

// 全局业务组件
import selectDict from '@/views/components/selectors/select-dict.vue'
// 二次封装ElDialog组件, 全屏时内容区域高度自适应铺满
import GzDialog from '@/components/GzDialog/index.vue';


// 修改 el-dialog 默认点击遮照为不关闭
import { ElDialog } from 'element-plus';
ElDialog.props.closeOnClickModal.default = false;

const app = createApp(App);

app.component('selectDict', selectDict);
app.component('GzDialog', GzDialog);

app.use(HighLight);
app.use(ElementIcons);
app.use(router);
app.use(store);
app.use(i18n);
app.use(VxeUIAll);
app.use(VXETable);
app.use(plugins);
// 自定义指令
directive(app);

app.mount('#app');
