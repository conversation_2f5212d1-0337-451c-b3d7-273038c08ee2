<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="机构名称" prop="jgmc">
              <el-input v-model="queryParams.jgmc" placeholder="请输入机构名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作所处环节" prop="czschj">
              <el-input v-model="queryParams.czschj" placeholder="请输入操作所处环节" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="日志内容" prop="rznr">
              <el-input v-model="queryParams.rznr" placeholder="请输入日志内容" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作日期" prop="czrq">
              <el-date-picker clearable
                v-model="queryParams.czrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择操作日期"
              />
            </el-form-item>
            <el-form-item label="操作人" prop="czr">
              <el-input v-model="queryParams.czr" placeholder="请输入操作人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="queryParams.gzsbh" placeholder="请输入公证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="流程意见" prop="lcyj">
              <el-input v-model="queryParams.lcyj" placeholder="请输入流程意见" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzrzLcrzxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzrzLcrzxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzrzLcrzxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzrzLcrzxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzrzLcrzxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID(关联公证卷宗)" align="center" prop="gzjzId" />
        <el-table-column label="机构名称" align="center" prop="jgmc" />
        <el-table-column label="操作所处环节" align="center" prop="czschj" />
        <el-table-column label="日志内容" align="center" prop="rznr" />
        <el-table-column label="操作日期" align="center" prop="czrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.czrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="czr" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" />
        <el-table-column label="流程意见" align="center" prop="lcyj" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzrzLcrzxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzrzLcrzxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证日志-流程日志对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzrzLcrzxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" />
        </el-form-item>
        <el-form-item label="机构名称" prop="jgmc">
          <el-input v-model="form.jgmc" placeholder="请输入机构名称" />
        </el-form-item>
        <el-form-item label="操作所处环节" prop="czschj">
          <el-input v-model="form.czschj" placeholder="请输入操作所处环节" />
        </el-form-item>
        <el-form-item label="日志内容" prop="rznr">
            <el-input v-model="form.rznr" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="操作日期" prop="czrq">
          <el-date-picker clearable
            v-model="form.czrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择操作日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作人" prop="czr">
          <el-input v-model="form.czr" placeholder="请输入操作人" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsbh">
          <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" />
        </el-form-item>
        <el-form-item label="流程意见" prop="lcyj">
          <el-input v-model="form.lcyj" placeholder="请输入流程意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzrzLcrzxx" lang="ts">
import { listGzrzLcrzxx, getGzrzLcrzxx, delGzrzLcrzxx, addGzrzLcrzxx, updateGzrzLcrzxx } from '@/api/gongzheng/dev/gzrzLcrzxx';
import { GzrzLcrzxxVO, GzrzLcrzxxQuery, GzrzLcrzxxForm } from '@/api/gongzheng/dev/gzrzLcrzxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzrzLcrzxxList = ref<GzrzLcrzxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzrzLcrzxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzrzLcrzxxForm = {
  id: undefined,
  gzjzId: undefined,
  jgmc: undefined,
  czschj: undefined,
  rznr: undefined,
  czrq: undefined,
  czr: undefined,
  gzsbh: undefined,
  lcyj: undefined
}
const data = reactive<PageData<GzrzLcrzxxForm, GzrzLcrzxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    jgmc: undefined,
    czschj: undefined,
    rznr: undefined,
    czrq: undefined,
    czr: undefined,
    gzsbh: undefined,
    lcyj: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证日志-流程日志列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzrzLcrzxx(queryParams.value);
  gzrzLcrzxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzrzLcrzxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzrzLcrzxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证日志-流程日志";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzrzLcrzxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzrzLcrzxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证日志-流程日志";
}

/** 提交按钮 */
const submitForm = () => {
  gzrzLcrzxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzrzLcrzxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzrzLcrzxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzrzLcrzxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证日志-流程日志编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzrzLcrzxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzrzLcrzxx/export', {
    ...queryParams.value
  }, `gzrzLcrzxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
