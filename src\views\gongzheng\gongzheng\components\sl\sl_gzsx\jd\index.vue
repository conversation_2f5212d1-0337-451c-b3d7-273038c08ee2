<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" append-to-body>
    <div v-loading="modelState.loading">
      <JdInfoForm v-model="jdInfo" ref="jdInfoFormRef" />
      <KhList v-model="jdKhList" ref="khListRef" />
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">保存</el-button>
        <el-button v-if="jdInfo.id" @click="comfirmDel" :loading="modelState.submitting" :disabled="modelState.submitting" type="danger">删除</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { addGzjzJd, delGzjzJd, getGzjzJdByGzjzGzsxId, listGzjzJd, updateGzjzJd } from '@/api/gongzheng/bzfz/gzjzJd';
import JdInfoForm from './JdInfoForm.vue';
import KhList from './KhList.vue'
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { GzsxJdVo, JdKhVo } from './type';
import { GzjzJdVO, JdKhVO } from '@/api/gongzheng/bzfz/gzjzJd/types';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '借贷'
})

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const curGzsxGzsBh = ref('')

provide('curGzsxGzsBh', curGzsxGzsBh)

const modelState = reactive({
  visible: false,
  title: '借贷',
  gzjzId: undefined,
  loading: false,
  submitting: false,
})

const jdInfoFormRef = ref<any>(null)
const khListRef = ref<any>(null)

const jdData = ref<GzjzJdVO>({})

const jdInfo = ref<GzjzJdVO>({})
const jdKhList = ref<JdKhVO[]>([])

const gzjzGzsxInfo = ref<any>({})

const close = () => {
  modelState.visible = false
  jdData.value = {}
  jdInfo.value = {}
  jdKhList.value = []
}

const closed = () => {
  jdData.value = {}
  jdInfo.value = {}
  jdKhList.value = []
}

const initData = async () => {
  try {
    modelState.loading = true;
    const params = {
      // gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
      // pageNum: 1,
      // pageSize: 100
    }
    // const res = await listGzjzJd(params);
    const res = await getGzjzJdByGzjzGzsxId(params);
    if(res.code === 200 && res.data.id) {
      jdData.value = res.data;
      jdInfo.value = res.data;
      jdKhList.value = JSON.parse(res.data.dsrList || '[]')
    } else {
      jdInfo.value = {
        gzyId: curGzjz.value?.gzybm,
        gzyXm: curGzjz.value?.gzyxm,
      }
    }
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.loading = false;
  }
 }

const open = (data?: any) => {
  modelState.visible  = true;
  modelState.gzjzId = data?.gzjzId || undefined;
  modelState.title = data?.title || '';
  gzjzGzsxInfo.value = data?.gzjzGzsxInfo || {};
  curGzsxGzsBh.value = data?.gzjzGzsxInfo?.gzsBh || '';

  initData();
}

const comfirmSave = async () => {
  try {
    const isValidOk = await jdInfoFormRef.value?.validate();
    if(!isValidOk) return;
    const params = {
      gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
      ...jdInfo.value,
      dsrList: JSON.stringify(jdKhList.value),
    }

    modelState.submitting = true;
    let res = null;
    if(params.id) {
      res = await updateGzjzJd(params);
    } else {
      res = await addGzjzJd(params);
    }
    if(res.code === 200) {
      ElMessage.success('保存成功')
      close()
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (err: any) {
    console.error(err)
  } finally {
    modelState.submitting = false;
  }
}

const comfirmDel = () => {
  ElMessageBox.confirm('确认要删除该借贷信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    modelState.submitting = true;
    const res = await delGzjzJd(jdData.value.id);
    if(res.code === 200) {
      ElMessage.success('删除成功');
      close();
    }
  }).finally(() => {
    modelState.submitting = false;
  })
}

defineExpose({
  open
})

</script>
