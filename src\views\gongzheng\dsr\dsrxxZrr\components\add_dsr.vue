<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="个人" name="first">
        <Grxx  ref="grxxRef" :vo="form" :dialigEdit="true" @update-count="handleUpdateGrxx">
        </Grxx>

      </el-tab-pane>
      <el-tab-pane label="企业" name="second">
        <Dwxx  ref="dwxxRef" :vo="form2" :dialigEdit="true"
          @update-count="handleUpdateGwxx">
        </Dwxx>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, toRefs, getCurrentInstance, watch, onMounted } from 'vue'
  import type { ComponentInternalInstance } from 'vue'
  import { DsrxxZrrVO, DsrxxZrrQuery, DsrxxZrrForm } from '@/api/gongzheng/dsr/dsrxxZrr/types';
  import { listDsrxxFrhzz, getDsrxxFrhzz, delDsrxxFrhzz, addDsrxxFrhzz, updateDsrxxFrhzz } from '@/api/gongzheng/dsr/dsrxxFrhzz';
  import { DsrxxFrhzzVO, DsrxxFrhzzQuery, DsrxxFrhzzForm } from '@/api/gongzheng/dsr/dsrxxFrhzz/types';
  import type { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
  import Dwxx from '@/views/gongzheng/dsr/dsrxxZrr/components/dw/dw_xx.vue'
  import Grxx from '@/views/gongzheng/dsr/dsrxxZrr/components/gr/gr_xx.vue'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  // 定义Props
  interface Props {
    editData?: GzjzDsrVO | null // 编辑时传入的当事人数据
  }

  const props = defineProps<Props>()

  // 定义Emits
  interface Emits {
    (e: 'submit', data: any): void
  }

  const emit = defineEmits<Emits>()
  const grxxRef = ref<InstanceType<typeof Grxx> | null>(null);
  const dwxxRef = ref<InstanceType<typeof Dwxx> | null>(null);
  const activeName = ref('first');
  const initFormData : DsrxxZrrForm = {
    id: undefined,
    slbh: undefined,
    xm: undefined,
    xb: undefined,
    lxdh: undefined,
    zz: undefined,
    zjlx: undefined,
    zjhm: undefined,
    dsrlb: undefined,
    zp: undefined,
    gj: undefined,
    mz: undefined,
    csrq: undefined,
    remark: undefined,
    khh: undefined,
    cym: undefined,
    ywm: undefined,
    dzyj: undefined,
    hyzk: undefined,
    gzdw: undefined,
    wxh: undefined,
    khhmc: undefined,
    khhzh: undefined,
    pjdj: undefined
  }
  const data = reactive<PageData<DsrxxZrrForm, DsrxxZrrQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      xm: undefined,
      xb: undefined,
      lxdh: undefined,
      zz: undefined,
      zjlx: undefined,
      zjhm: undefined,
      dsrlb: "1",
      zp: undefined,
      gj: undefined,
      mz: undefined,
      csrq: undefined,
      khh: undefined,
      cym: undefined,
      ywm: undefined,
      dzyj: undefined,
      hyzk: undefined,
      gzdw: undefined,
      wxh: undefined,
      khhmc: undefined,
      khhzh: undefined,
      pjdj: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });

  const { queryParams, form } = toRefs(data);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialigEdit = ref(true);

  const dialigEdit2 = ref(true);
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initDwxxFormData : DsrxxFrhzzForm = {
    slbh: undefined,
    dwmc: undefined,
    dwszd: undefined,
    zjlx: undefined,
    zjhm: undefined,
    lxdh: undefined,
    fddbr: undefined,
    fddbrxb: undefined,
    fddbrlxdh: undefined,
    fddbrzw: undefined,
    fddbrzjlx: undefined,
    fddbrzjhm: undefined,
    dsrlb: undefined,
    fddbrzp: undefined,
    fddbrzz: undefined,
    remark: undefined,
    hyhm: undefined,
    ywmc: undefined,
    fzrzjlx: undefined,
    fzrzjhm: undefined,
    fzrxm: undefined,
    fzrcsrq: undefined,
    fzrwxh: undefined,
    fzrdzyj: undefined,
    khh: undefined,
    khzh: undefined,
    dz: undefined,
    fzrxb: undefined,
    dlrzjhm: undefined,
    dlrxmzjlx: undefined,
    dlrxm: undefined,
    fddbrcsrq: undefined,
  }
  /** 单位客户*/
  const data2 = reactive({
    form2: { ...initDwxxFormData },
    queryParams2: {
      pageNum: 1,
      pageSize: 10,
      slbh: undefined,
      dwmc: undefined,
      dwszd: undefined,
      zjlx: undefined,
      zjhm: undefined,
      lxdh: undefined,
      fddbr: undefined,
      fddbrxb: undefined,
      fddbrlxdh: undefined,
      fddbrzw: undefined,
      fddbrzjlx: undefined,
      fddbrzjhm: undefined,
      dsrlb: undefined,
      fddbrzp: undefined,
      fddbrzz: undefined,
      hyhm: undefined,
      ywmc: undefined,
      fzrzjlx: undefined,
      fzrzjhm: undefined,
      fzrxm: undefined,
      fzrcsrq: undefined,
      fzrwxh: undefined,
      fzrdzyj: undefined,
      khh: undefined,
      khzh: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      khh: [
        { required: true, message: "客户号不能为空", trigger: "blur" }
      ],
      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      gj: [
        { required: true, message: "国际不能为空", trigger: "change" }
      ],
      mz: [
        { required: true, message: "证件类型不能为空", trigger: "change" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "change" }
      ],
      csrq: [
        { required: true, message: "出生日期不能为空", trigger: "change" }
      ],
    }
  });
  const { form2 } = toRefs(data2);
  const { queryParams2 } = toRefs(data2);
  const handleClick = () => {

  }
  const handleUpdateGrxx = (vo : DsrxxZrrVO) => {
    if (vo) {
      form.value = vo;
      console.log('add_dsr >>>>>> ', form.value)
    }
  };
  const handleUpdateGwxx = (vo : DsrxxFrhzzVO) => {
    if (vo) {
      form2.value = vo;
      console.log(form2.value)
    }
  };

  // 监听编辑数据的变化，初始化表单
  watch(() => props.editData, (newEditData) => {
    if (newEditData) {
      // 编辑模式：回显数据并自动切换标签页
      console.log('编辑模式，当事人数据:', newEditData)

      if (newEditData.dsrLx === '1') {
        // 个人 - 自动切换到个人标签页并回显数据
        activeName.value = 'first'

        // 优先使用zrrBo对象中的数据，如果没有则使用扁平数据
        const zrrData = newEditData.zrrBo

        form.value = {
          ...initFormData,
          id: zrrData?.id || newEditData.dsrId,
          xm: zrrData?.xm || newEditData.name || '', // 姓名
          xb: zrrData?.xb || newEditData.sex || '', // 性别
          zjlx: zrrData?.zjlx || newEditData.certificateType || '', // 证件类型
          zjhm: zrrData?.zjhm || newEditData.certificateNo || '', // 证件号码
          csrq: zrrData?.csrq || newEditData.birthDate || '', // 出生日期
          zz: zrrData?.zz || newEditData.address || '', // 住址
          lxdh: zrrData?.lxdh || newEditData.contactTel || '', // 联系电话
          dsrlb: zrrData?.dsrlb || '1', // 个人类别
          gj: zrrData?.gj || '', // 国籍
          mz: zrrData?.mz || '', // 民族
          remark: zrrData?.remark || '', // 备注
          khh: zrrData?.khh || '', // 开户行
          cym: zrrData?.cym || '', // 曾用名
          ywm: zrrData?.ywm || '', // 英文名
          dzyj: zrrData?.dzyj || '', // 电子邮件
          hyzk: zrrData?.hyzk || 0, // 婚姻状况
          gzdw: zrrData?.gzdw || '', // 工作单位
          wxh: zrrData?.wxh || '', // 微信号
          khhmc: zrrData?.khhmc || '', // 开户行名称
          khhzh: zrrData?.khhzh || '', // 开户行账号
          pjdj: zrrData?.pjdj || 0, // 评级等级
          zp: zrrData?.zp || '' // 照片
        }
        console.log('回显个人数据:', form.value)
        // 设置编辑状态
        dialigEdit.value = false // false表示编辑模式，true表示新增模式
      } else if (newEditData.dsrLx === '2') {
        // 单位 - 自动切换到企业标签页并回显数据
        activeName.value = 'second'

        // 优先使用frhzzBo对象中的数据，如果没有则使用扁平数据
        const frhzzData = newEditData.frhzzBo

        form2.value = {
          ...initDwxxFormData,
          dwmc: frhzzData?.dwmc || newEditData.name || '', // 单位名称
          dwszd: frhzzData?.dwszd || newEditData.address || '', // 单位所在地
          zjlx: frhzzData?.zjlx || newEditData.certificateType || '', // 证件类型
          zjhm: frhzzData?.zjhm || newEditData.certificateNo || '', // 证件号码
          lxdh: frhzzData?.lxdh || newEditData.contactTel || '', // 联系电话
          fddbr: frhzzData?.fddbr || '', // 法定代表人
          fddbrxb: frhzzData?.fddbrxb || '', // 法人性别
          fddbrlxdh: frhzzData?.fddbrlxdh || '', // 法人联系电话
          fddbrzw: frhzzData?.fddbrzw || '', // 法人职务
          fddbrzjlx: frhzzData?.fddbrzjlx || '', // 法人证件类型
          fddbrzjhm: frhzzData?.fddbrzjhm || '', // 法人证件号码
          dsrlb: frhzzData?.dsrlb || '2', // 单位类别
          fddbrzp: frhzzData?.fddbrzp || '', // 法人照片
          fddbrzz: frhzzData?.fddbrzz || '', // 法人住址
          remark: frhzzData?.remark || '', // 备注
          hyhm: frhzzData?.hyhm || '', // 会员号码
          ywmc: frhzzData?.ywmc || '', // 英文名称
          fzrzjlx: frhzzData?.fzrzjlx || '', // 负责人证件类型
          fzrzjhm: frhzzData?.fzrzjhm || '', // 负责人证件号码
          fzrxm: frhzzData?.fzrxm || '', // 负责人姓名
          fzrcsrq: frhzzData?.fzrcsrq || '', // 负责人出生日期
          fzrwxh: frhzzData?.fzrwxh || '', // 负责人微信号
          fzrdzyj: frhzzData?.fzrdzyj || '', // 负责人电子邮件
          khh: frhzzData?.khh || '', // 开户行
          khzh: frhzzData?.khzh || '' // 开户账号
        }
        // 单独设置ID，避免类型错误
        if (form2.value) {
          (form2.value as any).id = newEditData.dsrId
        }
        console.log('回显单位数据:', form2.value)
        // 设置编辑状态
        dialigEdit2.value = false // false表示编辑模式，true表示新增模式
      }
    } else {
      // 新增模式：清空表单，默认显示个人标签页
      form.value = { ...initFormData }
      form2.value = { ...initDwxxFormData }
      activeName.value = 'first'
      dialigEdit.value = true // true表示新增模式
      dialigEdit2.value = true
      console.log('新增模式，清空表单')
    }
  }, { immediate: true })

  // 暴露提交方法给父组件
  const submit = async () => {
    if (activeName.value === 'first') {
      const isGrxxValid = await grxxRef.value.validate();
      if (!isGrxxValid) {
        return;
      }
      // 个人数据
      const personData = {
        ...form.value,
        dsrLx: '1',
        dsrId: props.editData?.dsrId
      }
      emit('submit', personData)
    } else {
      const isDwxxValid = await dwxxRef.value.validate();
      if (!isDwxxValid) {
        return;
      }
      // 单位数据
      const companyData = {
        ...form2.value,
        dsrLx: '2',
        dsrId: props.editData?.dsrId
      }
      emit('submit', companyData)
    }
  }

  defineExpose({
    submit
  })
</script>

<style>
</style>
