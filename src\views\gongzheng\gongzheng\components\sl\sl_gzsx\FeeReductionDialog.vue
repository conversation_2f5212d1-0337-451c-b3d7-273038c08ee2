<template>
  <el-dialog v-model="visible" :title="'费用减免申请'" :width="width" @close="doClose" destroy-on-close>
    <div class="fee-reduction-form">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="form-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人:" prop="applicant">
              <el-input v-model="formData.applicant" placeholder="请输入申请人" readonly disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期:" prop="applicationDate">
              <el-date-picker v-model="formData.applicationDate" type="date" placeholder="请选择申请日期"
                value-format="YYYY-MM-DD" style="width: 100%" readonly disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审批人*:" prop="approver">
              <el-select v-model="formData.approver" filterable placeholder="请选择审批人" style="width: 100%">
                <el-option v-for="item in tsjzspy" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="申请原因*:" prop="applicationReason">
          <el-input v-model="formData.applicationReason" type="textarea" :rows="4" placeholder="请输入申请原因" />
        </el-form-item>
      </el-form>

      <!-- <div class="case-section">
        <div class="section-header">
          <h3>卷宗信息</h3>
        </div>

        <el-descriptions :column="3" border v-if="formData.caseInfo">
          <el-descriptions-item label="卷宗号">{{ formData.caseInfo.jzbh }}</el-descriptions-item>
          <el-descriptions-item label="公证员">{{ formData.caseInfo.gzyxm }}</el-descriptions-item>
          <el-descriptions-item label="助理">{{ formData.caseInfo.zlxm }}</el-descriptions-item>
          <el-descriptions-item label="受理日期">{{ formData.caseInfo.slrq }}</el-descriptions-item>
          <el-descriptions-item label="公证类别">{{ formData.caseInfo.lb }}</el-descriptions-item>
          <el-descriptions-item label="当事人">{{ formData.caseInfo.dsrxm }}</el-descriptions-item>
        </el-descriptions>
        <div v-else>
          <el-empty description="未获取到卷宗信息" />
        </div>
      </div> -->

      <div class="item-section">
        <h3>费用减免列表</h3>
        <el-table :data="formData.itemList" border stripe style="width: 100%" height="450px">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="费用类型" prop="fylx" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ dictMapFormat(gz_sf_lb, scope.row.fylx) }}
            </template>
          </el-table-column>
          <el-table-column label="公证事项" prop="gzjzGzsx" align="center" show-overflow-tooltip />
          <el-table-column label="公证书编号" prop="gzsbh" align="center" show-overflow-tooltip />
          <el-table-column label="应收" prop="fyys" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.fyys) }}
            </template>
          </el-table-column>
          <el-table-column label="已收" prop="fyss" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.fyss) }}
            </template>
          </el-table-column>
          <el-table-column label="减免金额" prop="fyjm" align="center" width="120">
            <template #default="scope">
              <el-input v-model.number="scope.row.fyjm" type="number" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="减免后费用" align="center">
            <template #default="scope">
              ¥{{ (Number(scope.row.fyys ?? 0) - Number(scope.row.fyjm ?? 0)).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="收费状态" prop="sfzt" align="center" width="100">
            <template #default="scope">
              {{ dictMapFormat(gz_sfzt, scope.row.sfzt) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="fee-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">原费用总额：</span>
                <span class="value">¥{{ totalOriginalFee.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">减免总额：</span>
                <span class="value">¥{{ totalReductionAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">减免后总额：</span>
                <span class="value">¥{{ totalFinalFee.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleSubmit">提交审批</el-button>
        <!-- <el-button :loading="saving" @click="handleSaveDraft">保存草稿</el-button> -->
        <!-- <el-button @click="handlePrint">打印申请书</el-button> -->
        <el-button @click="doClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, getCurrentInstance, toRefs, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { dictMapFormat, formatDate } from '@/utils/ruoyi'
  import { addTslcSqb, saveDraftTslcSqb, getChargeList } from '@/api/gongzheng/tslc/tslcSqb'
  import { docOpenShow, docGenerator } from '@/views/gongzheng/doc/DocEditor'
  import { getMbWd } from '@/api/gongzheng/mb/mbWd';
  import { UserDocGenParams } from '@/views/gongzheng/doc/type'
  import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types'
  import { addGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx'
  interface Props {
    recordId ?: string | number | null
    record ?: any
    width ?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    width: '1000px'
  })

  const emits = defineEmits<{
    (event : 'success', data ?: any) : void
    (event : 'close') : void
  }>()

  const { proxy } = getCurrentInstance() as any
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser ? proxy.useRoleUser('tsjzspy') : { tsjzspy: ref([]) })
  const { gz_sf_lb, gz_sfzt } = toRefs<any>(proxy?.useDict ? proxy.useDict('gz_sf_lb', 'gz_sfzt') : { gz_sf_lb: ref([]), gz_sfzt: ref([]) })

  const visible = ref(false)
  const formRef = ref()
  const submitting = ref(false)
  const saving = ref(false)

  const formatMoney = (amount : unknown) => {
    const n = Number(amount ?? 0)
    return Number.isNaN(n) ? '0.00' : n.toFixed(2)
  }

  const formData = reactive<any>({
    id: null,
    applicant: '',
    applicationDate: new Date().toISOString().split('T')[0],
    approver: '',
    decisionDocument: '',
    decisionNumber: '',
    decisionSerial: '',
    applicationReason: '',
    caseInfo: null,
    itemList: [] as any[]
  })

  const formRules = reactive({
    approver: [{ required: true, message: '请选择审批人', trigger: 'change' }],
    applicationReason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
  })

  const totalOriginalFee = computed(() => {
    return formData.itemList.reduce((sum : number, item : any) => sum + Number(item.fyys ?? 0), 0)
  })
  const totalReductionAmount = computed(() => {
    return formData.itemList.reduce((sum : number, item : any) => sum + Number(item.fyjm ?? 0), 0)
  })
  const totalFinalFee = computed(() => totalOriginalFee.value - totalReductionAmount.value)

  const buildCaseInfoFromRecord = (rec : any) => {
    if (!rec) return null
    const r = rec?.value ?? rec
    const caseInfo = {
      jzbh: r?.jzbh ?? r?.jzh ?? r?.caseNumber ?? '',
      gzyxm: r?.gzyxm ?? r?.gzyXm ?? r?.notaryName ?? '',
      zlxm: r?.zlxm ?? r?.assistantName ?? '',
      slrq: r?.slrq ?? r?.acceptDate ?? '',
      lb: (r?.lb ?? r?.category ?? r?.gzlbdm ?? '') + '',
      dsrxm: r?.dsrxm ?? r?.applicantName ?? '',
      gzsbh: r?.gzsbh ?? r?.gzsBh ?? r?.certificateNumber ?? '',
      gzjzId: r?.gzjzId ?? r?.id ?? props.recordId ?? undefined,
    }
    return caseInfo
  }

  const resetForm = () => {
    const userStore = useUserStore()
    Object.assign(formData, {
      id: null,
      applicant: userStore.nickname,
      applicationDate: new Date().toISOString().split('T')[0],
      approver: '',
      decisionDocument: '',
      decisionNumber: '',
      decisionSerial: '',
      applicationReason: '',
      caseInfo: null,
      itemList: []
    })
    formRef.value?.clearValidate()
  }

  const loadItemList = async (caseInfo : any) => {
    if (!caseInfo?.gzjzId || !caseInfo?.jzbh) return
    try {
      const res : any = await getChargeList({ gzjzId: caseInfo.gzjzId, jzh: caseInfo.jzbh, zt: "1" })
      const rows = res?.rows || res?.data?.rows || []
      formData.itemList = rows.map((it : any) => ({
        fylx: it.fylx,
        gzjzGzsx: it.gzjzGzsx || it.gzsx,
        gzsbh: it.gzsbh,
        fyys: Number(it.fyys ?? 0),
        fyss: Number(it.fyss ?? 0),
        fyjm: Number(it.fyjm ?? 0),
        sfzt: (it.sfzt ?? '') + '',
        gzjzGzsxId: it.gzjzGzsxId
      }))
    } catch (e) {
      ElMessage.error('加载费用明细失败')
    }
  }

  const open = async () => {
    visible.value = true
    resetForm()
    // 预填卷宗信息
    const caseInfo = buildCaseInfoFromRecord(props.record)
    if (caseInfo) {
      formData.caseInfo = caseInfo
      await loadItemList(caseInfo)
    }
  }

  const close = () => {
    visible.value = false
    emits('close')
  }

  const doClose = () => {
    close()
  }

  const handleSubmit = async () => {
    try {
      submitting.value = true
      await formRef.value?.validate()
      if (!formData.caseInfo) {
        ElMessage.warning('缺少卷宗信息')
        return
      }
      if (totalReductionAmount.value <= 0) {
        ElMessage.warning('本次减免金额必须大于0')
        return
      }
      for (let i = 0; i < formData.itemList.length; i++) {
        const it : any = formData.itemList[i]
        const val = Number(it.fyjm || 0)
        const max = Number(it.fyys || 0)
        if (val > max) {
          ElMessage.warning(`第${i + 1}行本次减免金额不能超过应收金额`)
          return
        }
      }
      const userStore = useUserStore()
      const payload : any = {
        id: formData?.id,
        tslcLx: '5',
        gzsbh: formData.caseInfo?.gzsbh,
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber,
        items: formData.itemList
      }
      await addTslcSqb(payload)
      ElMessage.success('提交成功')
      emits('success', payload)
      close()
    } catch (e) {
      ElMessage.error('请检查表单信息')
    } finally {
      submitting.value = false
    }
  }

  const handleSaveDraft = async () => {
    try {
      saving.value = true
      await formRef.value?.validate()
      if (!formData.caseInfo) {
        ElMessage.warning('缺少卷宗信息')
        return
      }
      if (totalReductionAmount.value <= 0) {
        ElMessage.warning('本次减免金额必须大于0')
        return
      }
      for (let i = 0; i < formData.itemList.length; i++) {
        const it : any = formData.itemList[i]
        const val = Number(it.fyjm || 0)
        const max = Number(it.fyys || 0)
        if (val > max) {
          ElMessage.warning(`第${i + 1}行本次减免金额不能超过应收金额`)
          return
        }
      }
      const userStore = useUserStore()
      const payload : any = {
        id: formData?.id,
        tslcLx: '5',
        gzjzId: formData.caseInfo?.gzjzId,
        gzsbh: formData.caseInfo?.gzsbh,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber,
        items: formData.itemList
      }
      await saveDraftTslcSqb(payload)
      ElMessage.success('草稿保存成功')
      emits('success', payload)
      close()
    } catch (e) {
      ElMessage.error('草稿保存失败')
    } finally {
      saving.value = false
    }
  }

  const handlePrint = async () => {

    let params : UserDocGenParams = {
      bizId: formData.caseInfo?.gzjzId,
      mbWdId: '1950199581615128578',
      extraParams: {
        gzxsId: formData.caseInfo?.gzjzId,
        // tslcId: "1957737649867935745"
        item: JSON.stringify(formData.itemList),
        sqyy:formData.applicationReason

      }
    }
    const loading2 = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    docGenerator(params, {
      success: (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        const fileName = `费用减免-${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
        const docInfo : GzjzWjccxxForm = {
          wbmc: fileName,
          wblj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            typeCode: 92,
            typeName: '费用减免',
            mbWdId: '1950199581615128578'
          }),
          lx: 92
        }
        relateDoc(docInfo, path);
      }
    }).catch((err : any) => {
      console.error('文档生成失败', err)
    }).finally(() => {
      loading2.close()
    })
  }
  // 添加生成后的文档（关联生成文档）
  const relateDoc = async (docInfo : GzjzWjccxxForm, path : string) => {
    try {
      const params = {
        ...docInfo,
        gzjzId: formData.caseInfo?.gzjzId,
      }
      const res = await addGzjzWjccxx(params);
      if (res.code === 200) {
        docOpenShow(path)
      }
    } catch (err : any) {
      console.log('关联生成文档错误', err)
      ElMessage.error('添加文档异常')
    }
  }
  // 格式化日期显示
  // const formatDate = (dateStr : string) => {
  //   if (!dateStr) return '';
  //   try {
  //     const date = new Date(dateStr);
  //     return date.toLocaleDateString('zh-CN', {
  //       year: 'numeric',
  //       month: 'long',
  //       day: 'numeric'
  //     });
  //   } catch {
  //     return dateStr;
  //   }
  // };

  defineExpose({ open, close })

  watch(
    () => props.recordId,
    () => {
      // 若父级切换了卷宗，可在打开时重新预填
    }
  )
</script>

<style scoped>
  .fee-reduction-form {
    padding: 20px;
  }

  .form-container {
    margin-bottom: 20px;
  }

  .case-section {
    margin-bottom: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .item-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: bold;
  }

  .fee-summary {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .summary-item:last-child {
    margin-bottom: 0;
  }

  .summary-item .label {
    font-weight: bold;
    color: #606266;
  }

  .summary-item .value {
    font-size: 16px;
    color: #ff0000;
    font-weight: bold;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>
