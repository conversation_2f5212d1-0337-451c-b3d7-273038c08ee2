<template>
  <div class="log-container">
    <!-- 流程日志列表 -->
    <div class="section-container">
      <div class="section-header">流程日志列表</div>
      <div class="section-content">
        <el-table :data="processLogList" border stripe v-loading="processLoading">
          <el-table-column type="index" label="#" width="50" align="center" />
          <el-table-column label="流程日志" align="center" min-width="200">
            <template #default="{ row }">
              <div class="process-log">
                <!-- <span>{{ row.czschj || '未知环节' }}</span>
                <span class="arrow">———></span> -->
                <span>{{ formatProcessAction(row) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="lcyj" label="流程意见" align="center" />
          <el-table-column prop="czrxm" label="审批人" align="center" />
          <el-table-column label="是否通过" align="center">
            <template #default="{ row }">
              {{ getApprovalStatus(row.sftg) }}
            </template>
          </el-table-column>
          <el-table-column prop="czschj" label="流程状态" align="center" />
          <el-table-column prop="czrq" label="日期" align="center" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.czrq) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 操作日志列表 -->
    <div class="section-container">
      <div class="section-header">操作日志列表</div>
      <div class="section-content">
        <el-table :data="operationLogList" border stripe v-loading="operationLoading">
          <el-table-column type="index" label="#" width="50" align="center" />
          <el-table-column prop="czhj" label="环节" align="center" width="120" />
          <el-table-column prop="rznr" label="日志内容" align="center" min-width="300" />
          <el-table-column prop="czrxm" label="操作人" align="center" width="120" />
          <el-table-column prop="czsj" label="操作时间" align="center" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.czsj) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 短信发送日志列表 -->
    <div class="section-container">
      <div class="section-header">短信发送日志列表</div>
      <div class="section-content">
        <el-table :data="smsLogList" border stripe>
          <el-table-column type="index" label="#" width="50" align="center" />
          <el-table-column prop="phoneNumber" label="手机号码" align="center" width="150" />
          <el-table-column prop="content" label="短信内容" align="center" min-width="300" />
          <el-table-column prop="sendTime" label="发送时间" align="center" width="180" />
          <el-table-column prop="status" label="发送状态" align="center" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, onMounted } from 'vue'
import type { Ref } from 'vue'
import { ElMessage } from 'element-plus'
import { listGzrzLcrzxx } from '@/api/gongzheng/gongzheng/gzrzLcrzxx'
import { listGzrzCzrzxx } from '@/api/gongzheng/gongzheng/gzrzCzrzxx'
import type { GzrzLcrzxxVO, GzrzLcrzxxQuery } from '@/api/gongzheng/gongzheng/gzrzLcrzxx/types'
import type { GzrzCzrzxxVO, GzrzCzrzxxQuery } from '@/api/gongzheng/gongzheng/gzrzCzrzxx/types'

// 获取当前查看的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

// 加载状态
const processLoading = ref(false)
const operationLoading = ref(false)

// 流程日志列表数据 - 使用真实API数据
const processLogList = ref<GzrzLcrzxxVO[]>([])

// 操作日志列表数据 - 使用真实API数据
const operationLogList = ref<GzrzCzrzxxVO[]>([])

// 短信发送日志列表数据 - 保留模拟数据，因为暂无对应API
const smsLogList = ref([
  {
    id: '1',
    phoneNumber: '13912345678',
    content: '尊敬的侯添盛先生/女士，您的公证业务已受理，业务编号为202500954，请保持电话畅通，我们将尽快处理您的业务。',
    sendTime: '2025-06-03 17:12:45',
    status: '成功'
  },
  {
    id: '2',
    phoneNumber: '13987654321',
    content: '尊敬的侯添盛先生/女士，您的公证业务已完成，请凭短信和身份证到前台领取公证书，谢谢。',
    sendTime: '2025-06-03 17:30:12',
    status: '成功'
  }
])

// 获取流程日志列表
const getProcessLogList = async (gzjzId: string | number) => {
  if (!gzjzId) return

  processLoading.value = true
  try {
    const query: GzrzLcrzxxQuery = {
      gzjzId: gzjzId,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzrzLcrzxx(query)
    if (res.code === 200) {
      processLogList.value = res.rows || []
    } else {
      ElMessage.error('获取流程日志失败：' + (res.msg || '未知错误'))
      processLogList.value = []
    }
  } catch (error: any) {
    console.error('获取流程日志失败:', error)
    ElMessage.error('获取流程日志失败: ' + (error?.message || '未知错误'))
    processLogList.value = []
  } finally {
    processLoading.value = false
  }
}

// 获取操作日志列表
const getOperationLogList = async (gzjzId: string | number) => {
  if (!gzjzId) return

  operationLoading.value = true
  try {
    const query: GzrzCzrzxxQuery = {
      gzjzId: gzjzId,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzrzCzrzxx(query)
    if (res.code === 200) {
      operationLogList.value = res.rows || []
    } else {
      ElMessage.error('获取操作日志失败：' + (res.msg || '未知错误'))
      operationLogList.value = []
    }
  } catch (error: any) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败: ' + (error?.message || '未知错误'))
    operationLogList.value = []
  } finally {
    operationLoading.value = false
  }
}

// 格式化流程操作内容
const formatProcessAction = (row: GzrzLcrzxxVO) => {
  if (row.rznr) {
    return row.rznr
  }
  return `${row.czschj || '操作'} 操作人(${row.czrxm || '未知'})`
}

// 获取审批状态
const getApprovalStatus = (sftg: string) => {
  if (!sftg) return '未知'

  if (sftg === '1') {
    return '通过'
  } else if (sftg === '0') {
    return '不通过'
  }
  return '待定'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateStr
  }
}

// 获取所有日志数据
const getAllLogs = async (gzjzId: string | number) => {
  if (!gzjzId) return

  // 并行获取两种日志数据
  await Promise.all([
    getProcessLogList(gzjzId),
    getOperationLogList(gzjzId)
  ])
}

// 监听当前记录ID变化
watch(
  () => currentRecordId.value,
  (newId) => {
    if (newId) {
      getAllLogs(newId)
    } else {
      // 清空数据
      processLogList.value = []
      operationLogList.value = []
    }
  },
  { immediate: true }
)

// 页面加载时执行
onMounted(() => {
  if (currentRecordId.value) {
    getAllLogs(currentRecordId.value)
  }
})
</script>

<style scoped>
.log-container {
  /* padding: 20px; */
  height: 100%;
  /* background: #f5f7fa; */
  overflow: auto;
}

.section-container {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 15px;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.section-content {
  padding: 0;
}

.process-log {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  margin: 0 10px;
  color: #409eff;
}
</style>
