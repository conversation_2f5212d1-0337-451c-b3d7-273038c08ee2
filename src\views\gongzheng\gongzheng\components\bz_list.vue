<template>
  <div>
    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px">
      <el-form-item label="卷宗号" prop="jzbh">
        <el-input v-model="queryParams.jzbh"  placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="公证书编号" prop="gzsbh">
        <el-select v-model="queryParams.params.nf" placeholder="请选择" clearable style="width: 100px; margin-right: 4px;">
          <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
        <el-select v-model="queryParams.params.zh" placeholder="请选择" clearable style="width: 140px;">
          <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
        第<el-input v-model="queryParams.params.ls" placeholder="请输入" clearable @keyup.enter="handleQuery"
          style="width: 100px;" />号
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" v-loading="loading" style="width: 100%" border highlight-current-row height="300px" >
      <el-table-column align="center" width="80">
        <template #default="{ row }">
          <el-button type="primary" @click="showBzjl(row)" size="small" link>查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="卷宗号" prop="jzbh" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="公证书编号" prop="gzsbh" width="160" align="center" show-overflow-tooltip />
      <el-table-column label="公证事项" prop="gzsx" align="center" show-overflow-tooltip />
      <el-table-column label="受理时间" prop="slrq" width="120" align="center" :formatter="formatDate" show-overflow-tooltip />
    </el-table>
    <pagination v-model:limit="queryParams.pageSize" v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" @pagination="getList" />

    <el-dialog v-model="dialogJzjbxx" title="办证记录信息" @close="closeJzjbxx">
      <BasicInfoForm v-model="Jzjbxx"></BasicInfoForm>
      <template #footer>
        <el-button type="primary" @click="closeJzjbxx">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx, getGzjzJbxxByDsr } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import { clearEmptyProperty, genYearOptions } from '@/utils/ruoyi';
  import BasicInfoForm from '@/views/gongzheng/gongzheng/components/sl/BasicInfoForm.vue';
  import { onMounted } from 'vue';

  interface Props {
    dsrInfo: GzjzDsrVO
  }

  const props = defineProps<Props>();

  const gzsbh_years = genYearOptions(2021);

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_nf, gz_gzs_bh_jg, gz_gzs_zh } = toRefs<any>(proxy?.useDict('gz_nf', 'gz_gzs_bh_jg', 'gz_gzs_zh'));

  const initFormData : GzjzJbxxForm = {
    id: undefined
  }
  const data = reactive<Partial<PageData<GzjzJbxxForm, GzjzJbxxQuery>>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      params: {
        nf: undefined,
        zh: undefined,
        ls: ''
      }

    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const total = ref(0)
  const list = ref([])
  const loading = ref(false)
  const queryFormRef = ref();

  const dialogJzjbxx = ref(false);
  const Jzjbxx = ref<GzjzJbxxForm>({} as GzjzJbxxForm);

  const handleQuery = () => {
    getList();
  }
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      params: {
        nf: undefined,
        zh: undefined,
        ls: ''
      }
    };
    handleQuery();
  }

  /** 日期格式化 */
  const formatDate = (row : any, column : any, cellValue : string) => {
    if (cellValue) {
      const date = new Date(cellValue), year = date.getFullYear(),
        month = String(date.getMonth() + 1).padStart(2, '0'),
        day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
      // return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
    }
    return '';
  };

  // 查看办证记录
  const showBzjl = (row: any) => {
    console.log('查看办证记录', row)
    dialogJzjbxx.value = true;
    Jzjbxx.value = row;
  }

  const closeJzjbxx = () => {
    dialogJzjbxx.value = false;
    Jzjbxx.value = {} as GzjzJbxxForm; // 清空数据
  }

  // 查看办证记录
  const getList = async () => {
    loading.value = true;
    try {
      // 清理空值参数，避免发送空字符串
      const cleanParams = clearEmptyProperty(queryParams.value);

      // const { nf, zh, ls } = clearEmptyProperty(cleanParams.params);
      // const gzsbh = `${nf ? `（${nf}）` : ''}${gz_gzs_zh.value.find(i => i.value === zh)?.label || ''}${ls ? `第${ls}号` : ''}`;
      // if(!!gzsbh) {
      //   cleanParams.gzsbh = gzsbh;
      // }

      const { dsrId } = props?.dsrInfo || {} as GzjzDsrVO;
      if(!dsrId) return;

      const { pageNum, pageSize, params, jzbh } = cleanParams;
      const { nf, zh, ls } = params;

      const pms = {
        dsrId,
        jzbh,
        pageNum,
        pageSize,
        params: {
          gzsNf: nf,
          gzsZh: zh,
          gzsLs: ls
        }
      }

      // const res = await listGzjzJbxx(cleanParams);
      const res = await getGzjzJbxxByDsr(pms);
      list.value = res.rows;
      total.value = res.total;

    } catch (error: any) {
      console.error('查询失败:', error);
      proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  }

  onMounted(() => {
    getList();
  });
</script>

<style>
</style>
