<script setup lang="ts">
  import {
    ref,
    onMounted
  } from 'vue'
  import {
    openDocument
  } from '@/api/gongzheng/dev/pageoffice';
  import {
    UnifiedDocumentVO
  } from '@/api/gongzheng/dev/pageoffice/types';

  const poHtmlCode = ref('');
  const open_params = ref('');
  const saveUrl = ref("");
  const extraParams = ref('');
  function OnPageOfficeCtrlInit() {
     // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
    if(open_params.value.action!=='view'){

     pageofficectrl.AddCustomToolButton("保存", "Save", 1); //其中"Save"对应function Save()函数，并且需要在onMounted中挂载。
     pageofficectrl.AddCustomToolButton("另存为", "SaveAs", 12);
    }
    pageofficectrl.AddCustomToolButton("页面设置", "PrintSet", 0);
    pageofficectrl.AddCustomToolButton("打印预览", "PrintViewFile()", 6);
    pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
    pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
    pageofficectrl.AddCustomToolButton("-", "", 0);
    pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
  }

  function Save() {
    //使用SaveFilePage属性设置后端保存方法的Controller路由地址，这个地址必须从"/"开始，并且也可以向此路由地址传递json字符串参数，示例如下：
    const saveFileUrl = saveUrl.value;
    let paramValue = new URLSearchParams(open_params.value); //为了简单起见，这里直接使用打开时的参数。
    const jsonStr = JSON.stringify(extraParams.value);
    const encodedStr = encodeURIComponent(jsonStr);
    paramValue=paramValue.toString()+"&extraParams="+encodedStr;
    console.log(paramValue)
    pageofficectrl.SaveFilePage = `${saveFileUrl}?${paramValue}`;
    //在这里写您保存前的代码
    pageofficectrl.WebSave();
  }

  function AfterDocumentOpened() {
    //在这里写您文档打开后自动触发的代码

  }

  function SaveAs() {
    pageofficectrl.ShowDialog(3);
  }

  function PrintSet() {
    pageofficectrl.ShowDialog(5);
  }

  function PrintViewFile() {
    pageofficectrl.PrintPreview();
  }

  function PrintFile() {
    pageofficectrl.ShowDialog(4);
  }

  function Close() {
    pageofficectrl.CloseWindow();
  }

  function IsFullScreen() {
    pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
  }

  async function handleProcess() {

    const {
      data
    } = await openDocument(open_params.value);
    console.log(data)
    if (data) {
      poHtmlCode.value = data.htmlCode;
      saveUrl.value = data.saveUrl;
      open_params.value = open_params.value
      delete open_params.value.extraParams;
      // console.log(poHtmlCode.value)
      // console.log(saveUrl.value)
    }
  }

  onMounted(() => {
    open_params.value = JSON.parse(pageofficectrl.WindowParams);
    extraParams.value = open_params.value.extraParams;
    console.log(open_params.value)
    // 请求后端打开文件
    handleProcess();
    //将需要回调的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
    window.POPageMounted = {
      OnPageOfficeCtrlInit,
      AfterDocumentOpened,
      Save,
      SaveAs,
      PrintSet,
      PrintFile,
      Close,
      IsFullScreen,
      PrintViewFile
    }; //其中OnPageOfficeCtrlInit必须
    //将当前页面methods中定义的函数挂载到PageOffice控件，例如控件触发的事件、自定义按钮触发的函数。
  })
</script>

<template>
  <!-- 此div用来加载PageOffice客户端控件，其中div的高宽及位置就决定了控件的大小及位置 -->
  <div style="width:auto; height:900px;" v-html="poHtmlCode" v-if="poHtmlCode"></div>
</template>
