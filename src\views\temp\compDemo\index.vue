<template>
  <!-- 组件演示首页 -->
  <div class="p-2">
    <h1>组件演示首页</h1>

    <div style="padding:15px;">
      <el-space>
        <el-button type="primary" @click="handleShowDetail">打开详情</el-button>

        <el-button type="primary" @click="handleShowPopEmpty">打开空的窗口</el-button>

        <el-button type="primary" @click="handleShowIdCardReader">读取身份证</el-button>
      </el-space>
    </div>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <b>业务组件</b>
        </div>
      </template>
      <div class="padding-content">
        <div>
          <el-space>
            <el-text>字典选择组件</el-text>
            <selectDict v-model="dictSelectedValue" dict-type="gz_flyz"></selectDict>
          </el-space>
        </div>
        <div>
          <el-space>
            <el-text>字典选择组件</el-text>
            <selectDict v-model="dictSelectedValue" dict-type="gz_flyz" display-type="radio"></selectDict>
          </el-space>
        </div>
        <div>
          <el-text>已选择:{{dictSelectedValue}}</el-text>
        </div>
      </div>
    </el-card>

    <popDetail ref="popDetailRef" v-if="showPopDetail" @close="showPopDetail=false;onPopClose" @success="onPopSuccess">
    </popDetail>

    <!-- 身份证读卡器组件，不用时从VNode中移除 -->
    <popIdCardReader v-if="showIdCardReader" ref="popIdCardReaderRef" @success="onReadCardSuccess"
      @close="showIdCardReader=false;">
    </popIdCardReader>

    <popEmpty ref="popEmptyRef" v-if="showPopEmpty" @close="showPopEmpty=false"></popEmpty>

  </div>

</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router'
  import { VxeModalProps, VxeFormProps, VxeFormItemPropTypes, VxeFormListeners } from 'vxe-pc-ui'
  import popDetail from './popDetail'
  import popEmpty from './popEmpty'

  // 引入身份证读卡器组件
  import popIdCardReader from '@/views/components/popIdCardReader.vue'

  // 显示详情页
  const showPopDetail = ref(false)
  const popDetailRef = ref(null)
  // 定义身份证读卡器组件引用
  const popIdCardReaderRef = ref(null)
  // 是否加载身份证读卡器组件
  const showIdCardReader = ref(false)
  // 演示空窗体
  const showPopEmpty = ref(false)
  const popEmptyRef = ref(null)


  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const route = useRoute()

  // 引用子组件(数组的引用方式)
  // const listItemRef = ref<Array<InstanceType<typeof myListItem>> | null>(null)  // 不用这么复制的写法，直接写 ref(null) 既可

  const dictSelectedValue = ref('5')

  // 打开演示窗口
  const handleShowPopEmpty = () => {
    showPopEmpty.value = true;
    proxy.$nextTick(() => {
      popEmptyRef.value?.open();
    })
  }

  // 打开身份证读卡器
  const handleShowIdCardReader = () => {
    console.log('id-card-reader');
    // 使用的时候才加载组件，不用的时候卸载组件，释放数据
    showIdCardReader.value = true;
    proxy.$nextTick(() => {
      popIdCardReaderRef.value?.open({});
    })
  }
  // 身份证读卡器读到数据时的回调
  const onReadCardSuccess = (cardInfo) => {
    console.log('onReadCardSuccess', cardInfo);
    popIdCardReaderRef.value?.close();
  }


  const handleShowDetail = () => {
    showPopDetail.value = true;
    proxy.$nextTick(() => {
      popDetailRef.value?.open({
        id: 1000
      });
    })
  }
  const onPopSuccess = () => {
    console.log('弹窗返回成功');
    // ElMessage({
    //   message: '弹窗返回成功.',
    //   type: 'success',
    // })
  }
  const onPopClose = () => {
    console.log('弹窗关闭了~~~');
  }


  onMounted(() => {
    console.log('compDemo:onMounted');
  });
  onUnmounted(() => {
    console.log('compDemo:onUnmounted');
  });



  console.log('一些有用的路由参数')
  console.log('路由地址', route.fullPath)
  console.log('页面标题', route.meta.title)
  console.log('路由参数', route.query)
</script>
