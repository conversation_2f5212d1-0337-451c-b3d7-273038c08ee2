import request from '@/utils/request'
import { UpdateGzsxFessData } from './types';

/**
 * 查询公证卷宗-公证事项-收费明细列表
 * @param query
 * @returns {*}
 */
export function listGzjzGzsxSfxx(query?: any) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/list',
    method: 'get',
    params: query
  });
}

export function listGzsxSfxxByGzjz(id: string | number) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/listByGzjz/' + id,
    method: 'get'
  });
}


/**
 * 查询公证卷宗-公证事项-收费明细详细
 * @param id
 */
export function getGzjzGzsxSfxx(id: string | number) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/' + id,
    method: 'get'
  });
}

/**
 * 新增公证卷宗-公证事项-收费明细
 * @param data
 */
export function addGzjzGzsxSfxx(data: any) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx',
    method: 'post',
    data: data
  });
}

/**
 * 修改公证卷宗-公证事项-收费明细
 * @param data
 */
export function updateGzjzGzsxSfxx(data: any) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx',
    method: 'put',
    data: data
  });
}

/**
 * 删除公证卷宗-公证事项-收费明细
 * @param id
 */
export function delGzjzGzsxSfxx(id: string | number | Array<string | number>) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/' + id,
    method: 'delete'
  });
}

/**
 * 导出公证卷宗-公证事项-收费明细
 * @param query
 * @returns {*}
 */
export function exportGzjzGzsxSfxx(query?: any) {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/export',
    method: 'post',
    data: query
  });
}


/**
 * 批量修改公证事项费用
 */
export const updateGzsxFees = (data: UpdateGzsxFessData) => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/batchAmount',
    method: 'post',
    data
  })
}
