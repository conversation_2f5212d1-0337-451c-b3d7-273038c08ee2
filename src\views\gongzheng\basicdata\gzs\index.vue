<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="告知名称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入告知名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:gzs:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" ref="gzsTableRef" :data="gzsList" row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :default-expand-all="isExpandAll">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="告知名称" align="left" prop="title" />
        <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="新增" placement="top">
              <el-button v-hasPermi="['basicdata:gzs:add']" link type="primary" icon="Plus"
                @click="handleAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzs:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzs:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

     <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
    </el-card>
    <!-- 添加或修改告知树对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父级" prop="parentCode">
          <el-tree-select v-model="form.parentCode" :data="deptOptions"
            :props="{ value: 'treeCode', label: 'title', children: 'children' } as any" value-key="parentCode"
            placeholder="选择上级告知树" check-strictly />
        </el-form-item>
        <el-form-item label="告知名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入告知名称" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Gzs" lang="ts">
  import { listGzs, getGzs, delGzs, addGzs, updateGzs, listExcludeChild, listTree } from '@/api/gongzheng/basicdata/gzs';
  import { GzsVO, GzsQuery, GzsForm } from '@/api/gongzheng/basicdata/gzs/types';
  interface GzsOptionsType {
    parentId : number | string;
    id : number | string;
    title : string;
    children : GzsOptionsType[];
  }
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const gzsList = ref<GzsVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const isExpandAll = ref(true);
  const detailFormShow = ref(false);
  const queryFormRef = ref<ElFormInstance>();
  const gzsFormRef = ref<ElFormInstance>();
  const gzsTableRef = ref<ElTableInstance>();
  const deptOptions = ref<GzsOptionsType[]>([]);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsForm = {
    id: undefined,
    title: undefined,
    parentId: undefined,
    treeCode: undefined,
    parentCode: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<GzsForm, GzsQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      parentId: undefined,
      treeCode: undefined,
      parentCode: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询告知树列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTreeCode<GzsVO>(res.data, 'treeCode');
    if (data) {
      gzsList.value = data;
    }
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = async (row ?: GzsVO) => {
    reset();
    await loadTreeData(row, true);
    dialog.visible = true;
    dialog.title = '添加告知树';
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzsVO) => {
    reset();
    await loadTreeData(row, false);
    const _id = row?.id || ids.value[0]
    const res = await getGzs(_id);
    form.value = res.data;
    dialog.visible = true;
    dialog.title = "修改告知树";
  }

  const loadTreeData = async (row ?: GzsVO, isAdd) =>{
    const res = await listTree();
    const data = proxy?.handleTreeCode<GzsOptionsType>(res.data, 'treeCode');
    if (data) {
      deptOptions.value = data;
      if (isAdd && row && row.treeCode) {
        form.value.parentCode = row?.treeCode;
      }
    }
  }

  /** 提交按钮 */
  const submitForm = () => {
    gzsFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzs(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzs(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzsVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除告知树编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGzs(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzs/export', {
      ...queryParams.value
    }, `gzs_${new Date().getTime()}.xlsx`)
  }

  /** 展开/折叠操作 */
  const handleToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleExpandAll(gzsList.value, isExpandAll.value);
  };
  /** 展开/折叠所有 */
  const toggleExpandAll = (data : GzsVO[], status : boolean) => {
    data.forEach((item) => {
      gzsTableRef.value?.toggleRowExpansion(item, status);
      if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
    });
  };
  onMounted(() => {
    getList();
  });
</script>
