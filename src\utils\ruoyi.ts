// 日期格式化
export function parseTime(time: any, pattern?: string) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === 'string') {
      time = time
        .replace(new RegExp(/-/gm), '/')
        .replace('T', ' ')
        .replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj: { [key: string]: any } = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result: string, key: string) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
}

/**
 * 添加日期范围
 * @param params
 * @param dateRange
 * @param propName
 */
export const addDateRange = (params: any, dateRange: any[], propName?: string) => {
  const search = params;
  search.params = typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (dateRange[0] && dateRange[1]) {
    if (typeof propName === 'undefined') {
      search.params['beginTime'] = dateRange[0];
      search.params['endTime'] = dateRange[1];
    } else {
      search.params['begin' + propName] = dateRange[0];
      search.params['end' + propName] = dateRange[1];
    }
  } else {
    if (typeof propName !== 'undefined'){
      delete search.params['begin' + propName];
      delete search.params['end' + propName];
    }
  }

  return search;
};

// 回显数据字典
export const selectDictLabel = (datas: any, value: number | string) => {
  if (value === undefined) {
    return '';
  }
  const actions: Array<string | number> = [];
  Object.keys(datas).some((key) => {
    if (datas[key].value == '' + value) {
      actions.push(datas[key].label);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join('');
};

// 回显数据字典（字符串数组）
export const selectDictLabels = (datas: any, value: any, separator: any) => {
  if (value === undefined || value.length === 0) {
    return '';
  }
  if (Array.isArray(value)) {
    value = value.join(',');
  }
  const actions: any[] = [];
  const currentSeparator = undefined === separator ? ',' : separator;
  const temp = value.split(currentSeparator);
  Object.keys(value.split(currentSeparator)).some((val) => {
    let match = false;
    Object.keys(datas).some((key) => {
      if (datas[key].value == '' + temp[val]) {
        actions.push(datas[key].label + currentSeparator);
        match = true;
      }
    });
    if (!match) {
      actions.push(temp[val] + currentSeparator);
    }
  });
  return actions.join('').substring(0, actions.join('').length - 1);
};

// 字符串格式化(%s )
export function sprintf(str: string) {
  if (arguments.length !== 0) {
    let flag = true,
      i = 1;
    str = str.replace(/%s/g, function () {
      const arg = arguments[i++];
      if (typeof arg === 'undefined') {
        flag = false;
        return '';
      }
      return arg;
    });
    return flag ? str : '';
  }
}

// 转换字符串，undefined,null等转化为""
export const parseStrEmpty = (str: any) => {
  if (!str || str == 'undefined' || str == 'null') {
    return '';
  }
  return str;
};

// 数据合并
export const mergeRecursive = (source: any, target: any) => {
  for (const p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
};

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export const handleTree = <T>(data: any[], id?: string, parentId?: string, children?: string): T[] => {
  const config: {
    id: string;
    parentId: string;
    childrenList: string;
  } = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  const childrenListMap: any = {};
  const tree: T[] = [];
  for (const d of data) {
    const id = d[config.id];
    childrenListMap[id] = d;
    if (!d[config.childrenList]) {
      d[config.childrenList] = [];
    }
  }

  for (const d of data) {
    const parentId = d[config.parentId];
    const parentObj = childrenListMap[parentId]
    if (!parentObj) {
      tree.push(d);
    } else {
      parentObj[config.childrenList].push(d)
    }
  }
  return tree;
};

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} treeCode id字段 默认 'treeCode'
 * @param {*} parentCode 父节点字段 默认 'parentCode'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export const handleTreeCode = <T>(data: any[], treeCode?: string, parentCode?: string, children?: string): T[] => {
  const config: {
    treeCode: string;
    parentCode: string;
    childrenList: string;
  } = {
    treeCode: treeCode || 'treeCode',
    parentCode: parentCode || 'parentCode',
    childrenList: children || 'children'
  };

  const childrenListMap: any = {};
  const tree: T[] = [];
  for (const d of data) {
    const tc = d[config.treeCode];
    childrenListMap[tc] = d;
    if (!d[config.childrenList]) {
      d[config.childrenList] = [];
    }
  }

  for (const d of data) {
    const pc = d[config.parentCode];
    const parentObj = childrenListMap[pc]
    if (!parentObj) {
      tree.push(d);
    } else {
      parentObj[config.childrenList].push(d)
    }
  }
  return tree;
};

/**
 * 参数处理
 * @param {*} params  参数
 */
export const tansParams = (params: any) => {
  let result = '';
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    const part = encodeURIComponent(propName) + '=';
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            const params = propName + '[' + key + ']';
            const subPart = encodeURIComponent(params) + '=';
            result += subPart + encodeURIComponent(value[key]) + '&';
          }
        }
      } else {
        result += part + encodeURIComponent(value) + '&';
      }
    }
  }
  return result;
};

// 返回项目路径
export const getNormalPath = (p: string): string => {
  if (p.length === 0 || !p || p === 'undefined') {
    return p;
  }
  const res = p.replace('//', '/');
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1);
  }
  return res;
};

// 验证是否为blob格式
export const blobValidate = (data: any) => {
  return data.type !== 'application/json';
};

// 清除空字段
export const clearEmptyProperty = (obj: any): any => {
  const cleanParams = Object.keys(obj).reduce((acc : any, key) => {
    const value = obj[key as keyof typeof obj];
    if (value !== '' && value !== null && value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {});
  return cleanParams;
}

/**
 * 生成年份选项组
 * @param startYear 开始年份
 * @param endYear 结束年份，默认当前年份
 * @returns
 */
export const genYearOptions = (startYear: number, endYear: number = new Date().getFullYear()): Array<{ label: string, value: string }> => {
  if (startYear > endYear) {
    throw new Error('开始年份不能大于结束年份');
  }
  const years = [];
  for (let i = endYear; i >= startYear; i--) {
    years.push({ label: i.toString(), value: i.toString() });
  }
  return years;
};

export const dictMapFormat = (dict: Array<Record<string, any>>, key: string) => {
  return dict.find(dt => dt.value === key)?.label || key || '';
}

/**
 * 将日期时间格式化为：YYYY年MM月DD日
 * @param d 日期 默认当前时间
 * @param format 格式 默认：YYYY年MM月DD日; YYYY-MM-DD, YYYYMMDD, YYYYMMDDhhmmss
 * @returns
 */
export const formatDate = (d: string | Date = new Date(), format: string = 'YYYY年MM月DD日'): string => {
  if (d) {
    const date = new Date(d), year = String(date.getFullYear()),
      month = String(date.getMonth() + 1).padStart(2, '0'),
      day = String(date.getDate()).padStart(2, '0'),
      hours = String(date.getHours()).padStart(2, '0'),
      mins = String(date.getMinutes()).padStart(2, '0'),
      seconds = String(date.getSeconds()).padStart(2, '0');

      // format = format.toUpperCase();
    return format.replace('YYYY', year).replace('MM', month).replace('DD', day).replace(/hh|HH/g, hours).replace('mm', mins).replace('ss', seconds);
    // return `${year}年${month}月${day}日`;
  }
  return '';
};

/**
 * 当前时间转成字符串，格式为：YYYY-MM-DD-hh-mm-ss
 * @returns 当前时间转成字符串，格式为：YYYY-MM-DD-hh-mm-ss
 */
export const curTimeToStr = () => {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;
}

/**
 * 当前日期转成字符串，格式为：YYYY-MM-DD
 * @returns 当前日期转成字符串，格式为：YYYY-MM-DD
 */
export const curDateToStr = () => {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

/**
 * 获取最近一段时间，以当日时间为基准，返回开始和结束日期[string, string]，格式为 YYYY-MM-DD
 * @param d 格式：+1M, -2Y, +7D
 * @param base 基准时间，默认为当前时间
 */
export const genRecentDate = (d: string = '0D', base: Date = new Date()): [string, string] => {
  // 解析基准时间
  const baseDate = typeof base === 'string' ? new Date(base) : new Date(base);

  // 验证输入格式
  const regex = /^([+-])(\d+)([YMD])$/i;
  const match = d.match(regex);

  if (!match) {
    throw new Error('Invalid format. Expected [+-][number][Y|M|D], e.g. "+1M", "-2Y", "+7D"');
  }

  const [_, sign, amountStr, unit] = match;
  const amount = parseInt(amountStr, 10);
  const multiplier = sign === '+' ? 1 : -1;

  // 创建结果日期的副本
  const resultDate = new Date(baseDate);

  // 根据单位调整日期
  switch (unit.toUpperCase()) {
    case 'Y':
      resultDate.setFullYear(resultDate.getFullYear() + (multiplier * amount));
      break;
    case 'M':
      resultDate.setMonth(resultDate.getMonth() + (multiplier * amount));
      break;
    case 'D':
      resultDate.setDate(resultDate.getDate() + (multiplier * amount));
      break;
    default:
      throw new Error('Invalid unit. Expected Y, M, or D');
  }

  // 格式化日期为 YYYY-MM-DD
  // const formatDate = (date: Date = new Date()) => {
  //   const year = date.getFullYear();
  //   const month = String(date.getMonth() + 1).padStart(2, '0');
  //   const day = String(date.getDate()).padStart(2, '0');
  //   return `${year}-${month}-${day}`;
  // };

  // 确定开始和结束日期（保证时间顺序）
  const startDate = baseDate < resultDate ? baseDate : resultDate;
  const endDate = baseDate < resultDate ? resultDate : baseDate;

  return [formatDate(startDate, 'YYYY-MM-DD'), formatDate(endDate, 'YYYY-MM-DD')];
}

/**
 * base64转file
 * @param base64
 * @returns
 */
export const base64ToFile = (base64: string): Promise<File> => {
  return new Promise((resolve, reject) => {
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }

    const timeStemp = new Date().getTime().toString();

    const fileName = curTimeToStr() + '_' + timeStemp.substring(timeStemp.length - 5)

    resolve(new File([u8arr], `${fileName}.${mime.split('/')[1]}`, { type: mime }))
  })
}

/**
 * file转base64
 * @param file
 * @returns
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.onload = (e: any) => {
      resolve(e.target.result);
    }
    fileReader.readAsDataURL(file);
  })
}

// 格式化性别
export const formatGender = (gender : string) => {
  if (gender === '1') return '男'
  if (gender === '2') return '女'
  return gender || ''
}

/**
 * 计算两个时间差值，返回天、小时、分钟、秒
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param type 返回差值类型：day, hour, minute, second
 * @returns
 */
export const timeDiff = (startTime: string | Date, endTime: string | Date, type: 'day' | 'hour' | 'minute' | 'second' = 'day') => {
  const start = typeof startTime === 'string' ? new Date(startTime).getTime() : startTime.getTime();
  const end = typeof endTime === 'string' ? new Date(endTime).getTime() : endTime.getTime();
  const diff = end - start

  // 差值
  let diffNum = 0

  switch (type) {
    case 'day':
      diffNum = diff / (1000 * 60 * 60 * 24);
    break;

    case 'hour':
      diffNum = diff / (1000 * 60 * 60) % 24;
    break;

    case 'minute':
      diffNum = diff / (1000 * 60) % 60;
    break;

    case 'second':
      diffNum = diff / 1000 % 60;
    break;

    // default:
    //   return Math.floor(diff / (1000 * 60 * 60 * 24))
  }

  return diffNum
}
 
export interface FilterRes<T> {
  matches: T[];
  noMatches: T[];
}

/**
 * 数组节点过滤
 * @param data 原数组数据
 * @param match 回调函数，其中参数是原数组中的一项，需返回true/false
 * @returns 
 */
export const nodeFilter = <T>(data: Array<T>, match: (node: T) => boolean = () => false): FilterRes<T> => {
  let y: Array<T> = [], n: Array<T> = [];
  data.forEach((item, index) => {
    if (match(item)) {
      y.push(item);
    } else {
      n.push(item);
    }
  });
  return {
    matches: y,
    noMatches: n
  }
}

export default {
  handleTree,
  handleTreeCode
};
