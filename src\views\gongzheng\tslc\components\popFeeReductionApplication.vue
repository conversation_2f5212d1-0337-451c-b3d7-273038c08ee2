<template>
  <!-- 费用减免申请弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <div class="fee-reduction-form">
      <!-- 申请人信息 -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="form-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人:" prop="applicant">
              <el-input v-model="formData.applicant" placeholder="请输入申请人" readonly disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期:" prop="applicationDate">
              <el-date-picker v-model="formData.applicationDate" type="date" placeholder="请选择申请日期"
                value-format="YYYY-MM-DD" style="width: 100%" readonly disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审批人*:" prop="approver">
              <el-select v-model="formData.approver" filterable placeholder="请选择审批人" style="width: 100%">
                <el-option v-for="item in tsjzspy" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请原因 -->
        <el-form-item label="申请原因*:" prop="applicationReason">
          <el-input v-model="formData.applicationReason" type="textarea" :rows="4" placeholder="请输入申请原因" />
        </el-form-item>
      </el-form>

      <!-- 卷宗信息 -->
      <div class="case-section">
        <div class="section-header">
          <h3>卷宗信息</h3>
          <el-button v-has-permi="['tslc:fq:edit']" type="primary" @click="handleSelectCase"
            v-if="formData.id==null">选择卷宗</el-button>
        </div>

        <el-descriptions :column="3" border v-if="formData.caseInfo">
          <el-descriptions-item label="卷宗号">{{ formData.caseInfo.jzbh }}</el-descriptions-item>
          <el-descriptions-item label="公证员">{{ formData.caseInfo.gzyxm }}</el-descriptions-item>
          <el-descriptions-item label="助理">{{ formData.caseInfo.zlxm }}</el-descriptions-item>
          <el-descriptions-item label="受理日期">{{ formData.caseInfo.slrq }}</el-descriptions-item>
          <el-descriptions-item label="公证类别">{{ formData.caseInfo.lb }}</el-descriptions-item>
          <el-descriptions-item label="当事人">{{ formData.caseInfo.dsrxm }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="formData.caseInfo?.tip" class="case-tip">
          <el-tag type="warning">{{ formData.caseInfo.tip }}</el-tag>
        </div>
        <div v-if="formData.caseInfo==null">
          <el-empty description="请选择卷宗" />
        </div>
      </div>

      <!-- 费用减免列表 -->
      <div class="item-section">
        <h3>费用减免列表</h3>
        <el-table :data="formData.itemList" border stripe style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="费用类型" prop="fylx" align="center">
            <template #default="scope">
              {{ dictMapFormat(gz_sf_lb, scope.row.fylx) }}
            </template>
          </el-table-column>
          <el-table-column label="公证事项" prop="gzjzGzsx" align="center" show-overflow-tooltip />
          <el-table-column label="公证书编号" prop="gzsbh" align="center" />
          <el-table-column label="应收" prop="fyys" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.fyys) }}
            </template>
          </el-table-column>
          <el-table-column label="已收" prop="fyss" align="center" width="100">
            <template #default="scope">
              {{ formatMoney(scope.row.fyss) }}
            </template>
          </el-table-column>
          <el-table-column label="减免金额" prop="fyjm" align="center" width="120">
            <template #default="scope">
              <el-input v-model.number="scope.row.fyjm" type="number" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="减免后费用" align="center">
            <template #default="scope">
              ¥{{ (scope.row.fyys - scope.row.fyjm).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="收费状态" prop="sfzt" align="center" width="100">
            <template #default="scope">
              {{ dictMapFormat(gz_sfzt, scope.row.sfzt) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 费用统计 -->
        <div class="fee-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">原费用总额：</span>
                <span class="value">¥{{ totalOriginalFee.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">减免总额：</span>
                <span class="value">¥{{ totalReductionAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="label">减免后总额：</span>
                <span class="value">¥{{ totalFinalFee.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable show-close destroy-on-close width="400">
      <div class="h-100px flex items-center justify-center">
        <div class="flex items-center justify-center gap-10px">
          <strong>文档模版：</strong>
          <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;">
            <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button type="primary" @click="genSpb" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
          <el-button @click="genClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-has-permi="['tslc:fq:edit']">提交审批</el-button>
        <el-button @click="handleSaveDraft" v-has-permi="['tslc:fq:edit']">保存草稿</el-button>
        <template v-if="formData.id">
          <el-button @click="toGen" v-if="!spbInfo" v-has-permi="['tslc:fq:query']">生成审批表</el-button>
          <el-button @click="() => openSpb()" v-else v-has-permi="['tslc:fq:query']">打开审批表</el-button>
        </template>
        <el-button @click="doModalClose">取消</el-button>
      </div>
    </template>
  </vxe-modal>

  <!-- 选择卷宗对话框 -->
  <SelectCaseDialog ref="selectCaseRef" @success="onCaseSelected" />
</template>

<script setup name="PopFeeReductionApplication" lang="ts">
  import { ref, reactive, computed, getCurrentInstance, toRefs } from 'vue';
  import { ElMessage } from 'element-plus';
  import type { VxeModalProps } from 'vxe-table';
  import SelectCaseDialog from './SelectCaseDialog.vue';
  import { getToken } from '@/utils/auth';
  import { addTslcSqb, saveDraftTslcSqb, getChargeList, applyDecisionNumber, cancelDecisionNumber, searchCases, updateTslcSqb } from '@/api/gongzheng/tslc/tslcSqb'
  import { useUserStore } from '@/store/modules/user'
  import { dictMapFormat, formatDate } from '@/utils/ruoyi';
import { UserDocGenParams } from '../../doc/type';
import { docGenerator, docOpenEdit } from '../../doc/DocEditor';
import { queryOssInfo } from '@/api/gongzheng/gongzheng/oss';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
  // Props 定义
  interface Props {
    title ?: string;
    width ?: string;
  }
  // 格式化金额（容错字符串/空值）
  const formatMoney = (amount : unknown) => {
    const n = Number(amount ?? 0)
    return Number.isNaN(n) ? '0.00' : n.toFixed(2)
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '费用减免申请',
    width: '900px'
  });

  // Emits 定义
  const emits = defineEmits<{
    (event : 'success', data ?: any) : void;
    (event : 'close') : void;
  }>();

  // 获取组件实例
  const { proxy } = getCurrentInstance() as any;

  // 响应式数据
  const showPopup = ref(false);
  const formRef = ref();
  const selectCaseRef = ref<InstanceType<typeof SelectCaseDialog> | null>(null);

  // 审批人列表
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser('tsjzspy'));
  const { gz_sf_lb, gz_sfzt, gz_gzlb, gz_tslc_tffs } = toRefs<any>(proxy?.useDict('gz_tslc_tffs', 'gz_sf_lb', 'gz_sfzt', 'gz_gzlb'));

  // 上传文件相关
  const uploadRef = ref();
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload');
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  });
  const fileList = ref([]);

  // 弹窗配置
  const modalOptions = reactive<VxeModalProps>({
    title: props.title,
    width: props.width,
    height: '80vh',
    escClosable: true,
    resize: true,
    showMaximize: true,
    destroyOnClose: true
  });

  const genState = reactive({
    visible: false,
    loading: false,
    title: '',
    typeData: [],
    mbId: '',
  })

  // 表单数据
  const formData = reactive({
    id: null,
    applicant: '',
    applicationDate: new Date().toISOString().split('T')[0],
    approver: '',
    decisionDocument: '',
    decisionNumber: '',
    decisionSerial: '',
    applicationReason: '',
    reasons: {
      poverty: false,
      disability: false,
      publicWelfare: false,
      emergency: false,
      otherReduction: false
    },
    caseInfo: null as any,
    itemList: [] as any[],
  });

  const spbInfo = ref(null);

  // 表单验证规则
  const formRules = reactive({
    approver: [
      { required: true, message: '请选择审批人', trigger: 'change' }
    ],
    applicationReason: [
      { required: true, message: '请输入申请原因', trigger: 'blur' }
    ]
  });
  // 计算属性
  const totalOriginalFee = computed(() => {
    return formData.itemList.reduce((sum, item) => sum + item.fyys, 0);
  });

  const totalReductionAmount = computed(() => {
    return formData.itemList.reduce((sum, item) => sum + item.fyjm, 0);
  });

  const totalFinalFee = computed(() => {
    return totalOriginalFee.value - totalReductionAmount.value;
  });


  // 打开弹窗
  const open = async (option : any = {}) => {
    showPopup.value = true
    resetForm()
    if (option && option.id) {
      await loadDetailForEdit(option.id)
    }
  };

  // 关闭弹窗
  const close = () => {
    showPopup.value = false;
    emits('close');
  };

  // 弹窗关闭处理
  const doModalClose = () => {
    emits('close');
    showPopup.value = false;
  };

  // 重置表单
  const resetForm = () => {
    const userStore = useUserStore()
    Object.assign(formData, {
      applicant: userStore.nickname,
      applicationDate: new Date().toISOString().split('T')[0],
      approver: '',
      decisionDocument: '',
      decisionNumber: '',
      decisionSerial: '',
      applicationReason: '',
      reasons: {
        poverty: false,
        disability: false,
        publicWelfare: false,
        emergency: false,
        otherReduction: false
      },
      caseInfo: null,
      itemList: [],
      // 文件相关
      fileUrl: '',
      fileName: '',
      wdOssId: undefined
    });
    // 清空文件列表
    fileList.value = [];
    formRef.value?.clearValidate();
  };





  // 选择卷宗
  const handleSelectCase = () => {
    selectCaseRef.value?.open();
  };

  // 卷宗选择成功回调
  const onCaseSelected = async (caseInfo : any) => {
    formData.caseInfo = caseInfo;
    await loadItemList(caseInfo);
  };

  // 加载事项列表
  const loadItemList = async (caseInfo : any) => {
    try {
      const res : any = await getChargeList({ gzjzId: caseInfo.gzjzId, jzh: caseInfo.jzbh })
      const rows = res.rows || res.data?.rows || []
      formData.itemList = rows.map((it : any) => ({
        fylx: it.fylx,
        gzjzGzsx: it.gzjzGzsx || it.gzsx,
        gzsbh: it.gzsbh,
        fyys: Number(it.fyys ?? 0),
        fyjm: Number(it.fyjm ?? 0),
        sfzt: it.sfzt + '',
        gzjzGzsxId: it.gzjzGzsxId
      }))
    } catch (e) {
      ElMessage.error('加载费用明细失败')
    }
  };

  // 编辑模式加载详情
  const loadDetailForEdit = async (id : string | number) => {
    try {
      const { getTslcSqb } = await import('@/api/gongzheng/tslc/tslcSqb')
      const res : any = await getTslcSqb(id)
      const data = res.data || {}
      formData.id = data.id;
      formData.applicant = data.tslcFqr || formData.applicant
      formData.applicationDate = data.tslcFqrq || formData.applicationDate
      if (data.tslcSprId) {
        formData.approver = data.tslcSprId
      }
      formData.applicationReason = data.tslcSqyy || ''
      formData.decisionNumber = data.jdsbh || data.decisionNumber || ''
      formData.caseInfo = data.caseInfo;
      formData.caseInfo.lb = formData.caseInfo.lb + ''
      // 审批表信息
      spbInfo.value = data.tslcSpbwj

      if (Array.isArray(data.items) && data.items.length) {
        formData.itemList = data.items.map((it : any) => ({
          fylx: it.fylx ?? it.feeType,
          gzjzGzsx: it.gzjzGzsx ?? it.gzsx ?? it.notarizationItem,
          gzjzGzsxId: it.gzjzGzsxId,
          gzsbh: it.gzsbh ?? it.certificateNumber,
          fyys: Number(it.fyys ?? it.receivable ?? 0),
          fyss: Number(it.fyss ?? it.received ?? 0),
          fyjm: Number(it.fyjm ?? it.reduction ?? 0),
          sfzt: it.sfzt
        }))
      } else if (data.gzjzId || data.jzh) {
        await loadItemList({ id: data.gzjzId, caseNumber: data.jzh })
      }
    } catch (e) {
      console.error('编辑加载失败', e)
    }
  }

  // 提交审批
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      // 验证是否选择了卷宗
      if (!formData.caseInfo) {
        ElMessage.warning('请先选择卷宗');
        return;
      }
      if (totalReductionAmount.value <= 0) {
        ElMessage.warning('本次减免金额必须大于0');
        return;
      }

      // 行级校验：本次减免 <= 已收
      for (let i = 0; i < formData.itemList.length; i++) {
        const it : any = formData.itemList[i]
        const val = Number(it.fyjm || 0)
        const max = Number(it.fyys || 0)
        console.log(val)
        console.log(max)
        if (val > max) {
          ElMessage.warning(`第${i + 1}行本次减免金额不能超过应收金额`)
          return
        }
      }
      const userStore = useUserStore()
      const payload : any = {
        id: formData?.id,
        tslcLx: '5',
        gzsbh: formData.caseInfo?.gzsbh,
        gzjzId: formData.caseInfo?.gzjzId,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber,
        items: formData.itemList
      }
      await addTslcSqb(payload)
      ElMessage.success('提交成功')
      emits('success', formData)
      close()
    } catch (error) {
      console.error('表单验证失败:', error);
      ElMessage.error('请检查表单信息');
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    const userStore = useUserStore()
    try {
      await formRef.value?.validate();

      // 验证是否选择了卷宗
      if (!formData.caseInfo) {
        ElMessage.warning('请先选择卷宗');
        return;
      }
      if (totalReductionAmount.value <= 0) {
        ElMessage.warning('本次减免金额必须大于0');
        return;
      }

      // 行级校验：本次减免 <= 已收
      for (let i = 0; i < formData.itemList.length; i++) {
        const it : any = formData.itemList[i]
        const val = Number(it.fyjm || 0)
        const max = Number(it.fyys || 0)
        if (val > max) {
          ElMessage.warning(`第${i + 1}行本次减免金额不能超过应收金额`)
          return
        }
      }
      const payload : any = {
        id: formData?.id,
        tslcLx: '5',
        gzjzId: formData.caseInfo?.gzjzId,
        gzsbh: formData.caseInfo?.gzsbh,
        jzh: formData.caseInfo?.jzbh,
        tslcFqr: formData.applicant,
        tslcFqrId: userStore.userId,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber,
        items: formData.itemList
      }
      await saveDraftTslcSqb(payload)
      ElMessage.success('草稿保存成功')
      emits('success', payload)
      close()
    } catch (e) {
      ElMessage.error('草稿保存失败')
    }
  };

    const genClose = () => {
    genState.loading = false;
    genState.visible = false;
    genClosed();
  }

  const genClosed = () => {
    genState.mbId = ''
  }

  // 生成模板选择
  const toGen = () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在获取文档模版，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.2)',
      fullscreen: true
    })
    queryMbFiles({ wdLb: 92 }).then((res) => {
      if (res.code === 200) {
        if(!res.data || res.data.length === 0) {
          ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
        } else {
          genState.typeData = res.data;
          genState.mbId = res.data[0].id;
          genState.visible = true;
          genState.title = `费用减免审批表生成`;
        }
      }
    }).catch((err: any) => {
      console.log('查询模版文件异常', err);
    }).finally(() => {
      loading.close();
    })
  }

  // 生成审批表
  const genSpb = () => {
    if (!genState.mbId) {
      ElMessage.warning('未选择生成指定模版')
      return;
    }
    genState.loading = true;

    console.log('打印申请书:', formData);

    let params : UserDocGenParams = {
      bizId: formData.caseInfo?.gzjzId,
      mbWdId: genState.mbId,
      extraParams: {
        // gzxsId: formData.caseInfo?.gzjzId,
        tslcId: formData.id,
        // item: JSON.stringify(formData.itemList),
        sqyy: formData.applicationReason
      }
    }

    const loading2 = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    docGenerator(params, {
      success: (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        // const fileName = `费用减免-${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`;

        updateSpb(ossId);
      }
    }).catch((err : any) => {
      console.error('文档生成失败', err)
      ElMessage.error('生成审批表异常')
    }).finally(() => {
      loading2.close()
      genState.loading = false;
    })
  };

  // 存储审批表ossId
  const updateSpb = async (ossId: string) => {
    try {
      const userStore = useUserStore()
      const params = {
        id: formData.id,
        tslcSpbwj: ossId,
        tslcFqrId: userStore.userId,
        tslcFqr: formData.applicant || userStore.nickname,
        tslcLx: '5',
        gzjzId: formData.caseInfo?.gzjzId,
        gzsbh: formData.caseInfo?.gzsbh,
        jzh: formData.caseInfo?.jzbh,
        tslcFqrq: formData.applicationDate,
        tslcSprId: formData.approver,
        tslcSqyy: formData.applicationReason,
        decisionNumber: formData.decisionNumber,
        items: formData.itemList
      }
      const res = await updateTslcSqb(params);
      if(res.code === 200) {
        spbInfo.value = ossId;
        ElMessageBox.confirm('生成完成，是否打开?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning',
        }).then(() => {
          openSpb(ossId)
        })
      }
    } catch (err: any) {
      console.error('添加审批表失败', err)
      ElMessage.error('添加审批表失败')
    }
  }

  // 打开审批表
  const openSpb = async (ossId?: string) => {
    try {
      const ossRes = await queryOssInfo(ossId || spbInfo.value);
      if(ossRes.code === 200) {
        const { ossId, fileName: path, fileSuffix } = ossRes.data[0];
        docOpenEdit(path)
      }
    } catch (err: any) {
      ElMessage.error('获取文件信息失败');
    }
  }

  // 暴露方法给父组件
  defineExpose({
    open,
    close
  });
</script>

<style scoped>
  .fee-reduction-form {
    padding: 20px;
  }

  .form-container {
    margin-bottom: 20px;
  }

  .reason-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .reason-checkboxes .el-checkbox {
    margin-right: 0;
  }

  .case-section {
    margin-bottom: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }

  .case-tip {
    margin-top: 10px;
  }

  .item-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: bold;
  }

  .fee-summary {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .summary-item:last-child {
    margin-bottom: 0;
  }

  .summary-item .label {
    font-weight: bold;
    color: #606266;
  }

  .summary-item .value {
    font-size: 16px;
    color: #409eff;
    font-weight: bold;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>
