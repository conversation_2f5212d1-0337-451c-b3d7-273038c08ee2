<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" append-to-body>
    <div v-loading="modelState.loading">
      <YzInfoForm v-model="yzData" ref="yzInfoFormRef" />
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">保存</el-button>
        <el-button v-if="yzData.id" @click="comfirmDel" :loading="modelState.submitting" :disabled="modelState.submitting" type="danger">删除</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { addGzjzYz, delGzjzYz, getGzjzYzByGzjzGzsxId, updateGzjzYz } from '@/api/gongzheng/bzfz/gzjzYz';
import YzInfoForm from './YzInfoForm.vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { GzjzYzVOEdit } from '@/api/gongzheng/bzfz/gzjzYz/types';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '遗嘱备案信息'
})

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const modelState = reactive({
  visible: false,
  title: '遗嘱备案信息',
  gzjzId: undefined,
  loading: false,
  submitting: false,
})

const yzData = ref<GzjzYzVOEdit>({})
const yzInfoFormRef = ref(null)

const gzjzGzsxInfo = ref<any>({})

const initData = async () => {
  try {
    modelState.loading = true;
    const params = {
      // gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
    }

    let res = await getGzjzYzByGzjzGzsxId(params);
    if(res.code === 200 && res.data.id) {
      const data = res.data;
      const qzrIds = data.qzrIds != '' ? data.qzrIds?.split(',').map((i) => Number(i)) : [],
        dlrIds = data.dlrIds != '' ? data.dlrIds?.split(',').map((i) => Number(i)) : [];

      yzData.value = {
        ...data,
        qzrIds,
        dlrIds
      }
    }
  } catch (err: any) {
    console.error('遗嘱信息初始化失败', err)
  } finally {
    modelState.loading = false;
  }
}

const comfirmSave = async () => {
  try {
    const isValidOk = await yzInfoFormRef.value?.validate();
    if (!isValidOk) return;
    modelState.submitting = true
    const params = {
      ...yzData.value,
      qzrIds: yzData.value.qzrIds?.join(','),
      dlrIds: yzData.value.dlrIds?.join(','),
      gzjzId: modelState.gzjzId || curGzjz.value?.id || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
    }
    console.log('yz comfirmSave > ', yzData.value, params)
    let res = null;
    if(params.id) {
      res = await updateGzjzYz(params); 
    } else {
      res = await addGzjzYz(params);
    }
    if(res.code === 200) {
      ElMessage.success('保存成功');
      close();
    }
  } catch (err: any) {
    console.error('遗嘱备案保存失败', err)
  } finally {
    modelState.submitting = false
  }
}

const comfirmDel = () => {
  ElMessageBox.confirm('确认要删除该遗嘱吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    modelState.submitting = true;
    const res = await delGzjzYz(yzData.value.id);
    if(res.code === 200) {
      ElMessage.success('删除成功');
      close();
    }
  }).finally(() => {
    modelState.submitting = false;
  })
}

const close = () => {
  modelState.visible = false;
  modelState.gzjzId = undefined;
  gzjzGzsxInfo.value = {};
  yzData.value = {}
}

const closed = () => {
  modelState.gzjzId = undefined;
  gzjzGzsxInfo.value = {};
  yzData.value = {}
}

const open = (data?: any) => {
  modelState.visible = true
  modelState.gzjzId = data?.gzjzId || undefined;
  modelState.title = data?.title || '';
  gzjzGzsxInfo.value = data?.gzjzGzsxInfo || {};

  initData();
}

defineExpose({
  open
})

</script>
