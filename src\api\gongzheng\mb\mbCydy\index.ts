import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbCydyVO, MbCydyForm, MbCydyQuery } from '@/api/mb/mbCydy/types';

/**
 * 查询模板-常用短语列表
 * @param query
 * @returns {*}
 */

export const listMbCydy = (query?: MbCydyQuery): AxiosPromise<MbCydyVO[]> => {
  return request({
    url: '/mb/mbCydy/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-常用短语详细
 * @param id
 */
export const getMbCydy = (id: string | number): AxiosPromise<MbCydyVO> => {
  return request({
    url: '/mb/mbCydy/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-常用短语
 * @param data
 */
export const addMbCydy = (data: MbCydyForm) => {
  return request({
    url: '/mb/mbCydy',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-常用短语
 * @param data
 */
export const updateMbCydy = (data: MbCydyForm) => {
  return request({
    url: '/mb/mbCydy',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-常用短语
 * @param id
 */
export const delMbCydy = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbCydy/' + id,
    method: 'delete'
  });
};
