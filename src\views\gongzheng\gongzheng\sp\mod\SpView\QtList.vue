<template>
  <el-table :data="data" border stripe size="small">
    <el-table-column type="index" label="#" width="60" align="center" />
    <el-table-column prop="typeName" label="文档类型" width="180" align="center" />
    <el-table-column prop="docList" label="文档名称">
      <template #default="{ row, column }">
        <div class="flex flex-wrap gap-6px">
          <el-tag v-for="item in row.docList" :key="item.id">
            <el-button type="primary" link @click="openPreview(item)">{{item.wbmc}}</el-button>
          </el-tag>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { type DocParams, EditDocParams, docOpenShow, editDoc, showDoc } from '@/views/gongzheng/doc/DocEditor'
import { EnumDocActionType, EnumDocType, EnumDocFileType } from '@/views/gongzheng/doc/enumType'

interface Props {
  data: any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

const openPreview = (data: any) => {
  const ossInfo = JSON.parse(data?.wblj || '{}')
  if(ossInfo.path) {
    docOpenShow(ossInfo.path)
  }
}

</script>