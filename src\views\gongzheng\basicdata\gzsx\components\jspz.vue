<template>
  <div class="p-2">

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <span style="line-height: 24px; display: block; width: 160px;">公证事项角色配置</span>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="primary" icon="Plus" @click="handleAdd()"
              v-hasPermi="['basicdata:gzsxJspz:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="danger" icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['basicdata:gzsxJspz:add']">删除</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzsxJspzList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="角色名称" align="center" prop="jsmc" />
        <el-table-column label="是否有效" align="center" prop="sfyx">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.sfyx" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzsxJspz:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzsxJspz:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="loadData" />
    </el-card>
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzsxJspzFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="jsmc">
          <el-input v-model="form.jsmc" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="是否有效" prop="sfyx">
          <el-radio-group v-model="form.sfyx">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :value="dict.value" :label="dict.label"></el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzsxJspz" lang="ts">
  import { listGzsxJspz, getGzsxJspz, delGzsxJspz, addGzsxJspz, updateGzsxJspz } from '@/api/gongzheng/basicdata/gzsxJspz';
  import { GzsxJspzVO, GzsxJspzQuery, GzsxJspzForm } from '@/api/gongzheng/basicdata/gzsxJspz/types';
  interface Props {
    gzsxId : string | number;
    selectId : [];
    gzsxCode: string;
    gzlbValue: string;
  }
  const props = defineProps<Props>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yes_or_no'));

  const gzsxJspzList = ref<GzsxJspzVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const gzsxJspzFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxJspzForm = {
    id: undefined,
    gzsxId: undefined,
    jsmc: undefined,
    sfyx: '1',
    gzsxCode: undefined,
    gzlbValue: undefined
  }
  const data = reactive<PageData<GzsxJspzForm, GzsxJspzQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzsxId: undefined,
      jsmc: undefined,
      sfyx: undefined,
      gzsxCode: undefined,
      gzlbValue: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询角色配置列表 */
  const getList = async (_gzsxCode, _gzlbValue) => {
    if(_gzsxCode && _gzlbValue){
      loading.value = true;
      queryParams.value.gzsxCode = _gzsxCode;
      queryParams.value.gzlbValue = _gzlbValue;
      const res = await listGzsxJspz(queryParams.value);
      gzsxJspzList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    }else{
      gzsxJspzList.value = [];
    }
  }
  const loadData = async () => {
    getList(props.gzsxCode, props.gzlbValue);
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsxJspzFormRef.value?.resetFields();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsxJspzVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加角色配置";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzsxJspzVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getGzsxJspz(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改角色配置";
  }

  /** 提交按钮 */
  const submitForm = () => {
    form.value.gzsxId = props.gzsxId;
    form.value.gzsxCode = props.gzsxCode;
    form.value.gzlbValue = props.gzlbValue;
    gzsxJspzFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzsxJspz(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzsxJspz(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList(props.gzsxCode, props.gzlbValue);
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzsxJspzVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除角色配置编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGzsxJspz(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList(props.gzsxCode, props.gzlbValue);
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzsxJspz/export', {
      ...queryParams.value
    }, `gzsxJspz_${new Date().getTime()}.xlsx`)
  }

  const init = (_gzsxCode, _gzlbValue) => {
    console.log("选中公证事项：" + _gzsxCode);
    console.log("选中公证类别：" + _gzlbValue);
    getList(_gzsxCode, _gzlbValue)
  }
  const setGzsxIds = (data) => {
    form.value.selectId = data
  }
  // 显式暴露方法给父组件
  defineExpose({
    init,
    setGzsxIds
  });
  onMounted(() => {
    getList(props.gzsxCode, props.gzlbValue);
  });
</script>
