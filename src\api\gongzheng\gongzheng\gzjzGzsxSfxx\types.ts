export interface GzjzGzsxSfxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId: string | number;

  /**
   * 费用类型
   */
  fylx: string;

  /**
   * 是否记账
   */
  sfjz: string;

  /**
   * 计价方式
   */
  jjfs: string;

  /**
   * 收费场景
   */
  sfcj: string;

  /**
   * 应收
   */
  fyys: number;

  /**
   * 减免
   */
  fyjm: number;

  /**
   * 减免申请ID
   */
  fyjmId: string | number;

  /**
   * 实收
   */
  fyss: number;

  /**
   * 退费
   */
  fytf: number;

  /**
   * 退费小计
   */
  fytfXj: number;

  /**
   * 退费审批ID
   */
  fytfId: string | number;

  /**
   * 收费状态
   */
  sfzt: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

}

export interface GzjzGzsxSfxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 费用类型
   */
  fylx?: string;

  /**
   * 是否记账
   */
  sfjz?: string;

  /**
   * 计价方式
   */
  jjfs?: string;

  /**
   * 收费场景
   */
  sfcj?: string;

  /**
   * 应收
   */
  fyys?: number;

  /**
   * 减免
   */
  fyjm?: number;

  /**
   * 减免申请ID
   */
  fyjmId?: string | number;

  /**
   * 实收
   */
  fyss?: number;

  /**
   * 退费
   */
  fytf?: number;

  /**
   * 退费小计
   */
  fytfXj?: number;

  /**
   * 退费审批ID
   */
  fytfId?: string | number;

  /**
   * 收费状态
   */
  sfzt?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

}

export interface GzjzGzsxSfxxQuery extends PageQuery {

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 费用类型
   */
  fylx?: string;

  /**
   * 是否记账
   */
  sfjz?: string;

  /**
   * 计价方式
   */
  jjfs?: string;

  /**
   * 收费场景
   */
  sfcj?: string;

  /**
   * 应收
   */
  fyys?: number;

  /**
   * 减免
   */
  fyjm?: number;

  /**
   * 减免申请ID
   */
  fyjmId?: string | number;

  /**
   * 实收
   */
  fyss?: number;

  /**
   * 退费
   */
  fytf?: number;

  /**
   * 退费小计
   */
  fytfXj?: number;

  /**
   * 退费审批ID
   */
  fytfId?: string | number;

  /**
   * 收费状态
   */
  sfzt?: string;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}

export interface UpdateGzsxFessData {
  gzjzId: string | number;
  gzjzGzsxIds: Array<string | number>;
  fylx: string; //费用类型（字典）
  fyys: number; //应收金额
}

