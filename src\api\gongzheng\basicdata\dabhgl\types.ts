export interface DabhglVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 分类（字典 介绍信编号、档案编号）
   */
  category: number;

  /**
   * 期限
   */
  term: string;

  /**
   * 年份
   */
  year: string;

  /**
   * 号头
   */
  numberStr: string;

  /**
   * 编号位数
   */
  numberDigits: number;

  /**
   * 起始编号
   */
  startNumber: string;

  /**
   * 状态
   */
  status: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 编号类型
   */
  numberType: number;

  /**
   * 公证分类
   */
  gzCategory: number;

}

export interface DabhglForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 分类（字典 介绍信编号、档案编号）
   */
  category?: number;

  /**
   * 期限
   */
  term?: string;

  /**
   * 年份
   */
  year?: string;

  /**
   * 号头
   */
  numberStr?: string;

  /**
   * 编号位数
   */
  numberDigits?: number;

  /**
   * 起始编号
   */
  startNumber?: string;

  /**
   * 状态
   */
  status?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 编号类型
   */
  numberType?: number;

  /**
   * 公证分类
   */
  gzCategory?: number;

}

export interface DabhglQuery extends PageQuery {

  /**
   * 分类（字典 介绍信编号、档案编号）
   */
  category?: number;

  /**
   * 期限
   */
  term?: string;

  /**
   * 年份
   */
  year?: string;

  /**
   * 号头
   */
  numberStr?: string;

  /**
   * 编号位数
   */
  numberDigits?: number;

  /**
   * 起始编号
   */
  startNumber?: string;

  /**
   * 状态
   */
  status?: number;

  /**
   * 编号类型
   */
  numberType?: number;

  /**
   * 公证分类
   */
  gzCategory?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



