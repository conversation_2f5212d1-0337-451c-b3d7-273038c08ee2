<template>
  <!-- 制证 》制证结束并通知 -->
  <gz-dialog v-model="dialog.visible" :title="dialog.title" width="1200" @close="cancel" show-close destroy-on-close>
    <div>
      <h3>短信接收人<span style="margin-left: 10px; color: red; font-size: 12px;">(注:本列表只会显示填有联系电话的人员，如果是联系电话显示红色说明号码有误或短信可能无法送达)</span></h3>
      <TableBySms ref="TableBySmsRef" ></TableBySms>
      <h3>发证信息</h3>
      <table-by-sdxx ref="TableBySdxxRef"></table-by-sdxx>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-loading="btnLoading" @click="submitForm">制证结束</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup name="PopFormByZzhtz" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { initiateSigned } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import type { ApprovalType } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import TableBySms from '@/views/gongzheng/gongzheng/components/set_sms/tableBySms.vue';
  import TableBySdxx from '@/views/gongzheng/gongzheng/components/sl/sdxx/tableBySdxx.vue';
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';

  const dialog = reactive<DialogOption>({
    visible: false,
    title: '制证结束并通知'
  });

  const TableBySmsRef = ref<InstanceType<typeof TableBySms>>(null);
  const TableBySdxxRef = ref<InstanceType<typeof TableBySdxx>>(null);

  const view = ref(false);
  const gzjzId = ref<string | number>(null);
  // 提供给子组件的数据和方法
  provide('view', view);
  provide('gzjzId', gzjzId);

  const btnLoading = ref(false);
  // 提交保存
  const submitForm = async () => {
    btnLoading.value = true;
    const result = await TableBySmsRef.value.submitForm();
    btnLoading.value = false
    if(!result.success){
      proxy?.$modal.msgWarning(result.msssage);
      return;
    }
    const signed = await initiateSigned({
      id: gzjzId.value,
      sftg: '1'
    });
    if(signed.code !== 200){
      proxy?.$modal.msgWarning(signed.msg);
      return;
    }
    emit('callback', {success: true});
    cancel();
  }

  // 打开
  const open = (_gzjzId: string | number) => {
    console.log('SetSMSIndex.gzjzId', _gzjzId);
    if(!_gzjzId){
      proxy?.$modal.msgWarning("卷宗编号为空！");
      return;
    }
    gzjzId.value = _gzjzId;
    view.value = true;
    dialog.visible = true;
  }
  // 取消
  const cancel = () => {
    dialog.visible = false;
    view.value = false;
  }
  // 回调方法
  const emit = defineEmits(['callback']);
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  });

  onMounted(() => {
  });

</script>

<style>
</style>
