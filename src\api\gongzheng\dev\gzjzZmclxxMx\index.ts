import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzZmclxxMxVO, GzjzZmclxxMxForm, GzjzZmclxxMxQuery } from '@/api/gongzheng/dev/gzjzZmclxxMx/types';

/**
 * 查询公证卷宗-公证证明材料信息-明细v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzZmclxxMx = (query?: GzjzZmclxxMxQuery): AxiosPromise<GzjzZmclxxMxVO[]> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证证明材料信息-明细v1.0详细
 * @param id
 */
export const getGzjzZmclxxMx = (id: string | number): AxiosPromise<GzjzZmclxxMxVO> => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证证明材料信息-明细v1.0
 * @param data
 */
export const addGzjzZmclxxMx = (data: GzjzZmclxxMxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证证明材料信息-明细v1.0
 * @param data
 */
export const updateGzjzZmclxxMx = (data: GzjzZmclxxMxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证证明材料信息-明细v1.0
 * @param id
 */
export const delGzjzZmclxxMx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzZmclxxMx/' + id,
    method: 'delete'
  });
};
