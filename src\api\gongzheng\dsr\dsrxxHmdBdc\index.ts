import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxHmdBdcVO, DsrxxHmdBdcForm, DsrxxHmdBdcQuery } from '@/api/gongzheng/dsr/dsrxxHmdBdc/types';

/**
 * 查询当事人-黑名单-不动产列表
 * @param query
 * @returns {*}
 */

export const listDsrxxHmdBdc = (query?: DsrxxHmdBdcQuery): AxiosPromise<DsrxxHmdBdcVO[]> => {
  return request({
    url: '/dsr/dsrxxHmdBdc/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-黑名单-不动产详细
 * @param id
 */
export const getDsrxxHmdBdc = (id: string | number): AxiosPromise<DsrxxHmdBdcVO> => {
  return request({
    url: '/dsr/dsrxxHmdBdc/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-黑名单-不动产
 * @param data
 */
export const addDsrxxHmdBdc = (data: DsrxxHmdBdcForm) => {
  return request({
    url: '/dsr/dsrxxHmdBdc',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-黑名单-不动产
 * @param data
 */
export const updateDsrxxHmdBdc = (data: DsrxxHmdBdcForm) => {
  return request({
    url: '/dsr/dsrxxHmdBdc',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-黑名单-不动产
 * @param id
 */
export const delDsrxxHmdBdc = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxHmdBdc/' + id,
    method: 'delete'
  });
};
