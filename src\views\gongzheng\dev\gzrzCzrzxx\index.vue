<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人姓名" prop="dsrxm">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入当事人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证事项" prop="gzsx">
              <el-input v-model="queryParams.gzsx" placeholder="请输入公证事项" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="日志内容" prop="rznr">
              <el-input v-model="queryParams.rznr" placeholder="请输入日志内容" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作时间" prop="czsj">
              <el-date-picker clearable
                v-model="queryParams.czsj"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择操作时间"
              />
            </el-form-item>
            <el-form-item label="操作人" prop="czr">
              <el-input v-model="queryParams.czr" placeholder="请输入操作人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzrzCzrzxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzrzCzrzxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzrzCzrzxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzrzCzrzxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzrzCzrzxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID(关联公证卷宗)" align="center" prop="gzjzId" />
        <el-table-column label="当事人姓名" align="center" prop="dsrxm" />
        <el-table-column label="公证事项" align="center" prop="gzsx" />
        <el-table-column label="日志内容" align="center" prop="rznr" />
        <el-table-column label="操作时间" align="center" prop="czsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.czsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="czr" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzrzCzrzxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzrzCzrzxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证日志-操作日志v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzrzCzrzxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" />
        </el-form-item>
        <el-form-item label="当事人姓名" prop="dsrxm">
          <el-input v-model="form.dsrxm" placeholder="请输入当事人姓名" />
        </el-form-item>
        <el-form-item label="公证事项" prop="gzsx">
          <el-input v-model="form.gzsx" placeholder="请输入公证事项" />
        </el-form-item>
        <el-form-item label="日志内容" prop="rznr">
            <el-input v-model="form.rznr" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="操作时间" prop="czsj">
          <el-date-picker clearable
            v-model="form.czsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作人" prop="czr">
          <el-input v-model="form.czr" placeholder="请输入操作人" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzrzCzrzxx" lang="ts">
import { listGzrzCzrzxx, getGzrzCzrzxx, delGzrzCzrzxx, addGzrzCzrzxx, updateGzrzCzrzxx } from '@/api/gongzheng/dev/gzrzCzrzxx';
import { GzrzCzrzxxVO, GzrzCzrzxxQuery, GzrzCzrzxxForm } from '@/api/gongzheng/dev/gzrzCzrzxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzrzCzrzxxList = ref<GzrzCzrzxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzrzCzrzxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzrzCzrzxxForm = {
  id: undefined,
  gzjzId: undefined,
  dsrxm: undefined,
  gzsx: undefined,
  rznr: undefined,
  czsj: undefined,
  czr: undefined,
}
const data = reactive<PageData<GzrzCzrzxxForm, GzrzCzrzxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    dsrxm: undefined,
    gzsx: undefined,
    rznr: undefined,
    czsj: undefined,
    czr: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证日志-操作日志v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzrzCzrzxx(queryParams.value);
  gzrzCzrzxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzrzCzrzxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzrzCzrzxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证日志-操作日志v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzrzCzrzxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzrzCzrzxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证日志-操作日志v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzrzCzrzxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzrzCzrzxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzrzCzrzxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzrzCzrzxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证日志-操作日志v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzrzCzrzxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzrzCzrzxx/export', {
    ...queryParams.value
  }, `gzrzCzrzxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
