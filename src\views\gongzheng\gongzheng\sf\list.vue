<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px">
            <el-form-item label="卷宗号" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证类别" prop="lb">
              <el-select v-model="queryParams.lb" placeholder="请选择公证类别" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsbh">
              (<el-input v-model="queryParams.nf" placeholder="年份" clearable @keyup.enter="handleQuery"
                style="max-width: 60px" />)
              <el-select v-model="queryParams.syd" placeholder="请选择字号" clearable style="width: 140px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.gzsbh" placeholder="请输入" clearable @keyup.enter="handleQuery"
                style="width: 100px;" />号
            </el-form-item>
            <el-form-item label="收费状态" prop="sfzt">
              <el-select v-model="queryParams.sfzt" placeholder="请选择收费状态" clearable>
                <el-option label="未收费" value="1" />
                <el-option label="已收费" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="受理日期" prop="slrq">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 300px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" >查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="">收费确认</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Edit" :disabled="single" @click="">批量收费</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="">导出收费清单</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="gzjzJbxxList"
        @selection-change="handleSelectionChange"
        border
        stripe
        height="500">
        <el-table-column type="selection" width="60" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleSf(scope.row)">收费</el-button>
            <el-button type="primary" link @click="handleTf(scope.row)">退费</el-button>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template #default="scope">
            <el-button type="primary" link @click="handleBz(scope.row)">备注</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="jzbh" label="卷宗号" />
        <el-table-column prop="sqr" label="申请人" />
        <el-table-column prop="dsrxm" label="当事人" />
        <el-table-column prop="gzsbh" label="公证书编号" />
        <el-table-column prop="gzsx" label="公证事项" />
        <el-table-column prop="gzyxm" label="公证员" />
        <el-table-column prop="zlxm" label="助理/受理" />
        <el-table-column prop="slrq" label="受理日期" :formatter="formatDate" />
        <el-table-column prop="sf" label="收费金额" align="right" />
        <el-table-column prop="sfzt" label="收费状态">
          <template #default="scope">
            <el-tag v-if="scope.row.sfzt === '1'" type="warning">未收费</el-tag>
            <el-tag v-else-if="scope.row.sfzt === '2'" type="success">已收费</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sfrq" label="收费日期" :formatter="formatDate" />
        <el-table-column prop="sfr" label="收费人" />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="sfList" lang="ts">
import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzs_zh, gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_gzlb', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt'));

const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[string, string] | null>(null);

const queryFormRef = ref<ElFormInstance>();

// 查询参数
const queryParams = ref<GzjzJbxxQuery>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  gzsbh: undefined,
  lb: undefined,
  dsrxm: undefined,
  sqr: undefined,
  gzyxm: undefined,
  zlxm: undefined,
  sfzt: undefined,
  slrq: undefined,
  nf: undefined,
  syd: undefined,
  params: {}
});

/** 查询公证卷宗收费列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginSlrq: dateRange.value[0],
        endSlrq: dateRange.value[1]
      };
    } else {
      delete queryParams.value.params?.beginSlrq;
      delete queryParams.value.params?.endSlrq;
    }

    const res = await listGzjzJbxx(queryParams.value);
    gzjzJbxxList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = null;
  queryParams.value.params = {};
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzJbxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (cellValue) {
    return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
  }
  return '';
}

// 收费
const handleSf = (row?: GzjzJbxxVO) => {
  console.log('收费操作', row);
}

// 退费
const handleTf = (row?: GzjzJbxxVO) => {
  console.log('退费操作', row);
}

// 备注
const handleBz = (row?: GzjzJbxxVO) => {
  console.log('备注操作', row);
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
