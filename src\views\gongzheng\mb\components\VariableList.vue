<template>
  <div>
    <!-- 模板选择区域 -->
    <el-card class="template-selector" style="margin-bottom: 20px;">
      <template #header>
        <span>模板选择</span>
      </template>
      <el-form inline>
        <el-form-item label="模板类型">
          <el-select v-model="selectedTemplateType" @change="onTemplateTypeChange">
            <el-option v-for="item in templateTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="具体模板">
          <el-select v-model="selectedTemplate" @change="onTemplateChange">
            <el-option v-for="template in templateList" :key="template.id" :label="template.wdMc"
              :value="template.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 模板变量列表 -->
    <el-card v-if="selectedTemplate">
      <template #header>
        <span>模板变量配置</span>
        <el-button type="primary" size="small" @click="openAddVariableDialog" style="float: right;">添加变量</el-button>
      </template>
      <el-table :data="templateVariables" border style="width: 100%">
        <el-table-column prop="blName" label="变量名称" width="150" />
        <el-table-column prop="blMc" label="变量描述" width="200" />
        <el-table-column prop="blType" label="变量类型" width="120" />
        <el-table-column prop="isMandatory" label="必填" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isMandatory ? 'danger' : 'info'">{{ scope.row.isMandatory ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="数据源配置" min-width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="$emit('select-variable', scope.row)">配置数据源</el-button>
            <el-tag v-if="scope.row.configCount > 0" type="success" size="small" style="margin-left: 10px;">已配置
              {{ scope.row.configCount }} 个数据源</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="openEditVariableDialog(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteVariable(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 变量新增/编辑弹窗 -->
    <el-dialog v-model="variableDialogVisible" :title="isEditVariable ? '编辑变量' : '新增变量'" width="500px">
      <el-form :model="variableForm" :rules="variableRules" ref="variableFormRef" label-width="100px">
        <el-form-item label="变量名称" prop="blName">
          <el-input v-model="variableForm.blName" maxlength="50" />
        </el-form-item>
        <el-form-item label="变量描述" prop="blMc">
          <el-input v-model="variableForm.blMc" maxlength="100" />
        </el-form-item>
        <el-form-item label="变量类型" prop="blType">
          <el-select v-model="variableForm.blType">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="日期" value="date" />
            <el-option label="布尔" value="boolean" />
          </el-select>
        </el-form-item>
        <el-form-item label="必填" prop="isMandatory">
          <el-switch v-model="variableForm.isMandatory" active-value="true" inactive-value="false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="variableDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveVariable">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import * as api from '@/api/gongzheng/mb/mbBlSjy'
  interface Props {
    blId : string | number
  }
  const props = defineProps<Props>();
  const templateTypeOptions = [
    { label: '个人信息表', value: 'GRXX' },
    { label: '单位信息表', value: 'DWXX' },
    { label: '公证书', value: 'GZS' }
  ]
  const selectedTemplateType = ref('')
  const selectedTemplate = ref('')
  const templateList = ref<any[]>([])
  const templateVariables = ref<any[]>([])

  const emit = defineEmits(['select-template', 'select-variable', 'refresh'])

  // 变量新增/编辑弹窗相关
  const variableDialogVisible = ref(false)
  const isEditVariable = ref(false)
  const variableForm = reactive<any>({
    id: '',
    blName: '',
    blMc: '',
    blType: 0,
    isMandatory: false
  })
  const variableFormRef = ref()
  const variableRules = {
    blName: [{ required: true, message: '请输入变量名称', trigger: 'blur' }],
    blMc: [{ required: true, message: '请输入变量描述', trigger: 'blur' }],
    blType: [{ required: true, message: '请选择变量类型', trigger: 'change' }]
  }

  const onTemplateTypeChange = async (type : string) => {
    selectedTemplate.value = ''
    templateVariables.value = []
    await loadTemplateList(type)
    emit('select-template', type)
  }

  const onTemplateChange = async (templateId : string) => {
    await loadTemplateVariables(templateId)
  }

  const loadTemplateList = async (type : string) => {
    try {
      const res = await api.getTemplateList(type)
      if (res.data.code === 0) {
        templateList.value = res.data.data || []
      } else {
        templateList.value = []
        ElMessage.error(res.data.msg || '获取模板列表失败')
      }
    } catch (e) {
      templateList.value = []
      ElMessage.error('获取模板列表异常')
    }
  }

  const loadTemplateVariables = async (templateId : string) => {
    try {
      const res = await api.getTemplateVariables(templateId)
      if (res.data.code === 0) {
        templateVariables.value = res.data.data || []
      } else {
        templateVariables.value = []
        ElMessage.error(res.data.msg || '获取模板变量失败')
      }
    } catch (e) {
      templateVariables.value = []
      ElMessage.error('获取模板变量异常')
    }
  }

  function openAddVariableDialog() {
    isEditVariable.value = false
    Object.assign(variableForm, { id: '', blName: '', blMc: '', blType: '', isMandatory: false })
    variableDialogVisible.value = true
  }

  function openEditVariableDialog(row : any) {
    isEditVariable.value = true
    Object.assign(variableForm, row)
    variableDialogVisible.value = true
  }

  const saveVariable = () => {
    variableFormRef.value.validate(async (valid : boolean) => {
      if (!valid) return
      try {
        if (isEditVariable.value) {
          await api.updateVariable(variableForm.id, {
            ...variableForm,
            templateId: selectedTemplate.value
          })
          ElMessage.success('编辑成功')
        } else {
          await api.addVariable({
            ...variableForm,
            templateId: selectedTemplate.value
          })
          ElMessage.success('新增成功')
        }
        variableDialogVisible.value = false
        await loadTemplateVariables(selectedTemplate.value)
        emit('refresh')
      } catch (e) {
        ElMessage.error('保存失败')
      }
    })
  }

  const deleteVariable = (variable : any) => {
    ElMessageBox.confirm('确定要删除这个变量吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        await api.deleteVariable(variable.id)
        ElMessage.success('删除成功')
        await loadTemplateVariables(selectedTemplate.value)
        emit('refresh')
      } catch (e) {
        ElMessage.error('删除失败')
      }
    })
  }

  function refresh() {
    if (selectedTemplate.value) {
      loadTemplateVariables(selectedTemplate.value)
    }
  }

  defineExpose({ refresh })

  onMounted(() => {
    // 可选：页面初始化时加载默认模板类型
  })
</script>

<style scoped>
  .template-selector {
    margin-bottom: 20px;
  }
</style>
