// 模板类型
export interface TemplateVO {
  id: number | string;
  wdMc: string;
  [key: string]: any;
}

// 模板变量类型
export interface TemplateVariableVO {
  id: number | string;
  blName: string;
  blMc: string;
  blType: string;
  isMandatory: boolean;
  configCount: number;
  [key: string]: any;
}

// 数据源配置类型
export interface DataSourceConfigVO {
  id: number | string;
  sjyType: string;
  priorityOrder: number;
  sjyConfig: string;
  isActive: string | number;
  remark?: string;
  [key: string]: any;
}

// 新增/编辑数据源配置参数
export interface SaveDataSourceConfigDTO {
  blId: number | string;
  sjyType: string;
  sjyConfig: string;
  priorityOrder: number;
  remark?: string;
}

// 测试数据源配置参数
export interface TestDataSourceConfigDTO {
  sjyType: string;
  sjyConfig: string;
}

// 通用API响应
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
} 