import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MbDzqzVO, MbDzqzForm, MbDzqzQuery } from '@/api/mb/mbDzqz/types';

/**
 * 查询模板-电子签章列表
 * @param query
 * @returns {*}
 */

export const listMbDzqz = (query?: MbDzqzQuery): AxiosPromise<MbDzqzVO[]> => {
  return request({
    url: '/mb/mbDzqz/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询模板-电子签章详细
 * @param id
 */
export const getMbDzqz = (id: string | number): AxiosPromise<MbDzqzVO> => {
  return request({
    url: '/mb/mbDzqz/' + id,
    method: 'get'
  });
};

/**
 * 新增模板-电子签章
 * @param data
 */
export const addMbDzqz = (data: MbDzqzForm) => {
  return request({
    url: '/mb/mbDzqz',
    method: 'post',
    data: data
  });
};

/**
 * 修改模板-电子签章
 * @param data
 */
export const updateMbDzqz = (data: MbDzqzForm) => {
  return request({
    url: '/mb/mbDzqz',
    method: 'put',
    data: data
  });
};

/**
 * 删除模板-电子签章
 * @param id
 */
export const delMbDzqz = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mb/mbDzqz/' + id,
    method: 'delete'
  });
};
