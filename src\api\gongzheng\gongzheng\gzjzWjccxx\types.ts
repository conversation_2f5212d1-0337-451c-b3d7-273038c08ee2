export interface GzjzWjccxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

  /**
   * 文本名称
   */
  wbmc: string;

  /**
   * 文本路径
   */
  wblj: string;

  /**
   * 类型
   */
  lx: string;

  /**
   * 译文名称
   */
  ywmc: string;

  /**
   * 译文路径
   */
  ywlj: string;

  /**
   * 公证事项（事务）
   */
  gzsx: string;

  /**
   * 公证书编号
   */
  gzsbh: string;

  /**
   * 其他证书编号
   */
  qtzsbh: string;

  /**
   * 区块链hash
   */
  qklhash: string;

  /**
   * 是否翻译（0否，1是）
   */
  sfFy: string;

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzWjccxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 文本名称
   */
  wbmc?: string;

  /**
   * 文本路径
   */
  wblj?: string;

  /**
   * 类型
   */
  lx?: string;

  /**
   * 译文名称
   */
  ywmc?: string;

  /**
   * 译文路径
   */
  ywlj?: string;

  /**
   * 公证事项（事务）
   */
  gzsx?: string;

  /**
   * 公证书编号
   */
  gzsbh?: string;

  /**
   * 其他证书编号
   */
  qtzsbh?: string;

  /**
   * 区块链hash
   */
  qklhash?: string;

  /**
   * 是否翻译（0否，1是）
   */
  sfFy?: string;

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzWjccxxQuery extends PageQuery {

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 文本名称
   */
  wbmc?: string;

  /**
   * 文本路径
   */
  wblj?: string;

  /**
   * 类型
   */
  lx?: string;

  /**
   * 译文名称
   */
  ywmc?: string;

  /**
   * 译文路径
   */
  ywlj?: string;

  /**
   * 公证事项（事务）
   */
  gzsx?: string;

  /**
   * 公证书编号
   */
  gzsbh?: string;

  /**
   * 其他证书编号
   */
  qtzsbh?: string;

  /**
   * 区块链hash
   */
  qklhash?: string;

  /**
   * 是否翻译（0否，1是）
   */
  sfFy?: string;

  /**
   * 公证卷宗-公证事项ID
   */
  gzjzGzsxId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



