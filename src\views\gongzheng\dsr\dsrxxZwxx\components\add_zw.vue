<template>
  <div>
    <el-card>
      <PrintComponent ref="printRef" :printBtn="printBtn">
        <template #trigger>
          <el-button v-if="printBtn" type="primary" style="margin-bottom: 10px;" @click="handleCustomPrint">打印</el-button>
        </template>
        <div>
          <h2>指纹卡片</h2>
          <el-form ref="dsrxxZwxxFormRef" :model="form" :rules="rules" label-width="110px">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="24" :md="16" :lg="16">
                <el-row>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="姓名" prop="xm">
                      <el-input :disabled="!props.dialigEdit" v-model="form.xm" placeholder="请输入姓名" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="证件号码" prop="zjhm">
                      <el-input :disabled="!props.dialigEdit" v-model="form.zjhm" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="性别" prop="xb">
                      <el-select :disabled="!props.dialigEdit" v-model="form.xb" placeholder="请选择性别">
                        <el-option v-for="dict in gz_xb" :key="dict.value" :label="dict.label"
                          :value="dict.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="出生日期" prop="csrq">
                      <el-date-picker :disabled="!props.dialigEdit" clearable v-model="form.csrq" type="date"
                        value-format="YYYY-MM-DD" placeholder="请选择出生日期">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <el-form-item label="住址" prop="zz">
                      <el-input :disabled="!props.dialigEdit" v-model="form.zz" placeholder="请输入住址" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :xs="24" :sm="24" :md="8" :lg="8">
                <el-form-item label="照片采集" prop="zp">
                  <el-row>
                    <el-col :span="24">
                      <el-image :src="form.zp" class="zpcss"></el-image>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-row :gutter="20">
            <el-col :span="6">签名</el-col>
            <el-col :span="6">手印</el-col>
            <el-col :span="6">年<span class="kgcss"></span>月<span class="kgcss"></span>日</el-col>
          </el-row>
        </div>
      </PrintComponent>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="props.closeBtn" type="info" @click="close">关 闭</el-button>
          <el-button v-if="props.dialigEdit" :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button type="primary" @click="handleCustomPrint">打 印</el-button>
          <el-button v-if="props.dialigEdit" type="primary" @click="savePhoto">拍照</el-button>
        </div>
      </template>
    </el-card>
    <!-- 拍照-->
    <el-dialog :title="dialog5.title" v-model="dialog5.visible" width="500px" append-to-body>
      <Pz v-if="dialog5.visible" ref="pzRef" @update-count="handleUpdatePhoto"></Pz>
    </el-dialog>

  </div>
</template>

<script setup name="AddZW" lang="ts">
  import { listDsrxxZwxx, getDsrxxZwxx, delDsrxxZwxx, addDsrxxZwxx, updateDsrxxZwxx } from '@/api/gongzheng/dsr/dsrxxZwxx';
  import { DsrxxZwxxVO, DsrxxZwxxQuery, DsrxxZwxxForm } from '@/api/gongzheng/dsr/dsrxxZwxx/types';


  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_xb } = toRefs<any>(proxy?.useDict('gz_xb'));
  const dsrxxZwxxFormRef = ref<ElFormInstance>();
  const loading = ref(true);
  const buttonLoading = ref(false);
  const printBtn = ref(true);

  interface Props {
    vo : DsrxxZwxxVO;
    dialigEdit : boolean;
    closeBtn : boolean;
  }
  const props = defineProps<Props>();


  const initFormData : DsrxxZwxxForm = {
    id: undefined,
    dsrId: undefined,
    zwtp: undefined,
    zwxx: undefined,
    createTime: undefined,
    qmxx: undefined,
    syxx: undefined,
    xm: undefined,
    xb: undefined,
    zjlx: undefined,
    zjhm: undefined,
    csrq: undefined,
    zz: undefined,
    zp: undefined,
  }
  const data = reactive<PageData<DsrxxZwxxForm, DsrxxZwxxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      xm: undefined,
      zjhm: undefined,
      params: {
        createTime: undefined,
      }
    },
    rules: {

      xm: [
        { required: true, message: "姓名不能为空", trigger: "blur" }
      ],
      xb: [
        { required: true, message: "性别不能为空", trigger: "blur" }
      ],
      zjhm: [
        { required: true, message: "证件号码不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);
  const submitForm = () => {
    dsrxxZwxxFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateDsrxxZwxx(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDsrxxZwxx(form.value).finally(() => buttonLoading.value = false);
        }
        updateForm();
        proxy?.$modal.msgSuccess("操作成功");
      }
    });
  };



  const close = () => {
    updateForm();
  };


  /** 拍照*/
  import Pz from '@/components/Gongzheng/pz/pz_no_upload.vue'
  const pzRef = ref<InstanceType<typeof Pz> | null>(null);
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  /** 上传成功后回传 */
  const handleUpdatePhoto = (img) => {
    form.value.zp = img;
    dialog5.visible = false;
    dialog5.title = "";
  }
  /** 拍照*/

  /** 打印*/
  import PrintComponent from '@/components/Gongzheng/print/print.vue'; // 引入打印组件
  // 也可以通过ref获取组件实例并调用打印方法
  const printRef = ref(null);

  const handleCustomPrint = () => {
    printRef.value?.print({
      margin: '2cm 1cm', // 自定义边距
      showHeaderFooter: false // 隐藏页眉页脚
    });
  };
  const print = () => {

  };
  /** 打印*/

  const savePhoto = () => {
    dialog5.visible = true;
    dialog5.title = "拍照";
  }

  /** 拍照*/
  // 定义事件
  const emits = defineEmits<{
    (e : 'update-refresht', vo : DsrxxZwxxVO) : void;
  }>();

  const updateForm = () => {
    emits('update-refresht', form.value);
  };
  const init = (vo : DsrxxZwxxVO) => {
    if (vo && vo.id) {
      form.value = vo;
    }
  }
  defineExpose({
    updateForm,
  });
  onMounted(() => {
    init(props.vo);
  });
</script>
<style scoped>
  .zpcss {
    width: 150px;
    height: 180px;
    border: 1px solid #ccc;
  }

  .kgcss {
    margin: 0px 25px;
  }
</style>
