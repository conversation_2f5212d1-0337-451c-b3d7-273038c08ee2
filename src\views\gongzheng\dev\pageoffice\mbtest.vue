<template>
  <div class="pageoffice-demo">
    <!-- 统一文档处理 DEMO 区域 -->
    <el-card style="margin-bottom: 30px;">
      <template #header>
        <span>统一文档处理 DEMO</span>
      </template>
      <el-form :model="processForm" class="process-form" @submit.prevent>
        <el-form-item label="必填：文档类型">
          <el-input v-model="processForm.type" placeholder="请输入文档类型" />
        </el-form-item>
        <el-form-item label="必填：业务ID">
          <el-input v-model="processForm.bizId" placeholder="业务ID" />
        </el-form-item>
        <el-form-item label="必填：附件类别">
          <el-input v-model="processForm.fjlb" placeholder="请输入附件类别" />
          <span style="font-weight: bold;color:red;">说明：（文档拟定1 证据材料2 笔录3 代书(文书)4 其他 99）</span>
        </el-form-item>
        <el-form-item label="动作">
          <el-input v-model="processForm.action" placeholder="请输入动作" />
          <span style="font-weight: bold;color:red;">说明： 默认编辑  edit  可选 view</span>
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 10px;">
        <span style="font-size: 13px;">自定义参数：</span>
        <el-input type="textarea" v-model="processForm.extraParams"></el-input>
        <!-- <el-button size="small" type="primary" @click="addCustomParam">添加参数</el-button> -->
      </div>
      <!--  <div v-for="(item, idx) in processForm.extraParams" :key="idx"
        style="display: flex; align-items: center; margin-bottom: 6px;">
        <el-input v-model="item.key" placeholder="参数名" style="width: 100px; margin-right: 6px;" />
        <el-input v-model="item.value" placeholder="参数值" style="width: 500px; margin-right: 6px;" />
        <el-button size="small" type="danger" @click="removeCustomParam(idx)">删除</el-button>
      </div> -->
      <el-button type="success" @click="handleProcess" :loading="processLoading">调用统一文档处理接口</el-button>
      <el-alert v-if="processResultMsg" :title="processResultMsg" type="info" show-icon style="margin: 10px 0;" />
      <el-descriptions v-if="processResultObj" :column="1" border>
        <el-descriptions-item v-for="(val, key) in processResultObj" :key="key"
          :label="key">{{ val }}</el-descriptions-item>
      </el-descriptions>
    </el-card>


  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { POBrowser } from "js-pageoffice";
  import {
    processDocument2
  } from '@/api/gongzheng/dev/pageoffice';
  import type { UnifiedDocumentVO } from '@/api/gongzheng/dev/pageoffice/types';
  const extraParams= {
      'dsrId':'13',
      'dsrLx':'1'
    };

  // 统一文档处理 DEMO 相关
  const processForm = reactive({
    extraParams:JSON.stringify(extraParams),
    bizId: '1946873914006556673',
    type: 'DSR_INFO',
    fjlb: '3',
    action: null
  });
  const processLoading = ref(false);
  const processResultMsg = ref('');
  const processResultObj = ref<Record<string, any> | null>(null);
  import { getToken } from '@/utils/auth';
  async function handleProcess() {
    processLoading.value = true;
    processResultMsg.value = '';
    processResultObj.value = null;
    // 组装参数
    const params : Record<string, any> = {
      type: processForm.type,
      bizId: processForm.bizId,
      fjlb: processForm.fjlb,
      action: processForm.action

    };
    if (processForm.extraParams) {
      params.extraParams = JSON.parse(processForm.extraParams)

    }
    const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;
    try {
      // const { data } = await processDocument2(params);
      // processResultMsg.value = '接口调用成功';
      // processResultObj.value = typeof data === 'object' ? data : { result: data };
      // console.log(processResultObj.value)
      // if (processResultObj.value) {

      POBrowser.setProxyBaseAPI(baseUrl)
      POBrowser.setHeader("Authorization", "Bearer " + getToken());
      POBrowser.setStorage("Admin-Token", getToken());
      POBrowser.openWindow("/backoffice/word2", 'width=1300px;height=900px;', JSON.stringify(params));

      // }
    } catch (e : any) {
      processResultMsg.value = e?.message || '接口调用失败';
    } finally {
      processLoading.value = false;
    }
  }
</script>

<style scoped>
  .pageoffice-demo {
    max-width: 800px;
    margin: 40px auto;
  }

  .toolbar {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .result-area {
    margin-top: 20px;
  }

  .process-form {
    margin-bottom: 10px;
  }
</style>
