<template>
  <div style="padding:10px">
    <AddZw ref="addZwRef" :dialigEdit="editShow" :closeBtn="closeBtn"></AddZw>
  </div>
</template>

<script setup name="DsrxxZwxx" lang="ts">
  import AddZw from '@/views/gongzheng/dsr/dsrxxZwxx/components/add_zw.vue'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const addZwRef = ref<InstanceType<typeof AddZw> | null>(null);
  const editShow = ref(true);
  const closeBtn = ref(false);
</script>

<style>
</style>
