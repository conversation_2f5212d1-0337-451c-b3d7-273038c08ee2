import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzWjccxxVO, GzjzWjccxxForm, GzjzWjccxxQuery } from '@/api/gongzheng/dev/gzjzWjccxx/types';

/**
 * 查询公证卷宗-公证书文件存储信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzWjccxx = (query?: GzjzWjccxxQuery): AxiosPromise<GzjzWjccxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzWjccxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证书文件存储信息v1.0详细
 * @param id
 */
export const getGzjzWjccxx = (id: string | number): AxiosPromise<GzjzWjccxxVO> => {
  return request({
    url: '/gongzheng/gzjzWjccxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证书文件存储信息v1.0
 * @param data
 */
export const addGzjzWjccxx = (data: GzjzWjccxxForm) => {
  return request({
    url: '/gongzheng/gzjzWjccxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证书文件存储信息v1.0
 * @param data
 */
export const updateGzjzWjccxx = (data: GzjzWjccxxForm) => {
  return request({
    url: '/gongzheng/gzjzWjccxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证书文件存储信息v1.0
 * @param id
 */
export const delGzjzWjccxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzWjccxx/' + id,
    method: 'delete'
  });
};
