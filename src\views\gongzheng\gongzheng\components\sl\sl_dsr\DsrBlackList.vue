<template>
  <gz-dialog v-model="visible" :title="title" width="680">
    <div v-loading="dsrFormLoading" class="flex flex-col items-center gap-14px pr-26px">
      <div class="w-full flex items-center">
        <div class="flex-1 flex items-center">
          <span class="w-120px flex justify-end font-bold pr-10px">客户名称:</span>
          <el-text type="info" class="flex-1">{{ dsrInfo.name || dsrInfo.xm || '-' }}</el-text>
        </div>
        <div class="flex-1 flex items-center">
          <span class="w-120px flex justify-end font-bold pr-10px">类型:</span>
          <el-text type="info" class="flex-1">{{ dictMapFormat(gz_dsr_jslx, dsrInfo.js) || '-' }}</el-text>
        </div>
      </div>
      <div class="w-full flex items-center">
        <div class="flex-1 flex items-center">
          <span class="w-120px flex justify-end font-bold pr-10px">证件名称:</span>
          <el-text type="info" class="flex-1">{{ dictMapFormat(dsrInfo.dsrLx === '1' ? gz_gr_zjlx : gz_jg_zjlx , dsrInfo.certificateType) || '-' }}</el-text>
        </div>
        <div class="flex-1 flex items-center">
          <span class="w-120px flex justify-end font-bold pr-10px">证件号码:</span>
          <el-text type="info" class="flex-1">{{ dsrInfo.idNumber || dsrInfo.zjhm || dsrInfo.certificateNo || '-' }}</el-text>
        </div>
      </div>
      <el-form style="width: 100%;" :model="dsrBLForm" :rules="dsrBLFormRules" ref="dsrBLFormRef" label-width="120">
        <el-form-item prop="qksm" label="不诚信记录:" style="margin: 0;">
          <el-input
            v-model="dsrBLForm.qksm"
            :autosize="{ minRows: 3, maxRows: 6 }"
            type="textarea"
            placeholder="请输入不诚信记录."
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-6px">
        <el-button @click="comfirm" :loading="submitting" :disabled="submitting" type="primary">{{ onBL ? '修改' : '添加黑名单' }}</el-button>
        <el-button @click="close" :disabled="submitting">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { addDsrhmdxx, listDsrhmdxx, updateDsrhmdxx } from '@/api/gongzheng/dsr/dsrhmdxx';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { dictMapFormat, formatDate } from '@/utils/ruoyi';
import { ref, reactive, computed, onMounted } from 'vue';
/**=================== 基础封装数据 ===================**/
interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
  dsrInfo?: GzjzDsrVO;
}

const props = withDefaults(defineProps<Props>(), {
  title: '当事人黑名单',
  dsrInfo: () => {return {} as GzjzDsrVO}
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

function comfirm() {
  if(!dsrBLFormRef.value) return;
  dsrBLFormRef.value.validate((valid, fields) => {
    if(valid) {
      if(onBL.value) {
        editCurDsrBL();
      } else {
        addHmdAction();
      }
    } else {
      const firstField = Object.values(fields)[0];
      if (firstField) {
        ElMessage.error(firstField[0].message);
      }
    }
  })
}

function close() {
  emit('update:modelValue', false)
}
/**=================== 基础封装数据 ===================**/

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gr_zjlx, gz_jg_zjlx, gz_dsr_jslx } = toRefs<any>(proxy?.useDict('gz_jg_zjlx', 'gz_gr_zjlx', 'gz_dsr_jslx'));

interface DsrInfo extends GzjzDsrVO {
  qksm?: string;
}

const dsrBLForm = ref<DsrInfo>({
  qksm: ''
})
const dsrFormLoading = ref(false)
const submitting = ref(false)

const onBL = ref(false);

const dsrBLFormRules = {
  qksm: [
    { required: true, message: '请输入不诚信记录', trigger: 'blur' },
    { required: true, message: '请输入不诚信记录', trigger: 'change' }
  ]
}

const dsrBLFormRef = ref<ElFormInstance>(null)

// 查询当事人是否在黑名单
async function loadCurDsrBL() {
  if (!props.dsrInfo) return;
  try {
    dsrFormLoading.value = true
    const params = {
      sxrId: props.dsrInfo.dsrId,
      sxr: props.dsrInfo.name || props.dsrInfo.xm,
      pageNum: 1,
      pageSize: 100
    }
    const res = await listDsrhmdxx(params);
    if (res.code === 200) {
      if(res.rows.length > 0) {
        dsrBLForm.value = {
          ...dsrBLForm.value,
          ...res.rows[0]
        }
        onBL.value = true
      } else {
        dsrBLForm.value = {
          ...dsrBLForm.value,
          ...props.dsrInfo
        }
      }
    }

  } catch (err: any) {
    ElMessage.error('当事人信息获取失败，请重试')
    console.log('黑名单：当事人信息获取失败，请重试', err)
  } finally {
    dsrFormLoading.value = false
  }
}

// 如果在黑名单内，提交时是进行编辑
async function editCurDsrBL() {
  if (!props.dsrInfo) return;
  const { dsrInfo } = props;
  try {
    submitting.value = true;
    const params = {
      id: dsrBLForm.value.id,
      sxrId: dsrInfo.dsrId,
      sxr: dsrInfo.name || dsrInfo.xm,
      sxrzjlx: dsrInfo.certificateType,
      sxrzjhm: dsrInfo.idNumber || dsrInfo.zjhm || dsrInfo.certificateNo,
      zt: 0,
      qksm: dsrBLForm.value.qksm,
    }
    const res = await updateDsrhmdxx(params);
    if(res.code === 200) {
      ElMessage.success(`${dsrInfo.name || dsrInfo.xm } 黑名单已更新`);
      emit('success', {...dsrBLForm.value});
      close()
    }
  } catch(err: any) {
    console.log('更新黑名单信息失败', err);
    ElMessage.error('更新黑名单信息失败');
  } finally {
    submitting.value = false;
  }
}

function addHmdAction() {
  ElMessageBox.confirm(`确定添加 ${dsrBLForm.value.name || dsrBLForm.value.xm } 至黑名单吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    addCurDsrBL();
  }).catch(() => {})
}

// 添加当事人到黑名单
async function addCurDsrBL() {
  if (!props.dsrInfo) return;
  const { dsrInfo } = props;
  try {
    submitting.value = true;
    const params = {
      sxrId: dsrInfo.dsrId,
      sxr: dsrInfo.name || dsrInfo.xm,
      sxrzjlx: dsrInfo.certificateType,
      sxrzjhm: dsrInfo.idNumber || dsrInfo.zjhm || dsrInfo.certificateNo,
      zt: 0,
      qksm: dsrBLForm.value.qksm,
      chrq: formatDate(new Date(), 'YYYY-MM-DD'),
    }
    const res = await addDsrhmdxx(params);
    if(res.code === 200) {
      ElMessage.success(`${dsrInfo.name || dsrInfo.xm } 已添加至黑名单`);
      emit('success', {...dsrBLForm.value});
      close()
    }
  } catch(err: any) {
    console.log('添加黑名单失败', err);
    ElMessage.error('添加黑名单失败');
  } finally {
    submitting.value = false;
  }
}

watch(() => props.modelValue, (val) => {
  if(!val) {
    dsrBLForm.value = {
      qksm: ''
    }
  }
})

onMounted(() => {
  loadCurDsrBL();
})

</script>
