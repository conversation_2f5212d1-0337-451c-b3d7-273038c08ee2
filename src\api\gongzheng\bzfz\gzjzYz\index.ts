import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzYzVO, GzjzYzForm, GzjzYzQuery } from '@/api/gongzheng/bzfz/gzjzYz/types';

/**
 * 查询公证卷宗-遗嘱列表
 * @param query
 * @returns {*}
 */

export const listGzjzYz = (query?: GzjzYzQuery): AxiosPromise<GzjzYzVO[]> => {
  return request({
    url: '/gongzheng/gzjzYz/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-遗嘱详细
 * @param id
 */
export const getGzjzYz = (id: string | number): AxiosPromise<GzjzYzVO> => {
  return request({
    url: '/gongzheng/gzjzYz/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-遗嘱
 * @param data
 */
export const addGzjzYz = (data: GzjzYzForm) => {
  return request({
    url: '/gongzheng/gzjzYz',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-遗嘱
 * @param data
 */
export const updateGzjzYz = (data: GzjzYzForm) => {
  return request({
    url: '/gongzheng/gzjzYz',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-遗嘱
 * @param id
 */
export const delGzjzYz = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzYz/' + id,
    method: 'delete'
  });
};

/**
 * 根据gzjzGzsxId查询遗嘱信息
 * @param params 
 * @returns 
 */
export const getGzjzYzByGzjzGzsxId = (params: { gzjzGzsxId: string }): AxiosPromise<GzjzYzVO> => {
  return request({
    url: '/gongzheng/gzjzYz/getByGzjzGzsxId',
    method: 'get',
    params
  })
}
