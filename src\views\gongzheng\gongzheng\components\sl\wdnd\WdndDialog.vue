<template>
  <gz-dialog v-model="visible" :title="title" fullscreen @closed="closed">
    <Wdnd />
    <template #footer>
      <div class="flex justify-end items-center gap-6px">
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import Wdnd from './index.vue'

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '文档拟定',
})

const emit = defineEmits(['update:modelValue', 'closed'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

function close() {
  emit('update:modelValue', false)
  emit('closed')
}

function closed() {
  emit('closed')
}

</script>
