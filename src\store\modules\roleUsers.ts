import { defineStore } from 'pinia';

export const useRoleUsersStore = defineStore('roleUsers', () => {
  const roleUsers = ref<Map<string, UsersDataOption[]>>(new Map());

  /**
   * 获取字典
   * @param _key 字典key
   */
  const getRoleUsers = (_key: string): UsersDataOption[] | null => {
    if (!_key) {
      return null;
    }
    return roleUsers.value.get(_key) || null;
  };

  /**
   * 设置字典
   * @param _key 字典key
   * @param _value 字典value
   */
  const setRoleUsers = (_key: string, _value: UsersDataOption[]) => {
    if (!_key) {
      return false;
    }
    try {
      roleUsers.value.set(_key, _value);
      return true;
    } catch (e) {
      console.error('Error in setUser:', e);
      return false;
    }
  };


  /**
   * 清空字典
   */
  const cleanUsersDataOption = (): void => {
    roleUsers.value.clear();
  };

  return {
    roleUsers,
    getRoleUsers,
    setRoleUsers,
    cleanUsersDataOption
  };
});
