import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzGzsbmVO, GzjzGzsbmForm, GzjzGzsbmQuery } from '@/api/gongzheng/gongzheng/gzjzGzsbm/types';

/**
 * 查询公证卷宗-公证书编号v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzGzsbm = (query?: GzjzGzsbmQuery): AxiosPromise<GzjzGzsbmVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsbm/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证书编号v1.0详细
 * @param id
 */
export const getGzjzGzsbm = (id: string | number): AxiosPromise<GzjzGzsbmVO> => {
  return request({
    url: '/gongzheng/gzjzGzsbm/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证书编号v1.0
 * @param data
 */
export const addGzjzGzsbm = (data: GzjzGzsbmForm) => {
  return request({
    url: '/gongzheng/gzjzGzsbm',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证书编号v1.0
 * @param data
 */
export const updateGzjzGzsbm = (data: GzjzGzsbmForm) => {
  return request({
    url: '/gongzheng/gzjzGzsbm',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证书编号v1.0
 * @param id
 */
export const delGzjzGzsbm = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzGzsbm/' + id,
    method: 'delete'
  });
};
