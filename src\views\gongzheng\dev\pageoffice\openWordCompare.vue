<template>
  <div class="pageoffice-demo">
    <el-card style="margin-bottom: 30px;">
      <template #header>
        <span>对比文档 DEMO</span>
      </template>
      <el-button type="success" @click="handleProcess" :loading="processLoading">调用统一文档打开接口</el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { POBrowser } from "js-pageoffice";
  import { getToken } from '@/utils/auth';
  const processLoading = ref(false);
  const processResultMsg = ref('');
  const processResultObj = ref<Record<string, any> | null>(null);
  async function handleProcess() {
    processLoading.value = true;
    processResultMsg.value = '';
    processResultObj.value = null;
    const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;
    try {
      POBrowser.setProxyBaseAPI(baseUrl)
      POBrowser.setHeader("Authorization", "Bearer " + getToken());
      POBrowser.setStorage("Admin-Token", getToken());
      POBrowser.openWindow("/backoffice/wordCompare", 'width=1300px;height=900px;', null);
    } catch (e : any) {
      processResultMsg.value = e?.message || '接口调用失败';
    } finally {
      processLoading.value = false;
    }
  }
</script>

<style scoped>
  .pageoffice-demo {
    max-width: 800px;
    margin: 40px auto;
  }

  .toolbar {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .result-area {
    margin-top: 20px;
  }

  .process-form {
    margin-bottom: 10px;
  }
</style>
