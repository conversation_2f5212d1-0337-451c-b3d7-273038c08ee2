import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzsVO, GzsForm, GzsQuery } from '@/api/gongzheng/basicdata/gzs/types';

/**
 * 查询告知树列表
 * @param query
 * @returns {*}
 */

export const listGzs = (query?: GzsQuery): AxiosPromise<GzsVO[]> => {
  return request({
    url: '/basicdata/gzs/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询告知树详细
 * @param id
 */
export const getGzs = (id: string | number): AxiosPromise<GzsVO> => {
  return request({
    url: '/basicdata/gzs/' + id,
    method: 'get'
  });
};

/**
 * 新增告知树
 * @param data
 */
export const addGzs = (data: GzsForm) => {
  return request({
    url: '/basicdata/gzs',
    method: 'post',
    data: data
  });
};

/**
 * 修改告知树
 * @param data
 */
export const updateGzs = (data: GzsForm) => {
  return request({
    url: '/basicdata/gzs',
    method: 'put',
    data: data
  });
};

/**
 * 删除告知树
 * @param id
 */
export const delGzs = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzs/' + id,
    method: 'delete'
  });
};


export const listTree = (query?: GzsQuery): AxiosPromise<GzsVO[]> => {
  return request({
    url: '/basicdata/gzs/listTree',
    method: 'get',
    params: query
  });
};


export const listExcludeChild = (gzsId: string | number): AxiosPromise<GzsVO[]> => {
  return request({
    url: '/basicdata/gzs/list/exclude/' + gzsId,
    method: 'get'
  });
};
