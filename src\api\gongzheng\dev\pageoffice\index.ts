import request from '@/utils/pageOfficeRequest';
import { AxiosPromise } from 'axios';
import {
  UnifiedDocumentProcessParams,
  UnifiedDocumentProcessParams2,
  UnifiedDocumentProcessParams3,
  UnifiedDocumentEditParams,
  UnifiedDocumentDeleteByPathParams,
  UnifiedDocumentVO
} from './types';
// 统一文档处理
export const processDocument = (data: UnifiedDocumentProcessParams): AxiosPromise<any> => {
  return request({
    url:  '/api/unified-document/process',
    method: 'post',
    data
  });
};
export const openDocument = (data: UnifiedDocumentProcessParams3): AxiosPromise<any> => {
  return request({
    url:  '/wordgenerate/document/openDocument',
    method: 'post',
    data
  });
};


export const processDocument2 = (data: UnifiedDocumentProcessParams2): AxiosPromise<any> => {
  return request({
    url:  '/wordgenerate/document/generate',
    method: 'post',
    data
  });
};
// 查看文档
export const viewDocument = (params: any): AxiosPromise<UnifiedDocumentVO> => {
  return request({
    url:  '/api/unified-document/view',
    method: 'get',
    params
  });
};

// 保存文档
export const saveDocument = (documentId: string, data: any): AxiosPromise<any> => {
  return request({
    url:  `/api/unified-document/save/${documentId}`,
    method: 'post',
    data
  });
};

// 编辑文档
export const editDocument = (data: UnifiedDocumentEditParams): AxiosPromise<any> => {
  return request({
    url:  '/api/unified-document/edit',
    method: 'post',
    data
  });
};

// 删除文档
export const deleteDocument = (data: any): AxiosPromise<any> => {
  return request({
    url:  '/api/unified-document/delete',
    method: 'delete',
    data
  });
};

// 关闭文档
export const closeDocument = (documentId: string): AxiosPromise<any> => {
  return request({
    url:  `/api/unified-document/close/${documentId}`,
    method: 'post'
  });
};

// 获取文档状态
export const getDocumentStatus = (documentId: string): AxiosPromise<any> => {
  return request({
    url:  `/api/unified-document/status/${documentId}`,
    method: 'get'
  });
};

// 根据ID删除文档
export const deleteDocumentById = (documentId: string): AxiosPromise<any> => {
  return request({
    url:  `/api/unified-document/delete/${documentId}`,
    method: 'delete'
  });
};

// 根据路径删除文档
export const deleteDocumentByPath = (data: UnifiedDocumentDeleteByPathParams): AxiosPromise<any> => {
  return request({
    url:  '/api/unified-document/delete-by-path',
    method: 'delete',
    data
  });
};


// 文档对比
export const wordCompare = (data: any): AxiosPromise<any> => {
  return request({
    url:  `/wordgenerate/document/wordCompare`,
    method: 'post',
    data
  });
};
