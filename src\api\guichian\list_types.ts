export interface GxbassChainVo {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 交易哈希
   */
  txHash: string | number;

  /**
   * 区块高度
   */
  blockHeight: string | number;

  /**
   * 区块哈希
   */
  blockHash: string | number;

  /**
   *  签名方
   */
  signer: string;

  /**
   * 业务ID
   */
  bizId: string;
  /**
   * 交易时间
   */
  txTime: string;
  /**
   * 交易内容
   */
  data: string;
  /**
   * 数字签名
   */
  signature: string;
  /**
   *  存证类型
   */
  proofType: Number;
  /**
   * 文件上链IPFS哈希
   */
  ipfsPath: string;
  /**
   * 账户地址
   */
  fromAddr: string;
  /**
   * 存证文件
   */
  fileName: string;
}

export interface GxbassChainForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;
}
export interface GxbassChainQuery extends PageQuery {
 /**
  * 交易哈希
  */
 txHash: string | number;

  /**
   * 交易时间
   */
  txTime: string;

 /**
   *  存证类型
   */
  proofType: Number;

  /**
   * 存证文件
   */
  fileName: string;


}
