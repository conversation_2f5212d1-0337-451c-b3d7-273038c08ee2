<template>
  <el-table :data="data" border stripe size="small">
    <el-table-column type="index" label="#" align="center"/>
    <el-table-column label="公证事项" width="240" prop="gzsx" align="center" />
    <el-table-column label="文书名称" prop="wbmc">
      <template #default="{ row, column }">
        <div class="flex gap-4px flex-wrap">
          <el-tag>
            <el-button type="primary" link @click="openWs(row)">{{row.wbmc}}</el-button>
          </el-tag>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { type DocParams, EditDocParams, docOpenShow, editDoc, showDoc } from '@/views/gongzheng/doc/DocEditor'
import { EnumDocActionType, EnumDocType, EnumDocFileType } from '@/views/gongzheng/doc/enumType'

interface Props {
  data: any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

function openWs(data: any) {
  const ossInfo = JSON.parse(data?.wblj || '{}')
  if(ossInfo.path) {
    docOpenShow(ossInfo.path)
  }
}

</script>