import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxZrrVO, DsrxxZrrForm, DsrxxZrrQuery } from '@/api/gongzheng/dsr/dsrxxZrr/types';

/**
 * 查询当事人-基本信息-自然人信息列表
 * @param query
 * @returns {*}
 */

export const listDsrxxZrr = (query?: DsrxxZrrQuery): AxiosPromise<DsrxxZrrVO[]> => {
  return request({
    url: '/dsr/dsrxxZrr/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-基本信息-自然人信息详细
 * @param id
 */
export const getDsrxxZrr = (id: string | number): AxiosPromise<DsrxxZrrVO> => {
  return request({
    url: '/dsr/dsrxxZrr/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-基本信息-自然人信息
 * @param data
 */
export const addDsrxxZrr = (data: DsrxxZrrForm) => {
  return request({
    url: '/dsr/dsrxxZrr',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-基本信息-自然人信息
 * @param data
 */
export const updateDsrxxZrr = (data: DsrxxZrrForm) => {
  return request({
    url: '/dsr/dsrxxZrr',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-基本信息-自然人信息
 * @param id
 */
export const delDsrxxZrr = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxZrr/' + id,
    method: 'delete'
  });
};

/**
 * 根据当事人证件号码查询当事人信息，包含该当事人黑名单信息
 * @param params 
 * @returns 
 */
export const queryDsrxxZrrByIdCard = (params: { IdcardNumber: string }) => {
  return request({
    url: '/dsr/dsrxxZrr/getInfoByIdcard',
    method: 'get',
    params
  })
};
