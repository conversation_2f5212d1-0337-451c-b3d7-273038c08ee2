// vite.config.ts
import { defineConfig, loadEnv } from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/vite/dist/node/index.js";

// vite/plugins/index.ts
import vue from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueDevTools from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// vite/plugins/unocss.ts
import UnoCss from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unocss/dist/vite.mjs";
var unocss_default = () => {
  return UnoCss({
    hmrTopLevelAwait: false
    // unocss默认是true，低版本浏览器是不支持的，启动后会报错
  });
};

// vite/plugins/auto-import.ts
import AutoImport from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-vue-components/dist/resolvers.js";
var __vite_injected_original_dirname = "E:\\lgc\\project-lgc-win\\gongzheng\\gongzheng-vueadmin\\vite\\plugins";
var auto_import_default = (path3) => {
  return AutoImport({
    // 自动导入 Vue 相关函数
    imports: ["vue", "vue-router", "@vueuse/core", "pinia"],
    eslintrc: {
      enabled: true,
      filepath: "./.eslintrc-auto-import.json",
      globalsPropValue: true
    },
    resolvers: [
      // 自动导入 Element Plus 相关函数ElMessage, ElMessageBox... (带样式)
      ElementPlusResolver()
    ],
    vueTemplate: true,
    // 是否在 vue 模板中自动导入
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname, "../../src"), "types", "auto-imports.d.ts")
  });
};

// vite/plugins/components.ts
import Components from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver as ElementPlusResolver2 } from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-vue-components/dist/resolvers.js";
import IconsResolver from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-icons/dist/resolver.js";
var __vite_injected_original_dirname2 = "E:\\lgc\\project-lgc-win\\gongzheng\\gongzheng-vueadmin\\vite\\plugins";
var components_default = (path3) => {
  return Components({
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver2(),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ["ep"]
      })
    ],
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname2, "../../src"), "types", "components.d.ts")
  });
};

// vite/plugins/icons.ts
import Icons from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-icons/dist/vite.js";
var icons_default = () => {
  return Icons({
    // 自动安装图标库
    autoInstall: true
  });
};

// vite/plugins/svg-icon.ts
import { createSvgIconsPlugin } from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/vite-plugin-svg-icons-ng/dist/index.mjs";
var __vite_injected_original_dirname3 = "E:\\lgc\\project-lgc-win\\gongzheng\\gongzheng-vueadmin\\vite\\plugins";
var svg_icon_default = (path3) => {
  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [path3.resolve(path3.resolve(__vite_injected_original_dirname3, "../../src"), "assets/icons/svg")],
    // 指定symbolId格式
    symbolId: "icon-[dir]-[name]"
  });
};

// vite/plugins/compression.ts
import compression from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/vite-plugin-compression/dist/index.mjs";
var compression_default = (env) => {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
};

// vite/plugins/setup-extend.ts
import setupExtend from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
var setup_extend_default = () => {
  return setupExtend({});
};

// vite/plugins/index.ts
import path from "path";
var plugins_default = (viteEnv, isBuild = false) => {
  const vitePlugins = [];
  vitePlugins.push(vue());
  vitePlugins.push(vueDevTools());
  vitePlugins.push(unocss_default());
  vitePlugins.push(auto_import_default(path));
  vitePlugins.push(components_default(path));
  vitePlugins.push(compression_default(viteEnv));
  vitePlugins.push(icons_default());
  vitePlugins.push(svg_icon_default(path));
  vitePlugins.push(setup_extend_default());
  return vitePlugins;
};

// vite.config.ts
import autoprefixer from "file:///E:/lgc/project-lgc-win/gongzheng/gongzheng-vueadmin/node_modules/autoprefixer/lib/autoprefixer.js";
import path2 from "path";
var __vite_injected_original_dirname4 = "E:\\lgc\\project-lgc-win\\gongzheng\\gongzheng-vueadmin";
var vite_config_default = defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname4, "./src")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    plugins: plugins_default(env, command === "build"),
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_APP_PORT),
      open: true,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: "http://localhost:8080",
          changeOrigin: true,
          ws: true,
          rewrite: (path3) => path3.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
        },
        [env.VITE_PAGE_OFFICE_BASE_API]: {
          target: "http://localhost:8080",
          changeOrigin: true,
          ws: true,
          rewrite: (path3) => path3.replace(new RegExp("^" + env.VITE_PAGE_OFFICE_BASE_API), "")
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/styles/variables.module.scss as *";'
          // javascriptEnabled: true
          api: "modern-compiler"
        }
      },
      postcss: {
        plugins: [
          // 浏览器兼容性
          autoprefixer(),
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                atRule.remove();
              }
            }
          }
        ]
      }
    },
    // 预编译
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "axios",
        "@vueuse/core",
        "echarts",
        "vue-i18n",
        "@vueup/vue-quill",
        "image-conversion",
        "element-plus/es/components/**/css"
      ]
    },
    build: {
      // minify: 'terser',   // terser时需安装terser
      // terserOptions: {
      //   compress: {
      //     drop_console: true,   // 打包清除代码中的console
      //     drop_debugger: true,  // 打包清除代码中的debugger
      //   }
      // },   
      rollupOptions: {
        output: {
          assetFileNames: ({ name, source, type }) => {
            const extType = name?.split(".").pop();
            if (extType === "css") {
              return "assets/css/[name].[hash].[ext]";
            }
            if (["png", "jpeg", "jpg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/img/[name].[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/font/[name].[hash].[ext]";
            }
            return "assets/other/[name].[hash].[ext]";
          },
          chunkFileNames: "assets/js/[name].[hash].js",
          entryFileNames: "assets/js/[name].[hash].js",
          manualChunks(id) {
            if (id.includes("node_modules")) {
              const moduleName = id.toString().split("node_modules/")[1].split("/")[0].toString();
              return `vendor/${moduleName}`;
            }
          }
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
