<template>
  <div class="p-2">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card style="min-height: 768px; max-height: 1060px;">
          <div style="margin-bottom: 10px">
            <el-button size="small" type="primary" style="margin-left: 10px;" @click="downloadPz">下载配置</el-button>
            <el-button size="small" type="primary" style="margin-left: 10px;" @click="doBatchSave">批量设置</el-button>
          </div>

          <el-form ref="gzlbpzFormRef"  label-width="80px">
            <el-form-item label="公证类别" prop="gzlbValue">
              <el-select v-model="gzlbValue" placeholder="请选择公证类别" style="width: 200px;" @change="handleChangeCategory">
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="公证事项" prop="selectId" style="width: 100%;">
              <el-input v-model="selectId" ></el-input>
            </el-form-item> -->
            <div style="min-height: 568px; max-height: 860px; overflow: hidden;">
              <Tree ref="gzsTreeRef" :selectId="selectId" :showCheckBox="false" @onSave="handleSave"
                @onClick="handleClickTree"></Tree>
            </div>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--其他配置-->
            <Qtpz v-if="gzsxId" ref="qtpzRef" :gzsxId="gzsxId" :gzsxCode="gzsxCode" :gzlbValue="gzlbValue" :selectId="selectId"></Qtpz>
          </el-col>
          <el-col :span="24">
            <!--角色配置-->
            <Jspz v-if="gzsxId" ref="jspzRef" :gzsxId="gzsxId" :gzsxCode="gzsxCode" :gzlbValue="gzlbValue" :selectId="selectId"></Jspz>
          </el-col>
          <el-col :span="24">
            <!--证据列表-->
            <Zjlb v-if="gzsxId" ref="zjlbRef" :gzsxId="gzsxId" :gzsxCode="gzsxCode" :gzlbValue="gzlbValue" :selectId="selectId"></Zjlb>
          </el-col>
          <el-col :span="24">
            <!--收费标准-->
            <Sfbz v-if="gzsxId" ref="sfbzRef" :gzsxId="gzsxId" :gzsxCode="gzsxCode" :gzlbValue="gzlbValue" :selectId="selectId"></Sfbz>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Gzsxpz" lang="ts">
  import Tree from '@/views/gongzheng/basicdata/gzsx/components/gzsx_tree2.vue';
  import Jspz from '@/views/gongzheng/basicdata/gzsx/components/jspz.vue';
  import Qtpz from '@/views/gongzheng/basicdata/gzsx/components/qtpz.vue';
  import Sfbz from '@/views/gongzheng/basicdata/gzsx/components/sfbz.vue';
  import Zjlb from '@/views/gongzheng/basicdata/gzsx/components/zjlb.vue';
  const gzsTreeRef = ref<InstanceType<typeof Tree> | null>(null);
  const qtpzRef = ref<InstanceType<typeof Qtpz> | null>(null);
  const jspzRef = ref<InstanceType<typeof Jspz> | null>(null);
  const zjlbRef = ref<InstanceType<typeof Zjlb> | null>(null);
  const sfbzRef = ref<InstanceType<typeof Sfbz> | null>(null);
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb } = toRefs<any>(proxy?.useDict('gz_gzlb'));
  const gzsxId = ref(null);
  const gzsxCode = ref(null);
  const gzlbValue = ref('1');
  const selectId = ref([]);
  const handleSave = (data) => {
    console.log("handleSave 数据：", data)
    if (data) {
      nextTick(() => {
        selectId.value = data.id;
        gzsxCode.value = data.code;
        gzlbValue.value = data.gzlbValue;
        qtpzRef.value?.setGzsxIds(data.id);
        jspzRef.value?.setGzsxIds(data.id);
        zjlbRef.value?.setGzsxIds(data.id);
        sfbzRef.value?.setGzsxIds(data.id);
      })
    }
  }
  const handleClickTree = (data) => {
    console.log("handleClickTree 选中：", data)
    if (data) {
      nextTick(() => {
        gzsxId.value = data.id;
        gzsxCode.value = data.code;
        gzlbValue.value = data.gzlbValue;
        qtpzRef.value?.init(gzsxCode.value, gzlbValue.value);
        jspzRef.value?.init(gzsxCode.value, gzlbValue.value);
        zjlbRef.value?.init(gzsxCode.value, gzlbValue.value);
        sfbzRef.value?.init(gzsxCode.value, gzlbValue.value);
      })
    }
  }
  const handleChangeCategory = (_value) => {
    //刷新 公证事项树
    console.log("handleChangeCategory", _value)
    gzsTreeRef.value?.getList(_value);
  }
  const downloadPz = () => {
    proxy?.$modal.msgError("开发中");
  }
  const doBatchSave = () => {
    proxy?.$modal.msgError("开发中");
  }
  onMounted(() => {
    gzsTreeRef.value?.getList(gzlbValue.value);
  });
</script>
