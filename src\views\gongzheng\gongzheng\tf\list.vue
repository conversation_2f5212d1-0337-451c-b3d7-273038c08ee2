<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px">
            <el-form-item label="卷宗号" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人">
              <el-input v-model="queryParams.jzbh" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="助理">
              <el-input v-model="queryParams.jzbh" placeholder="请输入助理" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员" prop="lb">
              <el-select v-model="queryParams.lb" placeholder="请选择公证员" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb">
              <el-select v-model="queryParams.lb" placeholder="请选择公证类别" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="公证书编号" prop="gzsbh">
              (<el-input v-model="queryParams.syd" placeholder="年份" clearable @keyup.enter="handleQuery"
                style="max-width: 60px" />)
              <el-select v-model="queryParams.syd" placeholder="请选择字号" clearable style="width: 140px;">
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
              第<el-input v-model="queryParams.gzsbh" placeholder="请输入" clearable @keyup.enter="handleQuery"
                style="width: 100px;" />号
            </el-form-item>

            <el-form-item label="受理日期" prop="slrq">
              <el-date-picker clearable style="width: 145px;" v-model="queryParams.slrq" type="date"
                value-format="YYYY-MM-DD" placeholder="请选择受理日期" />至 <el-date-picker style="width: 145px;" clearable
                v-model="queryParams.slrq" type="date" value-format="YYYY-MM-DD" placeholder="请选择受理日期" />
            </el-form-item>
            <el-form-item label="结算方式" prop="lzdd">
              <el-select v-model="queryParams.lb" placeholder="请选择结算方式" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="显示已退费" prop="lzdd">
              <el-checkbox></el-checkbox>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="gzjzJbxxList"
        @selection-change="handleSelectionChange"
        border
        stripe
        height="500">
        <el-table-column type="selection" width="60" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleGd">退费</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="jzbh" label="卷宗号" />
        <el-table-column prop="gdrxm" label="当事人" />
        <el-table-column prop="gzsbh" label="公证事项" />
        <el-table-column prop="gdrxm" label="公证员" />
        <el-table-column prop="gdrxm" label="助理/受理人" />
        <el-table-column prop="zlxm" label="助理/受理" />
        <el-table-column prop="slrq" label="受理日期" :formatter="formatDate" />
        <el-table-column prop="slrq" label="出证日期" :formatter="formatDate" />
        <el-table-column prop="slrq" label="流程状态" />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="tfList" lang="ts">
import { listGzjzJbxx, getGzjzJbxx, delGzjzJbxx, addGzjzJbxx, updateGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery, GzjzJbxxForm } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzs_zh, gz_ajly, gz_nf, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_sfmj, gz_rz_zt, gz_gzlb, gz_ywly } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_ajly', 'gz_ywly', 'gz_nf', 'gz_gzs_bh_jg', 'gz_gzlb', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_sfmj', 'gz_rz_zt'));

const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const direction = ref('rtl');
const queryFormRef = ref<ElFormInstance>();

const initFormData: GzjzJbxxForm = {
  id: undefined,
  jgbm: undefined,
  slbh: undefined,
  jzbh: undefined,
  gzsbh: undefined,
  gdrxm: undefined,
  gdrq: undefined,
  bgqx: undefined,
  bglx: undefined,
  lb: undefined,
  gzsx: undefined,
  sqr: undefined,
  lczt: undefined,
  gzybm: undefined,
  gzyxm: undefined,
  zlbm: undefined,
  zlxm: undefined,
  sldd: undefined,
  slrq: undefined,
  syd: undefined,
  rz: undefined,
  jjd: undefined,
  lzdd: undefined,
  lzrq: undefined,
  yt: undefined,
  ywwz: undefined,
  sfmj: undefined,
  dalx: undefined,
  remark: undefined,
  xbrbh: undefined,
  xbrxm: undefined,
  flxz: undefined,
  sfwyz: undefined,
  dplqdh: undefined,
  sfljc: undefined,
  sfdzqm: undefined,
  sfdzgzs: undefined,
  txFyyq: undefined,
  txZzyq: undefined,
  txFztx: undefined,
  txSftx: undefined
}

const data = reactive<PageData<GzjzJbxxForm, GzjzJbxxQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jgbm: undefined,
    slbh: undefined,
    jzbh: undefined,
    gzsbh: undefined,
    gdrxm: undefined,
    gdrq: undefined,
    bgqx: undefined,
    bglx: undefined,
    lb: undefined,
    gzsx: undefined,
    sqr: undefined,
    lczt: undefined,
    gzybm: undefined,
    gzyxm: undefined,
    zlbm: undefined,
    zlxm: undefined,
    sldd: undefined,
    slrq: undefined,
    syd: undefined,
    rz: undefined,
    jjd: undefined,
    lzdd: undefined,
    lzrq: undefined,
    yt: undefined,
    ywwz: undefined,
    sfmj: undefined,
    dalx: undefined,
    xbrbh: undefined,
    xbrxm: undefined,
    flxz: undefined,
    sfwyz: undefined,
    dplqdh: undefined,
    sfljc: undefined,
    sfdzqm: undefined,
    sfdzgzs: undefined,
    txFyyq: undefined,
    txZzyq: undefined,
    txFztx: undefined,
    txSftx: undefined,
    ljc: undefined,
    jzlc: undefined,
    createTime: undefined,
    params: {}
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-基本信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzJbxx(queryParams.value);
  gzjzJbxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzJbxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (cellValue) {
    return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
  }
  return '';
}

// 备注
const handleBz = () => {

}

// 归档
const handleGd = () => {

}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
