<template>
  <div>
    <el-form ref="queryFormRef" :model="queryParams" label-width="200px">
      <el-form-item label="客户名称" prop="bdcdjh">
        <el-input v-model="queryParams.bdcdjh" placeholder="请输入客户名称" clearable disabled/>
      </el-form-item>
      <el-form-item label="类型" prop="bdcSf">
        <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable disabled>
          <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="证件名称" prop="zt">
        <el-input v-model="queryParams.bdcdjh" placeholder="请输入证件名称" clearable  disabled/>
      </el-form-item>
      <el-form-item label="证件号码" prop="zt">
        <el-input v-model="queryParams.bdcdjh" placeholder="请输入证件号码" clearable  disabled/>
      </el-form-item>
      <el-form-item label="不诚信记录" prop="zt">
        <el-input type="textarea" v-model="queryParams.bdcdjh" placeholder="请输入不诚信记录" clearable />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
  import { listDsrxxHmdBdc, getDsrxxHmdBdc, delDsrxxHmdBdc, addDsrxxHmdBdc, updateDsrxxHmdBdc } from '@/api/gongzheng/dsr/dsrxxHmdBdc';
  import { DsrxxHmdBdcVO, DsrxxHmdBdcQuery, DsrxxHmdBdcForm } from '@/api/gongzheng/dsr/dsrxxHmdBdc/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_hmd_zt, gz_bdclx } = toRefs<any>(proxy?.useDict('gz_hmd_zt', 'gz_bdclx'));

  const initFormData : DsrxxHmdBdcForm = {
    id: undefined,
    bdcdjh: undefined,
    dz: undefined,
    bdcSf: undefined,
    bdcSq: undefined,
    bdcQ: undefined,
    zt: undefined,
    jgbs: undefined,
    jgbm: undefined,
    jgmc: undefined,
    chrq: undefined,
    gzsx: undefined,
    cqr: undefined,
    sqr: undefined,
    sqrsfz: undefined,
    qksm: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<DsrxxHmdBdcForm, DsrxxHmdBdcQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      bdcdjh: undefined,
      dz: undefined,
      bdcSf: undefined,
      bdcSq: undefined,
      bdcQ: undefined,
      zt: undefined,
      jgbs: undefined,
      jgbm: undefined,
      jgmc: undefined,
      chrq: undefined,
      gzsx: undefined,
      cqr: undefined,
      sqr: undefined,
      sqrsfz: undefined,
      qksm: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      bdcdjh: [
        { required: true, message: "不动产登记号不能为空", trigger: "blur" }
      ],
      dz: [
        { required: true, message: "不动产地址不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);
</script>

<style>
</style>
