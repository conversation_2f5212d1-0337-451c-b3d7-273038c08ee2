<template>
  <div>
    <!-- 详情对话框 -->
    <el-drawer
      :title="title"
      v-model="dialogVisible"
      :direction="direction"
      :before-close="handleCancel"
      size="100%">
      <SlDetail v-if="dialogVisible" :key="currentRecordId || 'detail'"></SlDetail>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="6" style="text-align: left;">
              <!-- <el-button @click="handleEvidenceMaterial">证据材料</el-button> -->
              <!-- <el-button @click="handleDeliveryInfo">送达信息</el-button>
              <el-button @click="handleReminderInfo">提醒信息</el-button>
              <el-button @click="handleShortInfo">短信预约</el-button>
              <el-button type="danger" @click="handleInvalidation">卷宗作废</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="handleDocumentDrafting">文档拟定</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="handleTranscript">笔录</el-button>
              <el-button :loading="buttonLoading" type="primary" @click="handleGhostwriting">代书</el-button> -->
            </el-col>
            <el-col :span="18" style="text-align: right;">
              <!-- 根据页面类型和流程状态显示不同的操作按钮 -->
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateAcceptance"
                v-if="pageType === 'sl' && lczt === '02'">
                发起受理
              </el-button>
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateReview"
                v-if="pageType === 'sl' && lczt === '03'">
                发起审查
              </el-button>
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateApproval"
                v-if="pageType === 'sl' && lczt === '04'">
                上报审批
              </el-button>
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateProduction"
                v-if="(pageType === 'sl' && lczt === '05') || (pageType === 'sp' && lczt === '05')">
                提交制证
              </el-button>
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateSigned"
                v-if="(pageType === 'sl' && lczt === '06') || (pageType === 'zz' && lczt === '07')">
                提交发证
              </el-button>
              <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleInitiateArchiving"
                v-if="(pageType === 'sl' && lczt === '07') || (pageType === 'fz' && lczt === '08')">
                提交归档
              </el-button>
              <!-- <el-button
                :loading="buttonLoading"
                type="primary"
                @click="handleDraftNotarization">
                拟定公证书
              </el-button> -->
              <el-button @click="handleEvidenceMaterial">证据材料</el-button>
              <el-button @click="handleEvidenceMaterial">日志</el-button>
              <el-button @click="handleCancel">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 证据材料对话框 -->
    <el-drawer
      :title="evidenceDialog.title"
      v-model="evidenceDialog.visible"
      :direction="direction"
      :before-close="cancelEvidenceDialog"
      size="100%">
      <ZjclAdd v-if="evidenceDialog.visible"></ZjclAdd>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="18" style="text-align: left;">
              <el-button @click="handleApplyInvestigation">申请调查核实</el-button>
              <el-button @click="handleMiniProgramDownload">小程序材料下载</el-button>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-button @click="cancelEvidenceDialog">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 送达信息对话框 -->
    <el-dialog v-model="deliveryDialog.visible" :title="deliveryDialog.title" width="60%" @close="cancelDeliveryDialog">
      <Sdxx v-if="deliveryDialog.visible"></Sdxx>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDeliveryForm">保存</el-button>
          <el-button @click="cancelDeliveryDialog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提醒消息对话框 -->
    <el-dialog v-model="reminderDialog.visible" :title="reminderDialog.title" width="40%" @close="cancelReminderDialog">
      <TxMsg v-if="reminderDialog.visible" ref="txMsgRef"></TxMsg>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReminderForm">保存</el-button>
          <el-button @click="cancelReminderDialog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 推送列表对话框 -->
    <el-dialog v-model="shortInfoDialog.visible" :title="shortInfoDialog.title" width="50%" @close="cancelShortInfoDialog">
      <Dxys v-if="shortInfoDialog.visible"></Dxys>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitShortInfoForm">保存</el-button>
          <el-button @click="cancelShortInfoDialog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文档拟定对话框 -->
    <el-drawer
      :title="documentDialog.title"
      v-model="documentDialog.visible"
      :direction="direction"
      :before-close="cancelDocumentDialog"
      size="100%">
      <Wdnd v-if="documentDialog.visible"></Wdnd>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="24" style="text-align: left;">
              <el-button @click="cancelDocumentDialog">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 笔录对话框 -->
    <el-drawer
      :title="transcriptDialog.title"
      v-model="transcriptDialog.visible"
      :direction="direction"
      :before-close="cancelTranscriptDialog"
      size="100%">
      <Bl v-if="transcriptDialog.visible"></Bl>
      <template #footer>
        <div class="">
          <el-row>
            <el-col :span="24" style="text-align: left;">
              <el-button @click="cancelTranscriptDialog">关 闭</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-drawer>

    <!-- 卷宗作废对话框 -->
    <el-dialog v-model="invalidationDialog.visible" :title="invalidationDialog.title" width="40%" @close="cancelInvalidationDialog">
      <el-form label-width="100px">
        <el-form-item label="作废原因" required>
          <el-input
            v-model="invalidationForm.zfyy"
            type="textarea"
            :rows="4"
            placeholder="请输入作废原因"
            maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitInvalidationForm" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelInvalidationDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { provide } from 'vue';
import SlDetail from '@/views/gongzheng/gongzheng/components/jz/detail.vue';
import ZjclAdd from '@/views/gongzheng/gongzheng/components/sl/zjcl/index.vue';
import Sdxx from '@/views/gongzheng/gongzheng/components/sl/sdxx.vue';
import TxMsg from '@/views/gongzheng/gongzheng/components/sl/tx_msg.vue';
import Dxys from '@/views/gongzheng/gongzheng/components/sl/dxys.vue';
import Wdnd from '@/views/gongzheng/gongzheng/components/sl/wdnd/index.vue';
import Bl from '@/views/gongzheng/gongzheng/components/sl/bl/index.vue';

// 对话框选项类型
interface DialogOption {
  visible: boolean;
  title: string;
}

interface Props {
  visible: boolean
  title: string
  currentRecordId: string | number | null
  lczt: string | number | null
  pageType?: string // 页面类型：sl-受理, sp-审批, zz-制证, fz-发证, gd-归档, sj-上架
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
  (e: 'evidence-material'): void
  (e: 'delivery-info'): void
  (e: 'reminder-info'): void
  (e: 'short-info'): void
  (e: 'invalidation', data: { id: string | number; zfyy: string }): void
  (e: 'document-drafting'): void
  (e: 'transcript'): void
  (e: 'ghostwriting'): void
  (e: 'draft-notarization'): void
  (e: 'initiate-acceptance'): void
  (e: 'initiate-review'): void
  (e: 'initiate-approval'): void
  (e: 'initiate-production'): void
  (e: 'initiate-signed'): void
  (e: 'initiate-archiving'): void
  (e: 'apply-investigation'): void
  (e: 'mini-program-download'): void
  (e: 'submit-delivery'): void
  (e: 'submit-reminder'): void
  (e: 'submit-short-info'): void
}

const props = withDefaults(defineProps<Props>(), {
  pageType: 'sl'
});

const emit = defineEmits<Emits>();

const buttonLoading = ref(false);
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');

// 主对话框状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 各个子对话框状态
const evidenceDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const deliveryDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const reminderDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const shortInfoDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const documentDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const transcriptDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const invalidationDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 作废原因表单
const invalidationForm = reactive({
  zfyy: ''
});

// 提供给子组件的数据
provide('currentRecordId', toRef(props, 'currentRecordId'));
provide('refreshList', () => emit('refresh'));

// 主对话框操作
const handleCancel = () => {
  dialogVisible.value = false;
};

// 证据材料功能
const handleEvidenceMaterial = () => {
  evidenceDialog.visible = true;
  evidenceDialog.title = "证据明细";
  emit('evidence-material');
};

const cancelEvidenceDialog = () => {
  evidenceDialog.visible = false;
};

const handleApplyInvestigation = () => {
  emit('apply-investigation');
};

const handleMiniProgramDownload = () => {
  emit('mini-program-download');
};

// 送达信息功能
const handleDeliveryInfo = () => {
  deliveryDialog.visible = true;
  deliveryDialog.title = "送达信息";
  emit('delivery-info');
};

const cancelDeliveryDialog = () => {
  deliveryDialog.visible = false;
};

const submitDeliveryForm = () => {
  emit('submit-delivery');
  cancelDeliveryDialog();
};

// 提醒信息功能
const handleReminderInfo = () => {
  if (!props.currentRecordId) {
    ElMessage.warning('请先选择要操作的卷宗');
    return;
  }
  reminderDialog.visible = true;
  reminderDialog.title = "提醒信息";
  emit('reminder-info');
};

const cancelReminderDialog = () => {
  reminderDialog.visible = false;
};

const txMsgRef = ref();
const submitReminderForm = async () => {
  if (txMsgRef.value) {
    const result = await txMsgRef.value.saveData();
    if (result) {
      cancelReminderDialog();
      emit('submit-reminder');
    }
  }
};

// 短信预约功能
const handleShortInfo = () => {
  shortInfoDialog.visible = true;
  shortInfoDialog.title = "推送列表";
  emit('short-info');
};

const cancelShortInfoDialog = () => {
  shortInfoDialog.visible = false;
};

const submitShortInfoForm = () => {
  emit('submit-short-info');
  cancelShortInfoDialog();
};

// 卷宗作废
const handleInvalidation = () => {
  if (!props.currentRecordId) {
    ElMessage.warning('请先选择要操作的卷宗');
    return;
  }

  // 重置表单
  invalidationForm.zfyy = '';
  invalidationDialog.visible = true;
  invalidationDialog.title = "卷宗作废";
};

const cancelInvalidationDialog = () => {
  invalidationDialog.visible = false;
};

const submitInvalidationForm = () => {
  if (!invalidationForm.zfyy.trim()) {
    ElMessage.warning('请输入作废原因');
    return;
  }

  if (!props.currentRecordId) {
    ElMessage.warning('未选择卷宗');
    return;
  }

  emit('invalidation', {
    id: props.currentRecordId,
    zfyy: invalidationForm.zfyy
  });

  cancelInvalidationDialog();
};

// 文档拟定
const handleDocumentDrafting = () => {
  documentDialog.visible = true;
  documentDialog.title = "文档拟定";
  emit('document-drafting');
};

const cancelDocumentDialog = () => {
  documentDialog.visible = false;
};

// 笔录
const handleTranscript = () => {
  transcriptDialog.visible = true;
  transcriptDialog.title = "笔录";
  emit('transcript');
};

const cancelTranscriptDialog = () => {
  transcriptDialog.visible = false;
};

// 代书
const handleGhostwriting = () => {
  transcriptDialog.visible = true;
  transcriptDialog.title = "代书";
  emit('ghostwriting');
};

// 拟定公证书
const handleDraftNotarization = () => {
  emit('draft-notarization');
};

// 流程操作按钮
const handleInitiateAcceptance = () => {
  emit('initiate-acceptance');
};

const handleInitiateReview = () => {
  emit('initiate-review');
};

const handleInitiateApproval = () => {
  emit('initiate-approval');
};

const handleInitiateProduction = () => {
  emit('initiate-production');
};

const handleInitiateSigned = () => {
  emit('initiate-signed');
};

const handleInitiateArchiving = () => {
  emit('initiate-archiving');
};

// 暴露方法给父组件
defineExpose({
  setButtonLoading: (loading: boolean) => {
    buttonLoading.value = loading;
  }
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
