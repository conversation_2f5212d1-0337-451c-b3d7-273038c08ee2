<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px" size="small">
            <el-form-item label="卷宗号" prop="jzbh" style="width: 260px;">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人" prop="dsrxm" style="width: 260px;">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm" style="width: 260px;">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable>
                <el-option
                  v-for="item in gzy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb" style="width: 260px;">
              <el-select v-model="queryParams.lb" placeholder="请选择公证类别" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable style="max-width: 80px; margin-right: 4px;" >
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable style="max-width: 140px; margin-right: 4px;" >
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery" style="max-width: 60px" />号
            </el-form-item>

            <el-form-item label="上架状态" prop="sjzt">
              <el-select v-model="queryParams.sjzt" placeholder="请选择" clearable style="width: 80px;">
                <el-option label="已上架" value="1" />
                <el-option label="未上架" value="0" />
              </el-select>
            </el-form-item>

            <el-form-item label="上架日期" prop="slrq">
              <el-date-picker clearable style="width: 145px;" v-model="dateRange[0]" type="date"
                value-format="YYYY-MM-DD" placeholder="请选择上架日期" />至 <el-date-picker style="width: 145px;" clearable
                v-model="dateRange[1]" type="date" value-format="YYYY-MM-DD" placeholder="请选择上架日期" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['gz:sj:query']">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="multiple" @click="handleBatchShelf"  v-hasPermi="['gz:sj:edit']">批量上架</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <GeneralProcessTable ref="generalProcessTableRef" :view-moudle="'fz'" :action-width="120" @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
        <template #my-actions="{ row }">

          <el-button type="primary" link @click="handleRowDblclick(row)"  v-hasPermi="['gz:sj:query']">详情</el-button>
        </template>
        <template #my-alerts="{ row }">
          <el-text type="danger">{{ row.ajtx ? `(${row.ajtx})` : '' }}</el-text>
        </template>
        <template #my-remarks="{ row }">
          <el-text v-hasPermi="['gz:sj:edit']" @click="() => handleBz(row)" :type="row.yqtxVo && row.yqtxVo.txFztx ? '' : 'primary'" style="cursor: pointer;">{{ row.yqtxVo ? row.yqtxVo.txFztx || '备注' : '备注' }}</el-text>
        </template>
      </GeneralProcessTable>
      <!-- <el-table
        v-loading="loading"
        :data="gzjzJbxxList"
        @selection-change="handleSelectionChange"
        @row-dblclick="handleRowDblclick"
        border
        stripe
        height="500">
        <el-table-column type="selection" width="60" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleShelf(scope.row)">上架</el-button>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template #default="scope">
            <el-button type="primary" link @click="handleBz">备注</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="jzbh" label="案件提醒" />
        <el-table-column prop="jzbh" label="卷宗号" />
        <el-table-column prop="sqr" label="申请人" />
        <el-table-column prop="dsrxm" label="当事人" />
        <el-table-column prop="gzsbh" label="公证书编号" />
        <el-table-column prop="gzyxm" label="公证员" />
        <el-table-column prop="zlxm" label="助理/受理" />
        <el-table-column prop="qmz" label="签名章" />
        <el-table-column prop="slrq" label="受理日期" :formatter="formatDate" />
        <el-table-column prop="czsj" label="出证日期" :formatter="formatDate" />
        <el-table-column prop="lczt" label="流程状态">
          <template #default="scope">
            <dict-tag :options="gz_sl_lczt" :value="scope.row.lczt" />
          </template>
        </el-table-column>
        <el-table-column prop="sprxm" label="审批人" />
        <el-table-column prop="sjzt" label="上架状态">
          <template #default="scope">
            <dict-tag :options="[{label: '未上架', value: '0'}, {label: '已上架', value: '1'}]" :value="scope.row.sjzt" />
          </template>
        </el-table-column>
        <el-table-column prop="sfzf" label="是否作废">
          <template #default="scope">
            <dict-tag :options="[{label: '否', value: '0'}, {label: '是', value: '1'}]" :value="scope.row.sfzf" />
          </template>
        </el-table-column>
        <el-table-column prop="wbddh" label="外部订单号" />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      /> -->
    </el-card>

    <!-- 详情对话框 - 使用共用组件 -->
    <DetailDrawer
      v-model:visible="dialog.visible"
      v-if="dialog.visible"
      :title="dialog.title"
      :current-record-id="currentRecordId"
      :lczt="lczt"
      page-type="sj"
      @refresh="getList"
      @evidence-material="handleEvidenceMaterial"
      @delivery-info="handleDeliveryInfo"
      @reminder-info="handleReminderInfo"
      @short-info="handleShortInfo"
      @invalidation="handleInvalidationSubmit"
      @document-drafting="handleWdnd"
      @transcript="handleBl"
      @ghostwriting="handleDs"
      @draft-notarization="handleNdgzs"
    />

    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />
  </div>
</template>

<script setup name="sjList" lang="ts">
import { provide } from 'vue';
import { listGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { invalidation } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import GeneralProcessTable from '@/views/gongzheng/gongzheng/components/GeneralProcessTable.vue';
import SlDetail from '@/views/gongzheng/gongzheng/components/jz/detail.vue';
import DetailDrawer from '@/views/gongzheng/gongzheng/components/sl/DetailDrawer.vue';
import { genYearOptions, clearEmptyProperty, dictMapFormat, genRecentDate } from '@/utils/ruoyi';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';

const gzsbh_years = genYearOptions(2015);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzs_zh, gz_sl_lczt, gz_gzlb } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_sl_lczt', 'gz_gzlb'));
const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

const generalProcessTableRef = ref<InstanceType<typeof GeneralProcessTable>>(null);

const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');
const dateRange = ref<[string, string] | null>([null, null]);

const currentRecordId = ref<string | number | null>(null);
const lczt = ref<string | number | null>(null);
const queryFormRef = ref<ElFormInstance>();

// 对话框状态管理
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 查询参数
const queryParams = ref<GzjzJbxxQuery>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  gzsbh: undefined,
  lb: undefined,
  dsrxm: undefined,
  gzyxm: undefined,
  lczt: '09', // 固定为上架状态
  syd: undefined,
  sjzt: undefined,
  params: {}
});

const jzDetailState = reactive({
  visible: false
})

/** 查询公证卷宗-基本信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginSjrq: dateRange.value[0],
        endSjrq: dateRange.value[1]
      };
    } else {
      delete queryParams.value.params?.beginSjrq;
      delete queryParams.value.params?.endSjrq;
    }
    generalProcessTableRef.value.getList(queryParams.value);
    // const res = await listGzjzJbxx(queryParams.value);
    // gzjzJbxxList.value = res.rows;
    // total.value = res.total;
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = [null, null];
  queryParams.value.params = {};
  handleQuery();
}

  /** 多选框选中数据 */
  const handleSelectionChange = (_ids : Array<number|string>, _single : boolean, _multiple: boolean) => {
    ids.value = _ids;
    single.value = _single;
    multiple.value = _multiple;
  };

/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (cellValue) {
    return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
  }
  return '';
}

// 上架
const handleShelf = (row?: GzjzJbxxVO) => {
  if (row?.id) {
    currentRecordId.value = row.id;
    lczt.value = row.lczt;
  }
  dialog.visible = true;
  dialog.title = "上架详情";
}

// 批量上架
const handleBatchShelf = () => {
  console.log('批量上架操作');
}

// 备注
const handleBz = () => {
  console.log('备注操作');
}

// 双击行查看详情
const handleRowDblclick = (row: GzjzJbxxVO) => {
  if (row?.id) {
    currentRecordId.value = row.id;
    lczt.value = row.lczt;
  }
  // dialog.visible = true;
  // dialog.title = "详情";
  jzDetailState.visible = true
}

/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  currentRecordId.value = null;
};

// 证据材料功能
const handleEvidenceMaterial = () => {
  console.log('证据材料');
};

// 送达信息功能
const handleDeliveryInfo = () => {
  console.log('送达信息');
};

// 提醒信息功能
const handleReminderInfo = () => {
  console.log('提醒信息');
};

// 短信预约功能
const handleShortInfo = () => {
  console.log('短信预约');
};

// 卷宗作废
const handleZf = () => {
  console.log('卷宗作废');
};

// 文档拟定
const handleWdnd = () => {
  console.log('文档拟定');
};

// 笔录
const handleBl = () => {
  console.log('笔录');
};

// 代书
const handleDs = () => {
  console.log('代书');
};

// 拟定公证书
const handleNdgzs = () => {
  console.log('拟定公证书');
};

// 处理共用组件的作废提交
const handleInvalidationSubmit = async (data: { id: string | number; zfyy: string }) => {
  buttonLoading.value = true;
  try {
    const res = await invalidation(data);
    if (res.code === 200) {
      ElMessage.success('卷宗作废成功');
      getList(); // 刷新列表
    } else {
      ElMessage.error('卷宗作废失败：' + (res.msg || '未知错误'));
    }
  } catch (error: any) {
    console.error('卷宗作废失败:', error);
    ElMessage.error('卷宗作废失败: ' + (error?.message || '未知错误'));
  } finally {
    buttonLoading.value = false;
  }
};

// 提供给子组件的数据和方法
provide('currentRecordId', currentRecordId);
provide('refreshList', getList);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
