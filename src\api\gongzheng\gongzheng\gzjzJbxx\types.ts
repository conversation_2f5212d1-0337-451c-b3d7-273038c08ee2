export interface GzjzJbxxVO {
  /**
   * $column.columnComment
   */
  id : string | number;

  /**
   * 机构编码
   */
  jgbm : string;

  /**
   * 受理编号
   */
  slbh : string;

  /**
   * 卷宗编号
   */
  jzbh : string;

  /**
   * 公证书编号（多个）
   */
  gzsbh : string;

  /**
   * 归档人姓名
   */
  gdrxm : string;

  /**
   * 归档日期
   */
  gdrq : string;

  /**
   * 保管/档案期限
   */
  bgqx : string;

  /**
   * 保管类型
   */
  bglx : string;

  /**
   * 公证类别
   */
  lb : string;

  /**
   * 公证事项（事务树）
   */
  gzsx : string;

  /**
   * 流程状态
   */
  lczt : string;

  /**
   * 公证员ID
   */
  gzybm : string;

  /**
   * 公证员姓名
   */
  gzyxm : string;

  /**
   * 助理ID
   */
  zlbm : string;

  /**
   * 助理姓名
   */
  zlxm : string;

  /**
   * 受理地点
   */
  sldd : string;

  /**
   * 受理日期
   */
  slrq : string;

  /**
   * 申请日期
   */
  sqsj : string;

  /**
   * 使用地
   */
  syd : string;

  /**
   * 是否认证
   */
  rz : string;

  /**
   * 紧急度
   */
  jjd : string;

  /**
   * 领证地点
   */
  lzdd : string;

  /**
   * 领证日期
   */
  lzrq : string;

  /**
   * 用途
   */
  yt : string;

  /**
   * 译文文种
   */
  ywwz : string;

  /**
   * 是否密卷
   */
  sfmj : string;

  /**
   * 档案类型
   */
  dalx : string;

  /**
   * 备注
   */
  remark : string;

  /**
   * 协办人ID
   */
  xbrbh : string;

  /**
   * 协办人姓名
   */
  xbrxm : string;

  /**
   * 法律援助
   */
  flxz : string;

  /**
   * 是否外译中
   */
  sfwyz : string;

  /**
   * 电票领取电话
   */
  dplqdh : string;

  /**
   * 是否零接触
   */
  sfljc : string;

  /**
   * 是否电子签名
   */
  sfdzqm : string;

  /**
   * 是否电子公证书
   */
  sfdzgzs : string;

  /**
   * 归档人ID
   */
  gdrbh : string;

  /**
   * 翻译人ID
   */
  fyrbh : string;

  /**
   * 翻译人姓名
   */
  fyrxm : string;

  /**
   * 外部订单号
   */
  wbddh : string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx : string;

  /**
   * 制证时间
   */
  zzsj : string;

  /**
   * 出证时间
   */
  czsj : string;

  /**
   * 审批人ID
   */
  sprbm : string;

  /**
   * 审批人姓名
   */
  sprxm : string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm : string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm : string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm : string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm : string;

  /**
   * 是否作废
   */
  sfzf : string;

  /**
   * 签名章
   */
  qmz : string;

  /**
   * 结案方式
   */
  jyfs : string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt : string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt : string;

  /**
   * 档案编号
   */
  dabh : string;

  /**
   * 公证要求和提醒
   */
  yqtxVo : any;
  /**
   * 公证事项
   */
  gzsxMc : string;

  [k: string]: any;
}

export interface GzjzJbxxForm extends BaseEntity {
  /**
   * $column.columnComment
   */
  id ?: string | number;

  /**
   * 机构编码
   */
  jgbm ?: string;

  /**
   * 受理编号
   */
  slbh ?: string;

  /**
   * 卷宗编号
   */
  jzbh ?: string;

  /**
   * 公证书编号（多个）
   */
  gzsbh ?: string;

  /**
   * 归档人姓名
   */
  gdrxm ?: string;

  /**
   * 归档日期
   */
  gdrq ?: string;

  /**
   * 保管/档案期限
   */
  bgqx ?: string;

  /**
   * 保管类型
   */
  bglx ?: string;

  /**
   * 公证类别
   */
  lb ?: string;

  /**
   * 公证事项（事务树）
   */
  gzsx ?: string;

  /**
   * 申请人
   */
  sqr ?: string;

  /**
   * 流程状态
   */
  lczt ?: string;

  /**
   * 公证员ID
   */
  gzybm ?: string;

  /**
   * 公证员姓名
   */
  gzyxm ?: string;

  /**
   * 助理ID
   */
  zlbm ?: string;

  /**
   * 助理姓名
   */
  zlxm ?: string;

  /**
   * 受理地点
   */
  sldd ?: string;

  /**
   * 受理日期
   */
  slrq ?: string;

  /**
   * 使用地
   */
  syd ?: string;

  /**
   * 是否认证
   */
  rz ?: string;

  /**
   * 紧急度
   */
  jjd ?: string;

  /**
   * 领证地点
   */
  lzdd ?: string;

  /**
   * 领证日期
   */
  lzrq ?: string;

  /**
   * 用途
   */
  yt ?: string;

  /**
   * 译文文种
   */
  ywwz ?: string;

  /**
   * 是否密卷
   */
  sfmj ?: string;

  /**
   * 档案类型
   */
  dalx ?: string;

  /**
   * 备注
   */
  remark ?: string;

  /**
   * 协办人ID
   */
  xbrbh ?: string;
  xbrbhArray ?: Array<string>;

  /**
   * 协办人姓名
   */
  xbrxm ?: string;

  /**
   * 法律援助
   */
  flxz ?: string;

  /**
   * 是否外译中
   */
  sfwyz ?: string;

  /**
   * 电票领取电话
   */
  dplqdh ?: string;

  /**
   * 是否零接触
   */
  sfljc ?: string;

  /**
   * 是否电子签名
   */
  sfdzqm ?: string;

  /**
   * 是否电子公证书
   */
  sfdzgzs ?: string;

  /**
   * 归档人ID
   */
  gdrbh ?: string;

  /**
   * 翻译人ID
   */
  fyrbh ?: string;

  /**
   * 翻译人姓名
   */
  fyrxm ?: string;

  /**
   * 外部订单号
   */
  wbddh ?: string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx ?: string;

  /**
   * 制证时间
   */
  zzsj ?: string;

  /**
   * 出证时间
   */
  czsj ?: string;

  /**
   * 审批人ID
   */
  sprbm ?: string;

  /**
   * 审批人姓名
   */
  sprxm ?: string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm ?: string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm ?: string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm ?: string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm ?: string;

  /**
   * 是否作废
   */
  sfzf ?: string;

  /**
   * 签名章
   */
  qmz ?: string;

  /**
   * 结案方式
   */
  jyfs ?: string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt ?: string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt ?: string;

  /**
   * 档案编号
   */
  dabh ?: string;

  /**
   * 申请时间
   */
  sqsj ?: string;

}

export interface GzjzJbxxQuery extends PageQuery {

  /**
   * 机构编码
   */
  jgbm ?: string;

  /**
   * 受理编号
   */
  slbh ?: string;

  /**
   * 卷宗编号
   */
  jzbh ?: string;

  /**
   * 公证书编号（多个）
   */
  gzsbh ?: string;

  /**
   * 归档人姓名
   */
  gdrxm ?: string;

  /**
   * 归档日期
   */
  gdrq ?: string;

  /**
   * 保管/档案期限
   */
  bgqx ?: string;

  /**
   * 保管类型
   */
  bglx ?: string;

  /**
   * 公证类别
   */
  lb ?: string;

  /**
   * 公证事项（事务树）
   */
  gzsx ?: string;

  /**
   * 申请人
   */
  sqr ?: string;

  /**
   * 流程状态
   */
  lczt ?: Array<string> | string;

  /**
   * 公证员ID
   */
  gzybm ?: string;

  /**
   * 公证员姓名
   */
  gzyxm ?: string;

  /**
   * 助理ID
   */
  zlbm ?: string;

  /**
   * 助理姓名
   */
  zlxm ?: string;

  /**
   * 受理地点
   */
  sldd ?: string;

  /**
   * 受理日期
   */
  slrq ?: string;

  /**
   * 使用地
   */
  syd ?: string;

  /**
   * 是否认证
   */
  rz ?: string;

  /**
   * 紧急度
   */
  jjd ?: string;

  /**
   * 领证地点
   */
  lzdd ?: string;

  /**
   * 领证日期
   */
  lzrq ?: string;

  /**
   * 用途
   */
  yt ?: string;

  /**
   * 译文文种
   */
  ywwz ?: string;

  /**
   * 是否密卷
   */
  sfmj ?: string;

  /**
   * 档案类型
   */
  dalx ?: string;

  /**
   * 协办人ID
   */
  xbrbh ?: string;

  /**
   * 协办人姓名
   */
  xbrxm ?: string;

  /**
   * 法律援助
   */
  flxz ?: string;

  /**
   * 是否外译中
   */
  sfwyz ?: string;

  /**
   * 电票领取电话
   */
  dplqdh ?: string;

  /**
   * 是否零接触
   */
  sfljc ?: string;

  /**
   * 是否电子签名
   */
  sfdzqm ?: string;

  /**
   * 是否电子公证书
   */
  sfdzgzs ?: string;

  /**
   * 归档人ID
   */
  gdrbh ?: string;

  /**
   * 翻译人ID
   */
  fyrbh ?: string;

  /**
   * 翻译人姓名
   */
  fyrxm ?: string;

  /**
   * 外部订单号
   */
  wbddh ?: string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx ?: string;

  /**
   * 制证时间
   */
  zzsj ?: string;

  /**
   * 出证时间
   */
  czsj ?: string;

  /**
   * 审批人ID
   */
  sprbm ?: string;

  /**
   * 审批人姓名
   */
  sprxm ?: string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm ?: string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm ?: string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm ?: string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm ?: string;

  /**
   * 是否作废
   */
  sfzf ?: string;

  /**
   * 签名章
   */
  qmz ?: string;

  /**
   * 结案方式
   */
  jyfs ?: string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt ?: string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt ?: string;

  /**
   * 档案编号
   */
  dabh ?: string;

  /**
* 申请时间
*/
  sqsj ?: string;

  /**
   * 审批时间
   */
  spsj ?: string;

  /**
   * 业务来源
   */
  ywly ?: string;

  /**
   * 案件来源
   */
  ajly ?: string;

  /**
   * 是否零接触
   */
  ljc ?: any;

  /**
   * 卷宗流程
   */
  jzlc ?: any;

  /**
   * 创建时间
   */
  createTime ?: string;

  /**
   * 路由/权限代码：【模块名:菜单名】 如 业务受理-审批 ： gz:sp ； 办证辅助-咨询 ： bzfz:zx
   */
  routeCode ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
  gzsNf?:string;
  gzsxMc?:string;
  gzsZh?:string;
  gzsLs?:string;
}

export interface GzjzJbxxQueryByDsr extends PageQuery {
  dsrId: string | number;

  jzbh?: string;

  params?: {
    gzsNf?: string;
    gzsZh?: string;
    gzsLs?: string;
  };

  sfzf?: string;  //是否作废
}

export interface CaiWuVO extends GzjzJbxxVO {
  // 收费日期
  sfrq ?: string;
  // 开票日期
  fprq ?: string;
  // 收费状态（1未收费，2已完成）
  sfsf ?: string;
  // 收费来源
  sfly ?: string;
  // 收费人ID
  sfr ?: string | number;
}

export interface CaiWuForm extends GzjzJbxxForm {
  // 收费日期
  sfrq ?: string;
  // 开票日期
  fprq ?: string;
  // 收费状态（1未收费，2已完成）
  sfsf ?: string;
  // 收费来源
  sfly ?: string;
  // 收费人ID
  sfr ?: string | number;
}

export interface CaiWuQuery extends GzjzJbxxQuery {
  // 收费日期
  sfrq ?: string;
  // 开票日期
  fprq ?: string;
  // 收费状态（1未收费，2已完成）
  sfsf ?: string;
  // 收费来源
  sfly ?: string;
  // 收费人ID
  sfr ?: string | number;
}

export interface ApprovalType {
  id : string | number;
  sftg : string;  // 1通过，0驳回
  spsj ?: string | Date;
  zzsj ?: string | Date;
  lcyj : string;   // 流程意见
  lczt ?: string;  // 驳回流程节点 03受理，04审查
}

export interface CertifiCationType {
  id : string | number;  //卷宗ID
  sftg : string;   //1通过，0驳回
  lcyj : string;   //流程意见
}

export interface ChangeProcessBo {
  id : string | number;  //卷宗ID
  origLczt : string;   //1通过，0驳回
  lczt : string;   //流程意见
}
export interface ChangeGzsbmBo {
  gzjzId : string | number;  //卷宗ID
  gzjzGzsxId : string | number; //公证事项ID
  origId : number;//公证书编号ID
  gzsbh : string;   //公证书编号
  nf : string;   //年份
  zhCode : string;//字典
  ls : number;//流水号
}
export interface ChangeCzsjBo {
  id : string | number;  //卷宗ID
  origCzsj : string ; //原出证时间
  czsj : string;//新出证时间

}
