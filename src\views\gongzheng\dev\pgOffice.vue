<template>
  <div>
    <el-button @click="handledOpen">打开</el-button>
    <PageOffice v-if="openShow" ref="pageOfficeRef" :title="title" :url="url"></PageOffice>
  </div>
</template>

<script setup lang="ts">
  import PageOffice from '@/components/pageOffice/index.vue';
  const pageOfficeRef = ref<InstanceType<typeof PageOffice> | null>(null);
  const openShow = ref(false);
  const title = ref(null);
  const url = ref(null);
  const handledOpen = () => {
    title.value = "测试文档";
    url.value = "C:\\Users\\<USER>\\Desktop\\临时文件\\解除协议.docx";
    openShow.value = true;
    const str = {
      title: title.value,
      url: url.value
    };
    pageOfficeRef.value?.open_pageoffice(str);
  }
</script>

<style>
</style>
