<template>
  <el-card>
    <template #header>
      <strong class="text-base">流程日志</strong>
    </template>
    <el-table :data="processLogList" v-loading="loading" border stripe>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column label="流程日志" align="center" min-width="200">
        <template #default="{ row }">
          <div class="process-log">
            <!-- <span>{{ row.czschj || '未知环节' }}</span>
            <span class="arrow">———></span> -->
            <span>{{ formatProcessAction(row) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="lcyj" label="流程意见" align="center" />
      <el-table-column prop="czrxm" label="审批人" align="center" />
      <el-table-column label="是否通过" align="center">
        <template #default="{ row }">
          {{ getApprovalStatus(row.sftg) }}
        </template>
      </el-table-column>
      <el-table-column prop="czschj" label="流程状态" align="center" />
      <el-table-column prop="czrq" label="日期" align="center" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.czrq) }}
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { listGzrzLcrzxx } from '@/api/gongzheng/gongzheng/gzrzLcrzxx'
import { GzrzLcrzxxQuery, GzrzLcrzxxVO } from '@/api/gongzheng/gongzheng/gzrzLcrzxx/types'
import { ref, inject, onMounted } from 'vue'

interface Props {
  gzjzId?: string
}

const props = defineProps<Props>()

// 获取当前查看的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const loading = ref(false)
const processLogList = ref([])

// 获取流程日志列表
const getProcessLogList = async () => {
  if (!props.gzjzId && !currentRecordId.value) return

  loading.value = true
  try {
    const query: GzrzLcrzxxQuery = {
      gzjzId: props.gzjzId || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzrzLcrzxx(query)
    if (res.code === 200) {
      processLogList.value = res.rows || []
    } else {
      ElMessage.error('获取流程日志失败：' + (res.msg || '未知错误'))
      processLogList.value = []
    }
  } catch (error: any) {
    console.error('获取流程日志失败:', error)
    ElMessage.error('获取流程日志失败: ' + (error?.message || '未知错误'))
    processLogList.value = []
  } finally {
    loading.value = false
  }
}

// 格式化流程操作内容
const formatProcessAction = (row: GzrzLcrzxxVO) => {
  if (row.rznr) {
    return row.rznr
  }
  return `${row.czschj || '操作'} 操作人(${row.czrxm || '未知'})`
}

// 获取审批状态
const getApprovalStatus = (sftg: string) => {
  if (!sftg) return '未知'

  if (sftg === '1') {
    return '通过'
  } else if (sftg === '0') {
    return '不通过'
  }
  return '待定'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateStr
  }
}

onMounted(() => {
  getProcessLogList();
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>