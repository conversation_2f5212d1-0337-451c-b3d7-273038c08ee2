<template>
  <div class="notary-matter-form">
    <div v-if="props.mode === 'add'" class="filter-wrap">
      <div class="filter-header">
        <el-input v-model="filterText" placeholder="请输入公证事项名称" clearable style="width: 240px;" @keyup.enter="handleSearch" />
        <el-button @click="handleSearch" type="primary">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
      <div class="filter-body">
        <div v-loading="treeLoading" class="filter-tree">
          <el-tree
            ref="gzsxTreeRef"
            :data="gzsxTreeOptions"
            :props="{
              label: 'title',
              children: 'children',
              disabled: 'disabled'
            }"
            node-key="id"
            :default-expanded-keys="treeExpanded"
            :default-checked-keys="treeSelected"
            show-checkbox
            check-strictly
            multiple
            :render-after-expand="false"
            :filter-node-method="filterGzsxNodes"
            @check-change="gzsxNodeCheckChange"
          />
        </div>
      </div>
    </div>
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" inline>
      <el-form-item label="公证事项" v-if="props.mode === 'edit'" prop="gzsxId">
        <el-tree-select
          v-model="formData.gzsxId"
          :data="gzsxTreeOptions"
          placeholder="请选择公证事项"
          filterable
          clearable
          disabled
          show-checkbox
          check-strictly
          collapse-tags
          collapse-tags-tooltip
          :render-after-expand="false"
          :filter-node-method="filterGzsxNode"
          @change="handleGzsxChange"
          style="width: 350px"
          node-key="id"
          :props="{
            value: 'id',
            label: 'title',
            children: 'children',
            disabled: 'disabled'
          }"
        />
      </el-form-item>

      <el-form-item label="公证书份数：" prop="gzsFs">
        <el-input-number v-model="formData.gzsFs" :min="1" :max="999" placeholder="请输入份数" style="width: 120px" />
      </el-form-item>
      <el-form-item label="公证件数：" prop="gzjs" v-if="props.mode === 'add'">
        <el-input-number v-model="formData.gzjs" :min="1" :max="999" placeholder="请输入公证件数" style="width: 120px" />
      </el-form-item>
      <el-form-item label="取双号：" prop="sfsh">
        <el-switch
          v-model="formData.sfsh"
          inline-prompt
          active-text="是"
          inactive-text="否"
          active-value="1"
          inactive-value="0"
          :disabled="disabledQsh"
        />
      </el-form-item>
      <el-form-item label="公益类服务：" prop="gylfw">
        <el-select v-model="formData.gylfw" placeholder="请选择" style="width: 200px">
          <el-option v-for="op in gz_gylfw" :key="op.value" :label="op.label" :value="op.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="新型公证业务：" prop="xxgzyw">
        <el-select v-model="formData.xxgzyw" placeholder="请选择" style="width: 200px">
          <el-option v-for="op in gz_xxgz" :key="op.value" :label="op.label" :value="op.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="涉疫情/灾情公证：" prop="syqzqgz">
        <el-select v-model="formData.syqzqgz" placeholder="无" style="width: 200px">
          <el-option v-for="op in gz_sysz" :key="op.value" :label="op.label" :value="op.value" />
        </el-select>
      </el-form-item>
        <el-col :span="24">
          <el-form-item label="服务类型：">
            <div class="service-type-list">
              <span class="s-type-i">
                <el-text>
                  上门办证
                </el-text>
                <el-switch
                  v-model="formData.sfsmbz"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                />
              </span>
              <span class="s-type-i">
                <el-text>
                  最多跑一次
                </el-text>
                <el-switch
                  v-model="formData.sfzdpyc"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                />
              </span>
              <span class="s-type-i">
                <el-text>
                  一带一路建设
                </el-text>
                <el-switch
                  v-model="formData.sfydyljs"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                />
              </span>
              <span class="s-type-i">
                <el-text>
                  在线受理
                </el-text>
                <el-switch
                  v-model="formData.sfzxsl"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                />
              </span>
              <span class="s-type-i">
                <el-text>
                  无接触办证
                </el-text>
                <el-switch
                  v-model="formData.sfwjcbz"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                />
              </span>
            </div>
          </el-form-item>
        </el-col>

      <!-- <el-form-item label="是否备案" prop="sfBa">
        <el-select v-model="formData.sfBa" placeholder="请选择" clearable style="width: 100px">
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注" style="width: 360px" />
      </el-form-item> -->
      <template v-if="props.mode === 'edit'">
        <el-form-item v-for="dc in formData.gxrJsonObject" :key="dc.valKey" :label="dc.label+'：'" :prop="dc.valKey">
          <el-date-picker v-if="dc.type === 'el-date'" v-model="dc.value" style="width: 200px" />
          <el-input v-else-if="dc.type === 'el-input'" v-model="dc.value" :placeholder="(dc.value || '请输入') + ''" style="width: 200px" />
          <el-select v-else-if="dc.type === 'el-select'" v-model="dc.value" clearable style="width: 200px">
            <el-option v-for="op in gzsxDsrList" :key="op.dsrId" :label="op.name" :value="op.name" />
          </el-select>
        </el-form-item>
      </template>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">
        {{ props.mode === 'add' ? '新增' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, inject, computed, onMounted, h } from 'vue'
import { ElMessage, ElSelect, ElOption, ElInput, ElDatePicker } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { Ref } from 'vue'
import type { GxrJsonObject, GzjzGzsxForm, GzjzGzsxQuery, GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
import { listTree } from '@/api/gongzheng/basicdata/gzsx'
import { GzsxVO } from '@/api/gongzheng/basicdata/gzsx/types'
import { listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx'
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr'
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
import { nodeFilter } from '@/utils/ruoyi'
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
import { listAreaname } from '@/api/gongzheng/basicdata/areaname'

interface Props {
  formData: GzjzGzsxForm;
  mode: 'add' | 'edit' | 'view';
  saving: boolean;
}

interface Emits {
  (e: 'save', data: GzjzGzsxForm): void
  (e: 'cancel'): void
}

const dynamicComps = {
  'el-select': ElSelect,
  'el-input': ElInput,
  'el-date': ElDatePicker
}

const testDynamicInputs = ref([
  {
    id: '123',
    label: '继承人',
    valKey: 'jsr',
    type: 'ElSelect',
  },
  {
    id: '456',
    label: '被继承人',
    valKey: 'bjsr',
    type: 'ElSelect',
  },
  {
    id: '789',
    label: '放弃继承人',
    valKey: 'fjsr',
    type: 'ElSelect',
  },
  {
    id: '101112',
    label: '代理人',
    valKey: 'dlr',
    type: 'ElSelect',
  },
  {
    id: '131415',
    label: '死亡日期',
    valKey: 'swrq',
    type: 'ElDatePicker',
  },
  {
    id: '161718',
    label: '死亡地点',
    valKey: 'swdd',
    type: 'ElInput'
  }
])

const props = defineProps<Props>()
const emit = defineEmits<Emits>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gylfw, gz_xxgz, gz_sysz } = toRefs<any>(proxy?.useDict('gz_gylfw', 'gz_xxgz', 'gz_sysz'));

// 获取卷宗ID
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));
const gzlbId = inject<Ref<string | number | null>>('gzlbId', ref(null))

const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 表单数据
const formData = reactive<GzjzGzsxForm>({
  gzsxId: '',
  gzjzId: currentRecordId.value || '',
  gzsxMc: '',
  gxrMc: '',
  gzsBh: '',
  gzsMc: '',
  gylfw: '1',
  xxgzyw: '1',
  gzsFs: 1,
  gzjs: 1,
  sfBa: '0',
  remark: '',
  gxrJson: '',
  gxrJsonObject: [],
})

const disabledQsh = ref(false)

// 公证事项树过滤查询内容
const filterText = ref('');
// 公证事项树组件实例
const gzsxTreeRef = ref();
const treeExpanded = ref([]);
const treeSelected = ref([]);
const treeSelectedNodes = ref([]);

// 公证事项树形选项
const gzsxTreeOptions = ref<any[]>([]);
const treeLoading = ref(false);
const gzsxDsrList = ref<GzjzDsrVO[]>([]);

interface TreeOption extends GzsxVO {
  // id?: string;
  // ancestors?: any;
  childCount?: number;
  // code?: string;
  // jcsx?: string;
  // level?: number;
  // notarLevel?: number;
  // parentCode?: string;
  // parentId?: string;
  // remark?: string;
  // temptreeCode?: string;
  // title?: string;
  disabled?: boolean; // 是否禁用
}

function buildTreeData<T>(data: Array<TreeOption>, suorce: Array<TreeOption>) {
  let treeData: Array<TreeOption> = [];
  let m: Array<TreeOption> = [], nm: Array<TreeOption> = [];
  treeData = data.map((item) => {
    const { matches, noMatches } = nodeFilter(suorce, (node: TreeOption) => node.parentCode === item.code);
    if(matches.length > 0) {
      m = matches; nm = noMatches;
      item.children = buildTreeData(matches, noMatches);
      item.disabled = true;
    }
    return item;
  });
  return treeData.sort((a, b) => b.childCount - a.childCount);
}

// 加载公证事项树形数据
const loadGzsxTree = async () => {
  try {
    treeLoading.value = true;
    const res = await listTree()
    //const data = proxy?.handleTreeCode<GzsxOptionsType>(res.rows, 'code');
    let root: Array<TreeOption> = [];
    if (res && res.data) {
      const { matches, noMatches } = nodeFilter(res.data, (item: TreeOption) => !item.parentCode);
      root = buildTreeData(matches, noMatches);
      treeExpanded.value = root.map(item => item.id);
      // console.log('root >>>>> ', root)
      gzsxTreeOptions.value = root
      // gzsxTreeOptions.value = res.data
    }
  } catch (error) {
    console.error('获取公证事项树形数据失败:', error)
    ElMessage.warning('获取公证事项数据失败，请稍后重试')
    // 提供备用数据
    gzsxTreeOptions.value = [
      { id: 1, gzsxMc: '委托书公证', children: [] },
      { id: 2, gzsxMc: '声明书公证', children: [] },
      { id: 3, gzsxMc: '合同公证', children: [] },
      { id: 4, gzsxMc: '遗嘱公证', children: [] },
      { id: 5, gzsxMc: '继承公证', children: [] }
    ]
  } finally {
    treeLoading.value = false
  }
}

// 过滤公证事项节点
const filterGzsxNode = (value: string, data: any) => {
  if (!value) return true
  return data.gzsxMc && data.gzsxMc.includes(value)
}

const filterGzsxNodes = (value: string, data: TreeOption) => {
  if (!value) return true;
  return data.title.includes(value);
}

const gzsxNodeCheckChange = (data: any, checked: boolean, hasCheckedNodes: any) => {
  console.log('gzsxNodeCheckChange >>>>> ', data, checked, hasCheckedNodes)
  if(checked) {
    treeSelected.value.push(data.id)
    treeSelectedNodes.value.push(data)
  } else {
    const index = treeSelected.value.indexOf(data.id)
    if(index > -1) {
      treeSelected.value.splice(index, 1)
      treeSelectedNodes.value.splice(index, 1)
    }
  }
}

const handleSearch = () => {
  gzsxTreeRef.value.filter(filterText.value);
}

const handleReset = () => {
  filterText.value = '';
  gzsxTreeRef.value.filter('');
}

const getDsrList = async () => {
  if (!currentRecordId.value) return;
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(queryParams);
    if (res && res.code === 200) {
      gzsxDsrList.value = res.rows || [];
    }

  } catch (err: any) {
    console.error('获取当事人列表失败:', err);
  }

}

// 表单验证规则
const rules = reactive<FormRules<GzjzGzsxForm>>({
  gzsxId: [
    { required: true, message: '请选择公证事项', trigger: 'change' }
  ],
  gzjzId: [
    { required: true, message: '公证卷宗ID不能为空', trigger: 'blur' }
  ],
  gzsFs: [
    { required: true, message: '请输入份数', trigger: 'blur' }
  ],
  gzjs: [
    { required: true, message: '请输入公证件数', trigger: 'blur' }
  ],
  gylfw: [
    { required: true, message: '公益类服务不能为空', trigger: 'change' }
  ],
  xxgzyw: [
    { required: true, message: '公证业务不能为空', trigger: 'change' }
  ]
})

// 监听传入的表单数据变化
watch(() => props.formData, (newData) => {
  Object.assign(formData, newData)
}, { immediate: true, deep: true })

// 监听卷宗ID变化
watch(currentRecordId, (newId) => {
  if (newId) {
    formData.gzjzId = newId
  }
}, { immediate: true })

watch(() => props.saving, (newVal) => {
  submitLoading.value = newVal
})

// 是否只读模式
const isReadonly = computed(() => props.mode === 'view')

// 公证事项选择变化
const handleGzsxChange = (value: string | number) => {
  // 递归查找树形数据中的节点
  const findNodeById = (nodes: any[], id: string | number): any => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, id)
        if (found) return found
      }
    }
    return null
  }

  const selectedNode = findNodeById(gzsxTreeOptions.value, value)
  if (selectedNode) {
    formData.gzsxMc = selectedNode.gzsxMc
  }
}

const booleanToString = <T>(obj: T, keys: string[], to: string = '1,0') => {
  const [trueStr, falseStr] = to.split(',')
  keys.forEach(key => {
    if (obj[key]) {
      obj[key] = trueStr
    } else {
      obj[key] = falseStr
    }
  });
  return obj
}

// 提交表单
const handleSubmit = async () => {
  if (isReadonly.value) return

  try {
    const valid = await formRef.value?.validate()
    if (!valid) return;
    if (props.mode === 'add' && treeSelected.value.length === 0) {
      ElMessage.warning('请选择公证事项');
      return;
    }

    if (!formData.gzjzId) {
      ElMessage.error('请先保存基本信息，获取卷宗ID后再添加公证事项')
      return
    }

    // submitLoading.value = true
    const data = booleanToString({ ...formData, gzsxIds: treeSelected.value, gzlbValue: gzlbId.value },
    ['sfsh', 'sfsmbz', 'sfzdpyc', 'sfydyljs', 'sfzxsl', 'sfwjcbz']);

    emit('save', data)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    // submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 检查取双号
const checkQsh = async () => {
  try {
    const res = await listAreaname({ pageSize: 300, pageNum: 1});
    const areas = res.rows;
    const { lb, syd } = curGzjz.value;
    const sydInfo = areas.find(i => i.numberCode == syd);

    
    if(sydInfo.evenNumbersStatus == '1' && (lb == '3' || lb == '4')) {
      formData.sfsh = '1';
      // disabledQsh.value = true;
    }
    // console.log('检查取双号 >> ', lb, syd, sydInfo, formData.sfsh)
  } catch(err: any) {
    console.error('检查取双号必要性失败', err)
  }
}

// 暴露方法供父组件调用
defineExpose({
  resetForm,
  validate: () => formRef.value?.validate()
})

// 页面挂载时加载数据
onMounted(() => {
  checkQsh()
  loadGzsxTree();
  if (props.mode === 'edit') {
    getDsrList();
  }
})
</script>

<style scoped>
.notary-matter-form {
  padding: 0;
}

.filter-wrap {
  width: 100%;
  margin-bottom: 10px;
}
.filter-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  margin-bottom: 10px;
}
.filter-body {
  height: 300px;
  box-shadow: inset 0 0 0 1px #ccc;
  padding: 4px;
  border-radius: 4px;
}

.filter-tree {
  height: 100%;
  overflow: auto;
}

.service-type-list {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

.s-type-i {
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: inset 0 0 0 1px rgba(204, 204, 204, 0.7);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}

.s-type-i:hover {
  box-shadow: inset 0 0 2px 1px rgba(139, 122, 235, 0.7);
}

.form-footer {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
