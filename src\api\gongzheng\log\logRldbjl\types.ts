export interface LogRldbjlVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 创建时间
   */
  createTime : string;

  /**
   * 备注
   */
  remark : string;

  /**
   * 对比人脸图片
   */
  dbrl : string;

  /**
   * 对比结果
   */
  dbjg : string;
  /**
   * 当事人ID
   */
  dsrId : string | number;
  /**
   * 当事人姓名
   */
  dsrXm : string;
  /**
   * 对比指数
   */
  dbzs : string;
  /**
   * 对比日期
   */
  dbrq : string;

  dsrZjhm : string;
}

export interface LogRldbjlForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 备注
   */
  remark ?: string;

  /**
   * 对比人脸图片
   */
  dbrl ?: string;

  /**
   * 对比结果
   */
  dbjg ?: string;
  /**
   * 当事人ID
   */
  dsrId ?: string | number;
  /**
   * 当事人姓名
   */
  dsrXm ?: string;
  /**
   * 对比指数
   */
  dbzs ?: string;
  /**
   * 对比日期
   */
  dbrq ?: string;

}

export interface LogRldbjlQuery extends PageQuery {

  /**
   * 对比人脸图片
   */
  dbrl ?: string;

  /**
   * 对比结果
   */
  dbjg ?: string;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;
  /**
   * 当事人姓名
   */
  dsrXm ?: string;
  /**
   * 对比指数
   */
  dbzs ?: string;
  /**
   * 对比日期
   */
  dbrq ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
  dsrZjhm : string;
  dsrXm : string;
}
