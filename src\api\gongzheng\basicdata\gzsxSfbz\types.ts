export interface GzsxSfbzVO {
  /**
   * 公证事项
   */
  gzsxId: string | number;

  /**
   * 序号
   */
  id: string | number;

  /**
   * 收费类别
   */
  sflb: string;

  /**
   * 收费方式
   */
  sffs: string;

  /**
   * 参考价
   */
  ckj: number;

  /**
   * 起始价（万元）
   */
  qsj: number;

  /**
   * 结束价（万元）
   */
  jsj: number;

  /**
   * 费用场景
   */
  fycj: string;

  /**
   * 公式
   */
  gs: string;

  /**
   * 默认状态
   */
  mrzt: string;

  gzsxCode : string;

  gzlbValue : string;
}

export interface GzsxSfbzForm extends BaseEntity {
  /**
   * 公证事项
   */
  gzsxId?: string | number;

  /**
   * 序号
   */
  id?: string | number;

  /**
   * 收费类别
   */
  sflb?: string;

  /**
   * 收费方式
   */
  sffs?: string;

  /**
   * 参考价
   */
  ckj?: number;

  /**
   * 起始价（万元）
   */
  qsj?: number;

  /**
   * 结束价（万元）
   */
  jsj?: number;

  /**
   * 费用场景
   */
  fycj?: string;

  /**
   * 公式
   */
  gs?: string;

  /**
   * 默认状态
   */
  mrzt?: string;

  selectId : [];

  gzsxCode ?: string;

  gzlbValue ?: string;
}

export interface GzsxSfbzQuery extends PageQuery {

  /**
   * 公证事项
   */
  gzsxId?:  string | number;

  /**
   * 收费类别
   */
  sflb?: string;

  /**
   * 收费方式
   */
  sffs?: string;

  /**
   * 默认状态
   */
  mrzt?: string;

  gzsxCode ?: string;

  gzlbValue ?: string;
    /**
     * 日期范围参数
     */
    params?: any;
}



