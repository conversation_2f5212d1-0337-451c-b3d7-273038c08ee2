<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" draggable>
    <div v-loading="modelState.loading" class="h-480px">
      <el-row :gutter="10" class="h-full">
        <el-col :span="10" class="h-full">
          <GzsxPicker :init-filter-name="initFilterName || modelState.initFilterName" @pick="gzsxPick" />
        </el-col>
        <el-col :span="14" class="h-full">
          <TemplatePicker :gzsx-options="gzsxOptions" @pick="tempPick" />
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="flex flex-nowrap justify-between items-center">
        <div class="flex flex-nowrap items-center gap-8px pl-20px">
          <strong style="font-size: 14px;">已选模版：</strong>
          <el-text v-if="pickTemp">{{ pickTemp?.wdMc }}</el-text>
        </div>
        <div class="flex justify-end items-center gap-4px">
          <el-button @click="comfirm" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">确认</el-button>
          <el-button @click="close">关闭</el-button>
        </div>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import GzsxPicker from './GzsxPicker.vue';
import TemplatePicker from './TemplatePicker.vue';

interface Props {
  title?: string;
  initFilterName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '使用模板'
})

const emit = defineEmits(['pick'])

const { userId, nickname } = useUserStore();

const modelState = reactive({
  visible: false,
  title: '使用模板',
  loading: false,
  submitting: false,
  initFilterName: ''
})

const pickTemp = ref(null);

const gzsxOptions = ref([]);

const gzsxPick = (obj: any, node: any, tree: any, ev: any) => {
  if(obj.childCount === 0 || !obj?.children) {
    const { id, title, code, jcsx } = obj
    gzsxOptions.value = [
      {
        id,
        title,
      }
    ];
  }
}

const tempPick = (cur: any, old: any) => {
  pickTemp.value = cur;
}

const initData = async () => {
  try {
    modelState.loading = true;
    const params = {
      // gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,

    }
  } catch (err: any) {
    console.error('遗嘱信息初始化失败', err)
  } finally {
    modelState.loading = false;
  }
}

const comfirm = async () => {
  emit('pick', pickTemp.value)
}

const close = () => {
  modelState.visible = false;
  closed();
}

const closed = () => {
  gzsxOptions.value = []
  pickTemp.value = null
}

const open = (data?: any) => {
  const { title, initFilterName } = data;
  modelState.visible = true
  modelState.initFilterName = initFilterName || ''
  modelState.title = title || null
  initData();
}

defineExpose({
  open,
  close
})

</script>
