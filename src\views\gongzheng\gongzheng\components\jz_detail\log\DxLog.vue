<template>
  <el-card>
    <template #header>
      <strong class="text-base">短信日志</strong>
    </template>
    <el-table :data="dxLogList" v-loading="loading" border stripe>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column label="短信内容" align="center" min-width="200" />
      <el-table-column prop="接收号码" label="流程意见" align="center" />
      <el-table-column prop="发送时间" label="审批人" align="center" />
      <el-table-column prop="发送状态" label="审批人" align="center" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { listGzrzLcrzxx } from '@/api/gongzheng/gongzheng/gzrzLcrzxx'
import { GzrzLcrzxxQuery, GzrzLcrzxxVO } from '@/api/gongzheng/gongzheng/gzrzLcrzxx/types'
import { ref, inject, onMounted } from 'vue'

interface Props {
  gzjzId?: string
}

const props = defineProps<Props>()

// 获取当前查看的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const loading = ref(false)
const dxLogList = ref([])

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateStr
  }
}

onMounted(() => {

})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>