<template>
  <div class="login-container">
    <!-- 左侧公证系统图片区域 -->
    <div class="login-left">
      <div class="login-left-content">
        <div class="system-logo">
          <!-- <img src="../assets/logo/notary-association.png" alt="中国公证协会" class="notary-logo-img" /> -->
        </div>
        <h1 class="system-title">广西公证业务办理系统</h1>
        <div class="system-slogan">公信天下 · 服务民生</div>
        <div class="system-features">
          <div class="feature-item">
            <svg-icon icon-class="documentation" class="feature-icon" />
            <span>公证文书管理</span>
          </div>
          <div class="feature-item">
            <svg-icon icon-class="peoples" class="feature-icon" />
            <span>当事人信息管理</span>
          </div>
          <div class="feature-item">
            <svg-icon icon-class="chart" class="feature-icon" />
            <span>数据统计分析</span>
          </div>
          <div class="feature-item">
            <svg-icon icon-class="monitor" class="feature-icon" />
            <span>业务全流程监控</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-right">
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <div class="title-box">
          <h3 class="title">广西公证业务办理系统</h3>
          <!-- <lang-select /> -->
        </div>
        <!-- <el-form-item v-if="tenantEnabled" prop="tenantId">
          <el-select v-model="loginForm.tenantId" filterable :placeholder="proxy.$t('login.selectPlaceholder')" style="width: 100%">
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"></el-option>
            <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
          </el-select>
        </el-form-item> -->
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" :placeholder="proxy.$t('login.username')">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            :placeholder="proxy.$t('login.password')"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="captchaEnabled" prop="code">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            :placeholder="proxy.$t('login.code')"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" class="login-code-img" @click="getCode" />
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 25px 0">{{ proxy.$t('login.rememberPassword') }}</el-checkbox>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
            <span v-if="!loading">{{ proxy.$t('login.login') }}</span>
            <span v-else>{{ proxy.$t('login.logging') }}</span>
          </el-button>
          <div v-if="register" style="float: right">
            <router-link class="link-type" :to="'/register'">{{ proxy.$t('login.switchRegisterPage') }}</router-link>
          </div>
        </el-form-item>
      </el-form>

      <!--  底部  -->
      <div class="el-login-footer">
        <span>Copyright © 2025-2026 广西公证协会 All Rights Reserved.</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getTenantList } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { useI18n } from 'vue-i18n';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();

const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);

const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
  username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
  password: [{ required: true, trigger: 'blur', message: t('login.rule.password.required') }],
  code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 租户开关
const tenantEnabled = ref(true);

// 注册开关
const register = ref(false);
const redirect = ref('/');
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        const redirectUrl = redirect.value || '/';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type, loginForm.value.tenantId).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

onMounted(() => {
  getCode();
  initTenantList();
  getLoginData();
});
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: #f0f2f5;
}

/* 左侧区域样式 */
.login-left {
  width: 55%;
  background-color: #a00000; /* 修改为红色主题，与logo颜色协调 */
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  position: relative;
  overflow: hidden;
  flex: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../assets/images/login-background.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.05; /* 降低透明度，使背景更暗 */
    z-index: 1;
  }

  .login-left-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 0 50px;

    .system-logo {
      margin-bottom: 40px; /* 增加与标题的间距 */
      background-color: rgba(255, 255, 255, 0.9); /* 添加白色背景 */
      padding: 20px 30px;
      border-radius: 8px;
      display: inline-block;

      .notary-logo-img {
        width: 280px;
        height: auto;
        margin: 0 auto;
        display: block;
      }
    }

    .system-title {
      margin-top: 0;
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
    }

    .system-slogan {
      color: #f0f0f0;
      font-weight: 300;
      font-size: 20px;
      margin-bottom: 60px;
      letter-spacing: 2px;
    }

    .system-features {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      background-color: rgba(0, 0, 0, 0.2); /* 添加半透明背景 */
      border-radius: 8px;
      padding: 20px;

      .feature-item {
        width: 45%;
        margin-bottom: 30px;
        display: flex;
        align-items: center;

        .feature-icon {
          font-size: 24px;
          margin-right: 10px;
          color: #ffcc00; /* 图标使用金色，增加对比度 */
        }

        span {
          font-size: 16px;
        }
      }
    }
  }
}

/* 右侧区域样式 */
.login-right {
  width: 45%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #fff;
  flex: 1;
}

.title-box {
  display: flex;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #a00000; /* 修改为红色主题，与左侧协调 */
    font-size: 24px;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #7483a3;
  }
}

.login-form {
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }

  .el-button--primary {
    background-color: #a00000; /* 修改为红色主题 */
    border-color: #a00000;

    &:hover, &:focus {
      background-color: #c00000;
      border-color: #c00000;
    }
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  color: #666;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .login-left {
    width: 45%;
  }

  .login-right {
    width: 55%;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    width: 100%;
    height: 200px;

    .login-left-content {
      .system-logo {
        padding: 10px 15px;

        .notary-logo-img {
          width: 180px;
        }
      }

      .system-title {
        font-size: 28px;
      }

      .system-slogan {
        font-size: 16px;
        margin-bottom: 20px;
      }

      .system-features {
        display: none;
      }
    }
  }

  .login-right {
    width: 100%;
    padding: 30px 0;
  }

  .login-form {
    width: 90%;
    max-width: 400px;
  }
}
</style>
