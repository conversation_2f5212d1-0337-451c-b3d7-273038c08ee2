<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="提存项ID" prop="tcxId">
              <el-input v-model="queryParams.tcxId" placeholder="请输入提存项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人ID" prop="dsrId">
              <el-input v-model="queryParams.dsrId" placeholder="请输入当事人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="已领取" prop="ylq">
              <el-input v-model="queryParams.ylq" placeholder="请输入已领取" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzTcxLqr:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzTcxLqr:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzTcxLqr:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzTcxLqr:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzTcxLqrList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="提存项ID" align="center" prop="tcxId" />
        <el-table-column label="当事人ID" align="center" prop="dsrId" />
        <el-table-column label="已领取" align="center" prop="ylq" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzTcxLqr:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzTcxLqr:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改提存项-领取人对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzTcxLqrFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="提存项ID" prop="tcxId">
          <el-input v-model="form.tcxId" placeholder="请输入提存项ID" />
        </el-form-item>
        <el-form-item label="当事人ID" prop="dsrId">
          <el-input v-model="form.dsrId" placeholder="请输入当事人ID" />
        </el-form-item>
        <el-form-item label="已领取" prop="ylq">
          <el-input v-model="form.ylq" placeholder="请输入已领取" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzTcxLqr" lang="ts">
import { listGzjzTcxLqr, getGzjzTcxLqr, delGzjzTcxLqr, addGzjzTcxLqr, updateGzjzTcxLqr } from '@/api/gongzheng/bzfz/gzjzTcxLqr';
import { GzjzTcxLqrVO, GzjzTcxLqrQuery, GzjzTcxLqrForm } from '@/api/gongzheng/bzfz/gzjzTcxLqr/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzTcxLqrList = ref<GzjzTcxLqrVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzTcxLqrFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzTcxLqrForm = {
  id: undefined,
  tcxId: undefined,
  dsrId: undefined,
  ylq: undefined,
}
const data = reactive<PageData<GzjzTcxLqrForm, GzjzTcxLqrQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tcxId: undefined,
    dsrId: undefined,
    ylq: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    tcxId: [
      { required: true, message: "提存项ID不能为空", trigger: "blur" }
    ],
    dsrId: [
      { required: true, message: "当事人ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询提存项-领取人列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzTcxLqr(queryParams.value);
  gzjzTcxLqrList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzTcxLqrFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzTcxLqrVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加提存项-领取人";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzTcxLqrVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzTcxLqr(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改提存项-领取人";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzTcxLqrFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzTcxLqr(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzTcxLqr(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzTcxLqrVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除提存项-领取人编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzTcxLqr(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzTcxLqr/export', {
    ...queryParams.value
  }, `gzjzTcxLqr_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
