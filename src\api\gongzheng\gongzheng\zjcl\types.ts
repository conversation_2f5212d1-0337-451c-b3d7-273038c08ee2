// 证据类型
export interface EvidenceType {
  id: string;
  name: string;
  party?: string;
  fileCount: number;
  isDefault?: boolean;
}

// 证据文件
export interface EvidenceFile {
  id: string;
  typeId: string;
  url: string;
  thumbnailUrl?: string;
  uploadDate: string;
  fileName: string;
}

// 新增证据类型参数
export interface AddEvidenceTypeParams {
  name: string;
  party?: string;
}

// 上传证据文件参数
export interface UploadEvidenceFileParams {
  typeId: string;
  file: File;
} 