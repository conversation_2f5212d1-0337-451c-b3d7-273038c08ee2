import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzZmclxxVO, GzjzZmclxxForm, GzjzZmclxxQuery } from '@/api/gongzheng/gongzheng/gzjzZmclxx/types';

/**
 * 查询公证卷宗-公证证明材料信息-主信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzZmclxx = (query?: GzjzZmclxxQuery): AxiosPromise<GzjzZmclxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证证明材料信息-主信息v1.0详细
 * @param id
 */
export const getGzjzZmclxx = (id: string | number): AxiosPromise<GzjzZmclxxVO> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/' + id,
    method: 'get'
  });
};

/**
 * 公证卷宗-添加证据名称（可批量） ok
 * @param data
 */
export const addGzjzZmclxx = (data: GzjzZmclxxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证证明材料信息-主信息v1.0
 * @param data
 */
export const updateGzjzZmclxx = (data: GzjzZmclxxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证证明材料信息-主信息v1.0
 * @param id
 */
export const delGzjzZmclxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzZmclxx/' + id,
    method: 'delete'
  });
};

/**
 * 获取过程文档列表与文档类型数据初始化
 * @param params {gzjzId: string, fjlb: string}
 * @param params.gzjzId 公证卷宗id
 * @param params.fjlb 附件类别 (1文档拟定，2证据材料，3笔录，4代书(文书)，9其他)
 * @returns
 */
export const queryGzjzZmclxx = (params: { gzjzId: string | number, fjlb: string}) => {
  return request({
    url: '/gongzheng/gzjzZmclxx/listByInit',
    method: 'get',
    params: params
  });
}


