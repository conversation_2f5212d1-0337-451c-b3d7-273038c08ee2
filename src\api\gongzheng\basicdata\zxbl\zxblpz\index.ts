import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZxblConfigVO, ZxblConfigForm, ZxblConfigQuery, MaterialOption } from '@/api/gongzheng/basicdata/zxbl/zxblpz/types';

/**
 * 查询在线办理配置列表
 * @param query
 * @returns {*}
 */
export const listZxblConfig = (query?: ZxblConfigQuery): AxiosPromise<ZxblConfigVO[]> => {
  return request({
    url: '/basicdata/gzsxZxblConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询在线办理配置详细
 * @param id
 */
export const getZxblConfig = (id: string | number): AxiosPromise<ZxblConfigVO> => {
  return request({
    url: '/basicdata/gzsxZxblConfig/' + id,
    method: 'get'
  });
};

/**
 * 根据公证事项ID查询在线办理配置
 * @param gzsxId
 */
export const getZxblConfigByGzsxId = (gzsxId: string | number): AxiosPromise<ZxblConfigVO> => {
  return request({
    url: '/basicdata/gzsxZxblConfig/gzsx/' + gzsxId,
    method: 'get'
  });
};

/**
 * 新增在线办理配置
 * @param data
 */
export const addZxblConfig = (data: ZxblConfigForm) => {
  return request({
    url: '/basicdata/gzsxZxblConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改在线办理配置
 * @param data
 */
export const updateZxblConfig = (data: ZxblConfigForm) => {
  return request({
    url: '/basicdata/gzsxZxblConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除在线办理配置
 * @param id
 */
export const delZxblConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsxZxblConfig/' + id,
    method: 'delete'
  });
};

/**
 * 导出在线办理配置
 * @param query
 */
export const exportZxblConfig = (query?: ZxblConfigQuery) => {
  return request({
    url: '/basicdata/gzsxZxblConfig/export',
    method: 'get',
    params: query
  });
};

/**
 * 批量保存在线办理配置
 * @param data
 */
export const batchSaveZxblConfig = (data: ZxblConfigForm[]) => {
  return request({
    url: '/basicdata/gzsxZxblConfig/batch',
    method: 'post',
    data: data
  });
};

/**
 * 启用/禁用在线办理
 * @param id 配置ID
 * @param isOnlineHandle 是否在线办理（1是/0否）
 */
export const toggleOnlineHandle = (id: string | number, isOnlineHandle: number) => {
  return request({
    url: '/basicdata/gzsxZxblConfig/toggle-online',
    method: 'put',
    data: { id, isOnlineHandle }
  });
};

/**
 * 启用/禁用办理功能
 * @param id 配置ID
 * @param isEnableHandle 是否启用办理（1是/0否）
 */
export const toggleEnableHandle = (id: string | number, isEnableHandle: number) => {
  return request({
    url: '/basicdata/gzsxZxblConfig/toggle-enable',
    method: 'put',
    data: { id, isEnableHandle }
  });
};

/**
 * 获取申办材料选项列表
 * @param category 材料类别（可选）
 */
export const getMaterialOptions = (category?: number): AxiosPromise<MaterialOption[]> => {
  return request({
    url: '/basicdata/gzsxZxblConfig/materials',
    method: 'get',
    params: { category }
  });
};
