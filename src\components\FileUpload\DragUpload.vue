<template>
  <el-dialog v-model="viewVisible" :title="props.title" @closed="handleClose" draggable>
    <slot name="header"/>
    <el-upload
      drag
      :multiple="multiple"
      ref="uploader"
      :headers="uploadState.headers"
      :accept="accept"
      :action="uploadFileUrl"
      :limit="limit"
      v-model:file-list="uploadState.fileList"
      :auto-upload="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-change="handleChange"
      :on-exceed="handleExceed"
      :on-progress="handleProgress"
      :before-upload="beforeUpload"
      :disabled="disabledUpload"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">只能上传 {{ props.accept }} 文件，每个文件限制 {{ props.maxSize }} MB</div>
      </template>
    </el-upload>
    <slot name="footer" />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmUpload" :loading="uploadState.uploading" :disabled="uploadState.uploading || disabledUpload">确定上传</el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
//  =======================  文件拖入 上传  =========================
import { ref, reactive, watch, computed } from 'vue';
import { genFileId, type UploadFiles, type UploadInstance, type UploadProgressEvent, type UploadRawFile } from 'element-plus'
import { globalHeaders } from '@/utils/request';
import type { UploadProps, UploadResult, UploadStatus } from './type';
import { nodeFilter } from '@/utils/ruoyi';

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
const uploader = ref<UploadInstance>();

const props = withDefaults(defineProps<UploadProps>(),{
  modelValue: false,
  title: '文件上传',
  accept: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf',
  limit: 10,
  disabledUpload: false,
  multiple: true,
  maxSize: 200, // MB
});

const emit = defineEmits(['update:modelValue', 'onEveryoneDone', 'onAllDone']);

const uploadState = reactive({
  total: 0, // 文件总数量
  count: 0, // 已成功上传数量
  failCount: 0, // 上传失败数量
  fileList: [] as any[],
  okList: [] as any[],
  uploadFileUrl: uploadFileUrl.value,
  uploading: false,
  headers: globalHeaders(),
});

const viewVisible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  }
})

const reset = () => {
  uploadState.fileList = [];
  uploadState.uploading = false;
  uploadState.count = 0;
  uploadState.failCount = 0;
  uploadState.total = 0;
  uploadState.okList = [];
}

const handleClose = () => {
  if (uploadState.total === uploadState.count + uploadState.failCount) {
    emit('update:modelValue', false);
    reset();
  } else {
    ElMessageBox.confirm('上传未完成，确认关闭上传窗口？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      // uploader.value?.abort();
      emit('update:modelValue', false);
      reset();
    })
  }
};

// 确认上传
const confirmUpload = () => {
  if (uploadState.fileList.length === 0) {
    ElMessage({
      message: '请选择文件再上传',
      type: 'warning',
      grouping: true
    });
    return;
  }

  uploadState.uploading = true;
  uploadState.total = uploadState.fileList.length;
  uploadState.count = 0;
  uploadState.failCount = 0;
  uploader.value?.submit();
  console.log('confirmUpload', uploadState.fileList);
}

// 文件上传前
const beforeUpload = (file: UploadRawFile) => {
  console.log('beforeUpload', file, uploadState.fileList);
  return true;
}

const onEveryoneDone = () => {

}

// 文件上传成功
const handleSuccess = (res: any, file: any, fileList: any) => {
  uploadState.count++;
  const uploadStatus: UploadStatus = {
    total: uploadState.total,
    count: uploadState.count,
    failCount: uploadState.failCount,
    status: 'success',
    result: res.data
  }
  uploadState.okList.push(res.data);
  emit('onEveryoneDone', uploadStatus);
  if (uploadState.total === uploadState.count + uploadState.failCount) {
    emit('onAllDone', uploadState.okList);
    uploadState.uploading = false;
  }
  if (uploadState.count === uploadState.total) {
    ElMessage.success('上传完成');
    handleClose();
  }
  // console.log('文档上传结果', res, file, fileList);
}

// 文件上传失败
const handleError = (err: any, file: UploadFile, fileList: UploadFiles) => {
  uploadState.failCount++;
  const uploadStatus: UploadStatus =  {
    total: uploadState.total,
    count: uploadState.count,
    failCount: uploadState.failCount,
    status: 'error',
    result: err.data
  };
  ElMessage.error('上传失败: ' + file.name);
  emit('onEveryoneDone', uploadStatus);
  if (uploadState.total === uploadState.count + uploadState.failCount) {
    emit('onAllDone', uploadState.okList);
    uploadState.uploading = false;
  }
}

// 文件移除
const handleRemove = (file: any, fileList: any) => {}

// 文件状态改变
const handleChange = (file: any, fileList: UploadFiles) => {
  // const maxbytes = props.maxSize * 1024 * 1024; // 限制文件大小
  // const { matches, noMatches } = nodeFilter(fileList, (f: UploadFile) => {
  //   if (f.size < maxbytes) {
  //     return true;
  //   }
  //   return false;
  // });

  // if (noMatches.length > 0) {
  //   ElMessage.warning(`${ noMatches.map((item: any) => item.name).join(',') } 大小超过 ${ props.maxSize } MB`);
  // }

  // uploadState.fileList = matches;
  // console.log('而欧文哦哦', matches, noMatches)
}

const handleExceed = (files: UploadRawFile[]) => {
  uploader.value?.clearFiles();
  files.forEach(file => {
    file.uid = genFileId()
    uploader.value!.handleStart(file)
  })
}

// 文件上传进度
const handleProgress = (event: UploadProgressEvent, file: UploadFile, fileList: UploadFiles) => {
  // console.log(event, file, fileList);
}

watch(() => uploadState.fileList, (newVal) => {
  const maxbytes = props.maxSize * 1024 * 1024; // 限制文件大小

  const files = newVal.reduce((pre, cur) => {
    const found = pre.find((i: any) => (i.name == cur.name && i.size == cur.size));
    if(!found) pre.push(cur);
    return pre;
  }, [])

  const { matches, noMatches } = nodeFilter(files, (f: UploadFile) => {
    if (f.size < maxbytes) {
      return true;
    }
    return false;
  });

  if(noMatches.length > 0) {
    ElMessage.warning(`${ noMatches.map((item: any) => item.name).join(',') } 大小超过 ${ props.maxSize } MB`);
    uploadState.fileList = matches
  } else if (files.length != uploadState.fileList.length) {
    uploadState.fileList = matches
  }
})

</script>
