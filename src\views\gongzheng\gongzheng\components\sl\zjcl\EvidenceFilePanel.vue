<template>
  <div>
    <div class="panel-header">
      <span>{{ typeName }}证据</span>
      <el-button type="primary" @click="openUploadDialog">拍照</el-button>
    </div>
    <div class="img-list">
      <div v-for="file in files" :key="file.id" class="img-item">
        <el-image :src="file.bclj" :preview-src-list="[file.bclj]" fit="cover" @click="openPreview(file)" />
        <div class="img-date">{{ file.uploadDate }}</div>
        <div class="img-actions">
          <el-button size="small" icon="Delete" style="color: red;" @click.stop="deleteFile(file.id)" circle />
        </div>
      </div>
      <div v-if="files.length==0">暂未上传证据材料</div>
    </div>
    <EvidenceImagePreview v-if="previewFile" :file="previewFile" @close="previewFile = null" @deleted="fetchFiles" />
    <el-dialog v-model="uploadDialogVisible" title="拍照上传证据文件" width="400px">
      <!-- <el-upload :action="''" :http-request="customUpload" :show-file-list="false" accept="image/*" :multiple="true">
        <el-button type="primary">选择图片</el-button>
      </el-upload>
      <el-button type="primary" @click="handledPz">拍照</el-button> -->
      <Camera v-if="uploadDialogVisible" :auto-start="uploadDialogVisible" ref="cameraRef" @on-take-photo="onTakePhoto"
        default-ratio="4x3" default-size="320p" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { listEvidenceFiles, addEvidenceFile, delEvidenceFile } from '@/api/gongzheng/gongzheng/zjcl';
  import EvidenceImagePreview from './EvidenceImagePreview.vue';
  import Camera from '@/views/gongzheng/components/Camera.vue';
  const props = defineProps<{
    typeId : string;
    typeName : string;
    gzjzId : number;
    selectedDsrId : string
  }>();
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const files = ref<any[]>([]);
  const previewFile = ref<any | null>(null);
  const uploadDialogVisible = ref(false);

  const fetchFiles = async () => {
    if (!props.typeId) return;
    const res = await listEvidenceFiles({ parentId: props.typeId });
    files.value = res.rows || [];
  };

  const openPreview = (file : any) => {
    previewFile.value = file.bclj;
  };
  const deleteFile = async (id : string) => {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      type: 'warning'
    }).then(async () => {
      // TODO: 调用删除接口
      await delEvidenceFile(id);
      fetchFiles();
    })
  };
  const openUploadDialog = () => {
    uploadDialogVisible.value = true;
  };
  const customUpload = async (option : any) => {
    // 适配API，上传图片并新增明细
    const formData = new FormData();
    formData.append('parentId', props.typeId);
    formData.append('file', option.file);
    await addEvidenceFile({ parentId: props.typeId, fileUrl: option.file.name }); // 实际应上传文件并获得url
    uploadDialogVisible.value = false;
    fetchFiles();
  };
  const handledPz = () => {
    dialog5.visible = true;
    dialog5.title = "拍照"
  }
  const onTakePhoto = async (val) => {
    // 适配API，上传图片并新增明细
    // const formData = new FormData();
    // formData.append('parentId', props.typeId);
    // formData.append('zp', val);
    // formData.append('gzjzId', props.gzjzId);
    // formData.append('clType', 1);
    const params = {
      parentId: props.typeId,
      zp: val, // base64
      gzjzId: props.gzjzId, // 公证卷宗ID
      clType: 1,
      dsrId: props.selectedDsrId //当事人
    }
    await addEvidenceFile(params);
    uploadDialogVisible.value = false;
    fetchFiles();
  }
  watch(() => props.typeId, fetchFiles, { immediate: true });
</script>

<style scoped>
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .img-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .img-item {
    width: 120px;
    position: relative;
  }

  .img-date {
    font-size: 12px;
    color: #888;
    margin-top: 4px;
  }

  .img-actions {
    position: absolute;
    top: 4px;
    right: 4px;
  }
</style>
