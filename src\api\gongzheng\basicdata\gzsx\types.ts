export interface GzsxVO {
  /**
   * 序号
   */
  id : string | number;

  /**
   * 事项名称
   */
  title : string;

  /**
   * 父级
   */
  parentId : string | number;

  /**
   * 事项code
   */
  code : string;

  /**
   * 层级
   */
  level : number;

  /**
   * 备注
   */
  remark : string;

  jcsx : string | number;

  parentCode: string;

  temptreeCode: string;

  children : GzsxVO[];

}

export interface GzsxForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 事项名称
   */
  title ?: string;

  /**
   * 父级
   */
  parentId ?: string | number;

  /**
   * 事项code
   */
  code ?: string;

  /**
   * 层级
   */
  level ?: number;

  /**
   * 备注
   */
  remark ?: string;

  jcsx ?: string | number;

  parentCode?: string;

  temptreeCode?: string;

  notarizationCategory ?: number;

}

export interface GzsxQuery extends PageQuery {

  /**
   * 事项名称
   */
  title ?: string;

  /**
   * 父级
   */
  parentId ?: string | number;
  parentCode?: string;

  /**
   * 事项code
   */
  code ?: string;

  /**
   * 层级
   */
  level ?: number;

  /**
   * 日期范围参数
   */
  params ?: any;
  jcsx ?: number;
  notarizationCategory ?: number;
}
