export interface ZjclmcVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 证据名称
   */
  title: string;

  /**
   * 父级
   */
  parentId: string | number;

  parentCode: string;

  treeCode: string;

}

export interface ZjclmcForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 证据名称
   */
  title?: string;

  /**
   * 父级
   */
  parentId?: string | number;

  parentCode?: string;

  treeCode?: string;

}

export interface ZjclmcQuery extends PageQuery {

  /**
   * 证据名称
   */
  title?: string;

  /**
   * 父级
   */
  parentId?: string | number;
  parentCode?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



