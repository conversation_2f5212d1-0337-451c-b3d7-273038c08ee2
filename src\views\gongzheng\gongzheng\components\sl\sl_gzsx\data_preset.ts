// 用于判断借贷、赋强事项
export const jdfq_data = [
  {
    gzsxId: '1941842463734591490',
    gzsxMc: '借款合同'
  },
  {
    gzsxId: '1941842463797506049',
    gzsxMc: '借用合同'
  },
  {
    gzsxId: '1941842463864614914',
    gzsxMc: '无财产担保的租赁合同'
  },
  {
    gzsxId: '1941842463864614915',
    gzsxMc: '抵押贷款合同'
  },
  {
    gzsxId: '1941842463931723777',
    gzsxMc: '担保合同、保函'
  },
  {
    gzsxId: '1941842463931723778',
    gzsxMc: '赊欠货物的债权文书'
  },
  {
    gzsxId: '1941842463998832641',
    gzsxMc: '各种借据、欠单'
  },
  {
    gzsxId: '1941842463998832642',
    gzsxMc: '还款（物）协议、还款承诺'
  },
  {
    gzsxId: '1941842463998832643',
    gzsxMc: '调解协议'
  },
  {
    gzsxId: '1941842464065941506',
    gzsxMc: '和解协议'
  },
  {
    gzsxId: '1941842464065941507',
    gzsxMc: '融资合同'
  },
  {
    gzsxId: '1941842464133050369',
    gzsxMc: '债务重组合同'
  },
  {
    gzsxId: '1941842464133050370',
    gzsxMc: '符合规定条件的其他债权文书'
  },
  {
    gzsxId: '1941842464200159233',
    gzsxMc: '执行证书'
  }
]

// 用于判断遗嘱事项
export const yz_data = [
    {
        gzsxId: '1941842460089741313',
        gzsxMc: '处分财产的遗嘱'
    },
    {
        gzsxId: '1941842460089741314',
        gzsxMc: '遗嘱监护'
    },
    {
        gzsxId: '1941842460156850177',
        gzsxMc: '遗嘱信托'
    },
    {
        gzsxId: '1941842460223959042',
        gzsxMc: '处理事务的遗嘱'
    },
    {
        gzsxId: '1941842460223959043',
        gzsxMc: '遗嘱的变更和撤销'
    },
    {
        gzsxId: '1941842460291067906',
        gzsxMc: '其他遗嘱'
    }
]