<template>
  <!-- 收费表单 - 对话框 -->
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="1200px" append-to-body>

    <div v-if="dialog.visible">
      <el-descriptions
        class="margin-top"
        title="收费列表"
        :column="1"
        border
      >
        <template #extra>
          <el-button type="primary" plain icon="Plus" @click="handleAddSfxx()" v-has-permi="['cwgl:sf:edit']">增加</el-button>
          <el-button type="danger" plain icon="Delete" :loading="deleteLoading" :disabled="multiple" @click="handleDeleteSfxx()" v-has-permi="['cwgl:sf:edit']">删除</el-button>
        </template>
      </el-descriptions>

      <el-table
        ref="multipleTableRef"
        :data="gzsxSfxxList"
        border
        v-loading="listLoading"
        height="250"
        :summary-method="getSummaries"
        show-summary
        @selection-change="handleSelectionChange"
        style="width: 100%; margin-bottom: 20px"
        size="small"
        row-key="id"
      >
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column prop="gzjzGzsx" label="公证事项" min-width="80px" show-overflow-tooltip/>
        <el-table-column prop="gzsbh" label="公证书编号" min-width="100px" show-overflow-tooltip/>
        <el-table-column prop="fylx" label="费用类型" width="80px" show-overflow-tooltip>
          <template #default="{row}">
            <el-text>{{ dictMapFormat(gz_sf_lb, row.fylx) }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="sfjz" label="是否记账" width="70px" >
          <template #default="{row}">
            <el-text>{{ dictMapFormat(gz_yes_or_no, row.sfjz) }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="fyys" label="应收金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.fyys).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="fyjm" label="减免金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.fyjm).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="yfje" label="应付金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.yfje).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="fyss" label="已收金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.fyss).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="wsje" label="未收金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.wsje).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="jfje" label="缴费金额" width="" >
          <template #default="scope">
            {{parseFloat(scope.row.jfje).toFixed(2)}}
          </template>
        </el-table-column>

        <el-table-column prop="sfzt" label="收费状态" width="70px" >
          <template #default="{row}">
            <el-text>{{ dictMapFormat(gz_sfzt, row.sfzt) }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="sfly" label="收费来源" width="70px" >
          <template #default="{row}">
            <el-text>{{ dictMapFormat(gz_sfly, row.sfly) }}</el-text>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <el-descriptions :title="'已选收费明细：' + sumAmount.count + ' 项'">
            <el-descriptions-item label="应收总金额：">{{sumAmount.fyys}}</el-descriptions-item>
            <el-descriptions-item label="减免总金额：">{{sumAmount.fyjm}}</el-descriptions-item>
            <el-descriptions-item label="应付总金额：">{{sumAmount.yfje}}</el-descriptions-item>
            <el-descriptions-item label="已收总金额：">{{sumAmount.fyss}}</el-descriptions-item>
            <el-descriptions-item label="未收总金额：">{{sumAmount.wsje}}</el-descriptions-item>
            <el-descriptions-item label="缴费总金额：">{{sumAmount.jfje}}</el-descriptions-item>
          </el-descriptions>
      </div>
    </div>
    <el-divider />
    <div>
      <el-descriptions
        class="margin-top"
        title="缴费信息"
        :column="1"
        border
      >
        <template #extra>
        </template>
      </el-descriptions>
      <el-form ref="gzjzSfFormRef" :model="formSf" :rules="rules" :inline="true" label-width="100px">
        <div>
          <el-form-item label="缴费人" prop="jfrId" >
            <el-select v-model="formSf.jfrId" filterable placeholder="请选择" style="width: 140px">
              <el-option
                v-for="item in gzjzDsrList"
                :key="item.dsrId"
                :label="item.name"
                :value="item.dsrId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式" prop="zffs">
            <el-select v-model="formSf.zffs" placeholder="请选择" style="width: 140px">
              <el-option
                v-for="dict in gz_zffs"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收费金额" prop="sfje">
            <el-input v-model="formSf.sfje" placeholder="请输入" style="width: 120px" disabled  />
          </el-form-item>
          <el-form-item label="收费日期" prop="sfrq">
            <el-date-picker clearable
                            style="width: 140px"
                            v-model="formSf.sfrq"
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="请选择">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="打印收据" prop="sfdysj" label-width="100px">
            <el-radio-group v-model="formSf.sfdysj">
              <el-radio
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :value="dict.value"
              >{{dict.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="打印收据证明" prop="sfdysjzm" label-width="120px">
            <el-radio-group v-model="formSf.sfdysjzm">
              <el-radio
                v-for="dict in gz_yes_or_no"
                :key="dict.value"
                :value="dict.value"
              >{{dict.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="收费提醒" prop="remark">
            <el-text >{{ gzjzYqtxVO.txSftx ? gzjzYqtxVO.txSftx || '无' : '无' }}</el-text>
          </el-form-item>
          <!-- <el-form-item label="电子发票"  label-width="120px">
            <el-radio-group v-model="formSf.dzfp">
              <el-radio value="0" >否</el-radio>
              <el-radio value="1" >是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="系统外开票"  label-width="120px">
            <el-radio-group v-model="formSf.xtwkp">
              <el-radio value="0" >否</el-radio>
              <el-radio value="1" >是</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </div>
      </el-form>
    </div>
    <!-- <el-divider />
    <div>
      <el-descriptions
        class="margin-top"
        title="发票信息"
        :column="1"
        border
      >
        <template #extra>
        </template>
      </el-descriptions>
      <el-form ref="gzjzFpFormRef" :model="formFp" :rules="rules" :inline="true">
        <el-form-item label="发票抬头" prop="fptt">
          <el-input v-model="formFp.fptt" placeholder="请输入" style="width: 160px" />
        </el-form-item>
        <el-form-item label="发票类型" prop="fplx">
          <el-select v-model="formFp.fplx" placeholder="请选择" style="width: 140px" >
            <el-option
              v-for="dict in gz_mr_zt"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发票号码" prop="fphm">
          <el-input v-model="formFp.fphm" placeholder="请输入" style="width: 160px" />
        </el-form-item>
        <el-form-item label="开票金额" prop="fpje">
          <el-input v-model="formFp.fpje" placeholder="请输入开票金额" style="width: 140px" />
        </el-form-item>
        <el-form-item label="开票日期" prop="fprq">
          <el-date-picker clearable
                          style="width: 140px"
                          v-model="formFp.fprq"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div> -->

    <AddSf v-model="addSfShow" v-if="addSfShow" :gzjz-id="gzjzId" @success="() => loadList()" />

    <ChargeRecord ref="chargeRecordRef" :gzjz-id="gzjzId" />

    <WdndDialog v-model="wdndShow" :gzjz-id="gzjzId" />

    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitFinishCharge" v-has-permi="['cwgl:sf:edit']" :disabled="formSf.sfje == 0">确认收款</el-button>
        <!-- <el-button :loading="buttonLoading" type="primary" @click="submitProgress" v-has-permi="['cwgl:sf:edit']">确认收款并开票</el-button> -->
        <el-button :loading="buttonLoading" type="primary" @click="handleChargeRecord" v-has-permi="['cwgl:sf:query']">收费记录</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="generateChargeList" v-has-permi="['cwgl:sf:edit']">生成收费清单</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="dialogChargeMain" lang="ts">
  import { CaiWuVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import { getGzjzSfjfp, addGzjzSfjfp } from '@/api/gongzheng/gongzheng/gzjzSfjfp';
  import { GzjzSfjfpVO, GzjzSfjfpForm } from '@/api/gongzheng/gongzheng/gzjzSfjfp/types';
  import { listGzjzGzsxSfxx, addGzjzGzsxSfxx, delGzjzGzsxSfxx } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx';
  import { GzjzGzsxSfxxVO, GzjzGzsxSfxxQuery, GzjzGzsxSfxxForm } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx/types';
  import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
  import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
  import { getByGzjzIdGzjzYqtx } from '@/api/gongzheng/gongzheng/gzjzYqtx';
  import { GzjzYqtxVO } from '@/api/gongzheng/gongzheng/gzjzYqtx/types';
  import { clearEmptyProperty, dictMapFormat, curDateToStr } from '@/utils/ruoyi';
  import type { TableColumnCtx } from 'element-plus';
  import AddSf from './AddSf.vue';
  import ChargeRecord from './ChargeRecord/index.vue';
  import WdndDialog from '@/views/gongzheng/gongzheng/components/sl/wdnd/WdndDialog.vue';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_sf_lb, gz_yes_or_no, gz_sfzt, gz_sfly, gz_zffs } = toRefs<any>(proxy?.useDict( 'gz_sf_lb', 'gz_yes_or_no', 'gz_sfzt', 'gz_sfly', 'gz_zffs' ));
  const { gzy, gzyzl } = toRefs<any>(proxy?.useDict('gzy', 'gzyzl'));

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const chargeRecordRef = ref(null);
  const wdndShow = ref(false);

  interface SummaryMethodProps<T = GzjzGzsxSfxxVO> {
    columns: TableColumnCtx<T>[]
    data: T[]
  }
  const sumColumns = ['fyys', 'fyjm', 'yfje', 'fyss', 'wsje', 'jfje'];

  const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param;
    const sums: (string | VNode)[] = [];
    columns.forEach((column, index) => {
      if (index === 1) {
        sums[index] = h('div', { style: { fontWeight: 700, color: 'red' } }, [
          '合计：',
        ])
        return
      }
      const values = data.map((item) => Number(item[column.property]));
      // console.log("column", column)
      if (!values.every((value) => Number.isNaN(value))) {
        if(sumColumns.indexOf(column.property) != -1){
          const amount = values.reduce((prev, curr) => {
            const value = Number(curr)
            let total= 0.0;
            if (!Number.isNaN(value)) {
              total = Number(prev) + Number(curr);
            } else {
              total = Number(prev);
            }
            return parseFloat(total).toFixed(2);
          }, 0);
          sums[index] = h('div', { style: { fontWeight: 700, color: 'red' } }, [`${amount}`])
        }else{
          sums[index] = ''
        }
      } else {
        sums[index] = ''
      }
    });

    return sums;
  }

  const sumAmount = ref<any>({
    count: 0,
    fyys: 0.0,
    fyjm: 0.0,
    yfje: 0.0,
    fyss: 0.0,
    wsje: 0.0,
    jfje: 0.0
  });
  const selectedTotalAmount = (selection: GzjzGzsxSfxxVO[]) => {
    sumAmount.value = {
      count: 0,
      fyys: 0.0,
      fyjm: 0.0,
      yfje: 0.0,
      fyss: 0.0,
      wsje: 0.0,
      jfje: 0.0
    };
    selection.forEach(item => {
      if(ids.value.indexOf(item.id + '') > -1){
        sumAmount.value.fyys = sumAmount.value.fyys + Number(item.fyys);
        sumAmount.value.fyjm = sumAmount.value.fyjm + Number(item.fyjm);
        sumAmount.value.yfje = sumAmount.value.yfje + Number(item.yfje);
        sumAmount.value.fyss = sumAmount.value.fyss + Number(item.fyss);
        sumAmount.value.wsje = sumAmount.value.wsje + Number(item.wsje);
        sumAmount.value.jfje = sumAmount.value.jfje + Number(item.jfje);
      }
    });
    sumAmount.value.count = selection.length;
    console.log('selectedTotalAmount', sumAmount);
    // 收费金额
    formSf.value.sfje = sumAmount.value.jfje;
    // 实收金额
    formSf.value.ysje = sumAmount.value.jfje;
    // 退费金额
    formSf.value.tfje = 0.0;
    // 减免金额
    formSf.value.jmje = sumAmount.value.fyjm;
  }
  /** 多选框选中数据 */
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const handleSelectionChange = (selection: GzjzGzsxSfxxVO[]) => {
    ids.value = selection.map(item => item.id + '');
    single.value = selection.length != 1;
    multiple.value = !selection.length;
    console.log('handleSelectionChange', sumAmount);
    selectedTotalAmount(selection);
  }

  const gzjzId = ref<number | string>(null);
  const gzjzJbxx = ref<CaiWuVO>(null);

  const gzsxSfxxList = ref<GzjzGzsxSfxxVO[]>([]);
  const gzjzDsrList = ref<GzjzDsrVO[]>([]);
  const gzjzYqtxVO = ref<GzjzYqtxVO>({});

  const gzjzSfFormRef = ref<ElFormInstance>();
  const multipleTableRef = ref<ElTableInstance>();

  const listLoading = ref(false);
  const deleteLoading = ref(false);
  const buttonLoading = ref(false);

  const addSfShow = ref(false)

  /**
   * 获取当事人
   */
  const loadDsr = async () => {
    const queryParams = {
      dsrLx: undefined,
      gzjzId: gzjzId.value,
      js: undefined,
      pageNum: 1,
      pageSize: 100
    }
    const res = await listGzjzDsrByGzjz(queryParams);
    if(res?.rows){
      // 去除重复的当事人
      let dsrIds = [];
      let dsrs = [];
      res.rows.forEach(e => {
        if(dsrIds.indexOf(e.dsrId as string) == -1){
          dsrIds.push(e.dsrId as string);
          dsrs.push(e);
        }
      })
      gzjzDsrList.value = dsrs;
    }
  }

  /**
   * 获取收费明细
   */
  const loadList = async () => {
    listLoading.value = true;
    const query = {
      gzjzId: gzjzId.value,
      sfzt: '1',
      pageNum: 1,
      pageSize: 100
    }
    const res = await listGzjzGzsxSfxx(query);
    if(res?.rows){
      gzsxSfxxList.value = res.rows;
      selectedTotalAmount(gzsxSfxxList.value);
      toggleSelection(gzsxSfxxList.value);
    }
    listLoading.value = false;
  }


  /**
   * 获取收费提醒
   */
  const loadSftx = async () => {
    const res = await getByGzjzIdGzjzYqtx(gzjzId.value);
    if(res?.data){
      gzjzYqtxVO.value = res.data;
    }else{
      gzjzYqtxVO.value = {
        "txFyyq": null,
        "txZzyq": null,
        "txFztx": null,
        "txSftx": null,
        "remark": null
      }
    }
  }

  const toggleSelection = (rows?: GzjzGzsxSfxxVO[]) => {
    // console.log('toggleSelection-rows', multipleTableRef)
    if (rows) {
      multipleTableRef.value.toggleAllSelection();
      ids.value = rows.map(item => item.id);
      single.value = false;
      multiple.value = true;
    } else {
      multipleTableRef.value.clearSelection()
    }

  };

  const initFormData: GzjzSfjfpForm = {
    id: undefined,
    gzjzId: undefined,
    sfqdh: undefined,
    jfrId: undefined,
    jfr: undefined,
    zffs: '1',
    sfrq: curDateToStr(),
    sfje: undefined,
    sfrId: undefined,
    sfr: undefined,
    sfdysj: '0',
    sfdysjzm: '0',
    fptt: undefined,
    fplx: undefined,
    fphm: undefined,
    fpje: undefined,
    fprq: undefined,
    ysje: undefined,
    tfje: undefined,
    jmje: undefined,
    sfsf: undefined,
    sfly: undefined,
    remark: undefined,
    sfxxIds: []
  };

  const formSf = ref<GzjzSfjfpForm>({...initFormData});

  const rules = ref({
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      jfrId: [
        { required: true, message: "请选择缴费人", trigger: "blur" }
      ],
      zffs: [
        { required: true, message: "请选择支付方式", trigger: "blur" }
      ],
      sfrq: [
        { required: true, message: "请选择收费日期", trigger: "blur" }
      ],
  });

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    formSf.value = {...initFormData};
    gzjzSfFormRef.value?.resetFields();
  }

  /** 新增按钮操作 */
  const handleAddSfxx = () => {
    // proxy?.$modal.alertWarning("新增收费 功能建设中...");
    addSfShow.value = true
  }
  /** 删除按钮操作 */
  const handleDeleteSfxx = async (row?: GzjzGzsxSfxxVO) => {
    const _ids = row?.id || ids.value;
    deleteLoading.value = true;
    await proxy?.$modal.confirm('是否确认删除选中的数据项？').finally(() => deleteLoading.value = false);
    await delGzjzGzsxSfxx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await loadList();
  }

  // 确认收费
  const submitFinishCharge = () => {
    //proxy?.$modal.alertWarning("确认收费 功能建设中...");
    gzjzSfFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        buttonLoading.value = true;
        // console.log('form2.value', form2.value);
        const data = {...formSf.value};
        data.gzjzId = gzjzId.value;
       //gzsxSfxxList.value.map(item => item.id); // 默认全部收费，暂不支持选择收费
        const _ids = ids.value;
        data.sfxxIds = _ids;
        if(data.sfxxIds.length == 0){
          proxy?.$modal.msgWarning("请选择收费明细");
          return;
        }
        console.log('data', data);
        await addGzjzSfjfp(data).finally(() =>  buttonLoading.value = false);
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        emit('success', data);
      }
    });

  }
  // 收费记录
  const handleChargeRecord = () => {
    // proxy?.$modal.alertWarning("收费记录 功能建设中...");
    chargeRecordRef.value?.open({
      gzjzId: gzjzId.value
    })
  }
  // 生成收费清单
  const generateChargeList = () => {
    // proxy?.$modal.alertWarning("生成收费清单 功能建设中...");
    // emit('error');
    wdndShow.value = true;
  }

  const loadData = async () => {
    listLoading.value = true;
    await loadList();
    await loadDsr();
    await loadSftx();
    listLoading.value = false;
  }

  const open = async (_entity: CaiWuVO) => {
    if(!_entity){
      proxy?.$modal.msgWarning("请选择收费的信息！");
      return;
    }
    //init();
    gzjzId.value = _entity.id;
    gzjzJbxx.value = _entity;
    dialog.visible = true;
    dialog.title = '收费'
    await loadData();
  }
  // 回调方法
  const emit = defineEmits(['success', 'error'])
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  });
  onMounted(() => {
  });
</script>

<style>
</style>
