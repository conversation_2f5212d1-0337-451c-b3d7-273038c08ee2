<template>
  <gz-dialog v-model="visible" :title="props.title" @closed="handleClosed" show-close>
    <div class="flex justify-center items-center rounded-md bg-gray-200 min-h-300px">
      <el-image :src="props.url" :preview-src-list="[props.url]" :max-scale="7" :min-scale="0.2" :initial-index="0" />
    </div>
    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue?: boolean;
  title?: string;
  url?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '图片预览',
  url: '',
});

const emit = defineEmits(['update:modelValue', 'onClosed']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
})

const close = () => {
  emit('update:modelValue', false);
}

const handleClosed = () => {
  emit('onClosed');
}


</script>
