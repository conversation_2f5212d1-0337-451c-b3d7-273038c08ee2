<template>
  <el-dialog :visible="props.visible" @update:visible="val => emits('update:visible', val)" width="800px" :show-close="true" @close="close">
    <div class="preview-container">
      <el-image
        :src="file.url"
        :style="imgStyle"
        fit="contain"
      />
      <div class="toolbar">
        <el-button-group>
          <el-button icon="el-icon-zoom-in" @click="zoomIn" />
          <el-button icon="el-icon-zoom-out" @click="zoomOut" />
          <el-button icon="el-icon-refresh" @click="rotate" />
          <el-button icon="el-icon-delete" type="danger" @click="deleteImg" />
        </el-button-group>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import {  } from '@/api/gongzheng/gongzheng/zjcl/index';
import type { EvidenceFile } from '@/api/gongzheng/gongzheng/zjcl/types';

const props = defineProps<{ file: EvidenceFile }>();
const emits = defineEmits(['close', 'deleted']);
const visible = ref(true);
const scale = ref(1);
const rotateDeg = ref(0);

const imgStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotateDeg.value}deg)`
}));

const zoomIn = () => {
  scale.value += 0.2;
};
const zoomOut = () => {
  if (scale.value > 0.4) scale.value -= 0.2;
};
const rotate = () => {
  rotateDeg.value = (rotateDeg.value + 90) % 360;
};
const deleteImg = async () => {
  // await deleteEvidenceFile(props.file.id);
  emits('deleted');
  close();
};
// 关闭弹窗时用 emits('update:visible', false)
const close = () => {
  emits('update:visible', false);
  emits('close');
};
</script>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}
.toolbar {
  margin-top: 16px;
  text-align: center;
}
</style>
