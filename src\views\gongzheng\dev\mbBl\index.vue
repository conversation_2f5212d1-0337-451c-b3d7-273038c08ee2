<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="模板基础信息ID" prop="mbId">
              <el-input v-model="queryParams.mbId" placeholder="请输入模板基础信息ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="变量key" prop="blKey">
              <el-input v-model="queryParams.blKey" placeholder="请输入变量key" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="变量名称" prop="blName">
              <el-input v-model="queryParams.blName" placeholder="请输入变量名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="有效状态" prop="yxZt">
              <el-input v-model="queryParams.yxZt" placeholder="请输入有效状态" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="变量格式" prop="blGs">
              <el-input v-model="queryParams.blGs" placeholder="请输入变量格式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mb:mbBl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mb:mbBl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mb:mbBl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mb:mbBl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="mbBlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="模板基础信息ID" align="center" prop="mbId" />
        <el-table-column label="变量key" align="center" prop="blKey" />
        <el-table-column label="变量类型" align="center" prop="blType" />
        <el-table-column label="变量名称" align="center" prop="blName" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="有效状态" align="center" prop="yxZt" />
        <el-table-column label="变量格式" align="center" prop="blGs" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mb:mbBl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mb:mbBl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改模板-变量对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mbBlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板基础信息ID" prop="mbId">
          <el-input v-model="form.mbId" placeholder="请输入模板基础信息ID" />
        </el-form-item>
        <el-form-item label="变量key" prop="blKey">
          <el-input v-model="form.blKey" placeholder="请输入变量key" />
        </el-form-item>
        <el-form-item label="变量名称" prop="blName">
          <el-input v-model="form.blName" placeholder="请输入变量名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="有效状态" prop="yxZt">
          <el-input v-model="form.yxZt" placeholder="请输入有效状态" />
        </el-form-item>
        <el-form-item label="变量格式" prop="blGs">
          <el-input v-model="form.blGs" placeholder="请输入变量格式" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MbBl" lang="ts">
import { listMbBl, getMbBl, delMbBl, addMbBl, updateMbBl } from '@/api/gongzheng/mb/mbBl';
import { MbBlVO, MbBlQuery, MbBlForm } from '@/api/gongzheng/mb/mbBl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const mbBlList = ref<MbBlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const mbBlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MbBlForm = {
  id: undefined,
  mbId: undefined,
  blKey: undefined,
  blType: undefined,
  blName: undefined,
  remark: undefined,
  yxZt: undefined,
  blGs: undefined
}
const data = reactive<PageData<MbBlForm, MbBlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    mbId: undefined,
    blKey: undefined,
    blType: undefined,
    blName: undefined,
    yxZt: undefined,
    blGs: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    mbId: [
      { required: true, message: "模板基础信息ID不能为空", trigger: "blur" }
    ],
    blKey: [
      { required: true, message: "变量key不能为空", trigger: "blur" }
    ],
    blType: [
      { required: true, message: "变量类型不能为空", trigger: "change" }
    ],
    blName: [
      { required: true, message: "变量名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询模板-变量列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMbBl(queryParams.value);
  mbBlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  mbBlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MbBlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加模板-变量";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MbBlVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMbBl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改模板-变量";
}

/** 提交按钮 */
const submitForm = () => {
  mbBlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMbBl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMbBl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MbBlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除模板-变量编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMbBl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('mb/mbBl/export', {
    ...queryParams.value
  }, `mbBl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
