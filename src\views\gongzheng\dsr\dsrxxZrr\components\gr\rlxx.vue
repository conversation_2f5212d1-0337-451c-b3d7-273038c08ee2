<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="当事人" prop="dsrId">
              <el-input v-model="queryParams.dsrId" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="人脸图片" prop="rltp">
              <el-input v-model="queryParams.rltp" placeholder="请输入人脸图片" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['dsr:dsrxxRlxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['dsr:dsrxxRlxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['dsr:dsrxxRlxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['dsr:dsrxxRlxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxRlxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="当事人" align="center" prop="dsrId" />
        <el-table-column label="人脸图片" align="center" prop="rltp" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['dsr:dsrxxRlxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['dsr:dsrxxRlxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改当事人-面部(人脸)信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="dsrxxRlxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="当事人" prop="dsrId">
          <el-input v-model="form.dsrId" placeholder="请输入当事人" />
        </el-form-item>
        <el-form-item label="人脸图片" prop="rltp">
          <el-input v-model="form.rltp" placeholder="请输入人脸图片" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DsrxxRlxx" lang="ts">
import { listDsrxxRlxx, getDsrxxRlxx, delDsrxxRlxx, addDsrxxRlxx, updateDsrxxRlxx } from '@/api/gongzheng/dsr/dsrxxRlxx';
import { DsrxxRlxxVO, DsrxxRlxxQuery, DsrxxRlxxForm } from '@/api/gongzheng/dsr/dsrxxRlxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dsrxxRlxxList = ref<DsrxxRlxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const dsrxxRlxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DsrxxRlxxForm = {
  id: undefined,
  dsrId: undefined,
  rltp: undefined,
}
const data = reactive<PageData<DsrxxRlxxForm, DsrxxRlxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dsrId: undefined,
    rltp: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    dsrId: [
      { required: true, message: "当事人不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询当事人-面部(人脸)信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDsrxxRlxx(queryParams.value);
  dsrxxRlxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  dsrxxRlxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DsrxxRlxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加当事人-面部(人脸)信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DsrxxRlxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDsrxxRlxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改当事人-面部(人脸)信息";
}

/** 提交按钮 */
const submitForm = () => {
  dsrxxRlxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDsrxxRlxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addDsrxxRlxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: DsrxxRlxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除当事人-面部(人脸)信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delDsrxxRlxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('dsr/dsrxxRlxx/export', {
    ...queryParams.value
  }, `dsrxxRlxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
