export interface MbCydyVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 内容
   */
  content: string;

  /**
   * 使用范围
   */
  syFw: number;

  /**
   * 备注
   */
  remark: string;

}

export interface MbCydyForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 内容
   */
  content?: string;

  /**
   * 使用范围
   */
  syFw?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface MbCydyQuery extends PageQuery {

  /**
   * 内容
   */
  content?: string;

  /**
   * 使用范围
   */
  syFw?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



