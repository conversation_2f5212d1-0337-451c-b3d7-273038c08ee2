<template>
  <div>
    <el-card class="no-padding-card dateil-card-main">
      <div slot="header" class="clearfix dateil-card-heard">
        <span>面部识别列表</span>
      </div>
      <el-table v-loading="loading" :data="dsrxxZrrList">
        <el-table-column label="操作" align="center">
        </el-table-column>
        <el-table-column label="对比指数" align="center">
        </el-table-column>
        <el-table-column label="对比结果" align="center">
        </el-table-column>
        <el-table-column label="对比日期" align="center">
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <el-card class="no-padding-card dateil-card-main">
      <div slot="header" class="clearfix dateil-card-heard">
        <span>实人核验结果列表</span>
      </div>
      <el-table v-loading="loading2" :data="list">
        <el-table-column label="操作" align="center" >
        </el-table-column>
        <el-table-column label="检验时间" align="center" >
        </el-table-column>
        <el-table-column label="检验结果" align="center" >
        </el-table-column>
        <el-table-column label="对比分值" align="center" >
        </el-table-column>
      </el-table>
      <pagination v-show="total2 > 0" :total="total2" v-model:page="queryParams2.pageNum"
        v-model:limit="queryParams2.pageSize" @pagination="getList2" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    dialog : boolean;
    dialigEdit : boolean;
    title : String;
  }
  const props = defineProps<Props>();
  const loading = ref(false);
  const dsrxxZrrList = ref([]);
  const total = ref(0);
  const queryParams = ref({
    pageSize: 10,
    pageNum: 1
  })
  const getList = () => { }

  const loading2 = ref(false);
  const list = ref([]);
  const total2 = ref(0);
  const queryParams2 = ref({
    pageSize: 10,
    pageNum: 1
  })
  const getList2 = () => { }
</script>

<style>
</style>
