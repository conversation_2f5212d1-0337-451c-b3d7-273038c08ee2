<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form"
             label-width="100px">
      <el-form-item label="卷宗号：" prop="jzbh">
        <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="公证员：" prop="gzybm">
        <el-select v-model="queryParams.gzybm" filterable placeholder="请选择公证员" clearable style="width: 180px">
          <el-option
            v-for="item in gzy"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="当事人：" prop="dsrId">
        <el-select v-model="queryParams.dsrId" filterable placeholder="请选择当事人" clearable style="width: 180px">
          <el-option v-for="item in partyOptions" :key="item.id" :label="item.xm" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="公证书编号：" prop="gzsbh">
        <el-select v-model="queryParams.params.gzsNf" placeholder="所有" clearable style="width: 100px">
          <el-option v-for="item in gzsbh_years" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable style="width: 100px" class="ml10">
          <el-option v-for="item in gz_gzs_zh" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model="queryParams.params.gzsLs" placeholder="号" clearable style="width: 100px" class="ml10" />
      </el-form-item>
      <el-form-item label="翻译状态：" prop="" style="width: 200px">
        <el-select v-model="queryParams.params.fyztParam" placeholder="请选择翻译状态" style="width: 180px">
          <el-option v-for="item in fyztOptions" :key="item.value" :label="item.label" :value="item.value"
                     @click="setFyztList(item.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="译文：" prop="ywwz">
        <el-select v-model="queryParams.ywwz" filterable placeholder="请选择译文文种" clearable style="width: 180px">
          <el-option
            v-for="item in gz_yw_wz"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" v-has-permi="['fygl:my:query']">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="flex flex-col gap-20px">
      <el-row :gutter="20">
        <el-col :span="12">
          <!-- 左侧翻译列表 -->
          <div>
            <div class="flex items-center h-32px">
              <strong>翻译列表</strong>
            </div>
            <el-table v-loading="loading" :data="fyglList" border height="550"  @row-click="handleRowClick" highlight-current-row>
              <el-table-column type="index" label="序号" align="center" width="60" fixed/>
              <el-table-column label="操作" align="center" width="80" fixed>
                <template #default="scope">
                  <el-button type="primary" link @click.stop="handleDetail(scope.row)" v-has-permi="['fygl:my:query']" size="small">详情</el-button>
                </template>
              </el-table-column>
              <el-table-column label="翻译状态" align="center" prop="fyStatus" show-overflow-tooltip fixed>
                <template #default="scope">
                  <dict-tag :options="fyztStatsXs" :value="scope.row.fyStatus" />
                </template>
              </el-table-column>
              <!--          <el-table-column label="案件摘要" align="center" prop="caseAbstract" width="150" />-->
              <el-table-column label="卷宗号" align="center" prop="jzbh" width="120" show-overflow-tooltip />
              <el-table-column label="当事人" align="center" prop="dsrxm" width="100" show-overflow-tooltip />
              <el-table-column label="公证书编号" align="center" prop="gzsbh" width="200" show-overflow-tooltip />
              <el-table-column label="领证时间" align="center" prop="lzrq" width="180" show-overflow-tooltip />
              <el-table-column label="公证员" align="center" prop="gzyxm" width="100" show-overflow-tooltip />
              <el-table-column label="译文文种" align="center" prop="ywwz" show-overflow-tooltip>
                <template #default="scope">
                  <dict-tag :options="gz_yw_wz" :value="scope.row.ywwz" />
                </template>
              </el-table-column>
            </el-table>
            <div class="flex items-center h-32px justify-end">
              <el-pagination
                background
                layout="prev, pager, next, jumper"
                :total="listTotal"
                :current-page="queryParams.pageNum"
                :page-size="queryParams.pageSize"
                @current-change="handleCurrentChange"
                size="small"
              >
              </el-pagination>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <!-- 右侧翻译明细 -->
          <div>
            <div class="flex justify-between items-center h-32px">
              <strong>翻译明细</strong>
              <div class="flex items-center justify-end">
                <el-button type="primary" @click="handleSubmitTranslation" :disabled="multiple" v-has-permi="['fygl:my:edit']" size="small">一键提交译文</el-button>
              </div>
            </div>
            <el-table v-loading="detailLoading" :data="wjccxxList" border height="550" @selection-change="handleSelectionChange">
              <el-table-column type="index" label="序号" align="center" width="60" fixed/>
              <el-table-column type="selection" width="50" align="center" fixed />
              <el-table-column label="操作" align="center" width="80" fixed>
                <template #default="scope">
                  <el-button  v-if="scope.row.fyzt ==='0'||scope.row.fyzt ==='2' "  type="primary" link @click="handleDetailOperation(scope.row,'translation')" v-has-permi="['fygl:my:edit']" size="small">翻译</el-button>
                  <el-button v-else-if="scope.row.fyzt ==='1' "  type="primary" link @click="handleDetailOperation(scope.row,'proofread')" v-has-permi="['fygl:my:edit']" size="small">校对</el-button>
                  <el-button v-else type="primary" link @click="handleDetailOperation(scope.row,'view')" v-has-permi="['fygl:my:edit']" size="small">详情</el-button>
                </template>
              </el-table-column>
              <el-table-column label="翻译状态" align="center" prop="fyzt" width="100" show-overflow-tooltip fixed>
                <template #default="scope">
                  <dict-tag :options="fyztStatsXs" :value="scope.row.fyzt" />
                </template>
              </el-table-column>
              <el-table-column label="公证书编号" align="center" prop="gzsbh" show-overflow-tooltip />
              <el-table-column label="公证事项" align="center" prop="gzsx" show-overflow-tooltip />
              <el-table-column label="翻译截止时间" align="center" prop="translateDeadline" width="180" />
              <el-table-column label="翻译时间" align="center" prop="translateTime" width="180" />
    
              <el-table-column label="文档类型" align="center" prop="lx" width="100" show-overflow-tooltip>
                <template #default="scope">
                  <dict-tag :options="wsOptions" :value="scope.row.lx" />
                </template>
              </el-table-column>
            </el-table>
            <div class="flex items-center h-32px justify-end">
              <el-pagination
                background
                layout="prev, pager, next, jumper"
                :total="wjccxxTotal"
                :current-page="wjccxxParams.pageNum"
                :page-size="wjccxxParams.pageSize"
                @current-change="handleCurrentChangeWjccxx"
                size="small"
              >
              </el-pagination>
            </div>
          </div>
        </el-col>
       </el-row>


      <!-- 收费明细 -->
      <div v-if="false">
        <div class="flex justify-between items-center h-32px">
          <strong>收费明细</strong>
          <div class="flex items-center justify-end">
            <el-button type="primary" @click="handleAddFee" v-has-permi="['fygl:my:edit']" size="small">新增</el-button>
          </div>
        </div>
        <el-table v-loading="feeLoading" :data="sfxxList" border height="360" size="small">
          <el-table-column type="index" label="序号" align="center" width="60" fixed/>
          <el-table-column label="操作" align="center" width="120" fixed>
            <template #default="scope">
              <el-button v-if="userId===scope.row.createBy && scope.row.sfzt!='2'" type="primary" link  @click="handleFeeOperation(scope.row)" v-has-permi="['fygl:my:edit']">编辑</el-button>
              <el-button v-if="userId===scope.row.createBy && scope.row.sfzt!='2'" type="danger" link  @click="handleFeeDel(scope.row)" v-has-permi="['fygl:my:edit']">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column label="公证事项" align="center" prop="gzjzGzsx" show-overflow-tooltip />
          <el-table-column label="公证书编号" align="center" prop="gzsbh" show-overflow-tooltip />
          <el-table-column label="费用类型" align="center" prop="fylx" show-overflow-tooltip>
            <template #default="scope">
              <dict-tag :options="gz_sf_lb" :value="scope.row.fylx" />
            </template>
          </el-table-column>
          <el-table-column label="计价方式" align="center" prop="jjfs" show-overflow-tooltip>
            <template #default="scope">
              <dict-tag :options="gz_jjfs" :value="scope.row.jjfs" />
            </template>
          </el-table-column>
          <el-table-column label="应收价（元）" align="center" prop="fyys" show-overflow-tooltip />
          <el-table-column label="收费状态" align="center" prop="sfzt" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-if="scope.row.sfzt === '2'" type="primary">已完成</el-tag>
              <el-tag v-else type="danger">未收费</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex items-center h-32px justify-end">
          <el-pagination
            background
            layout="prev, pager, next, jumper"
            :total="sfxxTotal"
            :current-page="sfxxParams.pageNum"
            :page-size="sfxxParams.pageSize"
            @current-change="handleCurrentChangeSfxx"
            size="small"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 卷宗详情 -->
    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <!-- 新增费用对话框 -->
    <el-dialog v-model="feeDialog.visible" :title="feeDialog.title" width="500px" append-to-body>
      <el-form ref="feeFormRef" :model="feeForm" label-width="120px" :rules="feeRules">
        <el-form-item label="公证事项" prop="gzjzGzsxId">
          <el-select v-model="feeForm.gzjzGzsxId" placeholder="请选择公证事项" style="width: 100%">
            <el-option
              v-for="item in gzsxList"
              :key="item.id"
              :label="item.gzsxMc"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用类型" prop="fylx">
          <el-select v-model="feeForm.fylx" filterable placeholder="请选择费用类型" style="width: 100%">
            <el-option
              v-for="item in gz_sf_lb"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计价方式" prop="jjfs">
          <el-select v-model="feeForm.jjfs" placeholder="请选择计价方式" style="width: 100%">
            <el-option
              v-for="item in gz_jjfs"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="应收价（元）" prop="fyys">
          <el-input v-model="feeForm.fyys" type="number" min="0" placeholder="请输入应收价" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFeeForm" v-has-permi="['fygl:my:edit']">确 定</el-button>
          <el-button @click="feeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 翻译明细对话框 -->
    <fymx-dialog ref="fymxDialogRef" @callback="fymxCallback" ></fymx-dialog>

  </div>
</template>

<script setup lang="ts">
import { onMounted, provide, reactive, ref } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { clearEmptyProperty, genYearOptions } from '@/utils/ruoyi';
import { listGzjzFygl, listWjccxxByLxlist } from '@/api/gongzheng/fy';
import { GzGzjzFyglQuerye, GzGzjzFyglVo } from '@/api/gongzheng/fy/types';
import { GzjzWjccxxQuery, GzjzWjccxxVO } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { listDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
import {
  addGzjzGzsxSfxx,
  delGzjzGzsxSfxx,
  listGzjzGzsxSfxx,
  updateGzjzGzsxSfxx
} from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx';
import { type GzjzGzsxSfxxForm, GzjzGzsxSfxxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx/types';
import { useUserStore } from '@/store/modules/user';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import SqfyDialog from '@/views/gongzheng/gongzheng/components/sl/SqfyDialog.vue';
import FymxDialog from '@/views/gongzheng/fy/dialog/fymxDialog.vue';
import { batchUpdateList, updateGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';

const fymxDialogRef = ref<InstanceType<typeof FymxDialog>>(null);

const userStore = useUserStore();
const userId = ref(userStore.userId);

// 显示搜索条件
const showSearch = ref(true);

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);
const feeLoading = ref(false);

// 选中数据
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const listTotal = ref(40);
const wjccxxTotal = ref(0);
const sfxxTotal = ref(0);

const gzsbh_years = genYearOptions(2010);
const jzDetailState = reactive({
  visible: false
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  gz_yw_wz,
  gz_gzs_zh,
  gz_sf_lb,
  gz_mb_lx,
  gz_sfzt,
  gz_jjfs
} = toRefs<any>(proxy?.useDict('gz_yw_wz', 'gz_gzs_zh', 'gz_sf_lb', 'gz_mb_lx', 'gz_sfzt', 'gz_jjfs'));

const { gzy } = toRefs<any>(proxy?.useRoleUser('gzy'));

// 当事人选项
const partyOptions = ref(
);
//当事人列表
const getDsrxxZrrList = async () => {
  const res = await listDsrxxZrr();
  if (res.rows?.length > 0) {
    partyOptions.value = res.rows;
  }
};

// 公证事项列表
const gzsxList = ref();

// 查询参数
const queryParams = ref<GzGzjzFyglQuerye>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  dsrId: undefined,
  gzybm: undefined,
  ywwz: undefined,
  fyztList: ['0'],
  params: {
    gzsNf: undefined,
    gzsZh: undefined,
    gzsLs: undefined,
    fyztParam: '未完成'
  }
});

const wjccxxParams = ref<GzjzWjccxxQuery>({
  pageNum: 1,
  pageSize: 10
});
const sfxxParams = ref<GzjzGzsxSfxxQuery>({
  pageNum: 1,
  pageSize: 10
});

//翻译状态选项
const fyztOptions = ref([
  { label: '全部', value: '99' },
  { label: '未完成', value: '0' },
  { label: '进行中', value: '1' }
]);

//文书类型显示
const wsOptions = ref([
  { label: '文书', value: '1' },
  { label: '公证书', value: '3' }
]);

//翻译状态显示
const fyztStatsXs = ref([
  { label: '未分配', value: '0',elTagType:'primary' },
  { label: '待校对', value: '1' ,elTagType:'info' },
  { label: '驳回校对', value: '2' ,elTagType:'danger' },
  { label: '已校对', value: '3' ,elTagType:'success' },
  { label: '驳回翻译', value: '9' ,elTagType:'danger' }
]);

const gzjzId = ref<string | number>(null);
const currentRecord = ref<GzjzJbxxVO>(null);

// 翻译列表数据
const fyglList = ref<GzGzjzFyglVo>(null);

// 翻译明细列表数据
const wjccxxList = ref<GzjzWjccxxVO>(null);

// 费用列表数据
const sfxxList = ref([]);

// 翻译明细列表选中数据
const fymxList = ref([]);

// 费用对话框
const feeDialog = ref({
  visible: false,
  title: '新增费用',
  type: 'add'
});

const feeForm = reactive<GzjzGzsxSfxxForm>({
  id: undefined,
  fylx: '',
  jjfs: '',
  fyys: 0,
  fyjm: 0,
  fyss: 0,
  sfcj: '1',
  sfjz: '0',
  sfzt: '1',
  remark: '',
  gzjzId: '',
  gzjzGzsxId: '',
  gzsxId: ''
});
const feeFormRef = ref<FormInstance>();

const feeRules = reactive<FormRules>({
  gzjzGzsxId: [{ required: true, message: '公证事项不能为空', trigger: 'blur' }],
  fylx: [{ required: true, message: '费用类型不能为空', trigger: 'blur' }],
  jjfs: [{ required: true, message: '计价方式不能为空', trigger: 'blur' }],
  fyys: [{ required: true, message: '应收金不能为空', trigger: 'blur' }]
});

// 查询翻译列表数据
const getList = async () => {
  loading.value = true;
  try {
    await getDsrxxZrrList();
    // 清理空值参数，避免发送空字符串
    const cleanParams = clearEmptyProperty(queryParams.value);
    // 确保基本的分页参数
    cleanParams.pageNum = queryParams.value.pageNum || 1;
    cleanParams.pageSize = queryParams.value.pageSize || 10;
    const res = await listGzjzFygl(cleanParams);
    fyglList.value = res.rows;
    listTotal.value = res.total;
  } catch (error: any) {
    proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

//改变翻译状态列表
const setFyztList = (value: string) => {
  switch (value) {
    case '0':
      queryParams.value.fyztList = ['0'];
      break;
    case '1':
      queryParams.value.fyztList = ['1', '2'];
      break;
    case '2':
      queryParams.value.fyztList = ['3'];
      break;
    case '99':
    default:
      queryParams.value.fyztList = ['0', '1', '2', '3'];
      break;
  }
};

// 查询翻译明细列表
const getDetailList = async () => {
  detailLoading.value = true;
  try {
    /** 查询案件列表 */
    wjccxxParams.value.gzjzId = gzjzId.value;
    const lxList = ['1', '3'];
    const res = await listWjccxxByLxlist(lxList, wjccxxParams.value.gzjzId, wjccxxParams.value.pageNum, wjccxxParams.value.pageSize);
    if (res?.rows) {
      wjccxxList.value = res.rows;
      wjccxxTotal.value = res.total;
    }
  } catch (error: any) {
    proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
  } finally {
    detailLoading.value = false;
  }
};

// 查询费用明细
const getFeeList = async () => {
  feeLoading.value = true;
  try {
    sfxxParams.value.gzjzId = gzjzId.value;
    const res = await listGzjzGzsxSfxx(sfxxParams.value);
    if (res?.rows) {
      sfxxList.value = res.rows;
      sfxxTotal.value = res.total;
    }
  } catch (error: any) {
    proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
  } finally {
    feeLoading.value = false;
  }
};

// 表格多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  fymxList.value = selection.map(item => item);
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  wjccxxParams.value.pageNum = 1;
  sfxxParams.value.pageNum = 1;
  gzjzId.value = undefined;
  currentRecord.value = undefined;
  wjccxxList.value = undefined;
  sfxxList.value = undefined;
  gzsxList.value = undefined;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.value.jzbh = undefined;
  queryParams.value.dsrId = undefined;
  queryParams.value.gzybm = undefined;
  queryParams.value.ywwz = undefined;
  queryParams.value.fyztList = ['0'];
  queryParams.value.params.gzsNf = undefined;
  queryParams.value.params.gzsZh = undefined;
  queryParams.value.params.gzsLs = undefined;
  queryParams.value.params.fyztParam = '未完成';
  handleQuery();
};

// 处理行点击事件
const handleRowClick = (row: any) => {
  gzjzId.value = row.id;
  currentRecord.value = row;
  gzsxList.value = row.gzsxVoList;
  getDetailList();
  getFeeList();
};

// 处理详情按钮点击
const handleDetail = (row: any) => {
  gzjzId.value = row.id;
  currentRecord.value = row;
  jzDetailState.visible = true;
};

// 处理列表分页变化
const handleCurrentChange = (val: number) => {
  queryParams.value.pageNum = val;
  getList();
};

// 处理明细分页变化
const handleCurrentChangeWjccxx = (val: number) => {
  wjccxxParams.value.pageNum = val;
  getDetailList();
};

// 处理收费分页变化
const handleCurrentChangeSfxx = (val: number) => {
  sfxxParams.value.pageNum = val;
  getFeeList();
};

// 处理明细操作按钮点击
const handleDetailOperation = (row: any, type:string) => {
  // ElMessage.success(`操作明细: ${row.gzsbh}`);
  handleFymx(row, type)
  // 实际操作逻辑
};


// 提交译文
const handleSubmitTranslation = () => {
  if (gzjzId.value == undefined) {
    ElMessage.warning('请先选择翻译列表记录');
    return;
  }
  if (ids.value.length === 0) {
    ElMessage.warning('请选择需要提交的翻译明细记录');
    return;
  }
  let flag=true
  const list=[];
  for (let i = 0; i < fymxList.value.length; i++) {
    var item = fymxList.value[i];
    if (item.fyzt==='3'){
      proxy?.$modal.msgError("公证事项："+item.gzsx+" 已经完成翻译校对，无法重复提交！");
      flag=false
      break
    }
    if (item.fyzt==='9'){
      proxy?.$modal.msgError("公证事项："+item.gzsx+" 已经驳回翻译，无法操作！");
      flag=false
      break
    }
    if (item.fyzt==='0'){
      proxy?.$modal.msgError("公证事项："+item.gzsx+" 未上传译文，无法提交！");
      flag=false
      break
    }
    list.push({id:item.id,fyzt:1});
  }
  if(flag){
    batchUpdateList(list).then(response => {
      getList()
    })
  }

};


// 智能打印
const handlePrint = () => {
  ElMessage.success('正在准备打印');
  // 实际打印逻辑
};

// 新增费用
const handleAddFee = () => {
  nextTick(()=>feeFormRef.value?.clearValidate())
  if (gzjzId.value == undefined) {
    ElMessage.warning('请先选择翻译列表记录');
    return;
  }
  feeDialog.value.title = '新增费用';
  Object.assign(feeForm, {
    id: undefined,
    fylx: '',
    jjfs: '',
    sfcj: '1',
    sfjz: '0',
    fyys: undefined,
    fyjm: undefined,
    fyss: undefined,
    sfzt: '1',
    remark: '',
    gzjzId: gzjzId,
    gzjzGzsxId: undefined
  })
  feeDialog.value.visible = true;
  feeDialog.value.type = 'add';
};

// 处理费用编辑按钮点击
const handleFeeOperation = (row: any) => {
  nextTick(()=>feeFormRef.value?.clearValidate())
  // ElMessage.success(`编辑费用: ${row.gzjzGzsx}`);
  // 实际操作逻辑
  Object.assign(feeForm, { ...row })
  feeDialog.value.visible = true;
  feeDialog.value.title = '编辑费用';
  feeDialog.value.type = 'edit';
};

// 处理费用删除按钮点击
const handleFeeDel = async (row: any) => {
  // ElMessage.success(`删除费用: ${row.gzjzGzsx}`);
  // 实际操作逻辑
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除选中的数据项？');
  await delGzjzGzsxSfxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getFeeList();
};

// 提交费用表单
const submitFeeForm = async () => {
  console.log(feeForm);
  if (!feeFormRef.value) return;
  await feeFormRef.value.validate(async (valid) => {
    if (valid) {
      if (feeDialog.value.type === 'add') {
        addGzjzGzsxSfxx(feeForm).then((res) => {
          if (res.code === 200) {
            ElMessage.success('添加成功');
          }
        }).catch((err: any) => {
          ElMessage.error('添加收费项失败');
          console.log('添加收费项失败', err);
        }).finally(() => {
          feeDialog.value.visible = false;
        });
      }
      if (feeDialog.value.type === 'edit') {
        updateGzjzGzsxSfxx(feeForm).then((res) => {
          if (res.code === 200) {
            ElMessage.success('修改成功');
          }
        }).catch((err: any) => {
          ElMessage.error('修改收费项失败');
          console.log('修改收费项失败', err);
        }).finally(() => {
          feeDialog.value.visible = false;
        });
      }
      await getFeeList();
    }
  });
};

const handleFymx = (data, type) => {
  fymxDialogRef.value.handleFymx(data, type);
}

const fymxCallback = (data : any) => {
  getList(); // 刷新列表
}

// 提供给子组件的数据和方法
provide('currentRecordId', gzjzId);
provide('currentRecord', currentRecord);

// 组件挂载时
onMounted(() => {
  getList();
});
</script>

<style scoped>
.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.main-content {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.left-content {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.right-content {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.fee-detail {
  background-color: #fff;
  border-radius: 4px;
  padding-bottom: 15px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

.pagination-info {
  margin: 0 10px;
}

.pagination-input {
  width: 50px;
}

.pagination-select {
  width: 80px;
  margin-left: 10px;
}

.ml10 {
  margin-left: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
