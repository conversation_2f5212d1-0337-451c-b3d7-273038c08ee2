<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" append-to-body>
    <div v-loading="modelState.loading">
      <FqInfoForm v-model="fqData" ref="fqInfoFormRef" />
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting" type="primary">保存</el-button>
        <el-button v-if="fqData.id" @click="comfirmDel" :loading="modelState.submitting" :disabled="modelState.submitting" type="danger">删除</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import { GzjzFqForm, GzjzFqFormEdit } from '@/api/gongzheng/bzfz/gzjzFq/types';
import FqInfoForm from './FqInfoForm.vue';
import { addGzjzFq, delGzjzFq, getGzjzFqByGzjzGzsxId, listGzjzFq, updateGzjzFq } from '@/api/gongzheng/bzfz/gzjzFq';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '赋强公证信息'
})

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

const modelState = reactive({
  visible: false,
  title: '赋强公证信息',
  gzjzId: undefined,
  loading: false,
  submitting: false,
})

const fqData = ref<GzjzFqFormEdit>({})
const fqInfoFormRef = ref(null)

const gzjzGzsxInfo = ref<any>({})

const initData = async () => {
  try {
    modelState.loading = true;
    const params = {
      // gzjzId: modelState.gzjzId || props.gzjzId || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
      // pageNum: 1,
      // pageSize: 100
    }
    // const res = await listGzjzFq(params);
    const res = await getGzjzFqByGzjzGzsxId(params);
    if (res.code === 200 && res.data.id) {
      const data = res.data;
      const zwrIds = data.zwrIds != '' ? data.zwrIds?.split(',').map((i) => Number(i)) : [],
        zqrIds = data.zqrIds != '' ? data.zqrIds?.split(',').map((i) => Number(i)) : [],
        dlrIds = data.dlrIds != '' ? data.dlrIds?.split(',').map((i) => Number(i)) : [],
        dbrIds = data.dbrIds != '' ? data.dbrIds?.split(',').map((i) => Number(i)) : [];

      fqData.value = {
        ...data,
        zwrIds,
        zqrIds,
        dlrIds,
        dbrIds,
        dbfs: data.dbfs?.split(','),
      }
    }
  } catch (err: any) {
    console.error('赋强信息初始化失败', err)
  } finally {
    modelState.loading = false;
  }
}

const comfirmSave = async () => {
  try {
    const isValidOk = await fqInfoFormRef.value?.validate();
    if (!isValidOk) return;

    console.log('fq comfirmSave > ', fqData.value)

    const params: GzjzFqForm = {
      ...fqData.value,
      zwrIds: fqData.value.zwrIds?.join(','),
      zqrIds: fqData.value.zqrIds?.join(','),
      dlrIds: fqData.value.dlrIds?.join(','),
      dbrIds: fqData.value.dbrIds?.join(','),
      dbfs: fqData.value.dbfs?.join(','),
      gzjzId: modelState.gzjzId || curGzjz.value?.id || currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
    }
    let res = null;
    if(params.id) {
      res = await updateGzjzFq(params);
    } else {
      res = await addGzjzFq(params);
    }

    if (res.code === 200) {
      ElMessage.success('保存成功');
      close();
    }
  } catch (err: any) {
    console.error('赋强保存失败', err)
  } finally {

  }
}

const comfirmDel = () => {
  ElMessageBox.confirm('确认要删除该赋强信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    modelState.submitting = true;
    const res = await delGzjzFq(fqData.value.id);
    if(res.code === 200) {
      ElMessage.success('删除成功');
      close();
    }
  }).finally(() => {
    modelState.submitting = false;
  })
}

const close = () => {
  modelState.visible = false;
  modelState.gzjzId = undefined;
  gzjzGzsxInfo.value = {};
  fqData.value = {}
}

const closed = () => {
  modelState.gzjzId = undefined;
  gzjzGzsxInfo.value = {};
  fqData.value = {}
}

const open = (data?: any) => {
  modelState.visible = true
  modelState.gzjzId = data?.gzjzId || undefined;
  modelState.title = data?.title || '';
  gzjzGzsxInfo.value = data?.gzjzGzsxInfo || {};

  initData();
}

defineExpose({
  open
})

</script>
