import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzSdxxVO, GzjzSdxxForm, GzjzSdxxQuery } from '@/api/gongzheng/dev/gzjzSdxx/types';

/**
 * 查询公证卷宗-送达信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzSdxx = (query?: GzjzSdxxQuery): AxiosPromise<GzjzSdxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzSdxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-送达信息v1.0详细
 * @param id
 */
export const getGzjzSdxx = (id: string | number): AxiosPromise<GzjzSdxxVO> => {
  return request({
    url: '/gongzheng/gzjzSdxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-送达信息v1.0
 * @param data
 */
export const addGzjzSdxx = (data: GzjzSdxxForm) => {
  return request({
    url: '/gongzheng/gzjzSdxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-送达信息v1.0
 * @param data
 */
export const updateGzjzSdxx = (data: GzjzSdxxForm) => {
  return request({
    url: '/gongzheng/gzjzSdxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-送达信息v1.0
 * @param id
 */
export const delGzjzSdxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzSdxx/' + id,
    method: 'delete'
  });
};
