<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="当事人ID" prop="dsrId">
              <el-input v-model="queryParams.dsrId" placeholder="请输入当事人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人类型" prop="dsrLx">
              <el-select v-model="queryParams.dsrLx" placeholder="请选择当事人类型" clearable >
                <el-option v-for="dict in gz_dsr_lb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="角色" prop="js">
              <el-input v-model="queryParams.js" placeholder="请输入角色" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否读卡" prop="sfdk">
              <el-select v-model="queryParams.sfdk" placeholder="请选择是否读卡" clearable >
                <el-option v-for="dict in gz_sf_wy" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzDsr:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzDsr:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzDsr:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzDsr:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzDsrList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="当事人ID" align="center" prop="dsrId" />
        <el-table-column label="当事人类型" align="center" prop="dsrLx">
          <template #default="scope">
            <dict-tag :options="gz_dsr_lb" :value="scope.row.dsrLx"/>
          </template>
        </el-table-column>
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="角色" align="center" prop="js" />
        <el-table-column label="是否读卡" align="center" prop="sfdk">
          <template #default="scope">
            <dict-tag :options="gz_sf_wy" :value="scope.row.sfdk"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzDsr:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzDsr:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-当事人v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzDsrFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="当事人ID" prop="dsrId">
          <el-input v-model="form.dsrId" placeholder="请输入当事人ID" />
        </el-form-item>
        <el-form-item label="当事人类型" prop="dsrLx">
          <el-select v-model="form.dsrLx" placeholder="请选择当事人类型">
            <el-option
                v-for="dict in gz_dsr_lb"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="角色" prop="js">
          <el-input v-model="form.js" placeholder="请输入角色" />
        </el-form-item>
        <el-form-item label="是否读卡" prop="sfdk">
          <el-select v-model="form.sfdk" placeholder="请选择是否读卡">
            <el-option
                v-for="dict in gz_sf_wy"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzDsr" lang="ts">
import { listGzjzDsr, getGzjzDsr, delGzjzDsr, addGzjzDsr, updateGzjzDsr } from '@/api/gongzheng/dev/gzjzDsr';
import { GzjzDsrVO, GzjzDsrQuery, GzjzDsrForm } from '@/api/gongzheng/dev/gzjzDsr/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_dsr_lb, gz_sf_wy } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_sf_wy'));

const gzjzDsrList = ref<GzjzDsrVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzDsrFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzDsrForm = {
  id: undefined,
  dsrId: undefined,
  dsrLx: undefined,
  gzjzId: undefined,
  js: undefined,
  sfdk: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzDsrForm, GzjzDsrQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dsrId: undefined,
    dsrLx: undefined,
    gzjzId: undefined,
    js: undefined,
    sfdk: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-当事人v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzDsr(queryParams.value);
  gzjzDsrList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzDsrFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzDsrVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-当事人v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzDsrVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzDsr(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-当事人v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzDsrFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzDsr(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzDsr(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzDsrVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-当事人v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzDsr(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzDsr/export', {
    ...queryParams.value
  }, `gzjzDsr_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
