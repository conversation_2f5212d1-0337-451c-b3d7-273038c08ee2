import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SyzdjxxVO, SyzdjxxForm, SyzdjxxQuery, SyzLyFfForm } from '@/api/gongzheng/syz/syzdjxx/types';

/**
 * 查询水印纸入库列表
 * @param query
 * @returns {*}
 */

export const listSyzdjxx = (query?: SyzdjxxQuery): AxiosPromise<SyzdjxxVO[]> => {
  return request({
    url: '/gongzheng/syzdjxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询水印纸入库详细
 * @param id
 */
export const getSyzdjxx = (id: string | number): AxiosPromise<SyzdjxxVO> => {
  return request({
    url: '/gongzheng/syzdjxx/' + id,
    method: 'get'
  });
};

/**
 * 新增水印纸入库
 * @param data
 */
export const addSyzdjxx = (data: SyzdjxxForm) => {
  return request({
    url: '/gongzheng/syzdjxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改水印纸入库
 * @param data
 */
export const updateSyzdjxx = (data: SyzdjxxForm) => {
  return request({
    url: '/gongzheng/syzdjxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除水印纸入库
 * @param id
 */
export const delSyzdjxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/syzdjxx/' + id,
    method: 'delete'
  });
};

/**
 * 查询可领用的信息
 * @param id
 */
export const queryAvailable = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/syzdjxx/queryAvailable/' + id,
    method: 'get'
  });
};

/**
 * 水印纸入库-领用
 * @param id
 */
export const receive = (data: SyzLyFfForm) => {
  return request({
    url: '/gongzheng/syzdjxx/receive',
    method: 'post',
    data: data
  });
};

/**
 * 水印纸入库-分配
 * @param id
 */
export const distribute = (data: SyzLyFfForm) => {
  return request({
    url: '/gongzheng/syzdjxx/distribute',
    method: 'post',
    data: data
  });
};
