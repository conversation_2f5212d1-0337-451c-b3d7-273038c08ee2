<template>
  <div>
    <el-table ref="listRef" v-loading="loading" :data="gzjzJbxxList" @selection-change="handleSelectionChange"
      @row-dblclick="handleRowDblclick" border stripe height="500" style="width: 100%">
      <el-table-column type="selection" width="45" />
      <el-table-column label="操作" :width="actionWidth + 'px'" fixed="left">
        <template #default="scope">
          <slot name="my-actions" :row="scope.row">

          </slot>
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('ajtx') " prop="ajtx" label="案件提醒" width="190" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <!-- <el-text type="danger" v-if="timeDiff(row.slrq, new Date()) > 1">(超期)</el-text> -->
           <div class="flex flex-wrap gap-4px">
             <el-text type="danger" v-if="row.isOverdue == 1" size="small">(超期)</el-text>
             <el-text type="danger" v-if="row.isCharge == 1" size="small">(未收费)</el-text>
             <el-button @click="handleInvestigate(row)" type="primary" v-if="row.isInvestigate == 1" link size="small">(调查结果)</el-button>
             <el-button @click="handleReject(row)" type="primary" v-if="row.isReject == 1" link size="small">(驳回)</el-button>
           </div>
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('remark') " prop="remark" label="备注" width="80" show-overflow-tooltip>
        <template #default="scope">
          <slot name="my-remarks" :row="scope.row">

          </slot>
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('jzbh') " prop="jzbh" label="卷宗号" width="120" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('sqrxm') " prop="sqrxm" label="申请人" width="120" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('dsrxm') " prop="dsrxm" label="当事人" width="120" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('gzsbh') " prop="gzsbh" label="公证书编号" width="150" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('lb') " prop="lb" label="公证类别" width="150" show-overflow-tooltip >
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_gzlb, row.lb) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('gzsx') " prop="gzsx" label="公证事项" width="150" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('sqsj') " prop="sqsj" label="申请日期" width="120" :formatter="formatDate" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('slrq') " prop="slrq" label="受理日期" width="120" :formatter="formatDate" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('gzyxm') " prop="gzyxm" label="公证员" width="100" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('zlxm') " prop="zlxm" label="助理/受理人" width="120" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('qmz') " prop="qmz" label="签名章" width="100" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('spsj') " prop="spsj" label="审批日期" width="120" :formatter="formatDate" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('czsj') " prop="czsj" label="出证日期" width="120" :formatter="formatDate" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('dyzt')" prop="dyzt" label="是否打印" width="100">
        <template #default="{ row }">
          <dict-tag :options="gz_yes_or_no" :value="row.dyzt" />
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('lczt') " prop="lczt" label="流程状态" width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <dict-tag :options="gz_sl_lczt" :value="row.lczt" />
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('xbrxm') " prop="xbrxm" label="协办人" width="100" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('sprxm')" prop="sprxm" label="审批人" width="100" show-overflow-tooltip />
      <el-table-column v-if="ifColumns('sfzf')" prop="sfzf" label="是否作废" width="100">
        <template #default="{ row }">
          <dict-tag :options="gz_yes_or_no" :value="row.sfzf" />
        </template>
      </el-table-column>
      <el-table-column v-if="ifColumns('wbddh')" prop="wbddh" label="外部订单号" width="150" show-overflow-tooltip />

      <el-table-column v-if="ifColumns('hczt')" prop="hczt" label="是否合成" width="100">
        <template #default="{ row }">
          <dict-tag :options="gz_yes_or_no" :value="row.hczt" />
        </template>
      </el-table-column>

      <el-table-column v-if="ifColumns('ywlx')" prop="ywlx" label="案件类型" width="100">
        <template #default="{ row }">
          <dict-tag :options="gz_ywlx" :value="row.ywlx" />
        </template>
      </el-table-column>

    </el-table>

    <pagination v-show="total > 0" v-model:page="thatQueryParams.pageNum" v-model:limit="thatQueryParams.pageSize"
      :total="total" @pagination="getList" :size="'small'" />

      <Dcjg ref="dcjgRef" :gzjz-id="gzjzId" />
      <BhLog v-model="bhLogShow" :gzjz-id="gzjzId" />
  </div>
</template>

<script setup name="GeneralProcessTable" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { listGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import type { GzjzJbxxVO, GzjzJbxxQuery } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import { timeDiff, dictMapFormat } from '@/utils/ruoyi';
  import Dcjg from '@/views/gongzheng/gongzheng/components/Dcjg/index.vue'
  import BhLog from '@/views/gongzheng/gongzheng/components/BhLog/index.vue'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const props = defineProps({
    viewMoudle: {
      type: String,
      default: '', // 受理 sl, 审批 sp, 制证 zz, 发证 fz, 归档 gd
      required: false
    },
    columns: {
      type: Array<String>,
      default: ['ajtx','remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'sqsj', 'gzyxm', 'zlxm', 'qmz', 'slrq', 'czsj', 'lczt', 'sprxm', 'xbrxm', 'sfzf', 'wbddh'],
      required: false
    },
    actionWidth: {
      type: Number,
      default: 150,
      required: false
    }
  });

  // 由父组件传入的卷宗信息
  const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

  const columnsByMoudle = ref({
    'sl':['ajtx','remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'sqsj', 'gzyxm', 'zlxm', 'qmz', 'slrq', 'czsj', 'lczt', 'xbrxm', 'sfzf', 'wbddh'],
    'sp':['ajtx','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'gzyxm', 'zlxm', 'slrq', 'czsj', 'lczt', 'xbrxm', 'spsj', 'sprxm' ],
    'zz':['ajtx','remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'gzyxm', 'zlxm', 'qmz', 'slrq', 'czsj', 'spsj', 'lczt', 'xbrxm','dyzt','hczt'],
    'fz':['ajtx','remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'gzyxm', 'zlxm', 'slrq', 'czsj', 'spsj', 'lczt', 'xbrxm','ywlx'],
    'gd':['remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'gzyxm', 'zlxm', 'slrq', 'czsj', 'spsj', 'lczt', 'xbrxm',]
    });
  const columnsByBase = ref(['ajtx','remark','jzbh', 'sqrxm', 'dsrxm', 'gzsbh', 'lb', 'gzsx', 'sqsj', 'gzyxm', 'zlxm', 'slrq', 'czsj', 'lczt', 'sprxm', 'xbrxm', 'sfzf', 'wbddh']);

  const { columns, viewMoudle, actionWidth } = toRefs(props);

  const {
    gz_gzs_zh, gz_ajly, gz_gzs_bh_jg, gz_sl_jjcd, gz_dalx, gz_flyz, gz_sf_wy, gz_sl_syd, gz_yw_wz, gz_sl_lczt, gz_yt, gz_rz_zt, gz_gzlb, gz_ywly, gz_yes_or_no, gz_ywlx, gz_gzjz_jafs
  } = toRefs<any>(proxy?.useDict(
    'gz_gzs_zh', 'gz_ajly', 'gz_gzs_bh_jg', 'gz_sl_jjcd', 'gz_dalx', 'gz_flyz', 'gz_sf_wy', 'gz_sl_syd', 'gz_yw_wz', 'gz_sl_lczt', 'gz_yt', 'gz_rz_zt', 'gz_gzlb', 'gz_ywly', 'gz_yes_or_no', 'gz_ywlx', 'gz_gzjz_jafs'
  ));

  const myActions = ref('myActions');

  const loading = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);

  const gzjzId = ref<string | number>('');
  const dcjgRef = ref(null);
  const bhLogShow = ref(false);

  const handleInvestigate = (row : GzjzJbxxVO) => {
    gzjzId.value = row.id;
    curGzjz.value = row;
    dcjgRef.value?.open({
      gzjzId: row.id,
    })
  }

  const handleReject = (row : GzjzJbxxVO) => {
    gzjzId.value = row.id;
    bhLogShow.value = true;
    curGzjz.value = row;
  }

  /** 日期格式化 */
  const formatDate = (row : any, column : any, cellValue : string) => {
    if (cellValue) {
      const date = new Date(cellValue), year = date.getFullYear(),
        month = String(date.getMonth() + 1).padStart(2, '0'),
        day = String(date.getDate()).padStart(2, '0');
      return `${year}年${month}月${day}日`;
    }
    return '';
  };

  // 查询参数
  const thatQueryParams = ref<GzjzJbxxQuery>({
    pageNum: 1,
    pageSize: 10,
    lczt: '',
    sfzf: '0'
  });

  const ifColumns = (column : string) => {
    let thisColumns = columnsByBase.value;
    const _key = viewMoudle.value;
    const _columns = columnsByMoudle.value;
    if(_key != '' && _columns[_key]){
      thisColumns = _columns[_key];
    }
    return thisColumns.indexOf(column) > -1;
  }

  /** 查询卷宗基本信息列表 */
  const getList = async (_queryParams ?: GzjzJbxxQuery) => {
    loading.value = true;
    //const queryParams = {...thatQueryParams.value};
    if(_queryParams){
      Object.assign(thatQueryParams.value, _queryParams);
    }
    console.log('发送查询参数:', thatQueryParams.value);
    try {
      const res = await listGzjzJbxx(thatQueryParams.value);
      gzjzJbxxList.value = res.rows;
      total.value = res.total;
    } catch (error : any) {
      proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzjzJbxxVO[]) => {
    ids.value = selection.map((item : GzjzJbxxVO) => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
    emit('selection-change', ids.value, single.value, multiple.value);
  };
  // 双击行查看详情
  const handleRowDblclick = (row : GzjzJbxxVO) => {
    if (row) {
      emit('row-dblclick', row);
    }
  };

  // 回调方法
  const emit = defineEmits(['selection-change', 'row-dblclick'])
  // 暴露方法给父组件
  defineExpose({
    getList,
  })
  onMounted(() => {

  })
</script>

<style>
</style>
