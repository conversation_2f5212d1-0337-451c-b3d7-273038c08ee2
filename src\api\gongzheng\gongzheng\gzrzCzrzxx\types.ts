export interface GzrzCzrzxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId: string | number;

  /**
   * 当事人姓名
   */
  dsrxm: string;

  /**
   * 公证事项（事务）
   */
  gzsx: string;

  /**
   * 日志内容
   */
  rznr: string;

  /**
   * 操作时间
   */
  czsj: string;

  /**
   * 操作人
   */
  czr: string;

}

export interface GzrzCzrzxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 当事人姓名
   */
  dsrxm?: string;

  /**
   * 公证事项（事务）
   */
  gzsx?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作时间
   */
  czsj?: string;

  /**
   * 操作人
   */
  czr?: string;

}

export interface GzrzCzrzxxQuery extends PageQuery {

  /**
   * 公证卷宗ID(关联公证卷宗)
   */
  gzjzId?: string | number;

  /**
   * 当事人姓名
   */
  dsrxm?: string;

  /**
   * 公证事项（事务）
   */
  gzsx?: string;

  /**
   * 日志内容
   */
  rznr?: string;

  /**
   * 操作时间
   */
  czsj?: string;

  /**
   * 操作人
   */
  czr?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



