<template>
  <div class="ndgzs-wrap">
    <el-row :gutter="12" style="height: 100%;">
      <el-col :span="10" style="height: 100%;">
        <div class="left-col">
          <el-card>
            <div class="flex items-center gap-12px flex-wrap">
              <span class="flex items-center">
                <el-text size="small">卷宗号：</el-text>
                <el-text type="info" size="small">{{ curGzjz.jzbh || '-' }}</el-text>
              </span>
              <span class="flex items-center">
                <el-button @click="openJzDetail" type="primary" size="small" link>【详情】</el-button>
              </span>
              <span class="flex items-center">
                <el-text size="small">公证类别：</el-text>
                <el-text type="info" size="small">{{ dictMapFormat(gz_gzlb, curGzjz.lb) }}</el-text>
              </span>
              <span class="flex items-center">
                <el-text size="small">公证员：</el-text>
                <el-text type="info" size="small">{{ curGzjz.gzyxm }}</el-text>
              </span>
              <span class="flex items-center">
                <el-text size="small">助理：</el-text>
                <el-text type="info" size="small">{{ curGzjz.zlxm }}</el-text>
              </span>
              <span class="flex items-center">
                <el-text size="small">申请日期：</el-text>
                <el-text type="info" size="small">{{ formatDate(curGzjz.sqsj) }}</el-text>
              </span>
              <span class="flex items-center">
                <el-text size="small">受理日期：</el-text>
                <el-text type="info" size="small">{{ formatDate(curGzjz.slrq) }}</el-text>
              </span>
            </div>
          </el-card>

          <el-card style="flex: 2;">
            <template #header>
              <div class="gzsx-header">
                <span>公证事项</span>
                <div class="btn-group">
                  <!-- <el-button type="primary" size="small">废号分配</el-button> -->
                  <el-button @click="gzsxGetNumber" :loading="gzsxState.takeNumberLoading" type="primary" size="small">取号</el-button>
                  <el-button @click="gzsxRevocationNumber" :loading="gzsxState.revocationNumberLoading" :disabled="gzsxState.multiple" type="primary" size="small">销号</el-button>
                </div>
              </div>
            </template>
            <div style="height: calc(100% - 46px);">
              <el-table ref="gzsxTbRef" :data="gzsxState.listData" style="width: 100%;height: 100%;" size="small" v-loading="gzsxState.loading" border @selection-change="handleSelectionChangeGzsxState">
                <el-table-column type="index" width="45" align="center"/>
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="gzsxMc" label="公证事项" min-width="120" align="center" show-overflow-tooltip/>
                <el-table-column prop="gzsBh" label="公证书编号" min-width="150" align="center" show-overflow-tooltip/>
                <el-table-column prop="gzsMc" label="公证书" min-width="120" align="center" show-overflow-tooltip>
                  <template #default="{ row }">
                    <el-button @click="() => genGzs(row)" :loading="gzsxState.btnLoading" type="primary" v-if="!row.gzsFile?.wblj" size="small" link>新建</el-button>
                    <el-button @click="() => openGzsDoc(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.wblj" size="small" link>证词</el-button>
                    <el-button @click="() => delGzsDoc(row)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.wblj" size="small" link>删除</el-button>
                  </template>
                </el-table-column>
                <el-table-column label="译文" min-width="120" align="center" show-overflow-tooltip>
                  <template #default="{ row }">
                    <el-button @click="() => genGzs(row, true)" :loading="gzsxState.btnLoading" type="primary" v-if="!row.gzsFile?.ywlj" size="small" link>新建</el-button>
                    <el-button @click="() => openGzsDoc(row, true)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.ywlj" size="small" link>译文</el-button>
                  <el-button @click="() => delGzsDoc(row, true)" :loading="gzsxState.btnLoading" type="primary" v-if="row.gzsFile?.ywlj" size="small" link>删除</el-button>
                </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <el-card style="flex: 2;">
            <SubDataMod ref="SubRef" :except-pane="['notaryMatters']" use-dsr />
          </el-card>
        </div>
      </el-col>
      <el-col :span="14" style="height: 100%;">
        <el-card style="height: 100%;">
          <el-tabs v-model="docState.activeName" @tab-click="handleTabClick" @tab-change="tabChange">
            <!-- <el-tab-pane label="公证词" name="gzc">
              <div class="right-pane-content">
                <el-input type="area" style="height: 100%;" />
              </div>
            </el-tab-pane> -->
            <el-tab-pane label="办证流程文档" name="lcwd">
              <div class="right-pane-content">
                <div class="pane-header">
                  <strong>办证流程文档</strong>
                  <!-- <el-button type="primary" size="small">一键生成</el-button> -->
                  <!-- <el-button type="primary" size="small" disabled>一键删除</el-button> -->
                </div>
                <el-table :data="ndgzsDocTypeList" v-loading="docState.loading" size="small" width="100%" border>
                  <el-table-column label="文档类型" prop="typeName" width="150" align="center"/>
                  <el-table-column label="状态" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag v-if="row.docList?.length > 0" size="small" type="success">已拟定</el-tag>
                      <el-tag v-else size="small" type="info">未拟定</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" align="center">
                    <template #default="{ row }">
                      <el-button type="primary" link size="small" @click="createDocSwitch(row)">{{ operateMapName(row) }}</el-button>
                      <!-- <el-button type="primary" link size="small" @click="genDoc(row)">上传</el-button> -->
                    </template>
                  </el-table-column>
                  <el-table-column label="名称">
                    <template #default="{ row }">
                      <div class="flex flex-wrap gap-4px">
                        <el-tag v-for="item in row.docList" :key="item.id" size="small">
                          <el-button type="primary" link @click="handleOpen(item)" size="small">{{item.wbmc}}</el-button>
                          <el-button type="danger" link icon="Delete" @click="handleDelete(item, 'wdnd')" style="margin-left: 4px;"></el-button>
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            <el-tab-pane label="代书" name="ds">
              <div class="right-pane-content">
                <el-table :data="dsList" v-loading="docState.loading" size="small" width="100%" border>
                  <el-table-column prop="gzsxMc" label="公证事项" width="120" show-overflow-tooltip/>
                  <el-table-column prop="gzsBh" label="公证书编号" width="180" show-overflow-tooltip/>
                  <el-table-column label="文书">
                    <template #default="{ row }">
                      <div class="flex flex-wrap gap-4px">
                        <el-tag v-for="item in row.wsList" :key="item.id" size="small">
                          <el-button type="primary" link @click="handleOpen(item)" size="small">{{item.wbmc}}</el-button>
                          <!-- <el-button type="danger" link icon="Delete" @click="handleDelete(item, 'ds')" style="margin-left: 4px;"></el-button> -->
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="译文">

                  </el-table-column>
                </el-table>
              </div>
              <div class="ds-btns">
                <el-button type="primary" size="small" @click="dsViewState.visible = true">编辑代书</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="证据材料" name="zjcl">
              <div class="right-pane-content">
                <div class="pane-header">
                  <strong>证据明细</strong>
                  <!-- <span>
                    <el-text>分类：</el-text>
                    <el-select size="small" style="width: 160px;">
                      <el-option value="1">全部</el-option>
                    </el-select>
                  </span> -->
                </div>
                <el-table :data="docState.zjclListData" v-loading="docState.loading" width="100%" size="small" border>
                  <el-table-column type="index" width="55" align="center"/>
                  <el-table-column prop="zmmc" label="证据名称" align="center" width="140"/>
                  <el-table-column label="文件名称" show-overflow-tooltip>
                    <template #default="{ row, column }">
                      <div class="flex flex-wrap gap-6px">
                        <el-tag v-for="item in row.zmclxxMxList" :key="item.id" size="small">
                          <el-button type="primary" link @click="zjclPreview(item)" size="small">{{item.xxmc}}</el-button>
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog v-model="gzsGenState.visible" :title="gzsGenState.title" @closed="gzsGenClosed" draggable :modal="false" show-close destroy-on-close width="400">
      <div class="h-100px flex items-center justify-center">
        <div class="flex items-center justify-center gap-10px">
          <strong>文档模版：</strong>
          <el-select v-model="gzsGenState.mbId" default-first-option filterable style="width: 200px;">
            <el-option v-for="item in gzsGenState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-end gap-10px">
          <el-button v-if="gzsGenState.isGenGzs" type="primary" @click="comfirmGzsGen" :loading="gzsGenState.loading" :disabled="gzsGenState.loading">生成公证书</el-button>
          <el-button v-else type="primary" @click="docGen" :loading="gzsGenState.loading" :disabled="gzsGenState.loading">确认生成</el-button>
          <el-button @click="gzsGenClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <DsView v-model="dsViewState.visible" v-if="dsViewState.visible" @closed="dsClosed"/>

    <Wdnd v-model="wdndShow" v-if="wdndShow" @closed="wdndOrBlClosed" />

    <Bl v-model="blShow" v-if="blShow" @closed="wdndOrBlClosed" />

    <DragUpload v-model="uploadState.visible" v-if="uploadState.visible" :title="uploadState.title" @on-everyone-done="uploadEveryDone" @on-all-done="uploadAllDone" />

    <ImgShower v-model="previewState.imgVisible" v-if="previewState.imgVisible" :url="previewState.zjclImgUrl" @on-closed="onImgShowClosed"/>
    <PdfShower v-if="pdfState.visible" v-model="pdfState.visible" :url="pdfState.url" />
    <MediaPlayer ref="mediaPlayerRef" />
  </div>
</template>

<script lang="ts" setup>
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { inject, ref, watch, onMounted, getCurrentInstance } from 'vue';
import type { Ref } from 'vue';
import { formatDate, dictMapFormat, nodeFilter } from '@/utils/ruoyi';
import { GzjzGzsxVO, GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
import { gzsxTakeNumber, revocationGzsNumber, listGzjzGzsx } from '@/api/gongzheng/gongzheng/gzjzGzsx';
import { GzjzDsrForm, GzjzDsrQuery, GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { addGzjzDsr, listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue';
import { queryGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';
import DragUpload from '@/components/FileUpload/DragUpload.vue';
import ImgShower from '@/components/DocShower/ImgShower.vue';
import DsView from '@/views/gongzheng/gongzheng/components/sl/ds/index.vue';
import Wdnd from '@/views/gongzheng/gongzheng/components/sl/wdnd/WdndDialog.vue';
import Bl from '@/views/gongzheng/gongzheng/components/sl/bl/index.vue';
import { UploadStatus } from '@/components/FileUpload/type';
import SubDataMod from '@/views/gongzheng/gongzheng/components/sl/wdnd/SubDataMod.vue'
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import { docGenerator, docOpenEdit, openDoc } from '@/views/gongzheng/doc/DocEditor';
import { addGzjzWjccxx, delGzjzWjccxx, listGzjzWjccxx, updateGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { ndgzsDocTableData } from '../preset_data';
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';
import { UserDocGenParams } from '@/views/gongzheng/doc/type';

interface NdgzsProps {
  data?: GzjzJbxxVO;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzlb, gz_dsr_jslx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_dsr_jslx', 'gz_dsr_lb'));

const props = withDefaults(defineProps<NdgzsProps>(), {
  data: null,
});

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const gzsxState = reactive({
  loading: false,
  listData: [],
  ids: [],
  multiple: false,
  btnLoading: false,
  takeNumberLoading: false,
  revocationNumberLoading: false
})

const dsList = ref([]);
const dsViewState = reactive({
  visible: false,
})

const wdndShow = ref(false);
const blShow = ref(false);

const dsrState = reactive({
  loading: false,
  listData: [],
  multiple: false
})

const docState = reactive({
  activeName: 'lcwd',
  curDoclb: '1',  // 附件类别(1文档拟定（流程文档），3笔录，4代书(文书)，9其他)
  loading: false,
  listData: [],   // 流程文档列表
  dsListData: [], // 代书列表
  zjclListData: [], // 证据材料列表
})

// 上传相关状态
const uploadState = reactive({
  loading: false,
  visible: false,
  title: '上传',
  docType: '',
  docId: '',
  sxRow: null,
})
const previewState = reactive({
  pdfVisible: false,
  wordVisible: false,
  imgVisible: false,
  title: '文档预览',
  docUrl: '',
  wordHtml: '',
  zjclImgUrl: ''
})

const pdfState = reactive({
  visible: false,
  url: '',
})

const mediaPlayerRef = ref(null);

const yydsrRef = ref(null);
const yydsrState = reactive({
  visible: false,
  editable: false,
  selectedDsrIds: [] as string[],
  loading: false,
  selectedDsr: null as GzjzDsrVO | null
})

const jzDetailState = reactive({
  visible: false,
})

const ndgzsDocTypeList = ref(ndgzsDocTableData);

const SubRef = ref(null);

const gzsxTbRef = ref<ElTableInstance>(null);

const gzsGenState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  gzsxInfo: null,
  typeData: [],
  mbId: '',
  genYw: false,

  isGenGzs: false,
  gzsOssId: '',
})

const openJzDetail = () => {
  jzDetailState.visible = true;
}

//const takeNumberLoading = ref(false);
// 公证事项取号
const gzsxGetNumber = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    ElMessage.error('数据异常，取号失败')
    return
  }

  try {
    gzsxState.takeNumberLoading = true;
    const res = await gzsxTakeNumber({ gzjzId: currentRecordId.value });
    if (res && res.code === 200) {
      ElMessage.success('取号成功');
    }
  } catch (err: any) {
    console.error('取号失败:', err);
    ElMessage.error('取号失败');
  } finally {
    gzsxState.takeNumberLoading = false;
    loadGzsxList();
  }
}

const gzsxRevocationNumber = async () => {
  try {
    gzsxState.revocationNumberLoading = true;
    const res = await revocationGzsNumber({ gzjzId: currentRecordId.value, ids: gzsxState.ids });
    if (res && res.code === 200) {
      ElMessage.success('销号成功');
    }
  } catch (err: any) {
    console.error('销号失败:', err);
    ElMessage.error('销号失败');
  } finally {
    gzsxState.revocationNumberLoading = false;
    loadGzsxList();
  }
}

const handleSelectionChangeGzsxState = (selection : GzjzGzsxVO[]) => {
  gzsxState.ids = selection.map((item : GzjzGzsxVO) => item.id);
  gzsxState.multiple = !selection.length;
}

const handleUpdateDsrSelectId = (data : any) => {
  if (Array.isArray(data)) {
    yydsrState.selectedDsrIds = data
  } else {
    // 兼容传入单个对象的情况
    yydsrState.selectedDsr = data
    yydsrState.selectedDsrIds = data.id ? [data.id] : []
  }

  console.log('引用选中的当事人信息:', data)
}

// tab页面点击切换
const handleTabClick = (tab: any, event: Event) => {}

const tabChange = (tabName: string) => {
  switch (tabName) {
    case 'lcwd':
      loadDocList();
    break;

    case 'ds':

    break;

    case 'zjcl':
      loadZmclList('2');
    break;
  }
}

// 确认引用当事人
const confirmYydsr = async () => {
  const selectedDsrInfo = yydsrRef.value?.getSelectedDsrInfo?.() || yydsrState.selectedDsr
  if (!yydsrState.selectedDsrIds.length || !selectedDsrInfo) {
    ElMessage.warning('请选择要引用的当事人');
    return;
  }

  try {
    yydsrState.loading = true;

    const formData : GzjzDsrForm = {
      dsrId: selectedDsrInfo.dsrId,
      dsrLx: selectedDsrInfo.dsrlb === '2' ? '2' : '1', // dsrlb: '1'个人 '2'单位,
      gzjzId: curGzjz.value.id || currentRecordId.value!,
      js: '1', // 默认申请人
      sfdk: '0',
      remark: '',
      zrrBo: selectedDsrInfo,
    }
    const res = await addGzjzDsr(formData);
    if (res && res.code === 200) {
      ElMessage.success('引用当事人成功');
      loadDsrList();
      yydsrState.visible = false;
    } else {
      ElMessage.error('引用当事人失败');
    }
  } catch (err: any) {
    console.error('引用当事人失败:', err);
    ElMessage.error('引用当事人失败');
  } finally {
    yydsrState.loading = false;
    // yydsrState.visible = false;
  }
}

// 加载公证事项列表
const loadGzsxList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    gzsxState.listData = []
    return;
  };
  gzsxState.loading = true;
  try {
    const queryParams : GzjzGzsxQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 200 // 获取所有数据
    };

    const res = await listGzjzGzsx(queryParams);
    if(res && res.code === 200) {
      gzsxState.listData = res.rows || [];
      loadGzsDocList();
      // dsList.value = gzsxState.listData.filter((item: GzjzGzsxVO) => !!item.gzsBh && item.gzsBh !== '');
      dsList.value = [...gzsxState.listData];
      loadDsDocList();
    }

  } catch (err: any) {
    console.log('加载公证事项列表失败', err);
    ElMessage.error('加载公证事项列表失败');
  } finally {
    gzsxState.loading = false;
  }
}

// 加载当事人列表
const loadDsrList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    dsrState.listData = []
    return;
  };
  dsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        dsrState.listData = res.rows || [];
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    dsrState.loading = false;
  }
}

// 打开公证书
const openGzsDoc = (data: any, yw: boolean = false) => {
  let docInfo = null;
  if(yw) {
    // 公证书译文
    docInfo = JSON.parse(data.gzsFile.ywlj || '{}');
  } else {
    // 公证书
    docInfo = JSON.parse(data.gzsFile.wblj || '{}');
  }

  if(docInfo?.path) {
    docOpenEdit(docInfo.path)
  }
}

// 删除公证书
const delGzsDoc = (data: any, yw: boolean = false) => {
  let { id, wblj, ywlj } = data?.gzsFile || {};
  if(!id) {
    ElMessage.error('获取公证书信息错误')
    return;
  }

  if(yw) {
    // 删除公证书译文
    if(!wblj) {
      // 如果没有原文，则直接删除文档信息
      comfirmDelGzsDoc(id)
    } else {
      // 如果有原文，则只删除译文
      updateGzsDocInfo({
        id,
        ywlj: '',
        ywmc: '',
      })
    }
  } else {
    // 删除公证书
    if(!ywlj) {
      comfirmDelGzsDoc(id)
    } else {
      // 如果有译文，则只删除原文
      updateGzsDocInfo({
        id,
        wblj: '',
        wbmc: '',
      })
    }
  }
}

// 删除公证书文档信息
const comfirmDelGzsDoc = (id: string | number) => {
  ElMessageBox.confirm('确定删除该公证书吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadGzsDocList();
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
  }).catch(() => {})

}

// 修改公证书文档信息
const updateGzsDocInfo = (data: any) => {
  updateGzjzWjccxx(data).then((res) => {
    if(res.code === 200) {
      ElMessage.success('修改成功');
      loadGzsDocList();
    }
  }).catch((err: any) => {
    ElMessage.success('修改失败');
    console.log('修改公证书文档失败', err, data)
  })
}

// 新建公证书
const genGzs = async (data: GzjzGzsxVO, yw: boolean = false) => {
  console.log('公证事项', data)
  if(!data.gzsBh) {
    ElMessage.warning('请先取号后再生成公证书');
    return;
  }
  const mbType = {
    typeCode: 3,  // 公证书 3，公证书译文 5
    typeName: yw ? '公证书译文' : '公证书'
  }
  const { gzsxId } = data
  gzsGenState.isGenGzs = true;

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  queryMbFiles({ wdLb: mbType.typeCode, ywId: gzsxId as string }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        gzsGenState.typeData = res.data;
        gzsGenState.mbId = res.data[0].id;
        gzsGenState.sxRow = mbType;
        gzsGenState.gzsxInfo = data;
        gzsGenState.visible = true;
        gzsGenState.title = `${mbType.typeName}${yw ? '译文' : ''} 文档生成`;
        gzsGenState.genYw = yw;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

// 确认生成公证书
const comfirmGzsGen = async () => {
  if (!gzsGenState.mbId) {
    ElMessage.warning('未选择生成指定模版')
    return;
  }

  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || [])
  const dsrIds = dsrIdArr.map((dsr: GzjzDsrVO) => dsr.dsrId).join(',')
  const dsr = dsrIdArr.map((dsr: GzjzDsrVO) => {
    return {
      dsrId: dsr.dsrId,
      dsrLx: dsr.dsrLx,
      js: dsr.js,
    }
  })

  const mbWdId = gzsGenState.mbId;
  if(!mbWdId) {
    ElMessage.error('获取模版信息错误')
    console.log('获取模版信息错误 mbWdId: ', mbWdId)
    return;
  }

  let params: UserDocGenParams = {
    bizId: (curGzjz.value.id || currentRecordId.value) as string,
    // type: gzsGenState.sxRow.typeCode,
    // fjlb: EnumDocFileType.QT,
    mbWdId,
    extraParams: {
      dsrIds,
      dsr,
      gzxsId: gzsGenState.gzsxInfo.gzsxId,
    }
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  gzsGenState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      const { gzsxId, id, gzsxMc, gzsBh } = gzsGenState.gzsxInfo;
      const { typeCode, typeName } = gzsGenState.sxRow;
      const fileName = `${typeName}_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`;
      let docInfo: GzjzWjccxxForm = {
        lx: '3',
        gzjzGzsxId: id, // gzjzGzsxId
        gzsx: gzsxMc,
        gzsbh: gzsBh,
      }

      if(gzsGenState.genYw) {
        docInfo = {
          ...docInfo,
          ywmc: fileName,
          ywlj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            typeCode,
            typeName,
            mbWdId: gzsGenState.mbId,
            gzsx: gzsxMc,
            gzsxId,
            gzjzGzsxId: id,
            dsrIds,
          }),
        }
      } else {
        docInfo = {
          ...docInfo,
          wbmc: fileName,
          wblj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            typeCode,
            typeName,
            mbWdId: gzsGenState.mbId,
            gzsx: gzsxMc,
            gzsxId,
            gzjzGzsxId: id,
            dsrIds,
          })
        }
      }
      relateGzsDoc(docInfo);
      gzsGenState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败')
  }).finally(() => {
    gzsGenState.loading = false;
    loading.close()
  })
}

// 添加生成后的文档（关联生成文档）
const relateGzsDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    console.log('关联生成文档参数', params)
    let res = null;
    const { gzsFile } = gzsGenState.gzsxInfo;
    if(!gzsFile) {
      res = await addGzjzWjccxx(params);
    } else if(gzsGenState.genYw) {
      // 关联生成公证书译文
      res = await updateGzjzWjccxx({
        id: gzsFile.id,
        ywmc: params.ywmc,
        ywlj: params.ywlj
      })
    } else if(!gzsGenState.genYw) {
      // 关联生成公证书
      res = await updateGzjzWjccxx({
        id: gzsFile.id,
        wblj: params.wblj,
        wbmc: params.wbmc
      })
    }
    if(res.code === 200) {
      ElMessage.success('文档添加成功')
      loadGzsDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

const loadGzsDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      lx: '3',  // 公证书 3, 公证书译文 5
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeGzsDoc((res.rows || []))
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

const placeGzsDoc = (list: any[]) => {
  let source: any[] = list
  gzsxState.listData.map((item: any) => {

    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj || node.ywlj || '{}');
      return (item.id === obj.gzjzGzsxId) || (item.id === node.gzjzGzsxId);
    });

    // let gzs = [], gzsYw = [];
    // matches.forEach(item => {
    //   if(item.wblj) {
    //     gzs.push(item)
    //   } else if(item.ywlj) {
    //     gzsYw.push(item)
    //   }
    // })

    // item.gzsFile = {
    //   wbmc: gzs.length > 0 ? gzs[0] : null,
    //   ywmc: gzsYw.length > 0 ? gzsYw[0] : null,
    // };
    // source = noMatches;

    // const foundGzs = list.find((i: any) => {
    //   console.log(' <<<<< ', i)

    //   return item.id === i.gzjzGzsxId;
    // });
    // if(foundGzs) {
    //   foundGzs.gzsFile = foundGzs;
    // }

    if(matches.length > 0) {
      item.gzsFile = matches[0];
    } else {
      item.gzsFile = null;
    }
    source = noMatches;

    console.log('处理公证书文件', item.gzsFile, source);
    return item;
  })
}

// 关闭生成公证书
const gzsGenClose = () => {
  gzsGenState.visible = false;
}
const gzsGenClosed = () => {
  gzsGenState.mbId = '';
  gzsGenState.sxRow = null;
  gzsGenState.typeData = [];
}

/**
 * 获取文档列表
 * @param fjlb 附件类别(1文档拟定，2证据材料，3笔录，4代书(文书)，9其他)
 */
const loadZmclList = async (fjlb: string = '1') => {
  try {
    docState.loading = true;
    const params = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      fjlb,   //  附件类别(1文档拟定，2证据材料, 3笔录，4代书(文书)，9其他)
    }
    // const res = await listGzjzZmclxx(params);
    const res = await queryGzjzZmclxx(params);
    if (res.code === 200) {
      if (fjlb === '1') {
        docState.listData = res.data;
      } else if (fjlb === '4') {
        docState.dsListData = res.data;
      } else if (fjlb === '2') {
        docState.zjclListData = res.data;
      }
    }
  } catch (err: any) {
    ElMessage.error(`获取文档列表失败: ${err.message}`)
  } finally {
    docState.loading = false;
  }
}

const operateMapName = (row: any) => {
  switch (row.typeCode) {
    case 8:   // 申请表
    case 9:   // 受理通知书
    case 116:   // 告知书
    case 77:   // 缴费清单
    case 2:   // 笔录
    case 11:    // 审批表
    case 10:    // 卷宗封皮
    case 85:    // 其他证明书
    case 13:    // 送达回执
      return '新建';
    break;
    case 20:    // 签发稿
      return '生成';
    break;
  }
}

// 根据各类型执行对应方法
const createDocSwitch = (row: any) => {
  switch (row.typeCode) {
    case 8:
    case 9:
    case 116:
    case 77:
    case 11:
    case 10:
    case 85:
    case 13:
      toWdnd();
    break;

    case 2:
      toBl();
    break;

    case 20:
      genDocSwitch(row)
    break;
  }
}

const toWdnd = () => {
  wdndShow.value = true;
}

const toBl = () => {
  blShow.value = true;
}

const wdndOrBlClosed = () => {
  console.log('文档拟定或笔录已关闭');
  loadDocList();
}

const dsClosed = () => {
  loadDsDocList()
}

const genQfgDoc = () => {
  const typeInfo = {
    typeName: '签发稿',
    typeCode: 20
  }
  genDocSwitch(typeInfo);
}

const genDocSwitch = (data: any) => {
  const gt = gzsxTbRef.value;
  if(!gt) return;
  const row = gt.getSelectionRows();
  if(row.length !== 1) {
    ElMessage.error('请选择一个公证事项');
    return;
  }

  const { gzsFile, gzsxId } = row[0];
  if(!gzsFile?.wbmc) {  //  && !gzsFile?.ywmc
    ElMessage.error('未生成公证书');
    return;
  }

  const subIns = SubRef.value;
  const selectedDsrList = subIns?.getSelectedDsr();
  if(selectedDsrList.length === 0) {
    ElMessage.warning('至少选一个当事人');
    return;
  }

  const gzsInfo = JSON.parse(gzsFile?.wblj);
  const gzsOssId = gzsInfo.ossId;

  gzsGenState.isGenGzs = false;
  gzsGenState.gzsOssId = gzsOssId;

  console.log('flsjlj>>>', gzsGenState)

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  queryMbFiles({ wdLb: data.typeCode, ywId: gzsxId as string }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        gzsGenState.typeData = res.data;
        gzsGenState.mbId = res.data[0].id;
        gzsGenState.sxRow = data;
        gzsGenState.gzsxInfo = row[0];
        gzsGenState.visible = true;
        gzsGenState.title = `${data.typeName} 文档生成`;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

// 非公证书文档生成
const docGen = () => {
  const subIns = SubRef.value;
  const dsrIdArr = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)
  const dsrIds = dsrIdArr.join(',')

  let params: UserDocGenParams = {
    bizId: (curGzjz.value.id || currentRecordId.value) as string,
    // type: genState.sxRow.typeCodeStr,
    // fjlb: EnumDocFileType.QT,
    mbWdId: gzsGenState.mbId,
    extraParams: {
      gzxsId: gzsGenState.gzsxInfo.gzsxId,
      gzsOssId: gzsGenState.gzsOssId,
      dsrIds,
    }
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  gzsGenState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      // const gzsxName = gzsxState.listData.find((item) => item.gzsxId === gzsxState.gzsxId)?.gzsxMc || ''
      const { gzsxId, gzsxMc } = gzsGenState.gzsxInfo;
      const fileName = `${gzsGenState.sxRow.typeName}_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
      const docInfo: GzjzWjccxxForm = {
        wbmc: fileName,
        wblj: JSON.stringify({
          ossId,
          gzxsId: gzsGenState.gzsxInfo.gzsxId,
          gzsOssId: gzsGenState.gzsOssId,
          path,
          fileSuffix,
          fileName,
          typeCode: gzsGenState.sxRow.typeCode,
          typeName: gzsGenState.sxRow.typeName,
          mbWdId: gzsGenState.mbId,
          dsrIds,
        }),
        lx: gzsGenState.sxRow.typeCode,
        gzjzGzsxId: gzsxId,
        gzsx: gzsxMc,
      }
      relateDoc(docInfo);
      gzsGenState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败,请检查模板是否完善')
  }).finally(() => {
    gzsGenState.loading = false;
    loading.close()
  })
}

// 添加生成后的文档（关联生成文档）
const relateDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    const res = await addGzjzWjccxx(params);
    if(res.code === 200) {
      ElMessage.success('生成文件添加成功')
      loadDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

// 上传文档
const genDoc = (row: any) => {
  // 实际上传逻辑
  uploadState.visible = true
  uploadState.title = `上传文档 - ${row.typeName}`;
  uploadState.sxRow = row;
}
// 每一个上传完成回调
const uploadEveryDone = (data: UploadStatus) => {
  if (data.status === 'success') {

  } else if (data.status === 'error') {

  }
}

// 全部上传完成回调 data 上传成功的文件信息数组，不含失败
const uploadAllDone = (data: any[]) => {
    const mxList = data.map((item) => {
    const { fileName, ossId, path } = item;
    const docInfo: GzjzWjccxxForm = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      wbmc: fileName,
      wblj: JSON.stringify({
        ossId,
        path,
        fileSuffix: fileName.substring(fileName.lastIndexOf('.')).toLowerCase(),
        fileName,
        typeCode: uploadState.sxRow.typeCode,
        typeName: uploadState.sxRow.typeName,
      }),
      lx: uploadState.sxRow.typeCode,
    }

    return addGzjzWjccxx(docInfo)
  });

  Promise.all(mxList).then(() => {

  }).finally(() => {
    loadDocList();
  })
}

// 打开预览文档
const handleOpen = (data: any) => {
  // 格式化json字符串为对象数据
  const obj = JSON.parse(data.wblj)
  docOpenEdit(obj.path)
}

// 文件删除
const handleDelete = (item: any, listName: 'wdnd' | 'ds') => {
  ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // delDoc(item.id)
    delGzjzWjccxx(item.id).then((res) => {
      if(res.code === 200) {
        ElMessage.success('删除成功');
        if(listName === 'wdnd') {
          loadDocList()
        } else if(listName === 'ds') {
          loadDsDocList()
        }
      }
    }).catch((err: any) => {
      ElMessage.success('删除失败');
      console.log('删除失败', err)
    })
  }).catch(() => {
    // ElMessage.info('已取消删除')
  })
}

// 删除关联文档
const delDoc = async (id: number | string) => {
  delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadDocList()
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
}

// 文档文件列表查询
const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 200,
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.error('文档文件列表查询失败', err)
  }
}

// 文书代书文档文件列表查询
const loadDsDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 200,
      gzjzId: curGzjz.value.id || currentRecordId.value,
      lx: '1'
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDsDoc((res.rows || []))
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  ndgzsDocTypeList.value.map(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      if(!obj) return false;
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    source = noMatches;
  })
}

const placeDsDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  dsList.value = dsList.value.map(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      return item.id == node.gzjzGzsxId;
    });

    item.wsList = matches;
    source = noMatches;
    return item;
  })

  console.log('======>', dsList.value)
}

const onImgShowClosed = () => {
  previewState.zjclImgUrl = '';
}

const zjclPreview = (data: any) => {
  const ext = data.xxmc.substring(data.xxmc.lastIndexOf('.') + 1);
  if (['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
    previewState.zjclImgUrl = data.visitUrl
    previewState.imgVisible = true
  } else if (['mp4', 'avi', 'mov', 'mp3'].includes(ext)) {
    mediaPlayerRef.value?.show(data.visitUrl);
  } else if (['pdf'].includes(ext)) {
    pdfState.url = data.visitUrl
    pdfState.visible = true
  } else if(['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {

  }
}

defineExpose({
  genQfgDoc
})

watch(() => curGzjz.value, (newVal) => {
  loadGzsxList();
  tabChange(docState.activeName);
})

onMounted(() => {
  loadGzsxList();
  // loadDsrList();
  tabChange(docState.activeName)
});
</script>

<style scoped>
.ndgzs-wrap {
  width: 100%;
  height: 100%;
}
.pd-0 {
  padding: 0;
  margin: 0;
  margin-bottom: 4px;
  font-size: 0.8rem;
}
.left-col {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.gzsx-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.btn-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.right-pane-content {
  height: 100%;
}
.pane-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  line-height: 24px;
  gap: 8px;
  margin-bottom: 8px;
}
.ds-btns {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 0;
}
.zmcl-tag+.zmcl-tag {
  margin-left: 8px;
}
:deep(.el-card__body) {
  height: 100%;
}
</style>
