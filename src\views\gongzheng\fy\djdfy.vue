<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form"
             label-width="100px">
      <el-form-item label="卷宗号：" prop="jzbh">
        <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="公证员：" prop="gzybm">
        <el-select v-model="queryParams.gzybm" filterable placeholder="请选择公证员" clearable style="width: 180px">
          <el-option
            v-for="item in gzy"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="当事人：" prop="dsrId">
        <el-select v-model="queryParams.dsrId" filterable placeholder="请选择当事人" clearable style="width: 180px">
          <el-option v-for="item in partyOptions" :key="item.id" :label="item.xm" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="翻译人姓名：" prop="fyrxm">
        <el-input v-model="queryParams.fyrxm" placeholder="请输入翻译人姓名" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="翻译状态：" prop="" style="width: 200px">
        <el-select v-model="queryParams.params.fyztParam" placeholder="请选择翻译状态" style="width: 180px">
          <el-option v-for="item in fyztOptions" :key="item.value" :label="item.label" :value="item.value"
                     @click="setFyztList(item.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="译文：" prop="ywwz">
        <el-select v-model="queryParams.ywwz" filterable placeholder="请选择译文文种" clearable style="width: 180px">
          <el-option
            v-for="item in gz_yw_wz"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" v-has-permi="['fygl:djd:query']">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="main-content">
      <!-- 左侧翻译列表 -->
      <div class="left-content">
        <div class="content-header" style="line-height: 32px;">
          <span class="header-title">翻译列表</span>
        </div>
        <el-table
          v-loading="loading"
          :data="fyglList"
          border
          style="width: 100%;height: 550px;"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column type="index" label="序号" align="center" width="60"fixed/>
          <el-table-column label="操作" align="center" width="80" fixed>
            <template #default="scope">
              <el-button type="primary" link @click.stop="handleDetail(scope.row)" v-has-permi="['fygl:djd:query']">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="翻译状态" align="center" prop="fyStatus" show-overflow-tooltip fixed>
            <template #default="scope">
              <dict-tag :options="fyztStatsXs" :value="scope.row.fyStatus" />
            </template>
          </el-table-column>
          <el-table-column label="卷宗号" align="center" prop="jzbh" width="120" show-overflow-tooltip />
          <el-table-column label="当事人" align="center" prop="dsrxm" width="100" show-overflow-tooltip />
          <el-table-column label="公证书编号" align="center" prop="gzsbh" width="200" show-overflow-tooltip />
          <el-table-column label="领证时间" align="center" prop="lzrq" width="180" show-overflow-tooltip />
          <el-table-column label="公证员" align="center" prop="gzyxm" width="100" show-overflow-tooltip />
          <el-table-column label="译文文种" align="center" prop="ywwz" show-overflow-tooltip>
            <template #default="scope">
              <dict-tag :options="gz_yw_wz" :value="scope.row.ywwz" />
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            background
            layout="prev, pager, next, jumper"
            :total="listTotal"
            :current-page="queryParams.pageNum"
            :page-size="queryParams.pageSize"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>

      <!-- 右侧翻译明细 -->
      <div class="right-content">
        <div class="content-header">
          <span class="header-title">翻译明细</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleSubmitJd" v-has-permi="['fygl:djd:edit']" :disabled="multiple">一键校对</el-button>
          </div>
        </div>
        <el-table
          v-loading="detailLoading"
          :data="wjccxxList"
          border
          style="width: 100% ;height: 550px;"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="index" label="序号" align="center" width="60" fixed/>
          <el-table-column type="selection" width="50" align="center" fixed />
          <el-table-column label="操作" align="center" width="80" fixed>
            <template #default="scope">
              <el-button v-if="scope.row.fyzt ==='0'||scope.row.fyzt ==='2' " type="primary" link @click="handleDetailOperation(scope.row,'translation')" v-has-permi="['fygl:djd:edit']">翻译</el-button>
              <el-button v-else-if="scope.row.fyzt ==='1'" type="primary" link @click="handleDetailOperation(scope.row,'proofread')" v-has-permi="['fygl:djd:edit']">校对</el-button>
              <el-button v-else type="primary" link @click="handleDetailOperation(scope.row,'view')" v-has-permi="['fygl:djd:edit']">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="翻译状态" align="center" prop="fyzt" width="100" show-overflow-tooltip fixed>
            <template #default="scope">
              <dict-tag :options="fyztStatsXs" :value="scope.row.fyzt" />
            </template>
          </el-table-column>
          <el-table-column label="公证书编号" align="center" prop="gzsbh" width="180" show-overflow-tooltip />
          <el-table-column label="公证事项" align="center" prop="gzsx" width="120" show-overflow-tooltip />
          <el-table-column label="翻译截止时间" align="center" prop="translateDeadline" width="180" />
          <el-table-column label="翻译时间" align="center" prop="translateTime" width="180" />
          <el-table-column label="文档类型" align="center" prop="lx" width="100" show-overflow-tooltip>
            <template #default="scope">
              <dict-tag :options="wsOptions" :value="scope.row.lx" />
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            background
            layout="prev, pager, next, jumper"
            :total="wjccxxTotal"
            :current-page="wjccxxParams.pageNum"
            :page-size="wjccxxParams.pageSize"
            @current-change="handleCurrentChangeWjccxx"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 卷宗详情 -->
    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />

    <!-- 翻译明细对话框 -->
    <fymx-dialog ref="fymxDialogRef" @callback="fymxCallback" ></fymx-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, provide, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { clearEmptyProperty, genYearOptions } from '@/utils/ruoyi';
import { listGzjzFygl, listWjccxxByLxlist } from '@/api/gongzheng/fy';
import { GzGzjzFyglQuerye, GzGzjzFyglVo } from '@/api/gongzheng/fy/types';
import { GzjzWjccxxQuery, GzjzWjccxxVO } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { listDsrxxZrr } from '@/api/gongzheng/dsr/dsrxxZrr';
import { useUserStore } from '@/store/modules/user';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import FymxDialog from '@/views/gongzheng/fy/dialog/fymxDialog.vue';
import { batchUpdateList } from '@/api/gongzheng/gongzheng/gzjzWjccxx';

const userStore = useUserStore();
const userId = ref(userStore.userId);

// 显示搜索条件
const showSearch = ref(true);

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);

// 选中数据
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const listTotal = ref(40);
const wjccxxTotal = ref(0);

const gzsbh_years = genYearOptions(2010);
const jzDetailState = reactive({
  visible: false
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  gz_yw_wz,
  gz_gzs_zh,
  gz_sf_lb,
  gz_mb_lx,
  gz_sfzt,
  gz_jjfs
} = toRefs<any>(proxy?.useDict('gz_yw_wz', 'gz_gzs_zh', 'gz_sf_lb', 'gz_mb_lx', 'gz_sfzt', 'gz_jjfs'));

const { gzy } = toRefs<any>(proxy?.useRoleUser('gzy'));

// 当事人选项
const partyOptions = ref(
);
//当事人列表
const getDsrxxZrrList = async () => {
  const res = await listDsrxxZrr();
  if (res.rows?.length > 0) {
    partyOptions.value = res.rows;
  }
};

// 公证事项列表
const gzsxList = ref();

// 查询参数
const queryParams = ref<GzGzjzFyglQuerye>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  dsrId: undefined,
  gzybm: undefined,
  ywwz: undefined,
  fyztList: ['1'],
  fyrxm: undefined,
  params: {
    gzsNf: undefined,
    gzsZh: undefined,
    gzsLs: undefined,
    fyztParam: '进行中'
  }
});

const wjccxxParams = ref<GzjzWjccxxQuery>({
  pageNum: 1,
  pageSize: 10
});

//翻译状态选项
const fyztOptions = ref([
  { label: '全部', value: '99' },
  { label: '进行中', value: '1' },
  { label: '已完成', value: '2' }
]);

//文书类型显示
const wsOptions = ref([
  { label: '文书', value: '1' },
  { label: '公证书', value: '3' }
]);

//翻译状态显示
const fyztStatsXs = ref([
  { label: '未分配', value: '0',elTagType:'primary' },
  { label: '待校对', value: '1' ,elTagType:'info' },
  { label: '驳回校对', value: '2' ,elTagType:'danger' },
  { label: '已校对', value: '3' ,elTagType:'success' },
  { label: '驳回翻译', value: '9' ,elTagType:'danger' }
]);

const gzjzId = ref<string | number>(null);
const currentRecord = ref<GzjzJbxxVO>(null);

// 翻译列表数据
const fyglList = ref<GzGzjzFyglVo>(null);

// 翻译明细列表数据
const wjccxxList = ref<GzjzWjccxxVO>(null);
// 翻译明细列表选中数据
const fymxList = ref([]);

const fymxDialogRef = ref<InstanceType<typeof FymxDialog>>(null);
// 查询翻译列表数据
const getList = async () => {
  loading.value = true;
  try {
    await getDsrxxZrrList();
    // 清理空值参数，避免发送空字符串
    const cleanParams = clearEmptyProperty(queryParams.value);
    // 确保基本的分页参数
    cleanParams.pageNum = queryParams.value.pageNum || 1;
    cleanParams.pageSize = queryParams.value.pageSize || 10;
    const res = await listGzjzFygl(cleanParams);
    fyglList.value = res.rows;
    listTotal.value = res.total;
  } catch (error: any) {
    proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

//改变翻译状态列表
const setFyztList = (value: string) => {
  switch (value) {
    case '0':
      queryParams.value.fyztList = ['0'];
      break;
    case '1':
      queryParams.value.fyztList = ['1','2'];
      break;
    case '2':
      queryParams.value.fyztList = ['3'];
      break;
    case '99':
    default:
      queryParams.value.fyztList = ['0', '1', '2', '3'];
      break;
  }
};
// 查询翻译明细列表
const getDetailList = async () => {
  detailLoading.value = true;
  try {
    /** 查询案件列表 */
    wjccxxParams.value.gzjzId = gzjzId.value;
    const lxList = ['1', '3'];
    const res = await listWjccxxByLxlist(lxList, wjccxxParams.value.gzjzId, wjccxxParams.value.pageNum, wjccxxParams.value.pageSize);
    if (res?.rows) {
      wjccxxList.value = res.rows;
      wjccxxTotal.value = res.total;
    }
  } catch (error: any) {
    proxy?.$modal.msgError('查询失败: ' + (error?.message || '未知错误'));
  } finally {
    detailLoading.value = false;
  }
};


// 表格多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  fymxList.value = selection.map(item => item);
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  wjccxxParams.value.pageNum = 1;
  gzjzId.value = undefined;
  currentRecord.value = undefined;
  wjccxxList.value = undefined;
  gzsxList.value = undefined;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.value.jzbh = undefined;
    queryParams.value.dsrId = undefined;
    queryParams.value.gzybm = undefined;
    queryParams.value.ywwz = undefined;
    queryParams.value.fyztList = ['1'];
    queryParams.value.params.gzsNf = undefined;
    queryParams.value.params.gzsZh = undefined;
    queryParams.value.params.gzsLs = undefined;
    queryParams.value.params.fyztParam = '进行中';
    queryParams.value.params.fyrxm = undefined;
    handleQuery();
};

// 处理行点击事件
const handleRowClick = (row: any) => {
  gzjzId.value = row.id;
  currentRecord.value = row;
  gzsxList.value = row.gzsxVoList;
  getDetailList();
};

// 处理详情按钮点击
const handleDetail = (row: any) => {
  gzjzId.value = row.id;
  currentRecord.value = row;
  jzDetailState.visible = true;
};

// 处理列表分页变化
const handleCurrentChange = (val: number) => {
  queryParams.value.pageNum = val;
  getList();
};

// 处理明细分页变化
const handleCurrentChangeWjccxx = (val: number) => {
  wjccxxParams.value.pageNum = val;
  getDetailList();
};


// 处理明细操作按钮点击
const handleDetailOperation = (row: any,type:string) => {
  // ElMessage.success(`操作明细: ${row.gzsbh}`);
  handleFymx(row.id,type)
  // 实际操作逻辑
};

const handleFymx = (data,type) => {
  fymxDialogRef.value.handleFymx(data,type);
}
const fymxCallback = (data : any) => {
  getList(); // 刷新列表
}

// 校对
const handleSubmitJd = () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请选择需要校对的记录');
    return;
  }
  let flag=true
  const list=[];
  for (let i = 0; i < fymxList.value.length; i++) {
    var item = fymxList.value[i];
    if (item.fyzt==='0'||item.fyzt==='2'){
      proxy?.$modal.msgError("公证事项："+item.gzsx+" 未完成翻译，无法校对！");
      flag=false
      break
    }
    if (item.fyzt!=='1'){
      proxy?.$modal.msgError("公证事项："+item.gzsx+" 已完成校对，无需重复校对！");
      flag=false
      break
    }
    list.push({id:item.id,fyzt:3});
  }
  if(flag){
    batchUpdateList(list).then(response => {
      getList()
    })
  }
  fymxList.value.forEach((item: any) => {

  })
  // ElMessage.success(`提交校对成功`);
  // 实际提交逻辑
};


// 提供给子组件的数据和方法
provide('currentRecordId', gzjzId);
provide('currentRecord', currentRecord);

// 组件挂载时
onMounted(() => {
  getList();
});
</script>

<style scoped>
.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.main-content {
  display: flex;
  gap: 15px;
  height: calc(100vh - 230px);
}

.left-content {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.right-content {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

.pagination-info {
  margin: 0 10px;
}

.pagination-input {
  width: 50px;
}

.pagination-select {
  width: 80px;
  margin-left: 10px;
}
</style>
