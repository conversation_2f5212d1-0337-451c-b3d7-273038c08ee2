<template>
  <div class="app-container">
    <div class="main-content">
      <!-- 左侧档案库 -->
      <div class="left-panel">
        <div class="panel-header">
          <div class="panel-title">档案库</div>
        </div>
        <div class="panel-content">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleAddArchive">新增</el-button>
            <el-button type="danger" size="small" @click="handleDeleteArchive">删除</el-button>
          </div>
          <!-- 左侧档案库列表，这里是一个空白区域，根据实际需求可以添加内容 -->
          <div class="archive-list">
            <!-- 档案库列表内容 -->
          </div>
        </div>
      </div>

      <!-- 右侧档案盒信息 -->
      <div class="right-panel">
        <div class="panel-header">
          <div class="panel-title">档案盒信息</div>
          <div class="panel-actions">
            <el-button-group>
              <el-button size="small" @click="handleArchiveInfo">档案盒信息</el-button>
              <el-button size="small" @click="handleUsage">使用</el-button>
              <el-button size="small" @click="handleArchiveManage">档案管理</el-button>
              <el-button size="small" @click="handleUpload">卷宗上架</el-button>
              <el-button size="small" @click="handleDownload">卷宗下架</el-button>
              <el-button size="small" @click="handleOther">其它</el-button>
            </el-button-group>
          </div>
        </div>
        <div class="panel-content">
          <!-- 右侧内容区域，这里是一个空白区域，根据实际需求可以添加内容 -->
          <div class="archive-info">
            <!-- 档案盒信息内容 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-panel">
      <div class="statistics-row">
        <div class="statistics-item">
          <span class="statistics-label">档案库共计：</span>
          <span class="statistics-value">0 个</span>
        </div>
        <div class="statistics-item">
          <span class="statistics-label">档案架共计：</span>
          <span class="statistics-value">0 架</span>
        </div>
        <div class="statistics-item">
          <span class="statistics-label">档案盒共计：</span>
          <span class="statistics-value">0 盒</span>
        </div>
      </div>
      <div class="statistics-row">
        <div class="statistics-item">
          <span class="statistics-label">使用中盒数：</span>
          <span class="statistics-value">0 盒</span>
        </div>
        <div class="statistics-item">
          <span class="statistics-label">上架档案盒共计：</span>
          <span class="statistics-value">0 盒</span>
        </div>
        <div class="statistics-item">
          <span class="statistics-label">上架卷宗共计：</span>
          <span class="statistics-value">0 卷</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 新增档案库
const handleAddArchive = () => {
  ElMessage.success('新增档案库')
}

// 删除档案库
const handleDeleteArchive = () => {
  ElMessage.warning('删除档案库')
}

// 档案盒信息
const handleArchiveInfo = () => {
  ElMessage.info('查看档案盒信息')
}

// 使用
const handleUsage = () => {
  ElMessage.info('档案盒使用')
}

// 档案管理
const handleArchiveManage = () => {
  ElMessage.info('档案管理')
}

// 卷宗上架
const handleUpload = () => {
  ElMessage.info('卷宗上架')
}

// 卷宗下架
const handleDownload = () => {
  ElMessage.info('卷宗下架')
}

// 其它
const handleOther = () => {
  ElMessage.info('其它操作')
}

// 组件挂载时
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.app-container {
  padding: 15px;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.main-content {
  display: flex;
  height: calc(100% - 120px);
  margin-bottom: 15px;
}

.left-panel {
  width: 350px;
  background-color: #fff;
  border-radius: 4px;
  margin-right: 15px;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow: auto;
}

.action-buttons {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.archive-list {
  height: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.archive-info {
  height: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.statistics-panel {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.statistics-row {
  display: flex;
  margin-bottom: 15px;
}

.statistics-row:last-child {
  margin-bottom: 0;
}

.statistics-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.statistics-label {
  font-weight: bold;
  margin-right: 5px;
}

.statistics-value {
  color: #606266;
}
</style> 