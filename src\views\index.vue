<template>
  <div class="app-container home">
    <!-- 公证处名称区域 -->
    <div class="notary-office-header">
      <div class="header-content">
        <div class="notary-office-name">广西南宁市公证处</div>
        <div class="welcome-message">欢迎使用公证管理信息系统</div>
      </div>
    </div>
    
    <div class="dashboard-container">
      <!-- 第一行：办证数和公证费卡片 -->
      <div class="card-row">
        <!-- 办证数卡片 -->
        <el-card class="dashboard-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon><User /></el-icon>
              <span>办证数</span>
            </div>
          </div>
          <div class="card-content">
            <div class="stat-item">
              <div class="stat-label">日：</div>
              <div class="stat-value">
                <span class="stat-number">27</span>件
              </div>
              <div class="stat-trend negative">-6.90%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">周：</div>
              <div class="stat-value">
                <span class="stat-number">47</span>件
              </div>
              <div class="stat-trend negative">-56.88%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">月：</div>
              <div class="stat-value">
                <span class="stat-number">386</span>件
              </div>
              <div class="stat-trend negative">-7.88%</div>
            </div>
          </div>
        </el-card>

        <!-- 公证费卡片 -->
        <el-card class="dashboard-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon><Money /></el-icon>
              <span>公证费</span>
            </div>
          </div>
          <div class="card-content">
            <div class="stat-item">
              <div class="stat-label">日：</div>
              <div class="stat-value">
                <span class="stat-number">6944.00</span>元
              </div>
              <div class="stat-trend negative">-34.91%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">周：</div>
              <div class="stat-value">
                <span class="stat-number">13596.00</span>元
              </div>
              <div class="stat-trend negative">-77.13%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">月：</div>
              <div class="stat-value">
                <span class="stat-number">194856.00</span>元
              </div>
              <div class="stat-trend negative">-6.65%</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 第二行：系统公告和待办事项 -->
      <div class="card-row">
        <!-- 系统公告卡片 -->
        <el-card class="dashboard-card notice-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon"><Bell /></el-icon>
              <span>系统公告</span>
            </div>
            <div class="card-more">
              <a href="javascript:void(0)">更多 >></a>
            </div>
          </div>
          <div class="card-content">
            <div class="notice-list">
              <div class="notice-item">
                <div class="notice-content">
                  <span class="notice-label">简讯：</span>
                  <span class="notice-text">系统正常升级维护</span>
                </div>
                <div class="notice-time">2019-05-22 17:30:39</div>
              </div>
              <div class="notice-item">
                <div class="notice-content">
                  <span class="notice-label">简讯：</span>
                  <span class="notice-text">系统升级公告2.16.2-2.18</span>
                </div>
                <div class="notice-time">2019-04-21 23:21:55</div>
              </div>
              <div class="notice-item">
                <div class="notice-content">
                  <span class="notice-label">简讯：</span>
                  <span class="notice-text">重要通知-机房升级公告</span>
                </div>
                <div class="notice-time">2019-01-05 16:00:54</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 待办事项卡片 -->
        <el-card class="dashboard-card todo-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon"><Document /></el-icon>
              <span>待办事项</span>
            </div>
            <div class="card-more">
              <a href="javascript:void(0)">更多 >></a>
            </div>
          </div>
          <div class="card-content">
            <div class="todo-list">
              <div class="todo-item">
                <div class="todo-label">待发证卷宗数：</div>
                <div class="todo-value">855 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待翻译数：</div>
                <div class="todo-value">0 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待审批卷宗数：</div>
                <div class="todo-value">3 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待审批退费申请卷宗数：</div>
                <div class="todo-value">0 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待校对数：</div>
                <div class="todo-value">0 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待收费卷宗数：</div>
                <div class="todo-value">81 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">受理中卷宗数：</div>
                <div class="todo-value">257 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待制证卷宗数：</div>
                <div class="todo-value">28 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待归档案卷数：</div>
                <div class="todo-value">1272 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待接收翻译数：</div>
                <div class="todo-value">10 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待审批特殊流程卷宗数：</div>
                <div class="todo-value">0 件</div>
              </div>
              <div class="todo-item">
                <div class="todo-label">待退费卷宗数：</div>
                <div class="todo-value">7 件</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup name="Index" lang="ts">
import { User, Money, Bell, Document } from '@element-plus/icons-vue'

const goTarget = (url: string) => {
  window.open(url, '__blank');
};
</script>

<style lang="scss" scoped>
.home {
  position: relative;
  padding-top: 60px; /* 为公证处名称区域留出空间 */
  
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

/* 公证处名称区域样式 */
.notary-office-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 15px 20px;
  z-index: 10;
  border-bottom: 1px solid #ebeef5;
}

.header-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.notary-office-name {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-right: 15px;
}

.welcome-message {
  font-size: 14px;
  color: #606266;
}

/* 仪表盘容器样式 */
.dashboard-container {
  margin-top: 10px;
}

/* 卡片行样式 */
.card-row {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

/* 卡片样式 */
.dashboard-card {
  flex: 1;
  margin-bottom: 0;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      
      .el-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }
    
    .card-more {
      a {
        color: #909399;
        font-size: 13px;
        text-decoration: none;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }
}

/* 统计数据样式 */
.card-content {
  .stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .stat-label {
      width: 40px;
      font-size: 14px;
      color: #606266;
    }
    
    .stat-value {
      margin: 0 10px;
      font-size: 14px;
      color: #303133;
      
      .stat-number {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-right: 5px;
      }
    }
    
    .stat-trend {
      font-size: 12px;
      
      &.positive {
        color: #67C23A;
      }
      
      &.negative {
        color: #F56C6C;
      }
    }
  }
}

/* 公告列表样式 */
.notice-list {
  .notice-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #EBEEF5;
    
    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
    
    .notice-content {
      .notice-label {
        color: #909399;
        margin-right: 5px;
      }
      
      .notice-text {
        color: #303133;
      }
    }
    
    .notice-time {
      color: #909399;
      font-size: 12px;
    }
  }
}

/* 待办事项样式 */
.todo-list {
  display: flex;
  flex-wrap: wrap;
  
  .todo-item {
    width: 50%;
    margin-bottom: 15px;
    display: flex;
    
    .todo-label {
      color: #606266;
      margin-right: 5px;
    }
    
    .todo-value {
      color: #F56C6C;
      font-weight: bold;
    }
  }
}

@media (max-width: 768px) {
  .notary-office-header {
    padding: 10px 15px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .notary-office-name {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .todo-list .todo-item {
    width: 100%;
  }
}

.title-icon {
  color: #E6A23C !important;
}
</style>
