import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DabhglVO, DabhglForm, DabhglQuery } from '@/api/basicdata/dabhgl/types';

/**
 * 查询档案号头列表
 * @param query
 * @returns {*}
 */

export const listDabhgl = (query?: DabhglQuery): AxiosPromise<DabhglVO[]> => {
  return request({
    url: '/basicdata/dabhgl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询档案号头详细
 * @param id
 */
export const getDabhgl = (id: string | number): AxiosPromise<DabhglVO> => {
  return request({
    url: '/basicdata/dabhgl/' + id,
    method: 'get'
  });
};

/**
 * 新增档案号头
 * @param data
 */
export const addDabhgl = (data: DabhglForm) => {
  return request({
    url: '/basicdata/dabhgl',
    method: 'post',
    data: data
  });
};

/**
 * 修改档案号头
 * @param data
 */
export const updateDabhgl = (data: DabhglForm) => {
  return request({
    url: '/basicdata/dabhgl',
    method: 'put',
    data: data
  });
};

/**
 * 删除档案号头
 * @param id
 */
export const delDabhgl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/dabhgl/' + id,
    method: 'delete'
  });
};
