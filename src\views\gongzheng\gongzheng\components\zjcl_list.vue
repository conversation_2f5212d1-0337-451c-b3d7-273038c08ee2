<template>
  <div>
    <el-row v-if="props.dialog"  :gutter="20">
      <el-col :span="8">
        <el-card class="no-padding-card dateil-card-main" style="height: 300px;">
          <template #header>
            <span>证据材料信息</span>
          </template>
          <el-table>
            <el-table-column prop="index" width="80" align="center" />
            <el-table-column label="证据材料名称" prop="name" align="center" show-overflow-tooltip />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card class="no-padding-card dateil-card-main" style="height: 300px;">

        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    dialog : boolean;
    dialigEdit : boolean;
    title : String;
  }
  const props = defineProps<Props>();
</script>

<style>
</style>
