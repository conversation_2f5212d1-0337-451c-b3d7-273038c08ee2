<template>
  <div class="pageoffice-demo">
    <!-- 统一文档处理 DEMO 区域 -->
    <el-card style="margin-bottom: 30px;">
      <template #header>
        <span>统一文档处理 DEMO</span>
      </template>
      <el-form :model="processForm" class="process-form" @submit.prevent>
        <el-form-item label="文件路径(path)">
          <el-input v-model="processForm.path" placeholder="请输入文件路径" />
        </el-form-item>
        <el-form-item label="文档名称">
          <el-input v-model="processForm.documentName" placeholder="文档名称" />
        </el-form-item>
        <el-form-item label="action">
          <el-input v-model="processForm.action" placeholder="如 view/save/edit/delete..." />
        </el-form-item>
        <el-form-item label="documentId">
          <el-input v-model="processForm.documentId" placeholder="文档ID" />
        </el-form-item>
        <el-form-item label="documentType">
          <el-input v-model="processForm.documentType" placeholder="" />
        </el-form-item>
        <el-form-item label="ywTyp">
          <el-input v-model="processForm.ywTyp" placeholder="" />
        </el-form-item>
        <el-form-item label="savePath">
          <el-input v-model="processForm.savePath" placeholder="" />
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 10px;">
        <span style="font-size: 13px;">自定义参数：</span>
        <el-button size="small" type="primary" @click="addCustomParam">添加参数</el-button>
      </div>
      <div v-for="(item, idx) in processForm.customParams" :key="idx"
        style="display: flex; align-items: center; margin-bottom: 6px;">
        <el-input v-model="item.key" placeholder="参数名" style="width: 100px; margin-right: 6px;" />
        <el-input v-model="item.value" placeholder="参数值"
          style="width: 500px; margin-right: 6px;" />
        <el-button size="small" type="danger" @click="removeCustomParam(idx)">删除</el-button>
      </div>
      <el-button type="success" @click="handleProcess" :loading="processLoading">调用统一文档处理接口</el-button>
      <el-alert v-if="processResultMsg" :title="processResultMsg" type="info" show-icon style="margin: 10px 0;" />
      <el-descriptions v-if="processResultObj" :column="1" border>
        <el-descriptions-item v-for="(val, key) in processResultObj" :key="key"
          :label="key">{{ val }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 原有功能区域 -->
    <!-- <el-card>
      <template #header>
        <span>PageOffice 文档演示</span>
      </template>
      <div class="toolbar">
        <el-input v-model="documentId" placeholder="输入文档ID" style="width: 200px; margin-right: 10px;" />
        <el-button type="primary" @click="handleView">查看文档</el-button>
        <el-button @click="handleEdit">编辑文档</el-button>
        <el-button @click="handleSave">保存文档</el-button>
        <el-button type="danger" @click="handleDelete">删除文档</el-button>
        <el-button @click="handleClose">关闭文档</el-button>
        <el-button @click="handleStatus">获取状态</el-button>
      </div>
      <div class="result-area">
        <el-alert v-if="resultMsg" :title="resultMsg" type="info" show-icon style="margin: 10px 0;" />
        <el-input v-model="editContent" type="textarea" :rows="8" placeholder="编辑内容，仅编辑时可用"
          style="margin-bottom: 10px;" />
        <el-descriptions v-if="docInfo" :column="1" border>
          <el-descriptions-item label="文档ID">{{ docInfo.documentId }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ docInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="路径">{{ docInfo.path }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ docInfo.status }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card> -->
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { POBrowser } from "js-pageoffice";
  import {
    processDocument,
    viewDocument,
    saveDocument,
    editDocument,
    deleteDocumentById,
    closeDocument,
    getDocumentStatus
  } from '@/api/gongzheng/dev/pageoffice';
  import type { UnifiedDocumentVO } from '@/api/gongzheng/dev/pageoffice/types';
  // 统一文档处理 DEMO 相关
  const processForm = reactive({
    path: 'D:\\projects\\temp\\pageoffice\\pageofficetemp\\公证事项证明材料清单（2023年版）.docx',
    action: 'edit',
    documentId: '123',
    documentName: '公证事项证明材料清单（2023年版）',
    documentType: 'word',
    customParams: [] as Array<{ key : string; value : string }>,
    savePath: '',
    ywTyp: ''
  });
  const processLoading = ref(false);
  const processResultMsg = ref('');
  const processResultObj = ref<Record<string, any> | null>(null);

  function addCustomParam() {
    processForm.customParams.push({ key: '', value: '' });
  }
  function removeCustomParam(idx : number) {
    processForm.customParams.splice(idx, 1);
  }

  async function handleProcess() {
    if (!processForm.path || !processForm.action) {
      ElMessage.warning('请填写文件路径和action');
      return;
    }
    processLoading.value = true;
    processResultMsg.value = '';
    processResultObj.value = null;
    // 组装参数
    const params : Record<string, any> = {
      documentPath: processForm.path,
      action: processForm.action,
      documentType: processForm.documentType,
      documentName: processForm.documentName,
      savePath: processForm.savePath,
      ywTyp: processForm.ywTyp
    };
    if (processForm.documentId) params.documentId = processForm.documentId;
    if (processForm.customParams) {
      params.customParams = JSON.parse(processForm.customParams[0].value)

    }
    try {
      const { data } = await processDocument(params);
      processResultMsg.value = '接口调用成功';
      processResultObj.value = typeof data === 'object' ? data : { result: data };
      console.log(processResultObj.value)
      if (processResultObj.value) {
        POBrowser.openWindow("/word", 'width=1300px;height=900px;', JSON.stringify(params));
      }
    } catch (e : any) {
      processResultMsg.value = e?.message || '接口调用失败';
    } finally {
      processLoading.value = false;
    }
  }

  // 原有功能区域
  const documentId = ref('');
  const editContent = ref('');
  const docInfo = ref<UnifiedDocumentVO | null>(null);
  const resultMsg = ref('');

  const handleView = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    try {
      const { data } = await viewDocument({ documentId: documentId.value });
      docInfo.value = data;
      editContent.value = data.content || '';
      resultMsg.value = '文档加载成功';
    } catch (e : any) {
      resultMsg.value = e?.message || '文档加载失败';
    }
  };

  const handleEdit = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    ElMessageBox.prompt('请输入新的文档内容', '编辑文档', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: editContent.value
    }).then(async ({ value }) => {
      try {
        await editDocument({ documentId: documentId.value, content: value });
        editContent.value = value;
        resultMsg.value = '文档编辑成功';
      } catch (e : any) {
        resultMsg.value = e?.message || '文档编辑失败';
      }
    });
  };

  const handleSave = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    try {
      await saveDocument(documentId.value, { content: editContent.value });
      resultMsg.value = '文档保存成功';
    } catch (e : any) {
      resultMsg.value = e?.message || '文档保存失败';
    }
  };

  const handleDelete = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
      type: 'warning'
    }).then(async () => {
      try {
        await deleteDocumentById(documentId.value);
        docInfo.value = null;
        editContent.value = '';
        resultMsg.value = '文档删除成功';
      } catch (e : any) {
        resultMsg.value = e?.message || '文档删除失败';
      }
    });
  };

  const handleClose = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    try {
      await closeDocument(documentId.value);
      resultMsg.value = '文档已关闭';
    } catch (e : any) {
      resultMsg.value = e?.message || '关闭失败';
    }
  };

  const handleStatus = async () => {
    if (!documentId.value) return ElMessage.warning('请输入文档ID');
    try {
      const { data } = await getDocumentStatus(documentId.value);
      resultMsg.value = '文档状态：' + (data.status || '未知');
    } catch (e : any) {
      resultMsg.value = e?.message || '获取状态失败';
    }
  };
</script>

<style scoped>
  .pageoffice-demo {
    max-width: 800px;
    margin: 40px auto;
  }

  .toolbar {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .result-area {
    margin-top: 20px;
  }

  .process-form {
    margin-bottom: 10px;
  }
</style>
