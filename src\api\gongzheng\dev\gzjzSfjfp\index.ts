import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzSfjfpVO, GzjzSfjfpForm, GzjzSfjfpQuery } from '@/api/gongzheng/dev/gzjzSfjfp/types';

/**
 * 查询公证卷宗-收费及开票记录v1.2列表
 * @param query
 * @returns {*}
 */

export const listGzjzSfjfp = (query?: GzjzSfjfpQuery): AxiosPromise<GzjzSfjfpVO[]> => {
  return request({
    url: '/gongzheng/gzjzSfjfp/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-收费及开票记录v1.2详细
 * @param id
 */
export const getGzjzSfjfp = (id: string | number): AxiosPromise<GzjzSfjfpVO> => {
  return request({
    url: '/gongzheng/gzjzSfjfp/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-收费及开票记录v1.2
 * @param data
 */
export const addGzjzSfjfp = (data: GzjzSfjfpForm) => {
  return request({
    url: '/gongzheng/gzjzSfjfp',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-收费及开票记录v1.2
 * @param data
 */
export const updateGzjzSfjfp = (data: GzjzSfjfpForm) => {
  return request({
    url: '/gongzheng/gzjzSfjfp',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-收费及开票记录v1.2
 * @param id
 */
export const delGzjzSfjfp = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzSfjfp/' + id,
    method: 'delete'
  });
};
