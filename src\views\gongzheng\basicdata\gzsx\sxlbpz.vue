<template>
  <div class="p-2">
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="10.5">
            <el-select v-model="queryParams.gzlbValue" placeholder="请选择" style="width: 250px;" @change="handleGzlbChange">
              <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain @click="handleSave" :loading="buttonLoading"
              v-hasPermi="['basicdata:gzlbpz:edit']">保存配置</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button  plain @click="handleReset" :loading="buttonLoading" :disabled="isReset"
              v-hasPermi="['basicdata:gzlbpz:edit']">还原配置</el-button>
          </el-col>
          <el-col :span="10.5">
            <div style="line-height: 35px;">共计 {{totalCount}} 项，已选择 {{selectedCount}} 项。</div>
          </el-col>
        </el-row>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-table ref="gzsxTableRef" v-loading="loading" :data="gzsxList" @selection-change="handleSelectionChange" height="680"
            row-key="code" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :default-expand-all="isExpandAll">
            <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
            <el-table-column label="事项名称" align="left" prop="title" min-width="300" />
            <el-table-column label="事项编号" align="center" prop="code" />
            <el-table-column label="是否基础公证事项" align="center" prop="jcsx">
              <template #default="scope">
                <dict-tag :options="gz_yes_or_no" :value="scope.row.jcsx" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="6">

        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="Gzlbpz" lang="ts">
  import { listGzlbpz, updateGzlbpz } from '@/api/gongzheng/basicdata/gzlbpz';
  import { GzlbpzVO, GzlbpzQuery, GzlbpzForm } from '@/api/gongzheng/basicdata/gzlbpz/types';
  import { getGzsx, delGzsx, addGzsx, updateGzsx, listTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO, GzsxQuery, GzsxForm } from '@/api/gongzheng/basicdata/gzsx/types';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_yes_or_no'));
  interface GzsxOptionsType {
    id : number | string;
    title : string;
    parentCode : string;
    code : string;
    children : GzsxOptionsType[];
  }
  const gzsxOptions = ref<GzsxOptionsType[]>([]);
  const gzsxList = ref<GzsxVO[]>([]);

  const selectedCount = ref(0);
  const totalCount = ref(0);
  const isExpandAll = ref(true);
  const isReset = ref(true);

  const buttonLoading = ref(false);
  const loading = ref(true);
  const ids = ref<Array<string | number>>([]);
  const total = ref(0);

  const gzlbpzList = ref<string[]>([]);
  const gzsxTableRef = ref();

  const initFormData : GzlbpzForm = {
    gzlbValue: undefined,
    gzsxCode: undefined,
    gzsxCodeList: undefined
  }
  const data = reactive<PageData<GzlbpzForm, GzlbpzQuery>>({
    form: { ...initFormData },
    queryParams: {
      gzlbValue: '1'
    }
  });

  const { queryParams, form } = toRefs(data);

  /** 查询公证事项树列表 */
  const getGzsxTree = async () => {
    loading.value = true;
    buttonLoading.value = true;
    const res = await listTree();
    totalCount.value = res.data.length;
    const data = proxy?.handleTreeCode<GzsxVO>(res.data, 'code');
    if (data) {
      gzsxList.value = data;
      await getGzsxPzList();
    }
    buttonLoading.value = false;
    loading.value = false;
  }
  /** 公证类别选择 */
  const handleGzlbChange = (selected : any) => {
    queryParams.value.gzlbValue = selected;
    isReset.value = true;
    getGzsxPzList();
  }

  /** 查询公证类别配置列表 */
  const getGzsxPzList = async () => {
    loading.value = true;
    buttonLoading.value = true;
    const res = await listGzlbpz(queryParams.value);
    gzlbpzList.value = res.rows.map(item => item.gzsxCode);
    selectedCount.value = gzlbpzList.value.length;
    //console.log("gzlbpzList", gzlbpzList.value);
    total.value = res.total;
    thatToggleRowSelection(gzsxList.value, gzlbpzList.value);
    isReset.value = true;
    buttonLoading.value = false;
    loading.value = false;
  }

  const thatToggleRowSelection = (_gzsxList : GzsxOptionsType[], _gzlbpzList: string[]) => {
    for(let i = 0; i < _gzsxList.length; i++){
      let item = _gzsxList[i];
      let isSelected = _gzlbpzList.indexOf(item.code) > -1;
      gzsxTableRef.value!.toggleRowSelection(item, isSelected); //让页面显示选中的数据
      if(item.children){
        thatToggleRowSelection(item.children, _gzlbpzList);
      }
    }
  }

  const handleReset = () => {
    thatToggleRowSelection(gzsxList.value, gzlbpzList.value);
    isReset.value = true;
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzlbpzVO[]) => {
    ids.value = selection.map(item => item.code);
    isReset.value = selectedCount.value == ids.value.length;
    selectedCount.value = ids.value.length;
  }

  const handleSave = async () => {
    loading.value = true;
    buttonLoading.value = true;
    form.value.gzlbValue = queryParams.value.gzlbValue;
    form.value.gzsxCodeList = ids.value;
    console.log("submit.form", form.value);
    await updateGzlbpz(form.value)
    .finally(() => {
      buttonLoading.value = false;
      loading.value = false;
      isReset.value = true;
    });
    proxy?.$modal.msgSuccess("保存成功");
  }
  onMounted(() => {
    getGzsxTree();
  });
</script>
