import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GxbassChainVo, GxbassChainQuery,GxbassChainForm } from '@/api/guichian/list_types';

/**
 * 查询列表
 * @param query
 * @returns {*}
 */
export const chainList = (query ?: GxbassChainQuery) : AxiosPromise<GxbassChainVo[]> => {
  return request({
    url: '/gxbass/chain/list',
    method: 'get',
    params: query
  });
};

/**
 * 删除测试单
 * @param id
 */
export const delChain = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gxbass/chain/' + id,
    method: 'delete'
  });
};
