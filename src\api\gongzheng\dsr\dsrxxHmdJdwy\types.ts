export interface DsrxxHmdJdwyVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 机构标识
   */
  jgbs: string;

  /**
   * 机构编码
   */
  jgbm: string;

  /**
   * 机构名称
   */
  jgmc: string;

  /**
   * 违约日期
   */
  wyrq: string;

  /**
   * 失信人
   */
  sxr: string;

  /**
   * 失信人证件类型
   */
  sxrzjlx: string;

  /**
   * 失信人证件号码
   */
  sxrzjhm: string;

  /**
   * 失信人姓名
   */
  sxrxm: string;

  /**
   * 借贷违约类型
   */
  jdwylx: number;

  /**
   * 债权人
   */
  zqr: string;

  /**
   * 逾期天数
   */
  yqts: number;

  /**
   * 合同金额
   */
  htje: number;

  /**
   * 拖欠合计
   */
  tjhj: number;

  /**
   * 情况说明
   */
  qksm: string;

  /**
   * 状态
   */
  zt: number;

}

export interface DsrxxHmdJdwyForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 机构标识
   */
  jgbs?: string;

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 违约日期
   */
  wyrq?: string;

  /**
   * 失信人
   */
  sxr?: string;

  /**
   * 失信人证件类型
   */
  sxrzjlx?: string;

  /**
   * 失信人证件号码
   */
  sxrzjhm?: string;

  /**
   * 失信人姓名
   */
  sxrxm?: string;

  /**
   * 借贷违约类型
   */
  jdwylx?: number;

  /**
   * 债权人
   */
  zqr?: string;

  /**
   * 逾期天数
   */
  yqts?: number;

  /**
   * 合同金额
   */
  htje?: number;

  /**
   * 拖欠合计
   */
  tjhj?: number;

  /**
   * 情况说明
   */
  qksm?: string;

  /**
   * 状态
   */
  zt?: number;

}

export interface DsrxxHmdJdwyQuery extends PageQuery {

  /**
   * 机构标识
   */
  jgbs?: string;

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 机构名称
   */
  jgmc?: string;

  /**
   * 违约日期
   */
  wyrq?: string;

  /**
   * 失信人
   */
  sxr?: string;

  /**
   * 失信人证件类型
   */
  sxrzjlx?: string;

  /**
   * 失信人证件号码
   */
  sxrzjhm?: string;

  /**
   * 失信人姓名
   */
  sxrxm?: string;

  /**
   * 借贷违约类型
   */
  jdwylx?: number;

  /**
   * 债权人
   */
  zqr?: string;

  /**
   * 逾期天数
   */
  yqts?: number;

  /**
   * 合同金额
   */
  htje?: number;

  /**
   * 拖欠合计
   */
  tjhj?: number;

  /**
   * 情况说明
   */
  qksm?: string;

  /**
   * 状态
   */
  zt?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



