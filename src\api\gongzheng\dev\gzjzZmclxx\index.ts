import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzZmclxxVO, GzjzZmclxxForm, GzjzZmclxxQuery } from '@/api/gongzheng/dev/gzjzZmclxx/types';

/**
 * 查询公证卷宗-公证证明材料信息-主信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzZmclxx = (query?: GzjzZmclxxQuery): AxiosPromise<GzjzZmclxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证证明材料信息-主信息v1.0详细
 * @param id
 */
export const getGzjzZmclxx = (id: string | number): AxiosPromise<GzjzZmclxxVO> => {
  return request({
    url: '/gongzheng/gzjzZmclxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证证明材料信息-主信息v1.0
 * @param data
 */
export const addGzjzZmclxx = (data: GzjzZmclxxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证证明材料信息-主信息v1.0
 * @param data
 */
export const updateGzjzZmclxx = (data: GzjzZmclxxForm) => {
  return request({
    url: '/gongzheng/gzjzZmclxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证证明材料信息-主信息v1.0
 * @param id
 */
export const delGzjzZmclxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzZmclxx/' + id,
    method: 'delete'
  });
};
