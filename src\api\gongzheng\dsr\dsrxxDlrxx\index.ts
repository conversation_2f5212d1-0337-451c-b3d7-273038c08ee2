import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxDlrxxVO, DsrxxDlrxxForm, DsrxxDlrxxQuery } from '@/api/gongzheng/dsr/dsrxxDlrxx/types';

/**
 * 查询当事人-法人或者其他组织-代理人信息列表
 * @param query
 * @returns {*}
 */

export const listDsrxxDlrxx = (query?: DsrxxDlrxxQuery): AxiosPromise<DsrxxDlrxxVO[]> => {
  return request({
    url: '/dsr/dsrxxDlrxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-法人或者其他组织-代理人信息详细
 * @param id
 */
export const getDsrxxDlrxx = (id: string | number): AxiosPromise<DsrxxDlrxxVO> => {
  return request({
    url: '/dsr/dsrxxDlrxx/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-法人或者其他组织-代理人信息
 * @param data
 */
export const addDsrxxDlrxx = (data: DsrxxDlrxxForm) => {
  return request({
    url: '/dsr/dsrxxDlrxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-法人或者其他组织-代理人信息
 * @param data
 */
export const updateDsrxxDlrxx = (data: DsrxxDlrxxForm) => {
  return request({
    url: '/dsr/dsrxxDlrxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-法人或者其他组织-代理人信息
 * @param id
 */
export const delDsrxxDlrxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxDlrxx/' + id,
    method: 'delete'
  });
};
