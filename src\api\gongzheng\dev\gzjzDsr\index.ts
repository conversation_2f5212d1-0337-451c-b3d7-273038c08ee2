import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzDsrVO, GzjzDsrForm, GzjzDsrQuery } from '@/api/gongzheng/dev/gzjzDsr/types';

/**
 * 查询公证卷宗-当事人v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzDsr = (query?: GzjzDsrQuery): AxiosPromise<GzjzDsrVO[]> => {
  return request({
    url: '/gongzheng/gzjzDsr/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-当事人v1.0详细
 * @param id
 */
export const getGzjzDsr = (id: string | number): AxiosPromise<GzjzDsrVO> => {
  return request({
    url: '/gongzheng/gzjzDsr/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-当事人v1.0
 * @param data
 */
export const addGzjzDsr = (data: GzjzDsrForm) => {
  return request({
    url: '/gongzheng/gzjzDsr',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-当事人v1.0
 * @param data
 */
export const updateGzjzDsr = (data: GzjzDsrForm) => {
  return request({
    url: '/gongzheng/gzjzDsr',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-当事人v1.0
 * @param id
 */
export const delGzjzDsr = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzDsr/' + id,
    method: 'delete'
  });
};
