<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="110px">
            <el-form-item label="失信人/单位" prop="sxr">
              <el-input v-model="queryParams.sxr" placeholder="请输入失信人/单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码" prop="sxrzjhm">
              <el-input v-model="queryParams.sxrzjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="失信类型" prop="sxrzjlx">
              <el-select v-model="queryParams.sxrzjlx" placeholder="请选择失信类型" clearable>
                <el-option v-for="dict in gz_sxlx" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable>
                <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['dsr:dsrhmdxx:add']">录入</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrhmdxxList" @selection-change="handleSelectionChange">
        <el-table-column label="查获机构" align="center" prop="jgmc" />
        <el-table-column label="查获日期" align="center" prop="chrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.chrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="失信类型" align="center" prop="sxrzjlx">
          <template #default="scope">
            <dict-tag :options="gz_sxlx" :value="scope.row.sxrzjlx" />
          </template>
        </el-table-column>
        <el-table-column label="失信人/单位" align="center" prop="sxr" />
        <el-table-column label="证件类型" align="center" prop="sxrzjlx">
          <template #default="scope">
            <dict-tag :options="gz_gr_zjlx" :value="scope.row.sxrzjlx" />
          </template>
        </el-table-column>
        <el-table-column label="证件号码" align="center" prop="sxrzjhm" />
        <el-table-column label="状态" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_hmd_zt" :value="scope.row.zt" />
          </template>
        </el-table-column>
        <el-table-column label="录入人" align="center" prop="createByName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                v-hasPermi="['dsr:dsrhmdxx:query']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['dsr:dsrhmdxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="恢复" placement="top">
              <el-button link type="primary" icon="Refresh" @click="handleRecover(scope.row)"
                v-hasPermi="['dsr:dsrhmdxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['dsr:dsrhmdxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改当事人-黑名单信息（风险信息）对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body>
      <el-row>
        <el-col :span="18">
          <el-form ref="dsrhmdxxFormRef" :model="form" :rules="rules" label-width="120px">
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="主体类型" prop="sxrzjlx">
                  <el-select :disabled="!editShow" v-model="form.sxrzjlx" placeholder="请选择主体类型" clearable>
                    <el-option v-for="dict in gz_dsr_lb" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item>
                  <el-button v-if="editShow" type="primary" @click="toSelectDsr">引用</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="姓名" prop="sxr">
                  <el-input :disabled="!editShow" v-model="form.sxr" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="证件类型" prop="sxrzjlx">
                  <el-select :disabled="!editShow" v-model="form.sxrzjlx" placeholder="请选择证件类型">
                    <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="证件号码" prop="sxrzjhm">
                  <el-input :disabled="!editShow" v-model="form.sxrzjhm" placeholder="请输入证件号码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="查获机构" prop="jgmc">
                  <el-input  v-model="form.jgmc" disabled readonly />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="查获日期" prop="chrq">
                  <el-date-picker :disabled="!editShow" clearable v-model="form.chrq" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择查获日期">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="失信类型" prop="lx">
                  <el-select :disabled="!editShow" v-model="form.lx" placeholder="请选择失信类型">
                    <el-option v-for="dict in gz_sxlx" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="公证事项" prop="">
                  <el-input :disabled="!editShow" v-model="form.sxrzjhm" placeholder="请输入公证事项" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="虚假类型" prop="xjlx">
                  <el-select :disabled="!editShow" v-model="form.xjlx" placeholder="请选择假证类型">
                    <el-option v-for="dict in gz_xjlx" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="假证类型" prop="jzlx">
                  <el-select :disabled="!editShow" v-model="form.jzlx" placeholder="请选择假证类型">
                    <el-option v-for="dict in gz_dsr_jzlx" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>

                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="假证号码" prop="jzhm">
                  <el-input :disabled="!editShow" v-model="form.jzhm" placeholder="请输入假证号码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="状态" prop="zt">
                  <el-select :disabled="!editShow" v-model="form.zt" placeholder="请选择状态">
                    <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="情况说明" prop="qksm">
                  <el-input :disabled="!editShow" v-model="form.qksm" type="textarea" placeholder="请输入情况说明" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="6">
          <div class="pc-img-div">
            <el-image :src="form.zp" class="pic-img"></el-image>
            <el-button v-if="editShow" type="primary" link @click="savePhoto">拍照</el-button>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="editShow" :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 引用当事人-->
    <el-dialog :title="dialog4.title" v-model="dialog4.visible" append-to-body>
      <Yydsr v-if="dialog4.visible" ref="yydsrRef" :editDlrlb="disabledDlrlb" @update-select="handledUpdateDsrSelectId">
      </Yydsr>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading2" type="primary" @click="submitForm2">确认引用</el-button>
          <el-button @click="cancel2">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 拍照-->
    <el-dialog :title="dialog5.title" v-model="dialog5.visible" width="500px" append-to-body>
      <Pz v-if="dialog5.visible" ref="pzRef" @update-count="handleUpdatePhoto"></Pz>
    </el-dialog>
  </div>
</template>

<script setup name="Dsrhmd" lang="ts">
  import { listDsrhmdxx, getDsrhmdxx, delDsrhmdxx, addDsrhmdxx, updateDsrhmdxx } from '@/api/gongzheng/dsr/dsrhmdxx';
  import { DsrhmdxxVO, DsrhmdxxQuery, DsrhmdxxForm } from '@/api/gongzheng/dsr/dsrhmdxx/types';
  import api from '@/api/system/user';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gr_zjlx, gz_hmd_zt, gz_sxlx, gz_dsr_jzlx, gz_xjlx, gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_dsr_lb', 'gz_xjlx', 'gz_gr_zjlx', 'gz_hmd_zt', 'gz_sxlx', 'gz_dsr_jzlx'));

  const dsrhmdxxList = ref<DsrhmdxxVO[]>([]);
  const buttonLoading = ref(false);

  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const img = ref(null);
  const queryFormRef = ref<ElFormInstance>();
  const dsrhmdxxFormRef = ref<ElFormInstance>();
  const editShow = ref(true);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrhmdxxForm = {
    id: undefined,
    jgbs: undefined,
    jgbm: undefined,
    jgmc: undefined,
    chrq: undefined,
    sxr: undefined,
    sxrId: undefined,
    sxrzjlx: undefined,
    sxrzjhm: undefined,
    jzlx: undefined,
    jzhm: undefined,
    qksm: undefined,
    zt: undefined,
    mcdx: undefined,
    xjlx: undefined,
    lx: undefined,
    remark: undefined,
    zp: undefined,
  }
  const data = reactive<PageData<DsrhmdxxForm, DsrhmdxxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      jgbs: undefined,
      jgbm: undefined,
      jgmc: undefined,
      chrq: undefined,
      sxr: undefined,
      sxrzjlx: undefined,
      sxrzjhm: undefined,
      jzlx: undefined,
      jzhm: undefined,
      qksm: undefined,
      zt: undefined,
      mcdx: undefined,
      lx: undefined,
      params: {
      }
    },
    rules: {
      sxr: [
        { required: true, message: "请填写姓名", trigger: "blur" }
      ],
      sxrzjhm: [
        { required: true, message: "请填写证件号码", trigger: "blur" }
      ],
      chrq: [
        { required: true, message: "请填写查获日期", trigger: "blur" }
      ],
      jgmc: [
        { required: true, message: "请选择查获机构", trigger: "change" }
      ],
      qksm: [
        { required: true, message: "请填写情况说明", trigger: "blur" }
      ],
      zt: [
        { required: true, message: "请选择状态", trigger: "change" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);
  /*** 当事人引用 */
  import Yydsr from '@/views/gongzheng/dsr/dsrxxZrr/components/yy_dsr.vue'
  const yydsrRef = ref<InstanceType<typeof Yydsr> | null>(null);
  const buttonLoading2 = ref(false);
  const dialog4 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dlrInfo = ref(null);
  const disabledDlrlb = ref(false);
  /*** 当事人引用 */

  /** 拍照*/
  import Pz from '@/components/Gongzheng/pz/pz_no_upload.vue'
  const pzRef = ref<InstanceType<typeof Pz> | null>(null);
  const dialog5 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  /** 上传成功后回传 */
  const handleUpdatePhoto = (img) => {
    form.value.zp = img;
    dialog5.visible = false;
    dialog5.title = "";
  }
  /** 拍照*/
  const savePhoto = () => {
    dialog5.visible = true;
    dialog5.title = "拍照";
  }

  /** 拍照*/

  /** 查询当事人-黑名单信息（风险信息）列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDsrhmdxx(queryParams.value);
    dsrhmdxxList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrhmdxxFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrhmdxxVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    handleGetLoginOrg();
    editShow.value = true;
    dialog.visible = true;
    dialog.title = "添加当事人风险信息";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrhmdxxVO) => {
    reset();
    handleGetLoginOrg();
    const _id = row?.id || ids.value[0]
    const res = await getDsrhmdxx(_id);
    Object.assign(form.value, res.data);
    editShow.value = true;
    dialog.visible = true;
    dialog.title = "修改当事人风险信息";
  }

  const handleRecover = async (row: DsrhmdxxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm(`是否确认恢复当事人: ${row.sxr} - ${_ids} 的黑名单状态`).finally(() => loading.value = false);
    const params = {
      ...row,
      zt: 1,
    }
    updateDsrhmdxx(params).then((res) => {
      proxy?.$modal.msgSuccess("恢复成功");
      getList();
    }).catch((err: any) => {
      proxy?.$modal.msgError('恢复失败');
    });
  }

  const handleDetail = async (row ?: DsrhmdxxVO) => {
    reset();
    handleGetLoginOrg();
    const _id = row?.id || ids.value[0]
    const res = await getDsrhmdxx(_id);
    Object.assign(form.value, res.data);
    editShow.value = false;
    dialog.visible = true;
    dialog.title = "查看当事人风险信息";
  }


  /** 提交按钮 */
  const submitForm = () => {
    dsrhmdxxFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateDsrhmdxx(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDsrhmdxx(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DsrhmdxxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除当事人风险信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrhmdxx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('dsr/dsrhmdxx/export', {
      ...queryParams.value
    }, `dsrhmdxx_${new Date().getTime()}.xlsx`)
  }
  /***获取当前登录账号所属机构 */
  const handleGetLoginOrg = async () => {
    const data = await api.getInfoByDeptInfo();
    const dept = data.data;
    form.value.jgbs = dept.deptId;
    // form.value.jgbm = dept.deptId;
    form.value.jgmc = dept.deptName
  }

  const toSelectDsr = () => {
    dialog4.title = "引用当事人";
    dialog4.visible = true;
  }
  // 确认选择 代理人
  const submitForm2 = () => {
    console.log(dlrInfo.value)
    if (dlrInfo.value == null) {
      proxy?.$modal.msgError("请选择当事人");
    } else {
      form.value.sxrzjlx = dlrInfo.value.dsrlb;
      if (dlrInfo.value.dsrlb === '1') {
        form.value.sxr = dlrInfo.value.xm;
        form.value.sxrId = dlrInfo.value.id;
        form.value.sxrzjlx = dlrInfo.value.zjlx;
        form.value.sxrzjhm = dlrInfo.value.zjhm;
      } else if (dlrInfo.value.dsrlb === '2') {
        form.value.sxr = dlrInfo.value.dwmc;
        form.value.sxrId = dlrInfo.value.id;
        form.value.sxrzjlx = dlrInfo.value.zjlx;
        form.value.sxrzjhm = dlrInfo.value.zjhm;
      }
      dlrInfo.value = null;
      dialog4.title = "";
      dialog4.visible = false;
    }

  }
  // 刷新代理人选择的 id
  const handledUpdateDsrSelectId = (selectDlrInfo) => {
    dlrInfo.value = selectDlrInfo;
  }
  const cancel2 = () => {
    dialog4.title = "";
    dialog4.visible = false;
  }

  onMounted(() => {

    getList();
  });
</script>

<style scoped>
  .pc-img-div {
    padding: 10px;
    margin: 10px;
  }

  .pic-img {
    width: 300px;
    height: 250px;
  }
</style>
