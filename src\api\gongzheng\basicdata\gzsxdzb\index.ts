import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzsxdzbVO, GzsxdzbForm, GzsxdzbQuery } from '@/api/basicdata/gzsxdzb/types';

/**
 * 查询公证事项对照列表
 * @param query
 * @returns {*}
 */

export const listGzsxdzb = (query?: GzsxdzbQuery): AxiosPromise<GzsxdzbVO[]> => {
  return request({
    url: '/basicdata/gzsxdzb/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证事项对照详细
 * @param id
 */
export const getGzsxdzb = (id: string | number): AxiosPromise<GzsxdzbVO> => {
  return request({
    url: '/basicdata/gzsxdzb/' + id,
    method: 'get'
  });
};

/**
 * 新增公证事项对照
 * @param data
 */
export const addGzsxdzb = (data: GzsxdzbForm) => {
  return request({
    url: '/basicdata/gzsxdzb',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证事项对照
 * @param data
 */
export const updateGzsxdzb = (data: GzsxdzbForm) => {
  return request({
    url: '/basicdata/gzsxdzb',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证事项对照
 * @param id
 */
export const delGzsxdzb = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsxdzb/' + id,
    method: 'delete'
  });
};
