<template>
  <div class="zz-container">
    <div class="page-header">
      <div class="flex items-center gap-12px flex-wrap">
        <span class="flex items-center">
          <el-text size="small">卷宗号：</el-text>
          <el-text type="info" size="small">{{ curGzjz.jzbh || '-' }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">公证员：</el-text>
          <el-text type="info" size="small">{{ curGzjz.gzyxm }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">助理：</el-text>
          <el-text type="info" size="small">{{ curGzjz.zlxm }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">受理日期：</el-text>
          <el-text type="info" size="small">{{ formatDate(curGzjz.slrq) }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">公证类别：</el-text>
          <el-text type="info" size="small">{{ curGzjz.lb }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">使用地：</el-text>
          <el-text type="info" size="small">{{ curGzjz.syd }}</el-text>
        </span>
        <span class="flex items-center">
          <el-text size="small">译文文种：</el-text>
          <el-text type="info" size="small">{{ curGzjz.yw }}</el-text>
        </span>
      </div>
      <div class="basic-info">
        <div class="info-item">
          <span class="label">卷宗号：</span>
          <span class="value">{{ basicInfo.recordNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">公证员：</span>
          <span class="value">{{ basicInfo.notaryOfficer }}</span>
        </div>
        <div class="info-item">
          <span class="label">助理/受理人：</span>
          <span class="value">{{ basicInfo.assistant }}</span>
        </div>
        <div class="info-item">
          <span class="label">受理日期：</span>
          <span class="value">{{ basicInfo.acceptanceDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">公证类别：</span>
          <span class="value">{{ basicInfo.notaryType }}</span>
        </div>
        <div class="info-item">
          <span class="label">使用地：</span>
          <span class="value">{{ basicInfo.useLocation }}</span>
        </div>
        <div class="info-item">
          <span class="label">译文文种：</span>
          <span class="value">{{ basicInfo.translationLanguage }}</span>
        </div>
      </div>
      <div class="info-item requirement">
        <span class="label">制证要求：</span>
        <span class="value">{{ basicInfo.requirements }}</span>
      </div>
    </div>

    <!-- 制证列表 -->
    <div class="section-container">
      <div class="section-header">制证列表</div>
      <div class="section-content">
        <el-table :data="certificateList" border stripe>
          <el-table-column prop="notaryMatter" label="公证事项" align="center" />
          <el-table-column prop="notaryNo" label="公证书编号" align="center" />
          <el-table-column prop="notaryDoc" label="公证书" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="viewDocument(scope.row, 'notaryDoc')">
                {{ scope.row.notaryDoc }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="combinedDoc" label="合成公证书" align="center" />
          <el-table-column prop="notaryTranslation" label="公证书译文" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="viewDocument(scope.row, 'notaryTranslation')">
                {{ scope.row.notaryTranslation }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="translationOperation" label="译文操作" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="handleTranslation(scope.row)">
                {{ scope.row.translationOperation }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="copies" label="公证书份数" align="center" width="100" />
          <el-table-column prop="hasBackup" label="盖章" align="center" width="80" />
          <el-table-column prop="hasPrinted" label="打印" align="center" width="80" />
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button type="primary" link @click="handleCombine(scope.row)">
                合成公证书
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 文书和水印纸使用清单 -->
    <div class="dual-section">
      <!-- 文书 -->
      <div class="section-container half-width">
        <div class="section-header">文书</div>
        <div class="section-content">
          <el-table :data="documentsList" border stripe>
            <el-table-column prop="name" label="文书名称" align="center" />
            <el-table-column prop="translation" label="译文" align="center">
              <template #default="scope">
                <el-button v-if="scope.row.translation" type="primary" link @click="viewTranslation(scope.row)">
                  {{ scope.row.translation }}
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="translationOperation" label="译文操作" align="center">
              <template #default="scope">
                <el-button v-if="scope.row.translationOperation" type="primary" link
                  @click="handleDocTranslation(scope.row)">
                  {{ scope.row.translationOperation }}
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 水印纸使用清单 -->
      <div class="section-container half-width">
        <div class="section-header">水印纸使用清单</div>
        <div class="section-content">
          <el-table :data="watermarkList" border stripe>
            <el-table-column prop="watermarkNo" label="水印纸编号" align="center" />
            <el-table-column prop="notaryNo" label="公证书编号" align="center" />
            <el-table-column prop="useDate" label="使用日期" align="center" />
            <el-table-column prop="status" label="状态" align="center" />
            <el-table-column label="操作" align="center" width="120">
              <template #default="scope">
                <el-button type="primary" link @click="handleWatermarkOperation(scope.row)">
                  操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="handleReturn">驳回</el-button>
      <!-- <el-button @click="handlePrinterSettings">打印机设置</el-button> -->
      <el-button @click="handleSignature">签发稿</el-button>
      <el-button @click="handleApprovalForm">审批表</el-button>
      <!-- <el-button @click="handlePrintNotary">打印公证书</el-button>
      <el-button @click="handlePrintDoc">打印文书</el-button>
      <el-button @click="handlePrintNotaryTranslation">打印公证书译文</el-button>
      <el-button @click="handlePrintDocTranslation">打印文书译文</el-button> -->
      <el-button @click="handleVolumeSealing">卷宗封皮</el-button>
      <el-button @click="handleGenerateFeeList">生成缴费清单</el-button>
      <!-- <el-button @click="handleReservePrint">留底打印</el-button> -->
      <!-- <el-button @click="handleReservePrintInProcess">留底打印(中)</el-button> -->
      <el-button @click="handleManualWatermark">手动取水印纸</el-button>
      <el-button @click="handleApplyTranslation">申请翻译</el-button>
      <el-button @click="handleGenerateDelivery">生成送达回执</el-button>
      <el-button @click="handleGenerateResult">制证结束并通知</el-button>
    </div>
    <PopFormByZzhtz ref="PopFormByZzhtzRef" @callback="callback" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import PopFormByZzhtz from './popFormByZzhtz.vue';
  import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
  import { formatDate, dictMapFormat, nodeFilter } from '@/utils/ruoyi';

  const PopFormByZzhtzRef = ref<InstanceType<typeof PopFormByZzhtz>>(null);

  const gzjzId = inject<Ref<string | number>>('currentRecordId', ref(null));
  // 由父组件传入的卷宗信息
  const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

  // 基本信息数据
  const basicInfo = reactive({
    recordNo: '202500954',
    notaryOfficer: '黄瑞光',
    assistant: '周瑜',
    acceptanceDate: '2025年04月09日',
    notaryType: '涉外民事',
    useLocation: '老挝',
    translationLanguage: '英文',
    requirements: ''
  })

  // 制证列表数据
  const certificateList = ref([
    {
      id: '1',
      notaryMatter: '文本相符',
      notaryNo: '(2025) 桂东博证字第665号',
      notaryDoc: '证词',
      combinedDoc: '',
      notaryTranslation: '译文',
      translationOperation: '取回',
      copies: '1',
      hasBackup: '否',
      hasPrinted: ''
    },
    {
      id: '2',
      notaryMatter: '文本相符',
      notaryNo: '(2025) 桂东博证字第666号',
      notaryDoc: '证词',
      combinedDoc: '',
      notaryTranslation: '译文',
      translationOperation: '取回',
      copies: '1',
      hasBackup: '否',
      hasPrinted: ''
    },
    {
      id: '3',
      notaryMatter: '文本相符',
      notaryNo: '(2025) 桂东博证字第667号',
      notaryDoc: '证词',
      combinedDoc: '',
      notaryTranslation: '译文',
      translationOperation: '取回',
      copies: '1',
      hasBackup: '否',
      hasPrinted: ''
    }
  ])

  // 文书列表数据
  const documentsList = ref([
    // 图片中没有显示具体的文书记录，所以这里留空
  ])

  // 水印纸使用清单数据
  const watermarkList = ref([
    // 图片中没有显示具体的水印纸记录，所以这里留空
  ])

  // 查看文档
  const viewDocument = (row : any, type : string) => {
    ElMessage.info(`查看${type === 'notaryDoc' ? '公证书' : '译文'}: ${row.notaryNo}`)
  }

  // 处理翻译
  const handleTranslation = (row : any) => {
    ElMessage.info(`处理翻译: ${row.notaryNo}`)
  }

  // 查看译文
  const viewTranslation = (row : any) => {
    ElMessage.info(`查看译文: ${row.name}`)
  }

  // 处理文书翻译
  const handleDocTranslation = (row : any) => {
    ElMessage.info(`处理文书翻译: ${row.name}`)
  }

  // 处理水印纸操作
  const handleWatermarkOperation = (row : any) => {
    ElMessage.info(`处理水印纸: ${row.watermarkNo}`)
  }

  // 合成公证书
  const handleCombine = (row : any) => {
    ElMessage.info(`合成公证书: ${row.notaryNo}`)
  }

  // 以下是底部按钮的处理函数
  const handleReturn = () => {
    ElMessage.info('执行取回操作')
  }

  const handlePrinterSettings = () => {
    ElMessage.info('打开打印机设置')
  }

  const handleSignature = () => {
    ElMessage.info('打开签发稿')
  }

  const handleApprovalForm = () => {
    ElMessage.info('打开审批表')
  }

  const handlePrintNotary = () => {
    ElMessage.info('打印公证书')
  }

  const handlePrintDoc = () => {
    ElMessage.info('打印文书')
  }

  const handlePrintNotaryTranslation = () => {
    ElMessage.info('打印公证书译文')
  }

  const handlePrintDocTranslation = () => {
    ElMessage.info('打印文书译文')
  }

  const handleVolumeSealing = () => {
    ElMessage.info('生成卷宗封皮')
  }

  const handleGenerateFeeList = () => {
    ElMessage.info('生成缴费清单')
  }

  const handleReservePrint = () => {
    ElMessage.info('执行留底打印')
  }

  const handleReservePrintInProcess = () => {
    ElMessage.info('执行留底打印(中)')
  }

  const handleManualWatermark = () => {
    ElMessage.info('手动取水印纸')
  }

  const handleApplyTranslation = () => {
    ElMessage.info('申请翻译')
  }

  const handleGenerateDelivery = () => {
    ElMessage.info('生成送达回执')
  }

  const handleGenerateResult = () => {
    PopFormByZzhtzRef.value.open(gzjzId.value);
  }

  const callback = (_result ?: any) => {

  }
</script>

<style scoped>
  .zz-container {
    /* padding: 20px; */
    height: 100%;
    /* background: #f5f7fa; */
    overflow: auto;
  }

  .page-header {
    background: #f5f7fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .basic-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
  }

  .info-item {
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .requirement {
    width: 100%;
  }

  .label {
    color: #606266;
    font-weight: bold;
  }

  .value {
    color: #303133;
  }

  .section-container {
    background: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .section-header {
    padding: 12px 15px;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  .section-content {
    padding: 0;
  }

  .dual-section {
    display: flex;
    gap: 20px;
  }

  .half-width {
    flex: 1;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
  }

  /* 响应式样式 */
  @media (max-width: 1200px) {
    .dual-section {
      flex-direction: column;
    }

    .half-width {
      width: 100%;
    }
  }
</style>
