<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="模板名称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入模板名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务ID" prop="ywId">
              <el-input v-model="queryParams.ywId" placeholder="请输入业务ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mb:mbJcxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mb:mbJcxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mb:mbJcxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mb:mbJcxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="mbJcxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="模板名称" align="center" prop="title" />
        <el-table-column label="模板类型" align="center" prop="templateType" />
        <el-table-column label="业务ID" align="center" prop="ywId" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="模板分类(字典 告知书、公证事项、其他)" align="center" prop="classify" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mb:mbJcxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mb:mbJcxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改模板-模板基础信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mbJcxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="业务ID" prop="ywId">
          <el-input v-model="form.ywId" placeholder="请输入业务ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MbJcxx" lang="ts">
import { listMbJcxx, getMbJcxx, delMbJcxx, addMbJcxx, updateMbJcxx } from '@/api/gongzheng/mb/mbJcxx';
import { MbJcxxVO, MbJcxxQuery, MbJcxxForm } from '@/api/gongzheng/mb/mbJcxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const mbJcxxList = ref<MbJcxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const mbJcxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MbJcxxForm = {
  id: undefined,
  title: undefined,
  templateType: undefined,
  ywId: undefined,
  remark: undefined,
  classify: undefined
}
const data = reactive<PageData<MbJcxxForm, MbJcxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    templateType: undefined,
    ywId: undefined,
    classify: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
    title: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    templateType: [
      { required: true, message: "模板类型不能为空", trigger: "change" }
    ],
    ywId: [
      { required: true, message: "业务ID不能为空", trigger: "blur" }
    ],
    classify: [
      { required: true, message: "模板分类(字典 告知书、公证事项、其他)不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询模板-模板基础信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMbJcxx(queryParams.value);
  mbJcxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  mbJcxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MbJcxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加模板-模板基础信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MbJcxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMbJcxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改模板-模板基础信息";
}

/** 提交按钮 */
const submitForm = () => {
  mbJcxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMbJcxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMbJcxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MbJcxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除模板-模板基础信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMbJcxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('mb/mbJcxx/export', {
    ...queryParams.value
  }, `mbJcxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
