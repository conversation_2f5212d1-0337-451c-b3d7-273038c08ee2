<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="材料名称" prop="catalogue">
              <el-input v-model="queryParams.catalogue" placeholder="请输入材料名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:dzjzml:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['basicdata:dzjzml:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dzjzmlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="顺序" align="center" prop="orderNumber" />
        <el-table-column label="材料名称" align="center" prop="catalogue" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="gz_dzjz_zt" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:dzjzml:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:dzjzml:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改电子卷宗目录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="dzjzmlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="材料名称" prop="catalogue">
          <el-input v-model="form.catalogue" placeholder="请输入材料名称" />
        </el-form-item>
        <el-form-item label="顺序" prop="orderNumber">
          <el-input v-model="form.orderNumber" placeholder="请输入顺序" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in gz_dzjz_zt" :key="dict.value"
              :value="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="父级" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父级" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dzjzml" lang="ts">
  import { listDzjzml, getDzjzml, delDzjzml, addDzjzml, updateDzjzml } from '@/api/gongzheng/basicdata/dzjzml';
  import { DzjzmlVO, DzjzmlQuery, DzjzmlForm } from '@/api/gongzheng/basicdata/dzjzml/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dzjz_zt } = toRefs<any>(proxy?.useDict('gz_dzjz_zt'));

  const dzjzmlList = ref<DzjzmlVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const dzjzmlFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DzjzmlForm = {
    id: undefined,
    catalogue: undefined,
    orderNumber: undefined,
    status: undefined,
    parentId: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<DzjzmlForm, DzjzmlQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      catalogue: undefined,
      status: undefined,
      parentId: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      catalogue: [
        { required: true, message: "材料名称不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询电子卷宗目录列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDzjzml(queryParams.value);
    dzjzmlList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dzjzmlFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DzjzmlVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加电子卷宗目录";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DzjzmlVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDzjzml(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改电子卷宗目录";
  }

  /** 提交按钮 */
  const submitForm = () => {
    dzjzmlFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateDzjzml(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDzjzml(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DzjzmlVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除电子卷宗目录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDzjzml(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/dzjzml/export', {
      ...queryParams.value
    }, `dzjzml_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
