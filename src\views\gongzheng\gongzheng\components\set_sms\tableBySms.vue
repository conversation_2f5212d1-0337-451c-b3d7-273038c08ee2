<template>
  <!-- 短信预设列表 -->
  <div>
    <el-table ref="multipleTableRef" v-loading="loading" :data="dsrList" @selection-change="handleSelectionChange" height="300px" border size="small">
      <el-table-column type="selection" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="角色类型" prop="js" align="center">
        <template #default="{row}">
          <dict-tag :options="gz_dsr_jslx" :value="row.js"/>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" align="center">
      </el-table-column>
      <el-table-column label="证件类型" prop="certificateType" align="center">
        <template #default="{row}">
          <dict-tag :options="row.dsrLx === '1' ? gz_gr_zjlx : gz_jg_zjlx" :value="row.certificateType"/>
        </template>
      </el-table-column>
      <el-table-column label="证件号码" prop="certificateNo" align="center">
      </el-table-column>
      <el-table-column label="联系电话" prop="contactTel" align="center">
        <template #default="{row}">
          <div v-if="checkPhone(row.contactTel)">{{row.contactTel}}</div>
          <div v-else style="color: red;">{{row.contactTel}}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="TableBySms" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { listGzjzDsrByGzjz, updateBySMSGzjzDsr } from '@/api/gongzheng/gongzheng/gzjzDsr'
  import type { GzjzDsrVO, GzjzDsrForm } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
  import { AxiosPromise } from 'axios';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dsr_jslx, gz_gr_zjlx, gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_dsr_jslx', 'gz_gr_zjlx', 'gz_jg_zjlx'));

  const multipleTableRef = ref<ElTableInstance>();

  const view = inject<Ref<boolean>>('view', ref(false));
  const gzjzId = inject<Ref<string | number>>('gzjzId', ref(null));

  const checkPhone = (_phone ?: string) => {
    const reg = /^1[3456789]\d{9}$/;
    if (!_phone){
      return false;
    }
    if (!reg.test(_phone)) {
      console.log(_phone + "手机号码有误");
      return false;
    }
    return true;
  }
  // 回显勾选的列
  const toggleSelection = (rows?: GzjzDsrVO[]) => {
    if (rows) {
      nextTick(() => {
        rows.forEach((item, index) => {
          console.log('toggleSelection: rows.sfdxtz=' + item.sfdxtz + ', index=' + index)
          if(item.sfdxtz === '1'){
            multipleTableRef.value.toggleRowSelection(item, true, true);
          }
        });
      });
      ids.value = rows.filter(item => item.sfdxtz === '1').map(item => item.id);
      multiple.value = !ids.value.length;
    } else {
      multipleTableRef.value.clearSelection();
      ids.value = [];
      multiple.value = !ids.value.length;;
    }
  };

  const loading = ref(false);
  const btnLoading = ref(false);
  const dsrList = ref<GzjzDsrVO[]>([]);

  const getDsrList = async () => {
    loading.value = true;
    try {
      const res = await listGzjzDsrByGzjz({
        gzjzId: gzjzId.value,
        pageNum: 1,
        pageSize: 1000
      });
      dsrList.value = res.rows.filter(item => item.contactTel) || [];
      toggleSelection(dsrList.value);
    } catch (error) {
      console.error('获取卷宗当事人信息失败:', error);
      proxy?.$modal.msgError('获取卷宗当事人信息失败，请重试！');
    } finally {
      loading.value = false;
    }
  }

  const ids = ref<Array<string | number>>([]);
  const multiple = ref(true);
  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzjzDsrVO[]) => {
    ids.value = selection.map((item : GzjzDsrVO) => item.id);
    multiple.value = !selection.length;
  };
  // 提交保存
  const submitForm = async () => {
    btnLoading.value = true;
    const _ids = !ids.value.length ? [1] : ids.value;
    console.log("submitForm: ids=" + _ids, _ids);
    const formData = {
      gzjzId: gzjzId.value
    }
    await updateBySMSGzjzDsr(_ids, formData).finally(() =>  btnLoading.value = false);
    proxy?.$modal.msgSuccess("操作成功");
    return {success: true, msssage: '操作成功'};
  }
  // 监听
  watch(() => view.value, (newVal: boolean) => {
    if(newVal){
      loadData();
    }else{
      rest();
    }
  });

  const loadData = () => {
    getDsrList();
  }
  const rest = () => {
    loading.value = false;
    btnLoading.value = false;
    dsrList.value = [];
    toggleSelection();
  }
  // 暴露方法给父组件
  defineExpose({
    loadData,
    submitForm,
    toggleSelection
  });

  onMounted(() => {
    loadData();
  });


</script>

<style>
</style>
