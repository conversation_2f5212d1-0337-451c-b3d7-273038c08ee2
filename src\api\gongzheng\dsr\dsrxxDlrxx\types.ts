export interface DsrxxDlrxxVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 当事人
   */
  dsrId: string | number;

  /**
   * 姓名
   */
  xm: string;

  /**
   * 证件类型
   */
  zjlx: string;

  /**
   * 证件号码
   */
  zjhm: string;

  /**
   * 代理人身份
   */
  dlrsf: string;

}

export interface DsrxxDlrxxForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 当事人
   */
  dsrId?: string | number;

  /**
   * 姓名
   */
  xm?: string;

  /**
   * 证件类型
   */
  zjlx?: string;

  /**
   * 证件号码
   */
  zjhm?: string;

  /**
   * 代理人身份
   */
  dlrsf?: string;

}

export interface DsrxxDlrxxQuery extends PageQuery {

  /**
   * 当事人
   */
  dsrId?: string | number;

  /**
   * 姓名
   */
  xm?: string;

  /**
   * 证件类型
   */
  zjlx?: string;

  /**
   * 证件号码
   */
  zjhm?: string;

  /**
   * 代理人身份
   */
  dlrsf?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



