import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AreanameVO, AreanameForm, AreanameQuery } from '@/api/gongzheng/basicdata/areaname/types';

/**
 * 查询地区名称列表
 * @param query
 * @returns {*}
 */

export const listAreaname = (query ?: AreanameQuery) : AxiosPromise<AreanameVO[]> => {
  return request({
    url: '/basicdata/areaname/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询地区名称详细
 * @param id
 */
export const getAreaname = (id : string | number) : AxiosPromise<AreanameVO> => {
  return request({
    url: '/basicdata/areaname/' + id,
    method: 'get'
  });
};

/**
 * 新增地区名称
 * @param data
 */
export const addAreaname = (data : AreanameForm) => {
  return request({
    url: '/basicdata/areaname',
    method: 'post',
    data: data
  });
};

/**
 * 修改地区名称
 * @param data
 */
export const updateAreaname = (data : AreanameForm) => {
  return request({
    url: '/basicdata/areaname',
    method: 'put',
    data: data
  });
};

/**
 * 删除地区名称
 * @param id
 */
export const delAreaname = (id : string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/areaname/' + id,
    method: 'delete'
  });
};

export const updateShowStatus = (id : string | number | Array<string | number>, status : string | number) => {
  return request({
    url: '/basicdata/areaname/updateShowStatus/' + id,
    method: 'post',
    params: {
      status: status
    }
  });
};

export const initBaseData = () => {
  return request({
    url: '/basicdata/areaname/initBaseData',
    method: 'post',
    params: {}
  });
};

