export interface GzjzJbxxVO {
  /**
   * $column.columnComment
   */
  id: string | number;

  /**
   * 机构编码
   */
  jgbm: string;

  /**
   * 受理编号
   */
  slbh: string;

  /**
   * 卷宗编号
   */
  jzbh: string;

  /**
   * 公证书编号（多个）
   */
  gzsbh: string;

  /**
   * 归档人姓名
   */
  gdrxm: string;

  /**
   * 归档日期
   */
  gdrq: string;

  /**
   * 保管/档案期限
   */
  bgqx: string;

  /**
   * 保管类型
   */
  bglx: string;

  /**
   * 公证类别
   */
  lb: string;

  /**
   * 公证事项（事务树）
   */
  gzsx: string;

  /**
   * 流程状态
   */
  lczt: string;

  /**
   * 公证员ID
   */
  gzybm: number;

  /**
   * 公证员姓名
   */
  gzyxm: string;

  /**
   * 助理ID
   */
  zlbm: number;

  /**
   * 助理姓名
   */
  zlxm: string;

  /**
   * 受理地点
   */
  sldd: string;

  /**
   * 受理日期
   */
  slrq: string;

  /**
   * 使用地
   */
  syd: string;

  /**
   * 是否认证
   */
  rz: string;

  /**
   * 紧急度
   */
  jjd: string;

  /**
   * 领证地点
   */
  lzdd: string;

  /**
   * 领证日期
   */
  lzrq: string;

  /**
   * 用途
   */
  yt: string;

  /**
   * 译文文种
   */
  ywwz: string;

  /**
   * 是否密卷
   */
  sfmj: string;

  /**
   * 档案类型
   */
  dalx: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 协办人ID
   */
  xbrbh: string;

  /**
   * 协办人姓名
   */
  xbrxm: string;

  /**
   * 法律援助
   */
  flxz: string;

  /**
   * 是否外译中
   */
  sfwyz: string;

  /**
   * 电票领取电话
   */
  dplqdh: string;

  /**
   * 是否零接触
   */
  sfljc: string;

  /**
   * 是否电子签名
   */
  sfdzqm: string;

  /**
   * 是否电子公证书
   */
  sfdzgzs: string;

  /**
   * 归档人ID
   */
  gdrbh: string;

  /**
   * 翻译人ID
   */
  fyrbh: number;

  /**
   * 翻译人姓名
   */
  fyrxm: string;

  /**
   * 外部订单号
   */
  wbddh: string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx: string;

  /**
   * 制证时间
   */
  zzsj: string;

  /**
   * 出证时间
   */
  czsj: string;

  /**
   * 审批人ID
   */
  sprbm: number;

  /**
   * 审批人姓名
   */
  sprxm: string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm: string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm: string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm: string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm: string;

  /**
   * 是否作废
   */
  sfzf: string;

  /**
   * 签名章
   */
  qmz: string;

  /**
   * 结案方式
   */
  jyfs: string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt: string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt: string;

  /**
   * 档案编号
   */
  dabh: string;

}

export interface GzjzJbxxForm extends BaseEntity {
  /**
   * $column.columnComment
   */
  id?: string | number;

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 受理编号
   */
  slbh?: string;

  /**
   * 卷宗编号
   */
  jzbh?: string;

  /**
   * 公证书编号（多个）
   */
  gzsbh?: string;

  /**
   * 归档人姓名
   */
  gdrxm?: string;

  /**
   * 归档日期
   */
  gdrq?: string;

  /**
   * 保管/档案期限
   */
  bgqx?: string;

  /**
   * 保管类型
   */
  bglx?: string;

  /**
   * 公证类别
   */
  lb?: string;

  /**
   * 公证事项（事务树）
   */
  gzsx?: string;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 流程状态
   */
  lczt?: string;

  /**
   * 公证员ID
   */
  gzybm?: string;

  /**
   * 公证员姓名
   */
  gzyxm?: string;

  /**
   * 助理ID
   */
  zlbm?: string;

  /**
   * 助理姓名
   */
  zlxm?: string;

  /**
   * 受理地点
   */
  sldd?: string;

  /**
   * 受理日期
   */
  slrq?: string;

  /**
   * 使用地
   */
  syd?: string;

  /**
   * 是否认证
   */
  rz?: string;

  /**
   * 紧急度
   */
  jjd?: string;

  /**
   * 领证地点
   */
  lzdd?: string;

  /**
   * 领证日期
   */
  lzrq?: string;

  /**
   * 用途
   */
  yt?: string;

  /**
   * 译文文种
   */
  ywwz?: string;

  /**
   * 是否密卷
   */
  sfmj?: string;

  /**
   * 档案类型
   */
  dalx?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 协办人ID
   */
  xbrbh?: string;

  /**
   * 协办人姓名
   */
  xbrxm?: string;

  /**
   * 法律援助
   */
  flxz?: string;

  /**
   * 是否外译中
   */
  sfwyz?: string;

  /**
   * 电票领取电话
   */
  dplqdh?: string;

  /**
   * 是否零接触
   */
  sfljc?: string;

  /**
   * 是否电子签名
   */
  sfdzqm?: string;

  /**
   * 是否电子公证书
   */
  sfdzgzs?: string;

  /**
   * 归档人ID
   */
  gdrbh?: string;

  /**
   * 翻译人ID
   */
  fyrbh?: string;

  /**
   * 翻译人姓名
   */
  fyrxm?: string;

  /**
   * 外部订单号
   */
  wbddh?: string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx?: string;

  /**
   * 制证时间
   */
  zzsj?: string;

  /**
   * 出证时间
   */
  czsj?: string;

  /**
   * 审批人ID
   */
  sprbm?: string;

  /**
   * 审批人姓名
   */
  sprxm?: string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm?: string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm?: string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm?: string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm?: string;

  /**
   * 是否作废
   */
  sfzf?: string;

  /**
   * 签名章
   */
  qmz?: string;

  /**
   * 结案方式
   */
  jyfs?: string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt?: string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt?: string;

  /**
   * 档案编号
   */
  dabh?: string;

  /**
   * 申请时间
   */
  sqsj?: string;

}

export interface GzjzJbxxQuery extends PageQuery {

  /**
   * 机构编码
   */
  jgbm?: string;

  /**
   * 受理编号
   */
  slbh?: string;

  /**
   * 卷宗编号
   */
  jzbh?: string;

  /**
   * 公证书编号（多个）
   */
  gzsbh?: string;

  /**
   * 归档人姓名
   */
  gdrxm?: string;

  /**
   * 归档日期
   */
  gdrq?: string;

  /**
   * 保管/档案期限
   */
  bgqx?: string;

  /**
   * 保管类型
   */
  bglx?: string;

  /**
   * 公证类别
   */
  lb?: string;

  /**
   * 公证事项（事务树）
   */
  gzsx?: string;

  /**
   * 申请人
   */
  sqr?: string;

  /**
   * 流程状态
   */
  lczt?: Array<string> | string;

  /**
   * 公证员ID
   */
  gzybm?: string;

  /**
   * 公证员姓名
   */
  gzyxm?: string;

  /**
   * 助理ID
   */
  zlbm?: string;

  /**
   * 助理姓名
   */
  zlxm?: string;

  /**
   * 受理地点
   */
  sldd?: string;

  /**
   * 受理日期
   */
  slrq?: string;

  /**
   * 使用地
   */
  syd?: string;

  /**
   * 是否认证
   */
  rz?: string;

  /**
   * 紧急度
   */
  jjd?: string;

  /**
   * 领证地点
   */
  lzdd?: string;

  /**
   * 领证日期
   */
  lzrq?: string;

  /**
   * 用途
   */
  yt?: string;

  /**
   * 译文文种
   */
  ywwz?: string;

  /**
   * 是否密卷
   */
  sfmj?: string;

  /**
   * 档案类型
   */
  dalx?: string;

  /**
   * 协办人ID
   */
  xbrbh?: string;

  /**
   * 协办人姓名
   */
  xbrxm?: string;

  /**
   * 法律援助
   */
  flxz?: string;

  /**
   * 是否外译中
   */
  sfwyz?: string;

  /**
   * 电票领取电话
   */
  dplqdh?: string;

  /**
   * 是否零接触
   */
  sfljc?: string;

  /**
   * 是否电子签名
   */
  sfdzqm?: string;

  /**
   * 是否电子公证书
   */
  sfdzgzs?: string;

  /**
   * 归档人ID
   */
  gdrbh?: string;

  /**
   * 翻译人ID
   */
  fyrbh?: string;

  /**
   * 翻译人姓名
   */
  fyrxm?: string;

  /**
   * 外部订单号
   */
  wbddh?: string;

  /**
   * 业务类型（01新增，02副本）
   */
  ywlx?: string;

  /**
   * 制证时间
   */
  zzsj?: string;

  /**
   * 出证时间
   */
  czsj?: string;

  /**
   * 审批人ID
   */
  sprbm?: string;

  /**
   * 审批人姓名
   */
  sprxm?: string;

  /**
   * 当事人编码(多人时以“, ”分隔)
   */
  dsrbm?: string;

  /**
   * 当事人姓名(多人时以“, ”分隔)
   */
  dsrxm?: string;

  /**
   * 申请人编码(多人时以“, ”分隔)
   */
  sqrbm?: string;

  /**
   * 申请人姓名(多人时以“, ”分隔)
   */
  sqrxm?: string;

  /**
   * 是否作废
   */
  sfzf?: string;

  /**
   * 签名章
   */
  qmz?: string;

  /**
   * 结案方式
   */
  jyfs?: string;

  /**
   * 合成状态（无合成，待合成、已合成）
   */
  hczt?: string;

  /**
   * 打印状态（未打印，已打印）
   */
  dyzt?: string;

  /**
   * 档案编号
   */
  dabh?: string;

      /**
   * 申请时间
   */
  sqsj?: string;

  /**
   * 审批时间
   */
  spsj?: string;

  /**
   * 业务来源
   */
  ywly?: string;

  /**
   * 案件来源
   */
  ajly?: string;

  /**
   * 是否零接触
   */
  ljc?: any;

  /**
   * 卷宗流程
   */
  jzlc?: any;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}



