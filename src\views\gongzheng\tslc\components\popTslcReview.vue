<template>
  <vxe-modal v-model="visible" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="handleClose">
    <div class="review-container">
      <!-- 详情展示，复用 Detail 的结构与字段 -->
      <el-form :model="detail" label-width="120px" class="block-card">
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="申请人:"><el-input v-model="detail.tslcFqr"
                disabled /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="申请日期:"><el-input :model-value="detail.tslcFqrq"
                disabled /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="审批人:"><el-input :model-value="detail.tslcSpr || detail.tslcSprName"
                disabled /></el-form-item></el-col>
        </el-row>
        <el-form-item label="申请原因:"><el-input v-model="detail.tslcSqyy" type="textarea" :rows="2"
            disabled /></el-form-item>
      </el-form>

      <div class="block-card" v-if="detail.caseInfo">
        <div class="section-title">卷宗信息</div>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="卷宗号">{{ detail.caseInfo.jzbh }}</el-descriptions-item>
          <el-descriptions-item label="公证员">{{ detail.caseInfo.gzyxm }}</el-descriptions-item>
          <el-descriptions-item label="助理">{{ detail.caseInfo.zlxm }}</el-descriptions-item>
          <el-descriptions-item label="受理日期">{{ detail.caseInfo.slrq }}</el-descriptions-item>
          <el-descriptions-item label="公证类别">{{ dictMapFormat(gz_gzlb, detail.caseInfo.lb) }}</el-descriptions-item>
          <el-descriptions-item label="当事人">{{ detail.caseInfo.dsrxm }}</el-descriptions-item>
          <el-descriptions-item label="公证书编号">{{ detail.gzsbh }}</el-descriptions-item>
          <el-descriptions-item v-if="detail.jdsbh" label="决定书编号">{{ detail.jdsbh }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="block-card">
        <component :is="currentTableComp" :items="tableItems" />
      </div>

      <!-- 审批输入区 -->
      <div class="block-card">
        <div class="section-title">审批意见</div>
        <el-input v-model="opinion" type="textarea" :rows="3" placeholder="请输入审批意见（必填）" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submit(true)" :loading="submitting" v-has-permi="['tslc:fq:edit']">同意</el-button>
        <el-button type="warning" @click="submit(false)" :loading="submitting" v-has-permi="['tslc:fq:edit']">不同意</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<script setup name="PopTslcReview" lang="ts">
  import { ref, reactive, computed, nextTick } from 'vue'
  import type { VxeModalProps } from 'vxe-table'
  import RefundTable from './detail-sections/RefundTable.vue'
  import FeeReductionTable from './detail-sections/FeeReductionTable.vue'
  import OtherTable from './detail-sections/OtherTable.vue';
  import TerminateTable from './detail-sections/TerminateTable.vue'
  import RejectTable from './detail-sections/RejectTable.vue'
  import { getTslcSqb, getChargeList, getCaseItems, approveTslcSqb } from '@/api/gongzheng/tslc/tslcSqb'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_gzlb } = toRefs<any>(proxy?.useDict('gz_gzlb'))
  import { dictMapFormat } from '@/utils/ruoyi'

  interface Props { title ?: string; width ?: string }
  const props = withDefaults(defineProps<Props>(), { title: '审批', width: '1000px' })
  const emit = defineEmits<{ (e : 'success') : void; (e : 'close') : void }>()

  const visible = ref(false)
  const submitting = ref(false)
  const modalOptions = reactive<VxeModalProps>({ title: props.title, width: props.width, height: '80vh', escClosable: true, resize: true, showMaximize: true, destroyOnClose: true })

  const detail = reactive<any>({})
  const tableItems = ref<any[]>([])
  const opinion = ref('')
  let currentId : string | number | null = null

  const currentTableComp = computed(() => {
    switch (String(detail.tslcLx || '')) {
      case '6':
        return RefundTable;
      case '5':
        return FeeReductionTable;
      case '2':
        return TerminateTable;
      case '3':
        return RejectTable;
      case '1':
        return RejectTable;
      case '4':
        return RejectTable;
      default:
        return OtherTable; // 占位，空数据不显示
    }
  })

  const open = async ({ id } : { id : string | number }) => {
    currentId = id
    reset()
    visible.value = true
    await nextTick()
    await loadDetail(id)
  }

  const close = () => { visible.value = false; emit('close') }
  const handleClose = () => close()

  const reset = () => { for (const k of Object.keys(detail)) delete (detail as any)[k]; tableItems.value = []; opinion.value = '' }

  const loadDetail = async (id : string | number) => {
    const res : any = await getTslcSqb(id)
    const data = res.data || {}
    data.tslcSpr = data.tslcSprName || data.tslcSpr || data.approver
    if (data.caseInfo) data.caseInfo.lb = data.caseInfo.lb + ''
    Object.assign(detail, data)
    let items = data.items
    if (!items || items.length === 0) {
      const lx = String(data.tslcLx || '')
      if (lx === '6' || lx === '5') {
        const r : any = await getChargeList({ gzjzId: data.gzjzId, jzh: data.jzh })
        const rows = r.rows || r.data?.rows || []
        items = rows.map((it : any) => ({
          feeType: it.fylx,
          notarizationItem: it.gzjzGzsx || it.gzsx,
          certificateNumber: it.gzsbh,
          amountPayable: it.fyys, amountReceived: it.fyss, amountRefunded: it.fytf, amountThisRefund: it.thisFytf || 0,
          amountReduction: it.fyjm, status: it.zt || '', chargeStatus: it.sfzt || ''
        }))
      } else if (lx === '1' || lx === '4' || lx === '2' || lx === '3') {
        const r : any = await getCaseItems({ gzjzId: data.gzjzId, jzh: data.jzh })
        const rows = r.rows || r.data?.rows || []
        console.log(rows)
        items = rows.map((it : any) => ({
          notarizationItem: it.notarizationItem || it.gzsx,
          certificateNumber: it.certificateNumber || it.gzsbh,
          certificate: it.certificate || it.gzs,
          translation: it.translation || it.yw
        }))
      }
    }
    tableItems.value = items || []
  }

  const submit = async (approve : boolean) => {
    if (!currentId) return
    if (!opinion.value || !opinion.value.trim()) {
      ElMessage.warning('请填写审批意见')
      return
    }
    try {
      submitting.value = true
      let spStatus = 3;
      if (approve) {
        spStatus = 2;
      }
      await approveTslcSqb({ id: currentId, spStatus: spStatus, tslcSpyj: opinion.value.trim() })
      ElMessage.success('提交成功')
      emit('success')
      close()
    } catch (e) {
      ElMessage.error('提交失败')
    } finally {
      submitting.value = false
    }
  }

  defineExpose({ open, close })
</script>

<style scoped>
  .review-container {
    padding: 16px;
  }

  .block-card {
    background: #fff;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 4px;
  }

  .section-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .dialog-footer {
    text-align: right;
  }

  .dialog-footer .el-button {
    margin-left: 10px;
  }
</style>











