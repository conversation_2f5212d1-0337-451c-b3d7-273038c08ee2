export interface GzjzDchsjlVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 卷宗号
   */
  jzbh: string;

  /**
   * 申请人ID
   */
  sqrId: string | number;

  /**
   * 证据材料列表，结构{nameId=证据名称ID，name=证据名称，remarks=调查备注}
   */
  zjclList: string;

  /**
   * 其他调查要求（500字内）
   */
  qtdcyq: string;

  /**
   * 申请调查日期
   */
  sqdcrq: string;

  /**
   * 调查人ID
   */
  jcrId: string | number;

  /**
   * 调查核实地点（250字内）
   */
  dchsdd: string;

  /**
   * 调查方式（250字内）
   */
  dcfs: string;

  /**
   * 调查核实结果（250字内）
   */
  dchsjg: string;

  /**
   * 调查结束日期
   */
  dcjsrq: string;

  /**
   * 调查结果文件，结构[{ossId=OSSId,fileName=文件名,path=文件路径}]
   */
  dcjgList: string;

  /**
   * 调查状态（字典：gz_jz_dczt）
   */
  dczt: string;

  /**
   * 本卷申请调查次数
   */
  bjsqdccs: number;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzDchsjlForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 卷宗号
   */
  jzbh?: string;

  /**
   * 申请人ID
   */
  sqrId?: string | number;

  /**
   * 证据材料列表，结构{nameId=证据名称ID，name=证据名称，remarks=调查备注}
   */
  zjclList?: string;

  /**
   * 其他调查要求（500字内）
   */
  qtdcyq?: string;

  /**
   * 申请调查日期
   */
  sqdcrq?: string | Date;

  /**
   * 调查人ID
   */
  jcrId?: string | number;

  /**
   * 调查核实地点（250字内）
   */
  dchsdd?: string;

  /**
   * 调查方式（250字内）
   */
  dcfs?: string;

  /**
   * 调查核实结果（250字内）
   */
  dchsjg?: string;

  /**
   * 调查结束日期
   */
  dcjsrq?: string;

  /**
   * 调查结果文件，结构[{ossId=OSSId,fileName=文件名,path=文件路径}]
   */
  dcjgList?: string;

  /**
   * 调查状态（字典：gz_jz_dczt）
   */
  dczt?: string;

  /**
   * 本卷申请调查次数
   */
  bjsqdccs?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzDchsjlQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 卷宗号
   */
  jzbh?: string;

  /**
   * 申请人ID
   */
  sqrId?: string | number;

  /**
   * 证据材料列表，结构{nameId=证据名称ID，name=证据名称，remarks=调查备注}
   */
  zjclList?: string;

  /**
   * 其他调查要求（500字内）
   */
  qtdcyq?: string;

  /**
   * 申请调查日期
   */
  sqdcrq?: string;

  /**
   * 调查人ID
   */
  jcrId?: string | number;

  /**
   * 调查核实地点（250字内）
   */
  dchsdd?: string;

  /**
   * 调查方式（250字内）
   */
  dcfs?: string;

  /**
   * 调查核实结果（250字内）
   */
  dchsjg?: string;

  /**
   * 调查结束日期
   */
  dcjsrq?: string;

  /**
   * 调查结果文件，结构[{ossId=OSSId,fileName=文件名,path=文件路径}]
   */
  dcjgList?: string;

  /**
   * 调查状态（字典：gz_jz_dczt）
   */
  dczt?: string;

  /**
   * 本卷申请调查次数
   */
  bjsqdccs?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



