export interface DzjzmlVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 材料名称
   */
  catalogue: string;

  /**
   * 顺序
   */
  orderNumber: number;

  /**
   * 状态
   */
  status: number;

  /**
   * 父级
   */
  parentId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface DzjzmlForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 材料名称
   */
  catalogue?: string;

  /**
   * 顺序
   */
  orderNumber?: number;

  /**
   * 状态
   */
  status?: number;

  /**
   * 父级
   */
  parentId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface DzjzmlQuery extends PageQuery {

  /**
   * 材料名称
   */
  catalogue?: string;

  /**
   * 状态
   */
  status?: number;

  /**
   * 父级
   */
  parentId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



