<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px" size="small">
            <el-form-item label="卷宗号" prop="jzbh" style="width: 260px;">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人" prop="dsrxm" style="width: 260px;">
              <el-input v-model="queryParams.dsrxm" placeholder="请输入当事人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证员" prop="gzybm" style="width: 260px;">
              <el-select v-model="queryParams.gzybm" placeholder="请选择" clearable>
                <el-option
                  v-for="item in gzy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公证类别" prop="lb" style="width: 260px;">
              <el-select v-model="queryParams.lb" placeholder="请选择公证类别" clearable>
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="公证书编号" prop="gzsbh">
              <el-select v-model="queryParams.params.gzsNf" placeholder="年份" clearable style="max-width: 80px; margin-right: 4px;" >
                <el-option v-for="dict in gzsbh_years" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              <el-select v-model="queryParams.params.gzsZh" placeholder="字号" clearable style="max-width: 140px; margin-right: 4px;" >
                <el-option v-for="dict in gz_gzs_zh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              第<el-input v-model="queryParams.params.gzsLs" clearable @keyup.enter="handleQuery" style="max-width: 60px" />号
            </el-form-item>

            <!-- <el-form-item label="归档日期" prop="slrq">
              <el-date-picker clearable style="width: 145px;" v-model="dateRange[0]" type="date"
                value-format="YYYY-MM-DD" placeholder="请选择归档日期" />至 <el-date-picker style="width: 145px;" clearable
                v-model="dateRange[1]" type="date" value-format="YYYY-MM-DD" placeholder="请选择归档日期" />
            </el-form-item> -->
            <el-form-item label="归档日期" prop="gdrq" style="width: 340px;">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['gz:gd:query']">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="multiple" @click="handleBatchArchive">批量归档</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <GeneralProcessTable ref="generalProcessTableRef" :view-moudle="'gd'" :action-width="120" @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
        <template #my-actions="{ row }">
          <el-button type="primary" link @click="handleArchive(row)" v-hasPermi="['gz:gd:edit']">归档</el-button>
          <el-button type="primary" link @click="handleRowDblclick(row)" v-hasPermi="['gz:gd:query']">详情</el-button>
        </template>
        <template #my-alerts="{ row }">
          <el-text type="danger">{{ row.ajtx ? `(${row.ajtx})` : '' }}</el-text>
        </template>
        <template #my-remarks="{ row }">
          <el-text @click="() => handleBz(row)" :type="row.yqtxVo && row.yqtxVo.remark ? '' : 'primary'" style="cursor: pointer;">{{ row.yqtxVo ? row.yqtxVo.remark || '备注' : '备注' }}</el-text>
        </template>
      </GeneralProcessTable>

    </el-card>

    <!-- 详情对话框 - 使用共用组件 -->
    <DetailDrawer
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :current-record-id="currentRecordId"
      :lczt="lczt"
      page-type="gd"
      @refresh="getList"
      @evidence-material="handleEvidenceMaterial"
      @delivery-info="handleDeliveryInfo"
      @reminder-info="handleReminderInfo"
      @short-info="handleShortInfo"
      @invalidation="handleInvalidationSubmit"
      @document-drafting="handleWdnd"
      @transcript="handleBl"
      @ghostwriting="handleDs"
      @draft-notarization="handleNdgzs"
    />

    <!-- 归档弹窗 -->
    <PopArchive
      v-model="archiveDialog.visible"
      :title="archiveDialog.title"
      :gzjz-id="currentRecordId"
      :basic-info="currentRecord"
      :gz-gzlb="gz_gzlb"
      :gz-tslc-lx="gz_tslc_lx"
      :gz-tslc-zt="gz_tslc_zt"
      @success="handleArchiveSuccess"
      @close="handleArchiveClose"
    />

    <JzDetailDialog v-model="jzDetailState.visible" v-if="jzDetailState.visible" />
  </div>
</template>

<script setup name="gdList" lang="ts">
import { provide } from 'vue';
import { listGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { invalidation } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO, GzjzJbxxQuery } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import GeneralProcessTable from '@/views/gongzheng/gongzheng/components/GeneralProcessTable.vue';
import SlDetail from '@/views/gongzheng/gongzheng/components/jz/detail.vue';
import DetailDrawer from '@/views/gongzheng/gongzheng/components/sl/DetailDrawer.vue';
import { genYearOptions, clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
import JzDetailDialog from '@/views/gongzheng/gongzheng/components/jz_detail/index.vue';
import PopArchive from './components/popArchive.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gzs_zh, gz_sl_lczt, gz_gzlb, gz_tslc_lx, gz_tslc_zt } = toRefs<any>(proxy?.useDict('gz_gzs_zh', 'gz_sl_lczt', 'gz_gzlb', 'gz_tslc_lx', 'gz_tslc_zt'));
const { gzy, gzyzl } = toRefs<any>(proxy?.useRoleUser('gzy', 'gzyzl'));

const gzsbh_years = genYearOptions(2015);

const gzjzJbxxList = ref<GzjzJbxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const direction = ref<'rtl' | 'ltr' | 'ttb' | 'btt'>('rtl');
const dateRange = ref<[string, string] | null>([null, null]);

const currentRecordId = ref<string | number | null>(null);
const lczt = ref<string | number | null>(null);
const queryFormRef = ref<ElFormInstance>();
const generalProcessTableRef = ref<InstanceType<typeof GeneralProcessTable>>(null);
// 对话框状态管理
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const jzDetailState = reactive({
  visible: false
})

// 归档弹窗状态
const archiveDialog = reactive({
  visible: false,
  title: '归档详情'
})

// 当前选中的记录
const currentRecord = ref<GzjzJbxxVO | null>(null);

// 查询参数
const queryParams = ref<GzjzJbxxQuery>({
  pageNum: 1,
  pageSize: 10,
  jzbh: undefined,
  gzsbh: undefined,
  lb: undefined,
  dsrxm: undefined,
  gzyxm: undefined,
  lczt: '08', // 固定为归档状态
  nf: undefined,
  syd: undefined,
  params: {}
});

/** 查询公证卷宗-基本信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
      queryParams.value.params = {
        ...queryParams.value.params,
        beginGdrq: dateRange.value[0],
        endGdrq: dateRange.value[1]
      };
    } else {
      delete queryParams.value.params?.beginGdrq;
      delete queryParams.value.params?.endGdrq;
    }

    generalProcessTableRef.value.getList(queryParams.value);
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  dateRange.value = [null, null];
  queryParams.value.params = {};
  handleQuery();
}

  /** 多选框选中数据 */
  const handleSelectionChange = (_ids : Array<number|string>, _single : boolean, _multiple: boolean) => {
    ids.value = _ids;
    single.value = _single;
    multiple.value = _multiple;
  };

/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (cellValue) {
    return proxy?.parseTime(cellValue, 'yyyy-MM-dd');
  }
  return '';
}

// 归档
const handleArchive = (row?: GzjzJbxxVO) => {
  console.log('归档操作，行数据:', row);
  if (row?.id) {
    currentRecordId.value = row.id;
    lczt.value = row.lczt;
    currentRecord.value = row;
    console.log('设置当前记录:', currentRecord.value);
  }
  archiveDialog.visible = true;
  archiveDialog.title = "归档详情";
}

// 批量归档
const handleBatchArchive = () => {
  console.log('批量归档操作');
}

// 备注
const handleBz = (row?: GzjzJbxxVO) => {
  console.log('备注操作');
}

// 双击行查看详情
const handleRowDblclick = (row: GzjzJbxxVO) => {
  if (row?.id) {
    currentRecordId.value = row.id;
    lczt.value = row.lczt;
  }
  // dialog.visible = true;
  // dialog.title = "详情";
  jzDetailState.visible = true
}

/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  currentRecordId.value = null;
};

// 归档成功处理
const handleArchiveSuccess = (data: any) => {
  ElMessage.success('归档成功');
  getList(); // 刷新列表
  archiveDialog.visible = false;
};

// 归档弹窗关闭处理
const handleArchiveClose = () => {
  archiveDialog.visible = false;
  currentRecord.value = null;
};

// 证据材料功能
const handleEvidenceMaterial = () => {
  console.log('证据材料');
};

// 送达信息功能
const handleDeliveryInfo = () => {
  console.log('送达信息');
};

// 提醒信息功能
const handleReminderInfo = () => {
  console.log('提醒信息');
};

// 短信预约功能
const handleShortInfo = () => {
  console.log('短信预约');
};

// 卷宗作废
const handleZf = () => {
  console.log('卷宗作废');
};

// 文档拟定
const handleWdnd = () => {
  console.log('文档拟定');
};

// 笔录
const handleBl = () => {
  console.log('笔录');
};

// 代书
const handleDs = () => {
  console.log('代书');
};

// 拟定公证书
const handleNdgzs = () => {
  console.log('拟定公证书');
};

// 处理共用组件的作废提交
const handleInvalidationSubmit = async (data: { id: string | number; zfyy: string }) => {
  buttonLoading.value = true;
  try {
    const res = await invalidation(data);
    if (res.code === 200) {
      ElMessage.success('卷宗作废成功');
      getList(); // 刷新列表
    } else {
      ElMessage.error('卷宗作废失败：' + (res.msg || '未知错误'));
    }
  } catch (error: any) {
    console.error('卷宗作废失败:', error);
    ElMessage.error('卷宗作废失败: ' + (error?.message || '未知错误'));
  } finally {
    buttonLoading.value = false;
  }
};

// 提供给子组件的数据和方法
provide('currentRecordId', currentRecordId);
provide('refreshList', getList);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>
