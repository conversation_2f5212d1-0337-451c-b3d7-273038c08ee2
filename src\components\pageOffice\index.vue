<script setup>
  import {
    POBrowser
  } from "js-pageoffice";

  import {
    propTypes
  } from '@/utils/propTypes';
  const props = defineProps({
    modelValue: {
      type: [String, Object, Array],
      default: () => []
    },
    // 图片数量限制
    title: null,
    url: null,
  });

  const open_pageoffice = (paramJson) => {
    let paramString = null;
    if (paramJson) {
      paramString = JSON.stringify(paramJson);
    }
    //openWindow()第三个参数用来向弹出的PageOffice浏览器（POBrowser）窗口传递参数(参数长度不限)，支持json格式字符串。
    //此处为了方便演示，我们传递了file_id和file_name两个参数，具体以您实际开发为准。
    POBrowser.openWindow(props.title, 'width=1300px;height=900px;', paramString);
  }

  defineExpose({
    open_pageoffice
  });
</script>

<style>
</style>
