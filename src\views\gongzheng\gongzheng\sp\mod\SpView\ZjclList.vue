<template>
  <el-table :data="data" border stripe size="small">
    <el-table-column type="index" label="#" align="center"/>
    <el-table-column prop="zmmc" label="证据名称" width="140" align="center"/>
    <el-table-column prop="dsrId" label="当事人" width="140" align="center">
      <template #default="{ row }">
        {{ findDsrName(row.dsrId) }}
      </template>
    </el-table-column>
    <el-table-column label="数量" width="80" align="center">
      <template #default="{ row }">
        {{ row.zmclxxMxList.length }}
      </template>
    </el-table-column>
    <el-table-column label="文件名称">
      <template #default="{ row, column }">
        <div class="flex gap-4px flex-wrap">
          <el-tag v-for="item in row.zmclxxMxList" :key="item.id">
            <el-button type="primary" link @click="openCl(item)" size="small">{{item.xxmc}}</el-button>
          </el-tag>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { type DocParams, EditDocParams, editDoc, showDoc } from '@/views/gongzheng/doc/DocEditor'
import { EnumDocActionType, EnumDocType, EnumDocFileType } from '@/views/gongzheng/doc/enumType'
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';

interface Props {
  data: any[];
  gzjzId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const dsrList = ref([]);

// 加载当事人列表
async function loadDsrList() {
  if (!curGzjz.value.id && !currentRecordId.value) {
    dsrList.value = []
    return;
  };
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        dsrList.value = res.rows || [];
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人信息失败');
  } finally {}
}

function findDsrName(dsrId: string) {
  const dsr = dsrList.value.find((item) => item.dsrId == dsrId);
  return dsr?.name || dsrId || '';
}

function openCl(data: any) {}

onBeforeMount(() => {
  loadDsrList();
})

</script>