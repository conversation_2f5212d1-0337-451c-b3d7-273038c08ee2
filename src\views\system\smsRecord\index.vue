<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-form-item label="公证卷宗编号" prop="gzjzBh">
              <el-input v-model="queryParams.gzjzBh" placeholder="请输入公证卷宗编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人姓名" prop="dsrName">
              <el-input v-model="queryParams.dsrName" placeholder="请输入当事人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当事人联系电话" prop="dsrPhone">
              <el-input v-model="queryParams.dsrPhone" placeholder="请输入当事人联系电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发送状态" prop="sendStatus">
              <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="smsRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="公证卷宗编号" align="center" prop="gzjzBh" />
        <el-table-column label="当事人姓名" align="center" prop="dsrName" />
        <el-table-column label="当事人联系电话" align="center" prop="dsrPhone" />
        <el-table-column label="短信内容" align="center" prop="smsContent" />
        <el-table-column label="发送时间" align="center" prop="sendTime" width="180">
          <template #default="scope">
            <span>{{ scope.row.sendTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发送状态" align="center" prop="sendStatus">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.sendStatus" />
          </template>
        </el-table-column>
        <el-table-column label="发送反馈时间" align="center" prop="feedbackTime" width="180">
          <template #default="scope">
            <span>{{scope.row.feedbackTime}}</span>
          </template>
        </el-table-column>
        <el-table-column label="发送反馈结果" align="center" prop="feedbackResult" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="SmsRecord" lang="ts">
  import { listSmsRecord, getSmsRecord, delSmsRecord, addSmsRecord, updateSmsRecord } from '@/api/system/smsRecord';
  import { SmsRecordVO, SmsRecordQuery, SmsRecordForm } from '@/api/system/smsRecord/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

  const smsRecordList = ref<SmsRecordVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const smsRecordFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : SmsRecordForm = {
    id: undefined,
    gzjzId: undefined,
    dsrId: undefined,
    dsrName: undefined,
    dsrPhone: undefined,
    smsContent: undefined,
    sendTime: undefined,
    sendStatus: undefined,
    feedbackTime: undefined,
    feedbackResult: undefined,
    remark: undefined,
  }
  const data = reactive<PageData<SmsRecordForm, SmsRecordQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzjzBh: undefined,
      dsrId: undefined,
      dsrName: undefined,
      dsrPhone: undefined,
      smsContent: undefined,
      sendTime: undefined,
      sendStatus: undefined,
      feedbackTime: undefined,
      feedbackResult: undefined,
      params: {
      }
    },

  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询短信发送日志列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listSmsRecord(queryParams.value);
    smsRecordList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    smsRecordFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : SmsRecordVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加短信发送日志";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: SmsRecordVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getSmsRecord(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改短信发送日志";
  }

  /** 提交按钮 */
  const submitForm = () => {
    smsRecordFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateSmsRecord(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addSmsRecord(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: SmsRecordVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除短信发送日志编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delSmsRecord(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('system/smsRecord/export', {
      ...queryParams.value
    }, `smsRecord_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
