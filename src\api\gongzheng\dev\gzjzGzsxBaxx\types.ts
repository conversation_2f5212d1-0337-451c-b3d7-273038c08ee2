export interface GzjzGzsxBaxxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证事项
   */
  gzsx: string;

  /**
   * 份数
   */
  fs: number;

  /**
   * 公益法律服务
   */
  gyflfw: string;

  /**
   * 新型公证业务
   */
  xxgzyw: string;

  /**
   * 涉疫情/灾情公证
   */
  sysz: string;

  /**
   * 服务类型
   */
  fwlx: string;

  /**
   * 文件哈希值
   */
  wjHash: string;

  /**
   * 上传时间
   */
  scsj: string;

  /**
   * 上传结果
   */
  scjg: string;

  /**
   * 关联卷宗公证事项ID
   */
  gzjzGzsxId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzGzsxBaxxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证事项
   */
  gzsx?: string;

  /**
   * 份数
   */
  fs?: number;

  /**
   * 公益法律服务
   */
  gyflfw?: string;

  /**
   * 新型公证业务
   */
  xxgzyw?: string;

  /**
   * 涉疫情/灾情公证
   */
  sysz?: string;

  /**
   * 服务类型
   */
  fwlx?: string;

  /**
   * 文件哈希值
   */
  wjHash?: string;

  /**
   * 上传时间
   */
  scsj?: string;

  /**
   * 上传结果
   */
  scjg?: string;

  /**
   * 关联卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzGzsxBaxxQuery extends PageQuery {

  /**
   * 公证事项
   */
  gzsx?: string;

  /**
   * 份数
   */
  fs?: number;

  /**
   * 公益法律服务
   */
  gyflfw?: string;

  /**
   * 新型公证业务
   */
  xxgzyw?: string;

  /**
   * 涉疫情/灾情公证
   */
  sysz?: string;

  /**
   * 服务类型
   */
  fwlx?: string;

  /**
   * 文件哈希值
   */
  wjHash?: string;

  /**
   * 上传时间
   */
  scsj?: string;

  /**
   * 上传结果
   */
  scjg?: string;

  /**
   * 关联卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



