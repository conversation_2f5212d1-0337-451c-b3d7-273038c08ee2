import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzrzCzrzxxVO, GzrzCzrzxxForm, GzrzCzrzxxQuery } from '@/api/gongzheng/gzrzCzrzxx/types';

/**
 * 查询公证日志-操作日志v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzrzCzrzxx = (query?: GzrzCzrzxxQuery): AxiosPromise<GzrzCzrzxxVO[]> => {
  return request({
    url: '/gongzheng/gzrzCzrzxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证日志-操作日志v1.0详细
 * @param id
 */
export const getGzrzCzrzxx = (id: string | number): AxiosPromise<GzrzCzrzxxVO> => {
  return request({
    url: '/gongzheng/gzrzCzrzxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证日志-操作日志v1.0
 * @param data
 */
export const addGzrzCzrzxx = (data: GzrzCzrzxxForm) => {
  return request({
    url: '/gongzheng/gzrzCzrzxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证日志-操作日志v1.0
 * @param data
 */
export const updateGzrzCzrzxx = (data: GzrzCzrzxxForm) => {
  return request({
    url: '/gongzheng/gzrzCzrzxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证日志-操作日志v1.0
 * @param id
 */
export const delGzrzCzrzxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzrzCzrzxx/' + id,
    method: 'delete'
  });
};
