<template>
  <!-- 选择卷宗弹窗 -->
  <vxe-modal v-model="showPopup" v-bind="modalOptions" show-zoom :fullscreen="false" show-footer draggable
    destroy-on-close @close="doModalClose">
    <template #default>
      <div class="select-case-dialog">
        <!-- 搜索条件 -->
        <div class="search-section">
          <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px" size="small">
            <el-form-item label="卷宗号：" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable style="width: 150px" />
            </el-form-item>
            <el-form-item label="当事人：" prop="partyInvolved">
              <el-input v-model="queryParams.partyInvolved" placeholder="请输入当事人" clearable style="width: 150px" />
            </el-form-item>
            <el-form-item label="公证书编号：" prop="certificateNumber">
              <el-select v-model="queryParams.certificateYear" placeholder="年份" style="width: 80px">
                <el-option label="所有" value="" />
                <el-option label="2025" value="2025" />
                <el-option label="2024" value="2024" />
              </el-select>
              <el-input v-model="queryParams.certificateNo" placeholder="第" style="width: 60px; margin: 0 5px" />
              <span>号</span>
            </el-form-item>
            <el-form-item label="公证员：" prop="notary">
              <el-input v-model="queryParams.notary" placeholder="请选择公证员" clearable style="width: 150px">
                <template #append>
                  <el-button @click="handleSelectNotary">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 卷宗列表 -->
        <div class="table-section">
          <h3>卷宗列表</h3>
          <el-table v-loading="loading" :data="caseList" border stripe style="width: 100%"
            @row-click="handleRowClick" highlight-current-row>
            <el-table-column type="index" label="#" width="50" align="center" />
            <el-table-column label="卷宗号" prop="jzbh" align="center" />
            <el-table-column label="公证书编号" prop="gzsbh" align="center" />
            <el-table-column label="当事人" prop="dsrxm" align="center" />
            <el-table-column label="公证事项" prop="gzsx" align="center" />
            <el-table-column label="公证员" prop="gzyxm" align="center" />
            <el-table-column label="助理/受理人" prop="zlxm" align="center" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-section">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="doModalClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedCase">确定</el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<script setup name="SelectCaseDialog" lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router'
import { VxeModalProps } from 'vxe-pc-ui'
import { ElMessage } from 'element-plus'
import { searchCases } from '@/api/gongzheng/tslc/tslcSqb'

const route = useRoute()

// 定义 emits 类型
const emits = defineEmits<{
  (event: 'success', data: any): void
  (event: 'close'): void
}>()

// 弹窗控制
const showPopup = ref(false)
const modalOptions = reactive<VxeModalProps>({
  title: '选择卷宗',
  width: '1000px',
  height: '80%',
  escClosable: true,
  resize: true,
  showMaximize: true,
  destroyOnClose: true
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  jzbh: '',
  dsrxm: '',
  gzyxm: ''
})

// 数据状态
const loading = ref(false)
const total = ref(0)
const caseList = ref<any[]>([])
const selectedCase = ref<any>(null)

// 表单引用
const queryForm = ref()

// 方法
const open = (option = {}) => {
  console.log('open-select-case', option);
  showPopup.value = true;
  // 重置数据
  resetData();
  // 加载数据
  getCaseList();
}

const close = () => {
  showPopup.value = false;
  emits('close');
}

const doModalClose = () => {
  emits('close');
  showPopup.value = false;
  console.log('选择卷宗窗口关闭了');
}

// 获取卷宗列表
const getCaseList = async () => {
  loading.value = true;
  try {
    const res: any = await searchCases({
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      jzbh: queryParams.jzbh,
      partyInvolved: queryParams.dsrxm,
      notary: queryParams.gzyxm
    });
    caseList.value = res.rows || res.data?.rows || [];
    total.value = res.total || res.data?.total || 0;
  } catch (error) {
    console.error('获取卷宗列表失败', error);
    ElMessage.error('获取卷宗列表失败');
  } finally {
    loading.value = false;
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getCaseList();
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    jzbh: '',
    dsrxm: '',
    gzyxm: ''
  });
  getCaseList();
}

// 选择公证员
const handleSelectNotary = () => {
  ElMessage.info('选择公证员功能开发中...');
}

// 行点击事件
const handleRowClick = (row: any) => {
  selectedCase.value = row;
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getCaseList();
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  getCaseList();
}

// 确认选择
const handleConfirm = () => {
  if (!selectedCase.value) {
    ElMessage.warning('请选择卷宗');
    return;
  }

  // 构造卷宗信息
  const caseInfo = {
    gzjzId: selectedCase.value.gzjzId ?? selectedCase.value.id,
    jzbh: selectedCase.value.jzbh ?? selectedCase.value.caseNumber,
    gzsbh: selectedCase.value.gzsbh ?? selectedCase.value.certificateNumber,
    gzyxm: selectedCase.value.gzyxm ?? selectedCase.value.notary,
    zlxm: selectedCase.value.zlxm ?? selectedCase.value.assistant,
    slrq: selectedCase.value.slrq ?? selectedCase.value.acceptanceDate,
    lb: selectedCase.value.lb ?? selectedCase.value.notarizationType,
    dsrxm: selectedCase.value.dsrxm ?? selectedCase.value.partyInvolved,
    gzsx: selectedCase.value.gzsx ?? selectedCase.value.notarizationMatter,
    tip: selectedCase.value.tip
  };

  emits('success', caseInfo);
  close();
}

// 重置数据
const resetData = () => {
  selectedCase.value = null;
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    jzbh: '',
    dsrxm: '',
    gzyxm: ''
  });
}

onMounted(() => {
  console.log('SelectCaseDialog:onMounted');
});

onUnmounted(() => {
  console.log('SelectCaseDialog:onUnmounted');
});

// 暴露方法给父组件
defineExpose({
  open, close
})
</script>

<style scoped>
.select-case-dialog {
  padding: 20px;
}

.search-section {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}
</style>
