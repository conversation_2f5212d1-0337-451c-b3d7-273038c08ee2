<template>
<el-table v-model:data="zjclState.listData" v-loading="zjclState.loading" ref="zjclTbRef" size="small" border stripe style="min-height: 240px;">
  <el-table-column type="selection" width="40" />
  <el-table-column type="index" width="55" align="center"/>
  <el-table-column prop="zmmc" label="证据名称" align="center"/>
  <el-table-column prop="dsrId" label="当事人" align="center">
    <template #default="{ row }">
      {{ findDsrName(row.dsrId) }}
    </template>
  </el-table-column>
  <el-table-column label="调查备注">
    <template #default="{ row }">
      <el-input v-model="row.remarks" />
    </template>
  </el-table-column>
</el-table>
</template>

<script setup lang="ts">
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { listGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';


interface Props {
  gzjzId?: string | number;
}

const props = defineProps<Props>();

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const zjclTbRef = ref<ElTableInstance>(null)

const zjclState = reactive({
  loading: false,
  listData: []
})

const dsrState = reactive({
  listData: [],
  loading: false
})

const findDsrName = (dsrId: string) => {
  const dsr = dsrState.listData.find((item) => item.dsrId == dsrId);
  return dsr?.name || dsrId || '';
}

// 加载当事人列表
const loadDsrList = async () => {
  if (!props.gzjzId && !curGzjz.value?.id && !currentRecordId.value) {
    dsrState.listData = []
    return;
  };
  dsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        dsrState.listData = res.rows || [];
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    dsrState.loading = false;
  }
}

// 加载证明材料列表
const loadZjclList = async () => {
  try {
    zjclState.loading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      fjlb: '2',   // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      pageNum: 1,
      pageSize: 200
    }
    const res = await listGzjzZmclxx(params);
    // const res = await queryGzjzZmclxx(params);
    if (res.code === 200) {
      zjclState.listData = (res.data || []).map((item: any) => {
        if(!item?.remarks) {
          item.remarks = ''
        }
        return item;
      });
    }
  } catch (err: any) {
    ElMessage.error(`获取证据列表失败: ${err.message}`)
  } finally {
    zjclState.loading = false;
  }
}

const getSelectionRows = (): any[] => {
  let arr = zjclTbRef.value?.getSelectionRows() || []
  return arr;
}

defineExpose({
  getSelectionRows
})

onMounted(async () => {
  await loadDsrList();
  await loadZjclList();
})
</script>