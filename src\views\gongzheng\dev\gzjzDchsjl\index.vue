<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卷宗号" prop="jzbh">
              <el-input v-model="queryParams.jzbh" placeholder="请输入卷宗号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="其他调查要求" prop="qtdcyq">
              <el-input v-model="queryParams.qtdcyq" placeholder="请输入其他调查要求" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请调查日期" prop="sqdcrq">
              <el-date-picker clearable
                v-model="queryParams.sqdcrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择申请调查日期"
              />
            </el-form-item>
            <el-form-item label="调查人ID" prop="jcrId">
              <el-input v-model="queryParams.jcrId" placeholder="请输入调查人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调查核实地点" prop="dchsdd">
              <el-input v-model="queryParams.dchsdd" placeholder="请输入调查核实地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调查方式" prop="dcfs">
              <el-input v-model="queryParams.dcfs" placeholder="请输入调查方式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调查核实结果" prop="dchsjg">
              <el-input v-model="queryParams.dchsjg" placeholder="请输入调查核实结果" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调查结束日期" prop="dcjsrq">
              <el-date-picker clearable
                v-model="queryParams.dcjsrq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择调查结束日期"
              />
            </el-form-item>
            <el-form-item label="调查状态" prop="dczt">
              <el-select v-model="queryParams.dczt" placeholder="请选择调查状态" clearable >
                <el-option v-for="dict in gz_jz_dczt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="本卷申请调查次数" prop="bjsqdccs">
              <el-input v-model="queryParams.bjsqdccs" placeholder="请输入本卷申请调查次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['bzfz:gzjzDchsjl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['bzfz:gzjzDchsjl:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['bzfz:gzjzDchsjl:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['bzfz:gzjzDchsjl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzDchsjlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="卷宗号" align="center" prop="jzbh" />
        <el-table-column label="申请人ID" align="center" prop="sqrId" />
        <el-table-column label="证据材料列表，结构{nameId=证据名称ID，name=证据名称，remarks=调查备注}" align="center" prop="zjclList" />
        <el-table-column label="其他调查要求" align="center" prop="qtdcyq" />
        <el-table-column label="申请调查日期" align="center" prop="sqdcrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sqdcrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="调查人ID" align="center" prop="jcrId" />
        <el-table-column label="调查核实地点" align="center" prop="dchsdd" />
        <el-table-column label="调查方式" align="center" prop="dcfs" />
        <el-table-column label="调查核实结果" align="center" prop="dchsjg" />
        <el-table-column label="调查结束日期" align="center" prop="dcjsrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.dcjsrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="调查结果文件，结构[{ossId=OSSId,fileName=文件名,path=文件路径}]" align="center" prop="dcjgList" />
        <el-table-column label="调查状态" align="center" prop="dczt">
          <template #default="scope">
            <dict-tag :options="gz_jz_dczt" :value="scope.row.dczt"/>
          </template>
        </el-table-column>
        <el-table-column label="本卷申请调查次数" align="center" prop="bjsqdccs" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['bzfz:gzjzDchsjl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['bzfz:gzjzDchsjl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-调查核实记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzDchsjlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="卷宗号" prop="jzbh">
          <el-input v-model="form.jzbh" placeholder="请输入卷宗号" />
        </el-form-item>
        <el-form-item label="其他调查要求" prop="qtdcyq">
          <el-input v-model="form.qtdcyq" placeholder="请输入其他调查要求" />
        </el-form-item>
        <el-form-item label="申请调查日期" prop="sqdcrq">
          <el-date-picker clearable
            v-model="form.sqdcrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择申请调查日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="调查人ID" prop="jcrId">
          <el-input v-model="form.jcrId" placeholder="请输入调查人ID" />
        </el-form-item>
        <el-form-item label="调查核实地点" prop="dchsdd">
            <el-input v-model="form.dchsdd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="调查方式" prop="dcfs">
            <el-input v-model="form.dcfs" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="调查核实结果" prop="dchsjg">
            <el-input v-model="form.dchsjg" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="调查结束日期" prop="dcjsrq">
          <el-date-picker clearable
            v-model="form.dcjsrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择调查结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="调查状态" prop="dczt">
          <el-select v-model="form.dczt" placeholder="请选择调查状态">
            <el-option
                v-for="dict in gz_jz_dczt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本卷申请调查次数" prop="bjsqdccs">
          <el-input v-model="form.bjsqdccs" placeholder="请输入本卷申请调查次数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzDchsjl" lang="ts">
import { listGzjzDchsjl, getGzjzDchsjl, delGzjzDchsjl, addGzjzDchsjl, updateGzjzDchsjl } from '@/api/gongzheng/bzfz/gzjzDchsjl';
import { GzjzDchsjlVO, GzjzDchsjlQuery, GzjzDchsjlForm } from '@/api/gongzheng/bzfz/gzjzDchsjl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_jz_dczt } = toRefs<any>(proxy?.useDict('gz_jz_dczt'));

const gzjzDchsjlList = ref<GzjzDchsjlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzDchsjlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzDchsjlForm = {
  id: undefined,
  gzjzId: undefined,
  jzbh: undefined,
  sqrId: undefined,
  zjclList: undefined,
  qtdcyq: undefined,
  sqdcrq: undefined,
  jcrId: undefined,
  dchsdd: undefined,
  dcfs: undefined,
  dchsjg: undefined,
  dcjsrq: undefined,
  dcjgList: undefined,
  dczt: undefined,
  bjsqdccs: undefined,
  remark: undefined,
}
const data = reactive<PageData<GzjzDchsjlForm, GzjzDchsjlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    jzbh: undefined,
    sqrId: undefined,
    zjclList: undefined,
    qtdcyq: undefined,
    sqdcrq: undefined,
    jcrId: undefined,
    dchsdd: undefined,
    dcfs: undefined,
    dchsjg: undefined,
    dcjsrq: undefined,
    dcjgList: undefined,
    dczt: undefined,
    bjsqdccs: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    gzjzId: [
      { required: true, message: "公证卷宗ID不能为空", trigger: "blur" }
    ],
    jzbh: [
      { required: true, message: "卷宗号不能为空", trigger: "blur" }
    ],
    sqrId: [
      { required: true, message: "申请人ID不能为空", trigger: "change" }
    ],
    zjclList: [
      { required: true, message: "证据材料列表，结构{nameId=证据名称ID，name=证据名称，remarks=调查备注}不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-调查核实记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzDchsjl(queryParams.value);
  gzjzDchsjlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzDchsjlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzDchsjlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-调查核实记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzDchsjlVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzDchsjl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-调查核实记录";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzDchsjlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzDchsjl(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzDchsjl(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzDchsjlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-调查核实记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzDchsjl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('bzfz/gzjzDchsjl/export', {
    ...queryParams.value
  }, `gzjzDchsjl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
