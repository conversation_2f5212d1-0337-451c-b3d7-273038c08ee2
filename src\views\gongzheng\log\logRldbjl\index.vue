<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="客户名称" prop="dsrXm">
              <el-input v-model="queryParams.dsrXm" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码" prop="dsrZjhm">
              <el-input v-model="queryParams.dsrZjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" border :data="logRldbjlList" @selection-change="handleSelectionChange">
        <el-table-column label="序号" align="center" prop="id" v-if="true"  width="70"/>
        <el-table-column label="客户名称" align="center" prop="remark" />
        <el-table-column label="证件号码" align="center" prop="dsrXm" />
        <el-table-column label="核验时间" align="center" prop="dbrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.dbrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="核验状态" align="center" prop="dbjg" />
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

  </div>
</template>

<script setup name="LogRldbjl" lang="ts">
import { listLogRldbjl, getLogRldbjl, delLogRldbjl, addLogRldbjl, updateLogRldbjl } from '@/api/gongzheng/log/logRldbjl';
import { LogRldbjlVO, LogRldbjlQuery, LogRldbjlForm } from '@/api/gongzheng/log/logRldbjl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const logRldbjlList = ref<LogRldbjlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const logRldbjlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: LogRldbjlForm = {
  id: undefined,
  remark: undefined,
  dbrl: undefined,
  dbjg: undefined
}
const data = reactive<PageData<LogRldbjlForm, LogRldbjlQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dsrZjhm: undefined,
    dsrXm: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "序号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询日志-人脸对比记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listLogRldbjl(queryParams.value);
  logRldbjlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  logRldbjlFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: LogRldbjlVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加日志-人脸对比记录";
}



/** 删除按钮操作 */
const handleDelete = async (row?: LogRldbjlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除日志-人脸对比记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delLogRldbjl(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('log/logRldbjl/export', {
    ...queryParams.value
  }, `logRldbjl_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
