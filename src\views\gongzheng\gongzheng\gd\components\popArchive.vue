<template>
  <el-dialog v-model="visible" :title="title" :width="'1400px'" :close-on-click-modal="false"
    :close-on-press-escape="false" destroy-on-close @close="handleClose">
    <div class="archive-dialog">

      <!-- 左右布局容器 -->
      <div class="layout-container">
        <!-- 左侧内容 -->
        <div class="left-content">
          <!-- 基本信息 -->
          <div class="basic-info-section" style="height: 200px;">
            <h3 class="section-title">基本信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="卷宗号">{{ basicInfo.jzbh || '暂无' }}</el-descriptions-item>
              <el-descriptions-item label="公证员">{{ basicInfo.gzyxm || '暂无' }}</el-descriptions-item>
              <el-descriptions-item label="助理">{{ basicInfo.zlxm || '暂无' }}</el-descriptions-item>
              <el-descriptions-item label="受理日期">{{ formatDate(basicInfo.slrq) || '暂无' }}</el-descriptions-item>
              <el-descriptions-item label="公证类别" :span="2">{{ formatGzlb(basicInfo.lb) || '暂无' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 公证书信息 -->
          <div class="certificate-section">
            <h3 class="section-title">公证书信息</h3>
            <div class="table-container">
              <el-table :data="basicInfo.certificates" border stripe size="small">
                <el-table-column prop="number" label="公证书编号" width="180" />
                <el-table-column prop="matter" label="公证事项" />
                <el-table-column prop="party" label="当事人" width="150" />
              </el-table>
              <div v-if="basicInfo.certificates.length === 0" style="text-align: center; padding: 20px; color: #999;">
                暂无公证书信息
              </div>
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="remarks-section" style="height: 450px;">
            <h3 class="section-title">备注</h3>
            <div class="form-container">
              <el-form :model="remarksForm" label-width="100px" size="small">
                <el-form-item label="翻译要求:">
                  <el-input v-model="remarksForm.translationReq" type="textarea" :rows="3" placeholder="" disabled />
                </el-form-item>
                <el-form-item label="制证要求:">
                  <el-input v-model="remarksForm.productionReq" type="textarea" :rows="3" placeholder="" disabled />
                </el-form-item>
                <el-form-item label="发证提醒:">
                  <el-input v-model="remarksForm.issuanceReminder" type="textarea" :rows="3" placeholder="" disabled />
                </el-form-item>
                <el-form-item label="备注:">
                  <el-input v-model="remarksForm.remarks" type="textarea" :rows="3" placeholder="" disabled />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="right-content">
          <!-- 归档详情 -->
          <div class="archive-details-section" style="height: 200px;">
            <h3 class="section-title">归档详情</h3>
            <el-form :model="archiveForm" :rules="archiveRules" ref="archiveFormRef" label-width="100px" size="small">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="档案盒号:" prop="dahh">
                    <el-input v-model="archiveForm.dahh" placeholder="请输入档案盒号" maxlength="150" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="档案编号:" prop="dabh">
                    <div class="dabh-input-group">
                      <el-input v-model="archiveForm.dabh" :placeholder="archiveForm.modifyDabh ? '请输入档案编号' : '档案编号'"
                        :disabled="!archiveForm.modifyDabh" readonly style="flex: 1; margin-right: 10px;" />
                      <el-checkbox v-model="archiveForm.modifyDabh">修改</el-checkbox>
                    </div>
                    <div v-if="!archiveForm.modifyDabh && !archiveForm.dabh"
                      style="font-size: 12px; color: #909399; margin-top: 5px;">
                      档案编号将从卷宗号自动获取
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="档案期限:" prop="bgqx">
                    <el-select v-model="archiveForm.bgqx" placeholder="请选择档案期限" style="width: 100%">
                      <el-option v-for="item in gz_dabh_qx" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否密卷:" prop="sfmj">
                    <el-select v-model="archiveForm.sfmj" placeholder="请选择是否密卷" style="width: 100%">
                      <el-option v-for="item in gz_yes_or_no" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="结案方式:" prop="jyfs">
                    <el-select v-model="archiveForm.jyfs" placeholder="请选择结案方式" style="width: 100%" disabled>
                      <el-option label="出证" value="10" />
                      <el-option label="终止公证" value="2" />
                      <el-option label="撤销公证书" value="1" />
                      <el-option label="不予签发执行证书" value="4" />
                      <el-option label="不予办理" value="3" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 特殊流程记录 -->
          <div class="special-process-section">
            <h3 class="section-title">特殊流程记录</h3>
            <div class="table-container">
              <el-table :data="specialProcessRecords" border stripe size="small">
                <el-table-column prop="tslcLx" label="申请类型" width="120">
                  <template #default="{ row }">
                    {{ formatTslcLx(row.tslcLx) }}
                  </template>
                </el-table-column>
                <el-table-column prop="tslcFqrq" label="申请时间" width="150">
                  <template #default="{ row }">
                    {{ formatDate(row.tslcFqrq) }}
                  </template>
                </el-table-column>
                <el-table-column prop="tslcZt" label="状态" width="80">
                  <template #default="{ row }">
                    {{ formatTslcZt(row.tslcZt) }}
                  </template>
                </el-table-column>
                <el-table-column prop="tslcFqr" label="发起人" />
              </el-table>
            </div>
          </div>

          <!-- 附件袋信息 -->
          <div class="attachment-bags-section" style="height: 450px;">
            <h3 class="section-title">附件袋信息</h3>
            <div class="attachment-bags-header">
              <el-button type="primary" size="small" @click="addAttachmentBag">添加</el-button>
              <el-button type="danger" size="small" @click="deleteSelectedAttachmentBags"
                :disabled="selectedAttachmentBags.length === 0">删除</el-button>
            </div>

            <div class="table-container">
              <el-table :data="attachmentBags" border stripe size="small"
                @selection-change="handleAttachmentBagSelectionChange">
                <el-table-column type="selection" width="45" />
                <el-table-column prop="catalogNumber" label="目录号" width="180">
                  <template #default="scope">
                    <el-input v-model="scope.row.catalogNumber" placeholder="请输入目录号" maxlength="150" show-word-limit
                      size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="物品名称" width="180">
                  <template #default="scope">
                    <el-input v-model="scope.row.itemName" placeholder="请输入物品名称" maxlength="150" show-word-limit
                      size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="0" style="width: 100%"
                      size="small" />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]" :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" size="small" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleGenerateCover">生成卷宗封皮</el-button>
        <el-button type="success" @click="handleSaveAndArchive" :loading="loading">保存并归档</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 驳回弹窗 -->
  <el-dialog v-model="rejectDialogVisible" title="驳回申请" width="500px" :close-on-click-modal="false"
    :close-on-press-escape="false" destroy-on-close>
    <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef" label-width="100px" size="default">
      <el-form-item label="驳回至:" prop="rejectTo">
        <el-select v-model="rejectForm.rejectTo" placeholder="请选择驳回至的阶段" style="width: 100%">
          <el-option label="审查" value="05" />
          <el-option label="制证" value="06" />
          <el-option label="发证" value="07" />
        </el-select>
      </el-form-item>
      <el-form-item label="驳回原因:" prop="rejectReason">
        <el-input v-model="rejectForm.rejectReason" type="textarea" :rows="4" placeholder="请输入驳回原因" maxlength="500"
          show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelReject">取消</el-button>
        <el-button type="danger" @click="confirmReject" :loading="rejectLoading">确认驳回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="PopArchive" lang="ts">
  import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { submitArchive, generateCaseCover } from '@/api/gongzheng/gongzheng/gd';
  import { ArchiveFormData, AttachmentBagInfo, SpecialProcessRecord } from '@/api/gongzheng/gongzheng/gd/types';
  import { listTslcSqb } from '@/api/gongzheng/tslc/tslcSqb';
  import { TslcSqbVO, TslcSqbQuery } from '@/api/gongzheng/tslc/tslcSqb/types';
  import { docOpenEdit, docOpenShow, docGenerator } from '@/views/gongzheng/doc/DocEditor'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_dabh_qx, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_dabh_qx', 'gz_yes_or_no'));
  import { UserDocGenParams } from '@/views/gongzheng/doc/type'
  import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types'
  import { addGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx'
  // Props 定义
  interface Props {
    modelValue : boolean;
    title ?: string;
    gzjzId ?: string | number;
    basicInfo ?: any;
    gzGzlb ?: any[];
    gzTslcLx ?: any[]; // 特殊流程类型字典
    gzTslcZt ?: any[]; // 特殊流程状态字典
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '归档详情',
    basicInfo: () => ({}),
    gzGzlb: () => [],
    gzTslcLx: () => [],
    gzTslcZt: () => []
  });

  // Emits 定义
  const emits = defineEmits<{
    (event : 'update:modelValue', value : boolean) : void;
    (event : 'success', data ?: any) : void;
    (event : 'close') : void;
    (event : 'reject', data : { rejectTo : string; rejectReason : string }) : void;
  }>();

  // 响应式数据
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emits('update:modelValue', val)
  });

  const loading = ref(false);
  const archiveFormRef = ref<ElFormInstance>();

  // 驳回弹窗相关数据
  const rejectDialogVisible = ref(false);
  const rejectFormRef = ref<ElFormInstance>();
  const rejectLoading = ref(false);

  // 驳回表单数据
  const rejectForm = reactive({
    rejectTo: '', // 驳回至
    rejectReason: '' // 驳回原因
  });

  // 驳回表单验证规则
  const rejectRules = reactive({
    rejectTo: [
      { required: true, message: '请选择驳回至的阶段', trigger: 'change' }
    ],
    rejectReason: [
      { required: true, message: '请输入驳回原因', trigger: 'blur' },
      { min: 5, max: 500, message: '驳回原因长度在 5 到 500 个字符', trigger: 'blur' }
    ]
  });

  // 基本信息
  const basicInfo = ref({
    jzbh: '',
    gzyxm: '',
    zlxm: '',
    slrq: '',
    lb: '',
    certificates: []
  });

  // 格式化日期显示
  const formatDate = (dateStr : string) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  // 格式化公证类别
  const formatGzlb = (value : string) => {
    if (!value) return '暂无';
    if (props.gzGzlb && Array.isArray(props.gzGzlb)) {
      const dictItem = props.gzGzlb.find((item : any) => item.value === value);
      return dictItem ? dictItem.label : value;
    }
    return value;
  };

  // 备注表单
  const remarksForm = reactive({
    translationReq: '',
    productionReq: '',
    issuanceReminder: '',
    remarks: ''
  });

  // 归档表单
  const archiveForm = reactive({
    dahh: '',
    dabh: 0,
    modifyDabh: false,
    bgqx: '2',
    sfmj: '0',
    jyfs: '10'
  });

  // 归档表单验证规则
  const archiveRules = reactive({
    dahh: [
      { required: true, message: '请输入档案盒号', trigger: 'blur' }
    ],
    dabh: [
      {
        required: true,
        message: '请输入档案编号',
        trigger: 'blur',
        validator: (rule : any, value : any, callback : any) => {
          if (archiveForm.modifyDabh && !value) {
            callback(new Error('请输入档案编号'));
          } else {
            callback();
          }
        }
      }
    ],
    bgqx: [
      { required: true, message: '请选择档案期限', trigger: 'change' }
    ],
    sfmj: [
      { required: true, message: '请选择是否密卷', trigger: 'change' }
    ],
    jafs: [
      { required: true, message: '请选择结案方式', trigger: 'change' }
    ]
  });

  // 特殊流程记录
  const specialProcessRecords = ref<TslcSqbVO[]>([]);

  // 获取特殊流程记录
  const getSpecialProcessRecords = async () => {
    if (!props.gzjzId) {
      console.log('卷宗ID为空，无法获取特殊流程记录');
      return;
    }

    try {
      const queryParams : TslcSqbQuery = {
        pageNum: 1,
        pageSize: 100, // 获取足够多的记录
        gzjzId: props.gzjzId
      };

      console.log('查询特殊流程记录，参数:', queryParams);
      const res = await listTslcSqb(queryParams);

      console.log('特殊流程API响应:', res);

      // 处理不同的响应格式
      if (res.data && Array.isArray(res.data)) {
        specialProcessRecords.value = res.data;
      } else if (res.rows && Array.isArray(res.rows)) {
        specialProcessRecords.value = res.rows;
      } else if (Array.isArray(res)) {
        specialProcessRecords.value = res;
      } else {
        specialProcessRecords.value = [];
      }

      console.log('获取到特殊流程记录:', specialProcessRecords.value);
    } catch (error) {
      console.error('获取特殊流程记录失败:', error);
      specialProcessRecords.value = [];
    }
  };

  // 格式化申请类型
  const formatTslcLx = (value : string) => {
    if (!value) return '暂无';
    if (props.gzTslcLx && Array.isArray(props.gzTslcLx)) {
      const dictItem = props.gzTslcLx.find((item : any) => item.value === value);
      return dictItem ? dictItem.label : value;
    }
    return value;
  };

  // 格式化状态
  const formatTslcZt = (value : string) => {
    if (!value) return '暂无';
    if (props.gzTslcZt && Array.isArray(props.gzTslcZt)) {
      const dictItem = props.gzTslcZt.find((item : any) => item.value === value);
      return dictItem ? dictItem.label : value;
    }
    return value;
  };

  // 附件袋信息
  const attachmentBags = ref<AttachmentBagInfo[]>([]);
  const selectedAttachmentBags = ref<AttachmentBagInfo[]>([]);

  // 分页信息
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  });

  // 初始化数据
  const initData = () => {
    console.log('初始化归档弹窗数据:', props.basicInfo);
    console.log('公证类别字典:', props.gzGzlb);
    console.log('特殊流程类型字典:', props.gzTslcLx);
    console.log('特殊流程状态字典:', props.gzTslcZt);

    // 设置基本信息
    if (props.basicInfo) {
      // 处理基本信息
      basicInfo.value.jzbh = props.basicInfo.jzbh || '';
      basicInfo.value.gzyxm = props.basicInfo.gzyxm || '';
      basicInfo.value.zlxm = props.basicInfo.zlxm || '';
      basicInfo.value.slrq = props.basicInfo.slrq || '';
      basicInfo.value.lb = props.basicInfo.lb || '';

      console.log('原始公证类别值:', props.basicInfo.lb);
      console.log('格式化后的公证类别:', formatGzlb(props.basicInfo.lb));

      // 处理公证书信息 - 支持多种数据结构
      let certificates = [];
      if (props.basicInfo.gzsxVoList && Array.isArray(props.basicInfo.gzsxVoList)) {
        // 标准结构
        certificates = props.basicInfo.gzsxVoList.map((item : any) => ({
          number: item.gzsBh || '',
          matter: item.gzsxMc || '',
          party: item.gxrMc || ''
        }));
      } else if (props.basicInfo.gzsx && typeof props.basicInfo.gzsx === 'string') {
        // 如果只有公证事项字符串，创建一个默认的公证书记录
        // 尝试获取当事人信息，支持多种字段
        const partyNames = [];
        if (props.basicInfo.dsrxm) {
          partyNames.push(props.basicInfo.dsrxm);
        }
        if (props.basicInfo.sqrxm) {
          partyNames.push(props.basicInfo.sqrxm);
        }

        certificates = [{
          number: props.basicInfo.gzsbh || '',
          matter: props.basicInfo.gzsx || '',
          party: partyNames.join('、') || '未知'
        }];
      }
      basicInfo.value.certificates = certificates;

      // 处理备注信息 - 从 yqtxVo 中获取
      if (props.basicInfo.yqtxVo) {
        remarksForm.translationReq = props.basicInfo.yqtxVo.txFyyq || '';
        remarksForm.productionReq = props.basicInfo.yqtxVo.txZzyq || '';
        remarksForm.issuanceReminder = props.basicInfo.yqtxVo.txFztx || '';
        remarksForm.remarks = props.basicInfo.yqtxVo.remark || '';
      } else {
        // 如果没有 yqtxVo，尝试从基本信息中获取备注
        remarksForm.remarks = props.basicInfo.remark || '';
      }

      console.log('处理后的基本信息:', basicInfo.value);
      console.log('处理后的备注信息:', remarksForm);
    }

    // 初始化附件袋信息
    attachmentBags.value = [];

    // 初始化特殊流程记录
    getSpecialProcessRecords();

    // 重置归档表单 - 档案编号从卷宗号获取
    Object.assign(archiveForm, {
      dahh: '',
      dabh: props.basicInfo?.jzbh || '', // 从卷宗号获取档案编号
      modifyDabh: false, // 默认不修改
      bgqx: '2',
      sfmj: '0',
      jafs: '10'
    });
  };

  // 添加附件袋
  const addAttachmentBag = () => {
    const newBag : AttachmentBagInfo = {
      catalogNumber: '',
      itemName: '',
      quantity: 1,
      gzjzId: props.basicInfo.id
    };
    attachmentBags.value.push(newBag);
  };

  // 删除选中的附件袋
  const deleteSelectedAttachmentBags = () => {
    if (selectedAttachmentBags.value.length === 0) {
      ElMessage.warning('请选择要删除的附件袋');
      return;
    }

    ElMessageBox.confirm('确认删除选中的附件袋吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const selectedIds = selectedAttachmentBags.value.map(item => item.id);
      attachmentBags.value = attachmentBags.value.filter(item => !selectedIds.includes(item.id));
      selectedAttachmentBags.value = [];
      ElMessage.success('删除成功');
    });
  };

  // 附件袋选择变化
  const handleAttachmentBagSelectionChange = (selection : AttachmentBagInfo[]) => {
    selectedAttachmentBags.value = selection;
  };

  // 分页大小变化
  const handleSizeChange = (size : number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
  };

  // 当前页变化
  const handleCurrentChange = (page : number) => {
    pagination.currentPage = page;
  };

  // 保存并归档
  const handleSaveAndArchive = async () => {
    if (!archiveFormRef.value) return;

    try {
      await archiveFormRef.value.validate();

      if (!props.gzjzId) {
        ElMessage.error('卷宗ID不能为空');
        return;
      }

      loading.value = true;

      const archiveData : ArchiveFormData = {
        id: props.gzjzId,
        sftg: '1',
        lcyj: '',
        dahh: archiveForm.dahh,
        bgqx: archiveForm.bgqx,
        dabh: archiveForm.dabh,
        sfmj: archiveForm.sfmj,
        jyfs: archiveForm.jyfs,
        gzGzjzArchiveAttachmentsBos: attachmentBags.value
      };
      console.log(archiveData)
      const res = await submitArchive(archiveData);

      if (res.code === 200) {
        emits('success', archiveData);
        handleClose();
      } else {
        ElMessage.error('归档失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('归档失败:', error);
      if (error.message) {
        ElMessage.error('归档失败: ' + error.message);
      }
    } finally {
      loading.value = false;
    }
  };

  // 生成卷宗封皮
  const handleGenerateCover = async () => {
    if (!props.gzjzId) {
      ElMessage.error('卷宗ID不能为空');
      return;
    }
    loading.value = true;
    let params : UserDocGenParams = {
      bizId: props.gzjzId,
      mbWdId: '1952271066190143489',
      extraParams: {
        gzxsId: props.gzjzId
      }
    }
    const loading2 = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.5)',
      fullscreen: true
    })
    docGenerator(params, {
      success: (res) => {
        const { ossId, fileName: path, fileSuffix } = res.data
        const fileName = `卷宗封皮-${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
        const docInfo : GzjzWjccxxForm = {
          wbmc: fileName,
          wblj: JSON.stringify({
            ossId,
            path,
            fileSuffix,
            fileName,
            typeCode: 10,
            typeName: '卷宗封皮',
            mbWdId: '1952271066190143489'
          }),
          lx: 10
        }
        relateDoc(docInfo);
      }
    }).catch((err : any) => {
      console.error('文档生成失败', err)
      ElMessage.error('生成失败,请检查模板是否完善')
    }).finally(() => {
      loading.value = false;
      loading2.close()
    })
  };
  // 添加生成后的文档（关联生成文档）
  const relateDoc = async (docInfo : GzjzWjccxxForm) => {
    try {
      const params = {
        ...docInfo,
        gzjzId: props.gzjzId,
      }
      const res = await addGzjzWjccxx(params);
      if (res.code === 200) {
        ElMessage.success('生成文件添加成功')
        // handleClose();
      }
    } catch (err : any) {
      console.log('关联生成文档错误', err)
      ElMessage.error('添加文档异常')
    }
  }
  // 驳回
  const handleReject = () => {
    rejectDialogVisible.value = true;
  };

  // 取消驳回
  const cancelReject = () => {
    rejectDialogVisible.value = false;
    rejectFormRef.value?.resetFields();
  };

  // 确认驳回
  const confirmReject = async () => {
    if (!rejectFormRef.value) return;

    try {
      await rejectFormRef.value.validate();

      rejectLoading.value = true;
      if (!props.gzjzId) {
        ElMessage.error('卷宗ID不能为空');
        return;
      }

      loading.value = true;

      const archiveData : ArchiveFormData = {
        id: props.gzjzId,
        sftg: '0',
        lcyj: rejectForm.rejectReason,
        lczt: rejectForm.rejectTo
      };
      const res = await submitArchive(archiveData);
      if (res.code === 200) {
        ElMessage.success('驳回成功');
        rejectDialogVisible.value = false;
        rejectFormRef.value?.resetFields();
        emits('reject', { rejectTo: rejectForm.rejectTo, rejectReason: rejectForm.rejectReason });
        emits('success', archiveData);
        handleClose();
      } else {
        ElMessage.error('驳回失败：' + (res.msg || '未知错误'));
      }
    } catch (error : any) {
      console.error('驳回失败:', error);
      if (error.message) {
        ElMessage.error('驳回失败: ' + error.message);
      }
    } finally {
      rejectLoading.value = false;
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    visible.value = false;
    emits('close');
  };

  // 暴露方法给父组件
  defineExpose({
    initData,
    resetRejectForm: () => {
      rejectForm.rejectTo = '';
      rejectForm.rejectReason = '';
      rejectFormRef.value?.resetFields();
    }
  });

  // 监听 props.basicInfo 变化，实时更新数据
  watch(() => props.basicInfo, (newVal) => {
    console.log('basicInfo prop 变化:', newVal);
    if (newVal) {
      initData();
    }
  }, { deep: true, immediate: true });

  // 监听 visible 变化，当弹窗打开时重新初始化数据
  watch(() => visible.value, (newVal) => {
    if (newVal && props.basicInfo) {
      console.log('弹窗打开，重新初始化数据');
      nextTick(() => {
        initData();
      });
    }
  });

  // 监听卷宗ID变化，重新获取特殊流程记录
  watch(() => props.gzjzId, (newVal) => {
    if (newVal && visible.value) {
      console.log('卷宗ID变化，重新获取特殊流程记录:', newVal);
      getSpecialProcessRecords();
    }
  });

  // 监听修改复选框状态变化
  watch(() => archiveForm.modifyDabh, (newVal) => {
    console.log('档案编号修改状态变化:', newVal);
    if (!newVal) {
      // 如果取消修改，恢复原始档案编号
      archiveForm.dabh = props.basicInfo?.jzbh || '';
    }
  });

  // 监听驳回弹窗关闭，重置表单
  watch(() => rejectDialogVisible.value, (newVal) => {
    if (!newVal) {
      // 弹窗关闭时重置表单
      rejectForm.rejectTo = '';
      rejectForm.rejectReason = '';
      rejectFormRef.value?.resetFields();
    }
  });

  // 组件挂载时初始化数据
  onMounted(() => {
    initData();
  });
</script>

<style scoped>
  .archive-dialog {
    max-height: 70vh;
    overflow-y: auto;
  }

  .layout-container {
    display: flex;
    gap: 20px;
    min-height: 500px;
  }

  .left-content,
  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    color: #303133;
  }

  .basic-info-section,
  .certificate-section,
  .remarks-section,
  .archive-details-section,
  .special-process-section,
  .attachment-bags-section {
    background-color: #f9fafc;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
    height: 300px;
    /* 统一高度 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* 防止内容溢出 */
  }

  .certificate-section {
    margin-top: 0;
  }

  .certificate-section h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #606266;
  }

  /* 表格容器样式 */
  .table-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
    /* 重要：允许flex子元素收缩 */
  }

  /* 表单容器样式 */
  .form-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
    /* 为滚动条留出空间 */
  }

  /* 表格样式 */
  :deep(.el-table) {
    flex: 1;
    height: 100%;
  }

  :deep(.el-table__body-wrapper) {
    overflow-y: auto;
    max-height: 180px;
    /* 限制表格体高度，超出显示滚动条 */
  }

  /* 确保表格头部固定 */
  :deep(.el-table__header-wrapper) {
    flex-shrink: 0;
  }

  .attachment-bags-header {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
  }

  .pagination-section {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .dabh-input-group {
    display: flex;
    align-items: center;
    width: 100%;
  }

  :deep(.el-descriptions__label) {
    font-weight: bold;
    color: #606266;
  }

  :deep(.el-form-item__label) {
    font-weight: bold;
    color: #606266;
  }

  :deep(.el-table) {
    margin-top: 10px;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  /* 响应式布局 */
  @media (max-width: 1200px) {
    .layout-container {
      flex-direction: column;
      gap: 15px;
    }

    .left-content,
    .right-content {
      gap: 15px;
    }

    .basic-info-section,
    .certificate-section,
    .remarks-section,
    .archive-details-section,
    .special-process-section,
    .attachment-bags-section {
      padding: 15px;
      height: auto;
      /* 小屏幕下自适应高度 */
      min-height: 250px;
    }
  }
</style>
