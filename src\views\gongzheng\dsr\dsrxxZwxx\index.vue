<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">

            <el-form-item label="姓名" prop="xm">
              <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码" prop="zjhm">
              <el-input v-model="queryParams.zjhm" placeholder="请输入证件号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['dsr:dsrxxZwxx:add']">新增指纹卡片</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxZwxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="姓名" align="center" prop="xm" />
        <el-table-column label="证件号码" align="center" prop="zjhm" />
        <el-table-column label="性别" align="center" prop="xb" />
        <el-table-column label="出生日期" align="center" prop="csrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.csrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="生成日期" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                v-hasPermi="['dsr:dsrxxZwxx:query']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['dsr:dsrxxZwxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['dsr:dsrxxZwxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <el-dialog :title="dialog.title" v-model="dialog.visible" append-to-body width="50%">
      <AddZw v-if="dialog.visible" :vo="form" :dialigEdit="editShow" :closeBtn="closeBtn" ref="addZwRef"
        @update-refresht="handleGetList"></AddZw>
    </el-dialog>
  </div>
</template>

<script setup name="DsrxxZwxx" lang="ts">
  import { listDsrxxZwxx, getDsrxxZwxx, delDsrxxZwxx, addDsrxxZwxx, updateDsrxxZwxx } from '@/api/gongzheng/dsr/dsrxxZwxx';
  import { DsrxxZwxxVO, DsrxxZwxxQuery, DsrxxZwxxForm } from '@/api/gongzheng/dsr/dsrxxZwxx/types';
  import AddZw from '@/views/gongzheng/dsr/dsrxxZwxx/components/add_zw.vue'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const addZwRef = ref<InstanceType<typeof AddZw> | null>(null);
  const dsrxxZwxxList = ref<DsrxxZwxxVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const dateRangeCreateTime = ref<[DateModelType, DateModelType]>(['', '']);
  const closeBtn = ref(true);
  const editShow = ref(true);
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxZwxxFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrxxZwxxForm = {
    id: undefined,
    dsrId: undefined,
    zwtp: undefined,
    zwxx: undefined,
    createTime: undefined,
    qmxx: undefined,
    syxx: undefined
  }
  const data = reactive<PageData<DsrxxZwxxForm, DsrxxZwxxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      xm: undefined,
      zjhm: undefined,
      params: {
        createTime: undefined,
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      dsrId: [
        { required: true, message: "当事人ID不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询当事人-指纹信息列表 */
  const getList = async () => {
    loading.value = true;
    queryParams.value.params = {};
    proxy?.addDateRange(queryParams.value, dateRangeCreateTime.value, 'CreateTime');
    const res = await listDsrxxZwxx(queryParams.value);
    dsrxxZwxxList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxZwxxFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    dateRangeCreateTime.value = ['', ''];
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxZwxxVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    editShow.value = true;
    dialog.visible = true;
    dialog.title = "新增当事人指纹信息";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrxxZwxxVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZwxx(_id);
    Object.assign(form.value, res.data);
    editShow.value = true;
    dialog.visible = true;
    dialog.title = "修改当事人指纹信息";
  }

  const handleDetail = async (row ?: DsrxxZwxxVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxZwxx(_id);
    Object.assign(form.value, res.data);
    editShow.value = false;
    dialog.visible = true;
    dialog.title = "查看当事人指纹信息";
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DsrxxZwxxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除当事人-指纹信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxZwxx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('dsr/dsrxxZwxx/export', {
      ...queryParams.value
    }, `dsrxxZwxx_${new Date().getTime()}.xlsx`)
  }

  const handleGetList = async () => {
    await getList();
    dialog.visible = false;
    dialog.title = "";
  }
  onMounted(() => {
    getList();
  });
</script>
