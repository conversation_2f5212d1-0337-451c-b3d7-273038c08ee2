<template>
  <div class="w-150px h-120px object-contain relative sfz-img-wrap overflow-hidden">
    <div v-if="hasControl" class="w-full h-32px sfz-img-mask absolute flex justify-center items-center">
      <span class="p-16px flex gap-10px">
        <!-- <el-button @click="show" type="primary" link icon="ZoomIn" style="font-size: 22px;"></el-button> -->
        <el-button v-if="controls.includes('take')" @click="takePic" type="primary" link icon="Camera" style="font-size: 22px;"></el-button>
        <el-button v-if="controls.includes('upload')" @click="upload" type="primary" link icon="Upload" style="font-size: 22px;"></el-button>
      </span>
    </div>
    <el-image ref="imag" :src="src" fit="cover" :preview-src-list="[src]" class="w-full h-full">
      <template #error>
        <div class="w-full h-full flex justify-center items-center bg-gray-100">
          <span class="text-gray-400">暂无</span>
        </div>
      </template>
    </el-image>
  </div>
</template>

<script setup lang="ts">

type Control = 'take' | 'upload'

interface Props {
  src: string;
  hasControl?: boolean;
  controls?: Control[];
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  hasControl: true,
  controls: () => ['take', 'upload']
})

const emit = defineEmits(['takePic', 'upload'])

const takePic = () => {
  emit('takePic')
}

const upload = () => {
  emit('upload')
}

</script>

<style>
.sfz-img-mask {
  z-index: 9;
  background-color: rgba(51, 51, 51, 0.704);
  opacity: 0;
  transform: scale(1.2);
  transition: .3s;
  bottom: 0;
}
.sfz-img-wrap:hover > .sfz-img-mask {
  opacity: 1;
  transform: scale(1);
}
</style>
