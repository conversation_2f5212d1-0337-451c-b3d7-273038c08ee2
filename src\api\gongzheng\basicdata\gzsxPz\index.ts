import request from '@/utils/request'

/**
 * 查询公证事项配置列表树
 * @param query
 * @returns {*}
 */
export function listGzsxPzTree(query?: any) {
  return request({
    url: '/basicdata/gzsxPz/listTree',
    method: 'get',
    params: query
  })
}

/**
 * 查询公证事项配置列表
 * @param query
 * @returns {*}
 */
export function listGzsxPz(query?: any) {
  return request({
    url: '/basicdata/gzsxPz/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取公证事项配置详细信息
 * @param id
 * @returns {*}
 */
export function getGzsxPz(id: string | number) {
  return request({
    url: '/basicdata/gzsxPz/' + id,
    method: 'get'
  })
}
/**
 * 获取公证事项配置详细信息
 * @param id
 * @returns {*}
 */
export function getByCodeGzsxPz(gzsxCode: string, gzlbValue: string) {
  return request({
    url: '/basicdata/gzsxPz/by/' + gzsxCode + "/" + gzlbValue,
    method: 'get'
  })
}


/**
 * 新增公证事项配置
 * @param data
 * @returns {*}
 */
export function saveGzsxPz(data: any) {
  return request({
    url: '/basicdata/gzsxPz/save',
    method: 'post',
    data: data
  })
}

/**
 * 新增公证事项配置
 * @param data
 * @returns {*}
 */
export function addGzsxPz(data: any) {
  return request({
    url: '/basicdata/gzsxPz',
    method: 'post',
    data: data
  })
}

/**
 * 修改公证事项配置
 * @param data
 * @returns {*}
 */
export function updateGzsxPz(data: any) {
  return request({
    url: '/basicdata/gzsxPz',
    method: 'put',
    data: data
  })
}

/**
 * 删除公证事项配置
 * @param id
 * @returns {*}
 */
export function delGzsxPz(id: string | number | Array<string | number>) {
  return request({
    url: '/basicdata/gzsxPz/' + id,
    method: 'delete'
  })
}

/**
 * 查询排除子节点的列表
 * @param gzsId
 * @returns {*}
 */
export function listExcludeChild(gzsId: string | number) {
  return request({
    url: '/basicdata/gzsxPz/list/exclude/' + gzsId,
    method: 'get'
  })
}
