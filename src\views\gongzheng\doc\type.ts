import { EnumDocActionType, EnumDocFileType, EnumDocType } from "./enumType";

interface ExtraParams {
  dsrIds ?: string;
  dsrId ?: number;
  dsrLx ?: string;
  [k : string] : any;
}

interface BaseGenParams {
  bizId : string;
  action : EnumDocActionType.GEN;
  taskId : string;
  extraParams ?: ExtraParams;
  saveDocumentType ?: string;//保存文档格式 word / pdf
}

interface DocGenByMbType extends BaseGenParams {
  type : EnumDocType | string;
  fjlb : EnumDocFileType;
}

interface DocGenByMbFile extends BaseGenParams {
  mbWdId : string;
}

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };
type XOR<T, U> = (T | U) extends object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;

interface UserBaseGenParams {
  bizId : string;
  extraParams ?: ExtraParams;
  saveDocumentType ?: 'word' | 'pdf';//保存文档格式 word / pdf
}

interface UserDocByMbType extends UserBaseGenParams {
  type : EnumDocType | string;
  fjlb : EnumDocFileType;
}
interface UserDocByMbFile extends UserBaseGenParams {
  mbWdId : string;
}

export type DocGenParams = XOR<DocGenByMbType, DocGenByMbFile>
export type UserDocGenParams = XOR<UserDocByMbType, UserDocByMbFile>

export interface DocOpenParams {
  path : string;
  action : EnumDocActionType.EDIT | EnumDocActionType.VIEW;
}

export interface GenerateCallBack {
  success ?: (val : any) => void;
  error ?: (val : any) => void;
  progress ?: (val : any) => void;
}
