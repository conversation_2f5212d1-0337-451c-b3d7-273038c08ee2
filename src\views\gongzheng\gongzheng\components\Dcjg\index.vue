<template>
  <gz-dialog v-model="dcjgState.visible" :title="dcjgState.title || title" @closed="closed" append-to-body>
    <DcjgList :gzjz-id="dcjgState.gzjzId || gzjzId" />

    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import DcjgList from './DcjgList.vue';
import { DcjgOpenParams } from './type';

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '申请调查核实'
})

const dcjgState = reactive({
  visible: false,
  title: '申请调查核实',
  gzjzId: undefined
})

const close = () => {
  dcjgState.visible = false
}

const closed = () => {}

const open = (data?: DcjgOpenParams) => {
  dcjgState.visible  = true;
  dcjgState.gzjzId = data.gzjzId;
  dcjgState.title = data?.title || '';
}

defineExpose({
  open
})

</script>
