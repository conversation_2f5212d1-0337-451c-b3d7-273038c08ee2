<template>
  <el-card>
    <template #header>
      <strong>{{ title }}</strong>
    </template>
    <el-table :data="mxState.listData" v-loading="mxState.loading" border stripe>
      <el-table-column type="index" label="#" align="center"/>
      <el-table-column prop="gzjzGzsx" label="公证事项" align="center"/>
      <el-table-column prop="fylx" label="费用类型" align="center">
        <template #default="{row}">
            <el-text>{{ dictMapFormat(gz_sf_lb, row.fylx) }}</el-text>
          </template>
      </el-table-column>
      <el-table-column prop="jjfs" label="计价方式" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_jjfs, row.jjfs) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="sfcj" label="收费场景" align="center">
        <template #default="{ row }">
          <el-text>{{ dictMapFormat(gz_sfcj, row.sfcj) }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="sfjz" label="是否记账" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.sfjz === '1'" type="primary">是</el-tag>
          <el-tag v-else>否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="fyjm" label="减免" align="center" />
      <el-table-column prop="fytf" label="退费" align="center" />
      <el-table-column prop="fyys" label="应收（元）" align="center"/>
      <el-table-column prop="fyss" label="已收（元）" align="center"/>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { listGzjzGzsxSfxx } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, watch, computed, onBeforeMount, onMounted } from 'vue'
import { dictMapFormat } from '@/utils/ruoyi'

interface Props {
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '收费明细',
  gzjzId: '',
});

const mxState = reactive({
  loading: false,
  listData: []
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_sf_lb, gz_jjfs, gz_sfcj } = toRefs<any>(proxy?.useDict( 'gz_sf_lb', 'gz_jjfs', 'gz_sfcj' ));

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

async function loadSfmx() {
  try {
    mxState.loading = true;
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 200
    }
    const res = await listGzjzGzsxSfxx(params);
    if (res.code === 200) {
      mxState.listData = res.rows || []
    }
  } catch (err: any) {
    console.log('费用明细获取失败', err);
    ElMessage.error('费用明细获取失败,请尝试重新加载页面')
  } finally {
    mxState.loading = false;
  }
}

onMounted(() => {
  loadSfmx();
})

</script>