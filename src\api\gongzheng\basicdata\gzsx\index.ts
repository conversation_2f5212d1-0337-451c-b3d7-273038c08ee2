import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzsxVO, GzsxForm, GzsxQuery } from '@/api/gongzheng/basicdata/gzsx/types';

/**
 * 查询基础数据-公证事项列表
 * @param query
 * @returns {*}
 */

export const listGzsx = (query?: GzsxQuery): AxiosPromise<GzsxVO[]> => {
  return request({
    url: '/basicdata/gzsx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询基础数据-公证事项详细
 * @param id
 */
export const getGzsx = (id: string | number): AxiosPromise<GzsxVO> => {
  return request({
    url: '/basicdata/gzsx/' + id,
    method: 'get'
  });
};

/**
 * 新增基础数据-公证事项
 * @param data
 */
export const addGzsx = (data: GzsxForm) => {
  return request({
    url: '/basicdata/gzsx',
    method: 'post',
    data: data
  });
};

/**
 * 修改基础数据-公证事项
 * @param data
 */
export const updateGzsx = (data: GzsxForm) => {
  return request({
    url: '/basicdata/gzsx',
    method: 'put',
    data: data
  });
};

/**
 * 删除基础数据-公证事项
 * @param id
 */
export const delGzsx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsx/' + id,
    method: 'delete'
  });
};



export const listTree = (query ?: GzsxQuery) : AxiosPromise<GzsxVO[]> => {
  return request({
    url: '/basicdata/gzsx/listTree',
    method: 'get',
    params: query
  });
};
export const listTreeByZxbl = (query ?: GzsxQuery) : AxiosPromise<GzsxVO[]> => {
  return request({
    url: '/basicdata/gzsx/listTreeByZxbl',
    method: 'get',
    params: query
  });
};



export const listExcludeChild = (gzsId : string | number) : AxiosPromise<GzsxVO[]> => {
  return request({
    url: '/basicdata/gzsx/list/exclude/' + gzsId,
    method: 'get'
  });
};


export const getGzsxByGzfl = (query ?: GzsxQuery) : AxiosPromise<GzsxVO[]> => {
  return request({
    url: '/basicdata/gzlbpz/getGzsxByGzfl',
    method: 'get',
    params: query
  });
};
