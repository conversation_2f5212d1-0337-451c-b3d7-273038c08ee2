<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="130px">
            <el-form-item label="借贷人/单位" prop="sxr">
              <el-input v-model="queryParams.sxr" placeholder="请输入借贷人/单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="借贷人/单位证件号" prop="sxrzjhm">
              <el-input v-model="queryParams.sxrzjhm" placeholder="请输入借贷人/单位证件号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态">
                <el-option v-for="dict in gz_hmd_zt" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['dsr:dsrxxHmdJdwy:add']">录入</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dsrxxHmdJdwyList" @selection-change="handleSelectionChange">
        <el-table-column label="借贷人/单位" align="center" prop="sxr" />
        <el-table-column label="借贷人/单位证件号" align="center" prop="zqr" />
        <el-table-column label="状态" align="center" prop="zt" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['dsr:dsrxxHmdJdwy:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['dsr:dsrxxHmdJdwy:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改当事人-借贷违约对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body>
      <el-row>
        <el-col :span="18">
          <el-form ref="dsrxxHmdJdwyFormRef" :model="form" :rules="rules" label-width="130px">
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="借贷违约主体类型" prop="jgbs">
                  <el-select v-model="form.sxrzjlx" placeholder="请选择主体类型" clearable>
                    <el-option v-for="dict in gz_dsr_lb" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item>
                  <el-button type="primary">引用</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="证件类型" prop="sxrzjlx">
                  <el-select v-model="form.sxrzjlx" placeholder="请选择证件类型">
                    <el-option v-for="dict in gz_gr_zjlx" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="姓名" prop="sxrxm">
                  <el-input v-model="form.sxrxm" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="失信人证件号码" prop="sxrzjhm">
                  <el-input v-model="form.sxrzjhm" placeholder="请输入失信人证件号码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="状态" prop="zt">
                  <el-input v-model="form.zt" placeholder="请输入状态" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="登记机构" prop="sxrxm">
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="违约日期" prop="wyrq">
                  <el-date-picker clearable v-model="form.wyrq" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择违约日期">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="借贷类型" prop="jdwylx">
                  <el-select v-model="form.jdwylx" placeholder="请选择借贷类型">
                    <el-option v-for="dict in gz_wyjdlx" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="债权人" prop="zqr">
                  <el-input v-model="form.zqr" placeholder="请输入债权人" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="逾期天数" prop="yqts">
                  <el-input v-model="form.yqts" placeholder="请输入逾期天数" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12">
                <el-form-item label="合同金额" prop="htje">
                  <el-input v-model="form.htje" placeholder="请输入合同金额" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="拖欠合计" prop="tjhj">
                  <el-input v-model="form.tjhj" placeholder="请输入拖欠合计" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="情况说明" prop="qksm">
                  <el-input v-model="form.qksm" type="textarea" placeholder="请输入情况说明" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="6">
          <div class="pc-img-div">
            <el-image :src="img" class="pic-img"></el-image>
            <el-button type="primary" link>拍照</el-button>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DsrxxHmdJdwy" lang="ts">
  import { listDsrxxHmdJdwy, getDsrxxHmdJdwy, delDsrxxHmdJdwy, addDsrxxHmdJdwy, updateDsrxxHmdJdwy } from '@/api/gongzheng/dsr/dsrxxHmdJdwy';
  import { DsrxxHmdJdwyVO, DsrxxHmdJdwyQuery, DsrxxHmdJdwyForm } from '@/api/gongzheng/dsr/dsrxxHmdJdwy/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gr_zjlx, gz_wyjdlx,gz_hmd_zt,gz_dsr_lb } = toRefs<any>(proxy?.useDict('gz_dsr_lb','gz_gr_zjlx', 'gz_wyjdlx','gz_hmd_zt'));
  const dsrxxHmdJdwyList = ref<DsrxxHmdJdwyVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const img = ref(null)
  const queryFormRef = ref<ElFormInstance>();
  const dsrxxHmdJdwyFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : DsrxxHmdJdwyForm = {
    id: undefined,
    jgbs: undefined,
    jgbm: undefined,
    jgmc: undefined,
    wyrq: undefined,
    sxr: undefined,
    sxrzjlx: undefined,
    sxrzjhm: undefined,
    sxrxm: undefined,
    jdwylx: undefined,
    zqr: undefined,
    yqts: undefined,
    htje: undefined,
    tjhj: undefined,
    qksm: undefined,
    zt: undefined
  }
  const data = reactive<PageData<DsrxxHmdJdwyForm, DsrxxHmdJdwyQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      jgbs: undefined,
      jgbm: undefined,
      jgmc: undefined,
      wyrq: undefined,
      sxr: undefined,
      sxrzjlx: undefined,
      sxrzjhm: undefined,
      sxrxm: undefined,
      jdwylx: undefined,
      zqr: undefined,
      yqts: undefined,
      htje: undefined,
      tjhj: undefined,
      qksm: undefined,
      zt: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询当事人-借贷违约列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listDsrxxHmdJdwy(queryParams.value);
    dsrxxHmdJdwyList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    dsrxxHmdJdwyFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : DsrxxHmdJdwyVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "借贷违约录入";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: DsrxxHmdJdwyVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getDsrxxHmdJdwy(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "借贷违约修改";
  }

  /** 提交按钮 */
  const submitForm = () => {
    dsrxxHmdJdwyFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateDsrxxHmdJdwy(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addDsrxxHmdJdwy(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: DsrxxHmdJdwyVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除当事人-借贷违约编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDsrxxHmdJdwy(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('dsr/dsrxxHmdJdwy/export', {
      ...queryParams.value
    }, `dsrxxHmdJdwy_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>

<style scoped>
  .pc-img-div {
    padding: 10px;
    margin: 10px;
  }

  .pic-img {
    width: 300px;
    height: 250px;
  }
</style>
