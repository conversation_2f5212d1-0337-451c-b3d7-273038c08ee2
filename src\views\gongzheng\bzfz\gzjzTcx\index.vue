<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
              <el-input v-model="queryParams.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="提存人ID" prop="tcrId">
              <el-input v-model="queryParams.tcrId" placeholder="请输入提存人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="提存人姓名" prop="tcrXm">
              <el-input v-model="queryParams.tcrXm" placeholder="请输入提存人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="提存类别" prop="tclb">
              <el-input v-model="queryParams.tclb" placeholder="请输入提存类别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务类别" prop="ywlb">
              <el-select v-model="queryParams.ywlb" placeholder="请选择业务类别" clearable >
                <el-option v-for="dict in gz_tc_ywlx" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="提存日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeTcrq"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="提存标的" prop="tcbd">
              <el-select v-model="queryParams.tcbd" placeholder="请选择提存标的" clearable >
                <el-option v-for="dict in gz_tc_bd" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="名称/证券名称" prop="wpMc">
              <el-input v-model="queryParams.wpMc" placeholder="请输入名称/证券名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="数量/证券数量" prop="wpSl">
              <el-input v-model="queryParams.wpSl" placeholder="请输入数量/证券数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注" prop="wpBz">
              <el-input v-model="queryParams.wpBz" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="领取条件" prop="lqtj">
              <el-input v-model="queryParams.lqtj" placeholder="请输入领取条件" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="zt">
              <el-select v-model="queryParams.zt" placeholder="请选择状态" clearable >
                <el-option v-for="dict in gz_tc_zt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="币种" prop="hbLx">
              <el-select v-model="queryParams.hbLx" placeholder="请选择币种" clearable >
                <el-option v-for="dict in gz_tc_bz" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="金额" prop="hbJe">
              <el-input v-model="queryParams.hbJe" placeholder="请输入金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="折合人名币" prop="hbZhrmb">
              <el-input v-model="queryParams.hbZhrmb" placeholder="请输入折合人名币" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开户行" prop="hbKhh">
              <el-select v-model="queryParams.hbKhh" placeholder="请选择开户行" clearable >
                <el-option v-for="dict in gz_tc_khh" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="账号" prop="hbYhzh">
              <el-input v-model="queryParams.hbYhzh" placeholder="请输入账号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="入账时间" prop="hbRzsj">
              <el-date-picker clearable
                v-model="queryParams.hbRzsj"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择入账时间"
              />
            </el-form-item>
            <el-form-item label="累计实缴" prop="hbLjsj">
              <el-input v-model="queryParams.hbLjsj" placeholder="请输入累计实缴" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="累计领取" prop="hbLjlq">
              <el-input v-model="queryParams.hbLjlq" placeholder="请输入累计领取" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="余额" prop="hbYe">
              <el-input v-model="queryParams.hbYe" placeholder="请输入余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:gzjzTcx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:gzjzTcx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:gzjzTcx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:gzjzTcx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzTcxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID" align="center" prop="gzjzId" />
        <el-table-column label="公证卷宗公证事项ID" align="center" prop="gzjzGzsxId" />
        <el-table-column label="提存人ID" align="center" prop="tcrId" />
        <el-table-column label="提存人姓名" align="center" prop="tcrXm" />
        <el-table-column label="提存类别" align="center" prop="tclb" />
        <el-table-column label="业务类别" align="center" prop="ywlb">
          <template #default="scope">
            <dict-tag :options="gz_tc_ywlx" :value="scope.row.ywlb"/>
          </template>
        </el-table-column>
        <el-table-column label="提存日期" align="center" prop="tcrq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.tcrq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提存标的" align="center" prop="tcbd">
          <template #default="scope">
            <dict-tag :options="gz_tc_bd" :value="scope.row.tcbd"/>
          </template>
        </el-table-column>
        <el-table-column label="名称/证券名称" align="center" prop="wpMc" />
        <el-table-column label="数量/证券数量" align="center" prop="wpSl" />
        <el-table-column label="备注" align="center" prop="wpBz" />
        <el-table-column label="领取条件" align="center" prop="lqtj" />
        <el-table-column label="状态" align="center" prop="zt">
          <template #default="scope">
            <dict-tag :options="gz_tc_zt" :value="scope.row.zt"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="币种" align="center" prop="hbLx">
          <template #default="scope">
            <dict-tag :options="gz_tc_bz" :value="scope.row.hbLx"/>
          </template>
        </el-table-column>
        <el-table-column label="金额" align="center" prop="hbJe" />
        <el-table-column label="折合人名币" align="center" prop="hbZhrmb" />
        <el-table-column label="开户行" align="center" prop="hbKhh">
          <template #default="scope">
            <dict-tag :options="gz_tc_khh" :value="scope.row.hbKhh"/>
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" prop="hbYhzh" />
        <el-table-column label="入账时间" align="center" prop="hbRzsj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.hbRzsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="累计实缴" align="center" prop="hbLjsj" />
        <el-table-column label="累计领取" align="center" prop="hbLjlq" />
        <el-table-column label="余额" align="center" prop="hbYe" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:gzjzTcx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:gzjzTcx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改提存项对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzTcxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID" />
        </el-form-item>
        <el-form-item label="公证卷宗公证事项ID" prop="gzjzGzsxId">
          <el-input v-model="form.gzjzGzsxId" placeholder="请输入公证卷宗公证事项ID" />
        </el-form-item>
        <el-form-item label="提存人ID" prop="tcrId">
          <el-input v-model="form.tcrId" placeholder="请输入提存人ID" />
        </el-form-item>
        <el-form-item label="提存人姓名" prop="tcrXm">
          <el-input v-model="form.tcrXm" placeholder="请输入提存人姓名" />
        </el-form-item>
        <el-form-item label="提存类别" prop="tclb">
          <el-input v-model="form.tclb" placeholder="请输入提存类别" />
        </el-form-item>
        <el-form-item label="业务类别" prop="ywlb">
          <el-select v-model="form.ywlb" placeholder="请选择业务类别">
            <el-option
                v-for="dict in gz_tc_ywlx"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提存日期" prop="tcrq">
          <el-date-picker clearable
            v-model="form.tcrq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择提存日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="提存标的" prop="tcbd">
          <el-select v-model="form.tcbd" placeholder="请选择提存标的">
            <el-option
                v-for="dict in gz_tc_bd"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称/证券名称" prop="wpMc">
          <el-input v-model="form.wpMc" placeholder="请输入名称/证券名称" />
        </el-form-item>
        <el-form-item label="数量/证券数量" prop="wpSl">
          <el-input v-model="form.wpSl" placeholder="请输入数量/证券数量" />
        </el-form-item>
        <el-form-item label="备注" prop="wpBz">
          <el-input v-model="form.wpBz" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="领取条件" prop="lqtj">
          <el-input v-model="form.lqtj" placeholder="请输入领取条件" />
        </el-form-item>
        <el-form-item label="状态" prop="zt">
          <el-select v-model="form.zt" placeholder="请选择状态">
            <el-option
                v-for="dict in gz_tc_zt"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="币种" prop="hbLx">
          <el-select v-model="form.hbLx" placeholder="请选择币种">
            <el-option
                v-for="dict in gz_tc_bz"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="金额" prop="hbJe">
          <el-input v-model="form.hbJe" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item label="折合人名币" prop="hbZhrmb">
          <el-input v-model="form.hbZhrmb" placeholder="请输入折合人名币" />
        </el-form-item>
        <el-form-item label="开户行" prop="hbKhh">
          <el-select v-model="form.hbKhh" placeholder="请选择开户行">
            <el-option
                v-for="dict in gz_tc_khh"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号" prop="hbYhzh">
          <el-input v-model="form.hbYhzh" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="入账时间" prop="hbRzsj">
          <el-date-picker clearable
            v-model="form.hbRzsj"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择入账时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="累计实缴" prop="hbLjsj">
          <el-input v-model="form.hbLjsj" placeholder="请输入累计实缴" />
        </el-form-item>
        <el-form-item label="累计领取" prop="hbLjlq">
          <el-input v-model="form.hbLjlq" placeholder="请输入累计领取" />
        </el-form-item>
        <el-form-item label="余额" prop="hbYe">
          <el-input v-model="form.hbYe" placeholder="请输入余额" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzTcx" lang="ts">
import { listGzjzTcx, getGzjzTcx, delGzjzTcx, addGzjzTcx, updateGzjzTcx } from '@/api/gongzheng/bzfz/gzjzTcx';
import { GzjzTcxVO, GzjzTcxQuery, GzjzTcxForm } from '@/api/gongzheng/bzfz/gzjzTcx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_tc_bd, gz_tc_ywlx, gz_tc_zt, gz_tc_khh, gz_tc_bz } = toRefs<any>(proxy?.useDict('gz_tc_bd', 'gz_tc_ywlx', 'gz_tc_zt', 'gz_tc_khh', 'gz_tc_bz'));

const gzjzTcxList = ref<GzjzTcxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeTcrq = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const gzjzTcxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzTcxForm = {
  id: undefined,
  gzjzId: undefined,
  gzjzGzsxId: undefined,
  tcrId: undefined,
  tcrXm: undefined,
  tclb: undefined,
  ywlb: undefined,
  tcrq: undefined,
  tcbd: undefined,
  wpMc: undefined,
  wpSl: undefined,
  wpBz: undefined,
  lqtj: undefined,
  zt: undefined,
  remark: undefined,
  hbLx: undefined,
  hbJe: undefined,
  hbZhrmb: undefined,
  hbKhh: undefined,
  hbYhzh: undefined,
  hbRzsj: undefined,
  hbLjsj: undefined,
  hbLjlq: undefined,
  hbYe: undefined
}
const data = reactive<PageData<GzjzTcxForm, GzjzTcxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    gzjzGzsxId: undefined,
    tcrId: undefined,
    tcrXm: undefined,
    tclb: undefined,
    ywlb: undefined,
    tcbd: undefined,
    wpMc: undefined,
    wpSl: undefined,
    wpBz: undefined,
    lqtj: undefined,
    zt: undefined,
    hbLx: undefined,
    hbJe: undefined,
    hbZhrmb: undefined,
    hbKhh: undefined,
    hbYhzh: undefined,
    hbRzsj: undefined,
    hbLjsj: undefined,
    hbLjlq: undefined,
    hbYe: undefined,
    params: {
      tcrq: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    gzjzId: [
      { required: true, message: "公证卷宗ID不能为空", trigger: "blur" }
    ],
    gzjzGzsxId: [
      { required: true, message: "公证卷宗公证事项ID不能为空", trigger: "blur" }
    ],
    tcrId: [
      { required: true, message: "提存人ID不能为空", trigger: "blur" }
    ],
    tclb: [
      { required: true, message: "提存类别不能为空", trigger: "blur" }
    ],
    ywlb: [
      { required: true, message: "业务类别不能为空", trigger: "change" }
    ],
    tcrq: [
      { required: true, message: "提存日期不能为空", trigger: "blur" }
    ],
    tcbd: [
      { required: true, message: "提存标的不能为空", trigger: "change" }
    ],
    wpMc: [
      { required: true, message: "名称/证券名称不能为空", trigger: "blur" }
    ],
    wpSl: [
      { required: true, message: "数量/证券数量不能为空", trigger: "blur" }
    ],
    lqtj: [
      { required: true, message: "领取条件不能为空", trigger: "blur" }
    ],
    hbLx: [
      { required: true, message: "币种不能为空", trigger: "change" }
    ],
    hbJe: [
      { required: true, message: "金额不能为空", trigger: "blur" }
    ],
    hbKhh: [
      { required: true, message: "开户行不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询提存项列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeTcrq.value, 'Tcrq');
  const res = await listGzjzTcx(queryParams.value);
  gzjzTcxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzTcxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeTcrq.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzTcxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加提存项";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzTcxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzTcx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改提存项";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzTcxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzTcx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzTcx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzTcxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除提存项编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzTcx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/gzjzTcx/export', {
    ...queryParams.value
  }, `gzjzTcx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
