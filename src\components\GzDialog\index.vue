<template>
  <component
    :is="h(ElDialog, {
      ...$attrs,
      ...props,
      class: '__gz-dialog',
    }, $slots)"
  />
</template>

<script lang="ts" setup>
import { type DialogProps, ElDialog } from 'element-plus';
import { h } from 'vue';

const props = withDefaults(defineProps<Partial<DialogProps>>(),{
  alignCenter: true,
  modal: true,
  showClose: true,
  destroyOnClose: true,
  appendToBody: true,
});

</script>

<style>
.__gz-dialog {
  display: flex;
  flex-direction: column;
}
.__gz-dialog .el-dialog__body {
  flex: 1;
  max-height: calc(100% - 60px) !important;
  box-sizing: border-box;
}
</style>
