<template>
  <!-- 新增或编辑送达信息 -->
  <el-dialog v-model="dialogForm.visible" :title="dialogForm.title" @close="cancel"  width="800px" append-to-body>
    <el-form :model="form" :rules="rules" ref="sdxxFormRef" label-width="90px">
      <el-form-item prop="tzfs" label="送达方式">
        <el-radio-group v-model="form.tzfs">
          <el-radio v-for="item in gz_fzxx_sdfs" :key="item.value" :label="item.label" :value="item.value" :disabled="item.value==='3'"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="sdrId" label="送达人">
        <el-select v-model="form.sdrId" style="max-width: 260px;" @change="handleSelected">
          <el-option v-for="item in dsrList" :key="item.dsrId" :label="item.name" :value="item.dsrId" />
        </el-select>
      </el-form-item>
      <el-form-item prop="sdhm" label="证件类型">
        <el-select v-model="dsrData.zjlx" style="max-width: 200px;" disabled>
          <el-option v-for="item in gz_gr_zjlx" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model="dsrData.zjhm" style="max-width: 260px; margin-left: 10px;" disabled />
      </el-form-item>
      <el-form-item prop="sdyx" label="住址" v-if="form.tzfs === '2'">
        <el-input v-model="dsrData.zz" style="max-width: 360px;" />
      </el-form-item>
      <el-form-item prop="lxdh" label="联系电话">
        <el-input v-model="form.lxdh" style="max-width: 200px;" />
      </el-form-item>
      <el-form-item prop="lqdd" label="领取地点" v-if="form.tzfs === '1'">
        <el-input v-model="form.lqdd" style="max-width: 360px;" />
      </el-form-item>
      <el-form-item prop="lqxx" label="领取信息">
        <el-radio-group v-model="form.lqxx">
          <el-radio v-for="item in gz_fzxx_lqxx" :key="item.value" :label="item.label" :value="item.value" :disabled="item.value==='2'"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="sdlx" label="送达类型">
        <el-radio-group v-model="form.sdlx" @change="handleSelectedSdlx">
          <el-radio v-for="item in gz_fzxx_sdlx" :key="item.value" :label="item.label" :value="item.value"></el-radio>
        </el-radio-group>
        <div v-if="form.sdlx==='2'" style="margin-left: 10px;">
          <el-row>
            <el-col :span="6" style="text-align: right;">公证事项：</el-col>
            <el-col :span="8">
              <el-select v-model="gzsxJson.id" @change="handleSelectedGzsx" style="">
                <el-option v-for="item in gzsxList" :key="item.id" :label="item.gzsxMc" :value="item.id" />
              </el-select>
            </el-col>
            <el-col :span="6" style="text-align: right;">领取份数：</el-col>
            <el-col :span="4">
              <el-input-number v-model="gzsxJson.lqfs" :min="0"></el-input-number>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
      <!-- <el-form-item prop="remark" label="备注">
        <el-input v-model="form.remark" type="textarea" style="width: 400px;" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="submitForm" type="primary" :loading="buttonLoading">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SdxxForm" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr'
  import type { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
  import { listGzjzSdxx, addGzjzSdxx, updateGzjzSdxx, delGzjzSdxx } from '@/api/gongzheng/gongzheng/gzjzSdxx'
  import type { GzjzSdxxVO, GzjzSdxxForm, GzjzSdxxQuery } from '@/api/gongzheng/gongzheng/gzjzSdxx/types';
  import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_fzxx_sdfs, gz_fzxx_sdlx, gz_gr_zjlx, gz_fzxx_lqxx } = toRefs<any>(proxy?.useDict('gz_fzxx_sdfs', 'gz_fzxx_sdlx', 'gz_gr_zjlx', 'gz_fzxx_lqxx'));

  // 获取父组件的数据
  const gzcAddr = inject<Ref<string>>('gzcAddr', ref(null));
  const gzjzId = inject<Ref<string | number>>('gzjzId', ref(null));
  const gzsxList = inject<Ref<GzjzGzsxVO[]>>('gzsxList', ref(null));

  const dialogForm = reactive<DialogOption>({
    visible: false,
    title: '送达信息'
  });

  const sdxxFormRef = ref<ElFormInstance>();

  const initFormData: GzjzSdxxForm = {
    id: undefined,
    gzjzId: gzjzId.value,
    tzfs: '1',
    sdyx: undefined,
    sdhm: undefined,
    sdrId: undefined,
    lxdh: undefined,
    lqdd: gzcAddr.value,
    lqxx: '1',
    sdlx: '1',
    gzsxJson: '{}'
  }

  const sdxxData = reactive<PageData<GzjzSdxxForm, GzjzSdxxQuery>>({
    form: {...initFormData},
    queryParams: {
      pageNum: 1,
      pageSize: 100,
      gzjzId: gzjzId.value,
      params: {
      }
    },
    rules: {
      tzfs: [
        { required: true, message: "请选择通知方式", trigger: "blur" }
      ],
      sdrId: [
        { required: true, message: "请选择送达人", trigger: "blur" }
      ],
      sdhm: [
        { required: true, message: "请选择证件信息", trigger: "blur" }
      ],
      lxdh: [
        { required: true, message: "请填写联系电话", trigger: "blur" }
      ],
      lqdd: [
        { required: true, message: "请填写领取地点", trigger: "blur" }
      ],
      lqxx: [
        { required: true, message: "请选择领取信息", trigger: "blur" }
      ],
      sdlx: [
        { required: true, message: "请选择送达类型", trigger: "blur" }
      ],
    }
  });

  const { form, rules } = toRefs(sdxxData);

  const dsrData = ref({
    dsrId: undefined,
    zjlx: undefined,
    zjhm: undefined,
    zz: undefined
  });

  const gzsxJson = ref({
    id: undefined,
    gzsxId: undefined,
    lqfs: undefined,
  });

  const dsrList = ref<GzjzDsrVO[]>([]);

  const getDsrList = async () => {
    buttonLoading.value = true;
    const res = await listGzjzDsrByGzjz({gzjzId: gzjzId.value, dsrLx: '1', pageSize: 100, pageNum: 1});
    if(res){
      // 去除重复的当事人
      let dsrIds = [];
      let dsrs = [];
      res.rows.forEach(e => {
        if(dsrIds.indexOf(e.dsrId as string) == -1){
          dsrIds.push(e.dsrId as string);
          dsrs.push(e);
        }
      })
      dsrList.value = dsrs;

    }
    buttonLoading.value = false;
  }

  const handleSelected = (_value: any) => {
    console.log('handleSelected', _value);
    if(_value){
      const dsrSelect = dsrList.value.find(item => item.dsrId === _value);
      console.log('handleSelected.find', dsrSelect);
      dsrData.value = {
          dsrId: dsrSelect.dsrId,
          zjlx: dsrSelect.certificateType,
          zjhm: dsrSelect.certificateNo,
          zz: dsrSelect.address
        };
      form.value.lxdh = dsrSelect.contactTel;
      form.value.sdhm = dsrSelect.contactTel;
    }else{
      dsrData.value = {
          dsrId: undefined,
          zjlx: undefined,
          zjhm: undefined,
          zz: undefined
        };
      form.value.lxdh = undefined;
      form.value.sdhm = undefined;
    }
  }
  const handleSelectedSdlx = (_value: any) => {
    if(_value && _value === '2'){
      const gzsxItem = gzsxList.value[0];
      gzsxJson.value = {
          id: gzsxItem.id,
          gzsxId: gzsxItem.gzsxId,
          lqfs: gzsxItem.gzsFs
        };
    }else{
      gzsxJson.value = {
          id: undefined,
          gzsxId: undefined,
          lqfs: undefined,
        };
    }
  }
  const handleSelectedGzsx = (_value: any) => {
    if(_value){
      const gzsxSelect = gzsxList.value.find(item => item.id === _value);
      gzsxJson.value = {
        id: gzsxSelect.id,
        gzsxId: gzsxSelect.gzsxId,
        lqfs: gzsxSelect.gzsFs
      };
    }else{
      gzsxJson.value = {
          id: undefined,
          gzsxId: undefined,
          lqfs: undefined
        };
    }
  }

  const buttonLoading = ref(false);

  const submitForm = async () => {
    sdxxFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        buttonLoading.value = true;
        form.value.gzjzId = gzjzId.value;
        form.value.gzsxJson = JSON.stringify(gzsxJson.value);
        if (form.value.id) {
          await updateGzjzSdxx(form.value).finally(() =>  buttonLoading.value = false);
        } else {
          await addGzjzSdxx(form.value).finally(() =>  buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialogForm.visible = false;
        emit('callback', {success: true, msssage: '操作成功'});
      }
    });
  }
  // 打开
  const open = (_gzjzId: string | number, _row ?: GzjzSdxxVO) => {
    console.log('sdxxForm.gzjzId', _gzjzId);
    gzjzId.value = _gzjzId;

    reset();
    getDsrList();
    if(_row?.id){
      Object.assign(form.value, _row);
      gzsxJson.value = eval('(' + _row.gzsxJson + ')');
      dialogForm.title = '编辑送达信息';
    }else{
      dialogForm.title = '新增送达信息';
    }
    dialogForm.visible = true;
  }
  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialogForm.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    dsrData.value = {
      dsrId: undefined,
      zjlx: undefined,
      zjhm: undefined,
      zz: undefined
    };
    form.value = {...initFormData};
    sdxxFormRef.value?.resetFields();
  }
  // 回调方法
  const emit = defineEmits(['callback'])
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  })
  onMounted(() => {

  })
</script>

<style>
</style>
