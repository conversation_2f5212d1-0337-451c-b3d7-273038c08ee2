import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GxbassLogVO, GxbassLogForm, GxbassLogQuery } from '@/api/guichain/gxbassLog/types';

/**
 * 查询公证-桂链-上链日志列表
 * @param query
 * @returns {*}
 */

export const listGxbassLog = (query?: GxbassLogQuery): AxiosPromise<GxbassLogVO[]> => {
  return request({
    url: '/guichain/gxbassLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证-桂链-上链日志详细
 * @param id
 */
export const getGxbassLog = (id: string | number): AxiosPromise<GxbassLogVO> => {
  return request({
    url: '/guichain/gxbassLog/' + id,
    method: 'get'
  });
};

/**
 * 新增公证-桂链-上链日志
 * @param data
 */
export const addGxbassLog = (data: GxbassLogForm) => {
  return request({
    url: '/guichain/gxbassLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证-桂链-上链日志
 * @param data
 */
export const updateGxbassLog = (data: GxbassLogForm) => {
  return request({
    url: '/guichain/gxbassLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证-桂链-上链日志
 * @param id
 */
export const delGxbassLog = (id: string | number | Array<string | number>) => {
  return request({
    url: '/guichain/gxbassLog/' + id,
    method: 'delete'
  });
};
