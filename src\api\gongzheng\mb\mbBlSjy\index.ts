import request from '@/utils/request';
import type {
  TemplateVO,
  TemplateVariableVO,
  DataSourceConfigVO,
  SaveDataSourceConfigDTO,
  TestDataSourceConfigDTO,
  ApiResponse
} from './types';
import { AxiosPromise } from 'axios';

// 获取模板列表
export function getTemplateList(type: string): AxiosPromise<ApiResponse<TemplateVO[]>> {
  return request({
    url: '/mb/sjy/list',
    method: 'get',
    params: { sjyType:type }
  });
}

// 获取模板变量列表
export function getTemplateVariables(templateId: string | number): AxiosPromise<ApiResponse<TemplateVariableVO[]>> {
  return request({
    url: '/mb/sjy/variable/list',
    method: 'get',
    params: { templateId }
  });
}

// 获取变量数据源配置列表
export function getDataSourceConfigs(variableId: string | number): AxiosPromise<ApiResponse<DataSourceConfigVO[]>> {
  return request({
    url: '/mbBlSjy/config/list',
    method: 'get',
    params: { variableId }
  });
}

// 新增数据源配置
export function addDataSourceConfig(data: SaveDataSourceConfigDTO): AxiosPromise<ApiResponse> {
  return request({
    url: '/mbBlSjy/config/add',
    method: 'post',
    data
  });
}

// 编辑数据源配置
export function updateDataSourceConfig(id: string | number, data: SaveDataSourceConfigDTO): AxiosPromise<ApiResponse> {
  return request({
    url: `/mbBlSjy/config/update/${id}`,
    method: 'put',
    data
  });
}

// 删除数据源配置
export function deleteDataSourceConfig(id: string | number): AxiosPromise<ApiResponse> {
  return request({
    url: `/mbBlSjy/config/delete/${id}`,
    method: 'delete'
  });
}

// 启用/禁用数据源配置
export function updateConfigStatus(id: string | number, isActive: string | number): AxiosPromise<ApiResponse> {
  return request({
    url: `/mbBlSjy/config/status/${id}`,
    method: 'patch',
    data: { isActive }
  });
}

// 测试数据源配置
export function testDataSourceConfig(data: TestDataSourceConfigDTO): AxiosPromise<ApiResponse<any>> {
  return request({
    url: '/mbBlSjy/config/test',
    method: 'post',
    data
  });
}

// 获取数据源类型
export function getDataSourceTypes(): AxiosPromise<ApiResponse<{label: string, value: string}[]>> {
  return request({
    url: '/mbBlSjy/config/dataSourceTypes',
    method: 'get'
  });
}

// 获取实体类列表
export function getEntityClasses(): AxiosPromise<ApiResponse<{label: string, value: string}[]>> {
  return request({
    url: '/mbBlSjy/config/entityClasses',
    method: 'get'
  });
}

// 删除变量
export function deleteVariable(id: string | number): AxiosPromise<ApiResponse> {
  return request({
    url: `/mbBlSjy/variable/delete/${id}`,
    method: 'delete'
  });
}

// 新增变量
export function addVariable(data: any): AxiosPromise<ApiResponse> {
  return request({
    url: '/mbBlSjy/variable/add',
    method: 'post',
    data
  });
}

// 编辑变量
export function updateVariable(id: string | number, data: any): AxiosPromise<ApiResponse> {
  return request({
    url: `/mbBlSjy/variable/update/${id}`,
    method: 'put',
    data
  });
}
