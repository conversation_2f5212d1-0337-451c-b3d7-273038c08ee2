export interface GzjzGzsbmVO {
  /**
   * ID
   */
  id : string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId : string | number;

  /**
   * 公证书编号
   */
  gzsbh : string;

  /**
   * 年份
   */
  nf : string;

  /**
   * 字号（gz_gzs_zh）
   */
  zh : string;

  /**
   * 流水号（6位数字）
   */
  ls : string;

  /**
   * 是否废号（0否，1是）
   */
  sfZf : string;

}

export interface GzjzGzsbmForm extends BaseEntity {
  /**
   * ID
   */
  id ?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 公证书编号
   */
  gzsbh ?: string;

  /**
   * 年份
   */
  nf ?: string;

  /**
   * 字号（gz_gzs_zh）
   */
  zh ?: string;

  /**
   * 流水号（6位数字）
   */
  ls ?: string;

  /**
   * 是否废号（0否，1是）
   */
  sfZf ?: string;

}

export interface GzjzGzsbmQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 公证书编号
   */
  gzsbh ?: string;

  /**
   * 年份
   */
  nf ?: string;

  /**
   * 字号（gz_gzs_zh）
   */
  zh ?: string;

  /**
   * 流水号（6位数字）
   */
  ls ?: string;

  /**
   * 是否废号（0否，1是）
   */
  sfZf ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
  lb : undefined,
  cy : undefined,
}
