<template>
  <div class="p-2">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="当事人风险信息" name="first">
        <DsrHmd ref="dsrhmdRef"></DsrHmd>
      </el-tab-pane>
      <el-tab-pane label="不动产黑名单" name="second">
        <BdcHmd ref="bdcmdRef"></BdcHmd>
      </el-tab-pane>
      <el-tab-pane label="借贷违约记录" name="third">
        <JdwyHmd ref="jdwyhmdRef"></JdwyHmd>
      </el-tab-pane>
      <el-tab-pane label="全国管理系统黑名单" name="fourth">
        <QgHmd ref="qgmdRef"></QgHmd>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Dsrhmdxx" lang="ts">
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  import DsrHmd from '@/views/gongzheng/dsr/dsrhmdxx/components/dsr_hmd.vue'
  import BdcHmd from '@/views/gongzheng/dsr/dsrhmdxx/components/bdc_hmd.vue'
  import JdwyHmd from '@/views/gongzheng/dsr/dsrhmdxx/components/jdwy_hmd.vue'
  import QgHmd from '@/views/gongzheng/dsr/dsrhmdxx/components/qg_hmd.vue'
  const dsrhmdRef = ref<InstanceType<typeof DsrHmd> | null>(null);
  const bdcmdRef = ref<InstanceType<typeof BdcHmd> | null>(null);
  const jdwyhmdRef = ref<InstanceType<typeof JdwyHmd> | null>(null);
  const qgmdRef = ref<InstanceType<typeof QgHmd> | null>(null);
  const activeName = ref("first")
  const handleClick = (tab, event) => {
    console.log(tab, event);
  }
  onMounted(() => {
  });
</script>
