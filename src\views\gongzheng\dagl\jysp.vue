<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
      <el-form-item label="申请时间：" prop="applyDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
        至
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="受理人：" prop="handler">
        <el-input v-model="queryParams.handler" placeholder="请输入受理人" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 审批列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">审批列表</div>
      </div>

      <el-table
        v-loading="loading"
        :data="approvalList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column label="受理人" align="center" prop="handler" />
        <el-table-column label="申请日期" align="center" prop="applyDate" width="180" />
        <el-table-column label="公证书号" align="center" prop="certNumber" />
        <el-table-column label="审批人" align="center" prop="approver" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>首页</span>
          <span>上一页</span>
          <el-input v-model="currentPage" class="page-input" />
          <span>共 {{ totalPages }} 页</span>
          <span>下一页</span>
          <span>尾页</span>
          <el-select v-model="pageSize" class="page-size-select">
            <el-option :value="10" label="10" />
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 加载状态
const loading = ref(false)

// 查询参数
const queryParams = reactive({
  handler: '',
  startDate: '',
  endDate: '',
  pageNum: 1,
  pageSize: 10
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

// 审批列表数据
const approvalList = ref([])

// 查询审批列表
const getList = () => {
  loading.value = true
  // 这里应该是实际的API调用
  setTimeout(() => {
    // 模拟API返回数据
    approvalList.value = []
    total.value = 0
    loading.value = false
  }, 300)
}

// 搜索按钮操作
const handleQuery = () => {
  currentPage.value = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.handler = ''
  queryParams.startDate = ''
  queryParams.endDate = ''
  handleQuery()
}

// 组件挂载时
onMounted(() => {
  // 初始加载数据
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 15px;
}

.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-input {
  width: 50px;
}

.page-size-select {
  width: 80px;
  margin-left: 10px;
}
</style> 