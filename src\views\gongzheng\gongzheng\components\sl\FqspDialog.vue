<template>
  <!-- 发起审批对话框 -->
  <el-dialog v-model="dialogFqsp.visible" :title="dialogFqsp.title" width="600px" @close="cancelFqsp" append-to-body>
    <div>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="卷宗号" prop="jzbh">
          <el-text>{{formData.jzbh}}</el-text>
        </el-form-item>
        <el-form-item label="公证员" prop="gzymc">
          <el-text>{{formData.gzyxm}}</el-text>
        </el-form-item>
        <el-form-item label="选择审批人" prop="sprbm">
          <el-select v-model="formData.sprbm" filterable placeholder="请选择">
            <el-option
              v-for="item in spy"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="lcyj">
          <el-input type="textarea" v-model="formData.lcyj" :rows="3" placeholder="请输入" minlength="100" show-word-limit/>

          <el-radio-group @change="selectLcyjList" size="small">
            <el-radio-button label="材料齐全！" value="材料齐全！" />
            <el-radio-button label="情况属实！" value="情况属实！" />
            <el-radio-button label="拟上报！" value="拟上报！" />
            <el-radio-button label="请审批！" value="请审批！" />
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitFormFqsp" :loading="buttonLoading" :disabled="buttonLoading">确 定</el-button>
        <el-button @click="cancelFqsp">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="FqspDialog" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { getGzjzJbxx, initiateApproval } from '@/api/gongzheng/gongzheng/gzjzJbxx';
  import type { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { spy, gzy } = toRefs<any>(proxy?.useRoleUser('spy', 'gzy'));

  const buttonLoading = ref(false);
  const loading = ref(true);
  const gzjzJbxx = ref<GzjzJbxxVO>(null);
  const formRef = ref<ElFormInstance>();

  interface GzjzSpData {
    id : string | number;
    jzbh : string; //卷宗号
    gzyxm : string; //公证员姓名
    sprbm : string | number; // 审批人ID
    sprxm : string; // 审批人姓名
    lcyj : string; // 审批意见
    sftg : string; // 是否通过 1通过， 0不通过
  }

  const initFormData : GzjzSpData ={
    id : undefined,
    jzbh : undefined,
    gzyxm : undefined,
    sprbm : undefined,
    sprxm : undefined,
    lcyj : '材料齐全！',
    sftg: '1'
  };

  const formData = ref<GzjzSpData>({...initFormData});

  const rules = ref<ElFormRules>({
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      sprbm: [
        { required: true, message: "请选择审批人", trigger: "blur" }
      ],
      lcyj: [
        { required: true, message: "请输入审批意见", trigger: "blur" }
      ]
    });

  const dialogFqsp = reactive<DialogOption>({
    visible: false,
    title: '发起审批'
  });

  // 回调方法
  const emit = defineEmits(["callback"])

  /** 表单重置 */
  const reset = () => {
    formData.value = {...initFormData};
    formRef.value?.resetFields();
  }

  const selectLcyjList = (selection : any) => {
    console.log('selectLcyjList', selection)
    formData.value.lcyj = selection;
  }

  const handleFqsp = async (gzjzId : string | number) =>{
    reset();
    const res = await getGzjzJbxx(gzjzId);
    gzjzJbxx.value = res.data;
    Object.assign(formData.value, res.data);
    dialogFqsp.visible = true;
  }

  const submitFormFqsp = async () => {
    formRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        buttonLoading.value = true;
        await initiateApproval(formData.value).finally(() =>  buttonLoading.value = false);
        proxy?.$modal.msgSuccess("操作成功");
        dialogFqsp.visible = false;
        emit('callback', gzjzJbxx.value);
      }
    });
  }

  const cancelFqsp = async () => {
    dialogFqsp.visible = false;
  }


  // 暴露方法给父组件
  defineExpose({
    handleFqsp,
    cancelFqsp
  })
  onMounted(() => {
    cancelFqsp();
  })
</script>

<style>
</style>
