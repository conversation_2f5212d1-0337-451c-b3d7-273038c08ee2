<template>
  <!-- 申请翻译对话框 -->
  <el-dialog v-model="dialogSqfy.visible" :title="dialogSqfy.title" top-0 text @close="cancelSqfy" @closed="closed" width="60%" append-to-body>
    <!--案件列表-->
    <el-card class="sqfy-card" shadow="never">
      <template #header>
        <div class="sqfy-card-header"><strong>案件列表</strong></div>
      </template>
      <div class="pd-5">
        <el-table height="240" border :data="gzjzWjccxxState.listData" @selection-change="handleAjSelectChange">
          <el-table-column type="index" width="45" align="center" fixed />
          <el-table-column type="selection" width="40" align="center" fixed />
          <el-table-column prop="sfFy" label="是否申请" min-width="80" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              <el-tag :type="row.sfFy == '1' ? 'primary' : 'info'">{{ row.sfFy == '1' ? '是' : '否' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gzsx" label="公证事项" min-width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="gzsbh" label="公证书编号" min-width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="wbmc" label="文件名称" min-width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="fyzt" label="翻译状态" min-width="120" align="center" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatStatus(row.sfFy, row.fyzt) }}
            </template>
          </el-table-column>

        </el-table>
      </div>
    </el-card>

    <!--文书列表-->
    <el-card class="sqfy-card" shadow="never">
      <template #header>
        <div class=" sqfy-card-header"><strong>文书列表</strong></div>
      </template>
      <div class="pd-5">
        <el-table border :data="gzjzZmclxxState.listData" height="240" @selection-change="handleWsSelectChange">
          <el-table-column type="index" width="45" align="center" fixed />
          <el-table-column type="selection" width="40" align="center" fixed />
          <el-table-column prop="sfFy" label="是否申请" min-width="80" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.sfFy == '1' ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="gzsx" label="公证事项" min-width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="gzsbh" label="公证书编号" min-width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="wbmc" label="文件名称" min-width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="fyzt" label="翻译状态" min-width="120" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ formatStatus(scope.row.sfFy, scope.row.fyzt) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!--翻译要求-->
    <div class="">
      <el-row :gutter=20 style="max-height: 60px">
        <el-col :span="4" style="font-size: 14px;">
          <span>翻译要求：</span>
        </el-col>
        <el-col span="auto" style="max-width: 70% ;">
          <el-text style="font-size: 14px;padding-top: 2px" line-clamp="2">{{ gzjzYqtxState.fyyqText }}</el-text>
        </el-col>
        <el-col :span="2" style="padding-left: 10px">
          <el-button style="font-size:14px" link type="primary" icon="Edit" @click="showFyyq"></el-button>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="buttonSqfyLoading" :disabled="buttonSqfyLoading" @click="batchSqfySub">
          申请翻译
        </el-button>
        <el-button :loading="buttonSqfyLoading" :disabled="buttonSqfyLoading" @click="batchCxfySub">撤销翻译</el-button>
        <el-button @click="cancelSqfy">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!--  翻译修改弹窗-->
  <el-dialog v-model="dialogFyxg.visible" top-0 text @close="cancelFyxg" width="30%" append-to-body>
    <template #header>
      <div class=""><span>{{ dialogFyxg.title }}</span></div>
    </template>
    <div>
      <el-input
        v-model="gzjzYqtxState.fyxgText"
        :autosize="{ minRows: 6, maxRows: 10 }"
        :maxlength="100"
        :show-word-limit="true"
        type="textarea"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitFyyq()" :loading="buttonXggyLoading" :disabled="buttonXggyLoading">
          确认修改
        </el-button>
        <el-button @click="cancelFyxg">关闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup name="SqfyDialog" lang="ts">
import { ComponentInternalInstance, getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
import { batchWjccxxSqfy, listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { listGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';
import { listGzjzYqtx, updateGzjzYqtx } from '@/api/gongzheng/gongzheng/gzjzYqtx';
import type { GzjzWjccxxVO } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import type { GzjzZmclxxVO } from '@/api/gongzheng/gongzheng/gzjzZmclxx/types';
import type { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { batchWjccxxMxSqfy } from '@/api/gongzheng/gongzheng/gzjzZmclxxMx';

const gzjzJbxx = ref<GzjzJbxxVO>(null);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { spy, gzy } = toRefs<any>(proxy?.useRoleUser('spy', 'gzy'));

const emit = defineEmits(['callback', 'closed'])

const buttonSqfyLoading = ref(false);
const buttonXggyLoading = ref(false);
const loading = ref(true);

const dialogSqfy = reactive<DialogOption>({
  visible: false,
  title: '申请翻译'
});

const dialogFyxg = reactive<DialogOption>({
  visible: false,
  title: '修改翻译要求'
});

const gzjzWjccxxState = reactive({
  loading: false,
  listData: [],
  ids: [],
  queryParams: {
    pageNum: 1,
    pageSize: 100,
    gzjzId: undefined,
    lx:'3',
  }
});


/** 查询案件列表 */
const getWjccxxList = async () => {
  const res = await listGzjzWjccxx(gzjzWjccxxState.queryParams);
  Object.assign(gzjzWjccxxState.listData, res.rows);
};

const   gzjzZmclxxState = reactive({
  loading: false,
  listData: [],
  ids: [],
  queryParams: {
    pageNum: 1,
    pageSize: 100,
    gzjzId: undefined,
    lx:'1',
  }
});

/** 查询文书列表 */
const getZmclxxList = async () => {
  const res = await listGzjzWjccxx(gzjzZmclxxState.queryParams);
  Object.assign(gzjzZmclxxState.listData, res.rows);

};


const gzjzYqtxState = reactive({
  loading: false,
  listData: [],
  ids: [],
  fyyqText: undefined,
  fyxgText: undefined,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined
  }
});

/** 查询翻译要求 */
const getYqtxList = async () => {
  const res = await listGzjzYqtx(gzjzYqtxState.queryParams);
  gzjzYqtxState.listData = res.rows;
  if (res.total > 0) {
    gzjzYqtxState.fyyqText = gzjzYqtxState.listData[0].txFyyq;
    gzjzYqtxState.fyxgText = gzjzYqtxState.listData[0].txFyyq;
  }
};

/** 表单重置 */
const reset = () => {

};
const getDateList = () => {
  getWjccxxList();
  getZmclxxList();
};

const handleSqfy = async (gzjzId: string | number, title?: string) => {
  reset();
  gzjzWjccxxState.queryParams.gzjzId = gzjzId;
  gzjzYqtxState.queryParams.gzjzId = gzjzId;
  gzjzZmclxxState.queryParams.gzjzId = gzjzId;
  getDateList();
  getYqtxList();
  dialogSqfy.visible = true;
  dialogSqfy.title = title || '申请翻译'
};

const cancelSqfy = async () => {
  dialogSqfy.visible = false;
  closed()
};

const closed = () => {
  emit('closed')
}

const showFyyq = async () => {
  dialogFyxg.visible = true;
};

const cancelFyxg = async () => {
  dialogFyxg.visible = false;
};

/** 案件多选框选中数据 */
const handleAjSelectChange = (selection: GzjzWjccxxVO[]) => {
  gzjzWjccxxState.ids.value = selection.map(item => item.id);
};

/** 文本多选框选中数据 */
const handleWsSelectChange = (selection: GzjzZmclxxVO[]) => {
  gzjzZmclxxState.ids.value = selection.map(item => item.id);
};

const batchSqfySub = async () => {
  if (gzjzWjccxxState.ids.value != undefined && gzjzWjccxxState.ids.value.length > 0) {
    buttonSqfyLoading.value = true;
    await batchWjccxxSqfy(gzjzWjccxxState.ids.value, gzjzWjccxxState.queryParams.gzjzId, '0').finally(() => {
      buttonSqfyLoading.value = false;
    });
    getWjccxxList()
  }
  if (gzjzZmclxxState.ids.value != undefined && gzjzZmclxxState.ids.value.length > 0) {
    buttonSqfyLoading.value = true;
    await batchWjccxxSqfy(gzjzZmclxxState.ids.value, gzjzZmclxxState.queryParams.gzjzId, '0').finally(() => {
      buttonSqfyLoading.value = false;
    });
    proxy?.$modal.msgSuccess('操作成功');
    getZmclxxList()
  }
};
const batchCxfySub = async () => {
  if (gzjzWjccxxState.ids.value != undefined && gzjzWjccxxState.ids.value.length > 0) {
    buttonSqfyLoading.value = true;
    await batchWjccxxSqfy(gzjzWjccxxState.ids.value, gzjzWjccxxState.queryParams.gzjzId, '1').finally(() => {
      buttonSqfyLoading.value = false;
    });
    getWjccxxList()
  }
  if (gzjzZmclxxState.ids.value != undefined && gzjzZmclxxState.ids.value.length > 0) {
    buttonSqfyLoading.value = true;
    await batchWjccxxSqfy(gzjzZmclxxState.ids.value, gzjzZmclxxState.queryParams.gzjzId, '1').finally(() => {
      buttonSqfyLoading.value = false;
    });
    proxy?.$modal.msgSuccess('操作成功');
    getZmclxxList()
  }
};

const submitFyyq = async () => {
  buttonXggyLoading.value = true;
  gzjzYqtxState.listData[0].txFyyq = gzjzYqtxState.fyxgText;
  await updateGzjzYqtx(gzjzYqtxState.listData[0]).finally(() => buttonXggyLoading.value = false);
  proxy?.$modal.msgSuccess('操作成功');
  await getYqtxList();
  dialogFyxg.visible = false;
};

const formatStatus = (sffy, status) => {
  if (sffy === '1') {
    switch (status) {
      case '0':
        return '待翻译';
      case '1':
        return '待校对';
      case '2':
        return '驳回校对';
      case '3':
        return '已校对';
      case '9':
        return '驳回翻译';
      default:
        return '未知状态';
    }
  } else {
    return '无';
  }

};

// 暴露方法给父组件
defineExpose({
  handleSqfy,
  cancelSqfy
});

// onMounted(() => {
//   cancelSqfy;
// });

</script>

<style lang=scss scoped>
.sqfy-card {
  border: 0;
  padding-bottom: 5px;
}

:deep(.el-card__header) {
  border: 0;
}

:deep(.el-card__body) {
  border: 0;
  padding: 0 5px !important;
  height: 240px;
}

.sqfy-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pd-5 {
  padding: 5px !important;
}
</style>
