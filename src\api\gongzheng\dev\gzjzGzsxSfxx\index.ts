import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzGzsxSfxxVO, GzjzGzsxSfxxForm, GzjzGzsxSfxxQuery } from '@/api/gongzheng/dev/gzjzGzsxSfxx/types';

/**
 * 查询公证卷宗-公证事项-收费明细v1.1列表
 * @param query
 * @returns {*}
 */

export const listGzjzGzsxSfxx = (query?: GzjzGzsxSfxxQuery): AxiosPromise<GzjzGzsxSfxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证事项-收费明细v1.1详细
 * @param id
 */
export const getGzjzGzsxSfxx = (id: string | number): AxiosPromise<GzjzGzsxSfxxVO> => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证事项-收费明细v1.1
 * @param data
 */
export const addGzjzGzsxSfxx = (data: GzjzGzsxSfxxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证事项-收费明细v1.1
 * @param data
 */
export const updateGzjzGzsxSfxx = (data: GzjzGzsxSfxxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证事项-收费明细v1.1
 * @param id
 */
export const delGzjzGzsxSfxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzGzsxSfxx/' + id,
    method: 'delete'
  });
};

