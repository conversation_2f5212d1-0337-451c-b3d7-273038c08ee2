export interface DsrxxRlxxVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 当事人
   */
  dsrId: string | number;

  /**
   * 人脸图片
   */
  rltp: string;

}

export interface DsrxxRlxxForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 当事人
   */
  dsrId?: string | number;

  /**
   * 人脸图片
   */
  rltp?: string;

}

export interface DsrxxRlxxQuery extends PageQuery {

  /**
   * 当事人
   */
  dsrId?: string | number;

  /**
   * 人脸图片
   */
  rltp?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



