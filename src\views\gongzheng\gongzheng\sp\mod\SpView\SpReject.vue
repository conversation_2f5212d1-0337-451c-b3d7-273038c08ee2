<template>
  <el-dialog v-model="visible" :title="title" show-close>
    <el-form ref="bhFormIns" :model="bhForm" :rules="bhFormRules" label-width="120">
      <el-form-item prop="lczt" label="驳回至：">
        <el-select v-model="bhForm.lczt" style="width: 240px;">
          <el-option value="03" label="受理"/>
          <el-option value="04" label="审查"/>
        </el-select>
      </el-form-item>
      <el-form-item prop="lcyj" label="驳回原因：">
        <el-input
          v-model="bhForm.lcyj"
          style="min-width: 200px;max-width: 480px;"
          :autosize="{ minRows: 3, maxRows: 6 }"
          type="textarea"
          placeholder="请输入审批意见."
        />
        <el-popover
          trigger="click"
          :width="320"
        >
          <template #reference>
            <el-button link icon="Edit" size="large"></el-button>
          </template>
          <div class="flex flex-col gap-10px overflow-hidden">
            <div v-for="item in presetBhyy" :key="item.value" class="flex items-center">
              <el-button @click="addBhyy(item.label)" link icon="Plus" size="small"></el-button>
              <el-button @click="setBhyy(item.label)" link size="small">{{ item.label }}</el-button>
            </div>
          </div>
        </el-popover>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex items-center justify-end gap-10px">
        <el-button @click="confirm" type="primary">确认驳回</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { initiateApproval, initiateProduction } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import eventBus from '@/utils/eventBus';
import { ref, computed } from 'vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择驳回节点'
})

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const emit = defineEmits(['update:modelValue', 'rejected', 'close', 'closed'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const bhFormIns = ref<ElFormInstance>(null)

const bhForm = reactive({
  lczt: '03',
  lcyj: ''
})

const bhFormRules = {
  lczt: [
    { required: true, message: '请选择驳回节点', trigger: 'change' }
  ],
  lcyj: [
    { required: true, message: '请输入驳回原因', trigger: 'blur' },
    { required: true, message: '请输入驳回原因', trigger: 'change' }
  ],
}

const presetBhyy = [
  {
    value: '1a',
    label: '材料不齐，驳回补齐！'
  },
  {
    value: '2a',
    label: '证词有误，驳回修改！'
  },
  {
    value: '3a',
    label: '当事人提供的证据有问题，驳回！'
  },
]

const presetRef = ref(null);
const popoverRef = ref(null);

function onClickOutside() {
  unref(popoverRef).popperRef?.delayHide?.()
}

function setBhyy(val: string) {
  bhForm.lcyj = val;
}

function addBhyy(val: string) {
  bhForm.lcyj += val;
}

async function confirmBh() {
  const load = ElLoading.service({
    lock: true,
    text: '正在提交审批，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
    fullscreen: true
  })
  try {
    const params = {
      id: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      sftg: '0',
      ...bhForm
    }
    const res = await initiateProduction(params);
    if (res.code === 200) {
      ElMessage.success('驳回提交完成');
      close()
      emit('rejected')
      eventBus.emit('sp:list:update', {
        msg: '已提交驳回，更新审批列表'
      })
    }
    load.close()
  } catch(err: any) {
    console.error('驳回提交失败', err)
    ElMessage.error('驳回提交失败');
    load.close()
  } finally {

  }
}

function confirm() {
  if (!bhFormIns.value) return;
  bhFormIns.value.validate((valid, feilds) => {
    if (valid) {
      confirmBh();
    }
  })
}

function close() {
  emit('close')
  emit('update:modelValue', false)
}

</script>