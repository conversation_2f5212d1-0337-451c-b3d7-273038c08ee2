<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="90px" size="small" v-show="showSearch"
      class="search-form">
      <el-form-item label="卷宗号：" prop="jzh">
        <el-input v-model="queryParams.jzh" placeholder="请输入卷宗号" clearable style="width: 100px" />
      </el-form-item>
      <el-form-item label="发起人：" prop="tslcFqr">
        <el-input v-model="queryParams.tslcFqr" placeholder="请输入发起人" clearable style="width: 100px" />
      </el-form-item>
      <el-form-item label="发起日期：" prop="tslcFqrq">
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 240px" />
      </el-form-item>
      <el-form-item label="状态：" prop="tslcZt">
        <el-select v-model="queryParams.tslcZt" placeholder="请选择状态" clearable style="width: 100px">
          <el-option v-for="dict in gz_tslc_zt" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="tslcLx">
        <el-select v-model="queryParams.tslcLx" placeholder="请选择类型" clearable style="width: 140px">
          <el-option v-for="dict in gz_tslc_lx" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" v-has-permi="['tslc:fq:query']">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery" v-has-permi="['tslc:fq:query']">重置</el-button>
        <el-button icon="Download" @click="handleExport" v-has-permi="['tslc:fq:query']">导出Excel</el-button>
      </el-form-item>
    </el-form>

    <!-- 申请表列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">申请列表</div>
        <div class="table-actions">
          <el-dropdown @command="handleAddWithType" class="dropdown-button">
            <el-button type="primary">
              特殊流程发起
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="dict in gz_tslc_lx" :key="dict.value" :command="dict.value">
                  {{ dict.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <el-table v-loading="loading" :data="applicationList" border stripe height="500" style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <el-button v-has-permi="['tslc:fq:query']" type="primary" link v-if="scope.row.tslcZt!=9 && scope.row.tslcZt!=3 "
            @click="handleView(scope.row)">查看</el-button>

            <el-button v-has-permi="['tslc:fq:edit']" type="danger" link v-if="scope.row.tslcZt==9 || scope.row.tslcZt==3 "
              @click="handleDelete(scope.row)">删除</el-button>

            <el-button v-has-permi="['tslc:fq:edit']" type="primary" link v-if="scope.row.tslcZt==9 || scope.row.tslcZt==3 "
              @click="handleEdit(scope.row)">编辑</el-button>

            <el-button v-has-permi="['tslc:fq:edit']" v-if="scope.row.tslcZt==1" type="primary" link
              @click="handleWithdraw(scope.row)">撤回</el-button>
            <!-- <el-button v-has-permi="['tslc:fq:query']" type="primary" link
              @click="handleViewDocument(scope.row)">查看文档</el-button> -->
          </template>
        </el-table-column>
        <el-table-column label="卷宗号" align="center" prop="jzh" width="140" />
        <el-table-column label="状态" align="center" prop="tslcZt" width="100">
          <template #default="scope">
            <dict-tag :options="gz_tslc_zt" :value="scope.row.tslcZt" />
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" prop="tslcLx" width="120">
          <template #default="scope">
            <dict-tag :options="gz_tslc_lx" :value="scope.row.tslcLx" />
          </template>
        </el-table-column>
        <el-table-column label="发起人" align="center" prop="tslcFqr" show-overflow-tooltip />
        <el-table-column label="发起日期" align="center" prop="tslcFqrq" width="120" />
        <el-table-column label="公证书编号" align="center" prop="gzsbh" show-overflow-tooltip />
        <el-table-column label="决定书编号" align="center" prop="jdsbh" show-overflow-tooltip />
        <el-table-column label="申请原因" align="center" prop="tslcSqyy" show-overflow-tooltip />
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        :total="total" @pagination="getList" :size="'small'" />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" destroy-on-close>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="卷宗号" prop="jzh">
              <el-input v-model="form.jzh" placeholder="请输入卷宗号" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="tslcLx">
              <el-select v-model="form.tslcLx" placeholder="请选择类型" style="width: 100%" :disabled="isView">
                <el-option v-for="dict in gz_tslc_lx" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发起人" prop="tslcFqr">
              <el-input v-model="form.tslcFqr" placeholder="请输入发起人" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发起日期" prop="tslcFqrq">
              <el-date-picker v-model="form.tslcFqrq" type="date" placeholder="请选择发起日期" value-format="YYYY-MM-DD"
                style="width: 100%" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公证书编号" prop="gzsbh">
              <el-input v-model="form.gzsbh" placeholder="请输入公证书编号" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="决定书编号" prop="jdsbh">
              <el-input v-model="form.jdsbh" placeholder="请输入决定书编号" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否取号" prop="sfQh">
              <el-radio-group v-model="form.sfQh" :disabled="isView">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否销号" prop="sfXh">
              <el-radio-group v-model="form.sfXh" :disabled="isView">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="申请原因" prop="tslcSqyy">
          <el-input v-model="form.tslcSqyy" type="textarea" :rows="4" placeholder="请输入申请原因" :disabled="isView" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" :disabled="isView" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-has-permi="['tslc:fq:edit']" v-if="!isView" type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <PopTslcDetail v-if="showDetail" ref="detailRef" @close="showDetail=false" />

    <!-- 特殊流程申请组件 -->
     <!-- 退费 6 -->
    <PopRefundApplication v-if="showRefundApplication" ref="refundApplicationRef" @success="onRefundSuccess"
      @close="showRefundApplication = false" />

    <PopTerminateNotarization v-if="showTerminateNotarization" ref="terminateNotarizationRef"
      @success="onTerminateSuccess" @close="showTerminateNotarization = false" />
    <PopRejectApplication v-if="showRejectApplication" ref="rejectApplicationRef" @success="onRejectSuccess"
      @close="showRejectApplication = false" />

      <!-- 费用减免 5 -->
    <PopFeeReductionApplication v-if="showFeeReductionApplication" ref="feeReductionApplicationRef"
      @success="onFeeReductionSuccess" @close="showFeeReductionApplication = false" />

    <PopRevokeNotarization v-if="showRevokeNotarization" ref="revokeNotarizationRef" @success="onRevokeSuccess"
      @close="showRevokeNotarization = false" />
    <PopRejectEnforcementCertificate v-if="showRejectEnforcementCertificate" ref="rejectEnforcementCertificateRef"
      @success="onRejectEnforcementSuccess" @close="showRejectEnforcementCertificate = false" />
    <PopDelayedArchiving v-if="showDelayedArchiving" ref="delayedArchivingRef" @success="onDelayedArchivingSuccess"
      @close="showDelayedArchiving = false" />
    <PopArrearsArchiving v-if="showArrearsArchiving" ref="arrearsArchivingRef" @success="onArrearsArchivingSuccess"
      @close="showArrearsArchiving = false" />
    <PopPreArchiving v-if="showPreArchiving" ref="preArchivingRef" @success="onPreArchivingSuccess"
      @close="showPreArchiving = false" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Refresh, Download, ArrowDown } from '@element-plus/icons-vue'
  import type { ComponentInternalInstance } from 'vue'
  import * as api from '@/api/gongzheng/tslc/tslcSqb'
  import { TslcSqbQuery, TslcSqbForm, TslcSqbVO } from '@/api/gongzheng/tslc/tslcSqb/types'
  import PopRefundApplication from './components/popRefundApplication.vue'
  import PopTerminateNotarization from './components/popTerminateNotarization.vue'
  import PopRejectApplication from './components/popRejectApplication.vue'
  import PopFeeReductionApplication from './components/popFeeReductionApplication.vue'
  import PopRevokeNotarization from './components/popRevokeNotarization.vue'
  import PopRejectEnforcementCertificate from './components/popRejectEnforcementCertificate.vue'
  import PopDelayedArchiving from './components/popDelayedArchiving.vue'
  import PopArrearsArchiving from './components/popArrearsArchiving.vue'
  import PopPreArchiving from './components/popPreArchiving.vue'
  import PopTslcDetail from './components/popTslcDetail.vue'

  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_tslc_zt, gz_tslc_lx } = toRefs<any>(proxy?.useDict('gz_tslc_zt', 'gz_tslc_lx'))



  // 显示搜索条件
  const showSearch = ref(true)

  // 加载状态
  const loading = ref(false)

  // 查询参数
  const queryParams = reactive<TslcSqbQuery>({
    pageNum: 1,
    pageSize: 10,
    jzh: '',
    tslcFqr: '',
    tslcZt: '',
    tslcLx: '',
    id: null,
    params: {}
  })

  // 日期范围
  const dateRange = ref<[string, string] | null>(null)

  // 分页参数
  const total = ref(0)

  // 申请表列表数据
  const applicationList = ref<TslcSqbVO[]>([])

  // 选中的ID列表
  const selectedIds = ref<(string | number)[]>([])

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const isView = ref(false)
  const formRef = ref()

  // 详情弹窗
  const showDetail = ref(false)
  const detailRef = ref<InstanceType<typeof PopTslcDetail> | null>(null)

  // 特殊流程申请相关
  const showRefundApplication = ref(false)
  const refundApplicationRef = ref<InstanceType<typeof PopRefundApplication> | null>(null)
  const showTerminateNotarization = ref(false)
  const terminateNotarizationRef = ref<InstanceType<typeof PopTerminateNotarization> | null>(null)
  const showRejectApplication = ref(false)
  const rejectApplicationRef = ref<InstanceType<typeof PopRejectApplication> | null>(null)
  const showFeeReductionApplication = ref(false)
  const feeReductionApplicationRef = ref<InstanceType<typeof PopFeeReductionApplication> | null>(null)
  const showRevokeNotarization = ref(false)
  const revokeNotarizationRef = ref<InstanceType<typeof PopRevokeNotarization> | null>(null)
  const showRejectEnforcementCertificate = ref(false)
  const rejectEnforcementCertificateRef = ref<InstanceType<typeof PopRejectEnforcementCertificate> | null>(null)
  const showDelayedArchiving = ref(false)
  const delayedArchivingRef = ref<InstanceType<typeof PopDelayedArchiving> | null>(null)
  const showArrearsArchiving = ref(false)
  const arrearsArchivingRef = ref<InstanceType<typeof PopArrearsArchiving> | null>(null)
  const showPreArchiving = ref(false)
  const preArchivingRef = ref<InstanceType<typeof PopPreArchiving> | null>(null)

  // 表单数据
  const form = reactive<TslcSqbForm>({
    id: '',
    gzjzId: '',
    jzh: '',
    tslcZt: '',
    tslcLx: '',
    tslcFqrId: '',
    tslcFqr: '',
    tslcFqrq: '',
    gzsbh: '',
    jdsbh: '',
    jdswj: '',
    sfQh: '0',
    sfXh: '0',
    tslcSqyy: '',
    tslcSprId: '',
    tslcSpr: '',
    tslcSprq: '',
    tslcSpyj: '',
    tslcSpbwj: '',
    remark: ''
  })

  // 表单验证规则
  const rules = {
    jzh: [{ required: true, message: '请输入卷宗号', trigger: 'blur' }],
    tslcLx: [{ required: true, message: '请选择类型', trigger: 'change' }],
    tslcFqr: [{ required: true, message: '请输入发起人', trigger: 'blur' }],
    tslcFqrq: [{ required: true, message: '请选择发起日期', trigger: 'change' }],
    tslcSqyy: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
  }

  // 获取列表数据
  const getList = async () => {
    loading.value = true
    try {
      // 处理日期范围
      if (dateRange.value && dateRange.value.length === 2) {
        queryParams.params = {
          beginTslcFqrq: dateRange.value[0],
          endTslcFqrq: dateRange.value[1]
        }
      } else {
        queryParams.params = {}
      }

      const res = await api.listTslcSqb(queryParams)
      applicationList.value = res.rows || []
      total.value = res.total || 0
    } catch (error) {
      console.error('获取列表失败', error)
      ElMessage.error('获取列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索按钮操作
  const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
  }

  // 重置按钮操作
  const resetQuery = () => {
    dateRange.value = null
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      jzh: '',
      tslcFqr: '',
      tslcZt: '',
      tslcLx: '',
      params: {}
    })
    getList()
  }

  // 导出Excel
  const handleExport = async () => {
    try {
      // 处理日期范围
      const exportParams = { ...queryParams }
      if (dateRange.value && dateRange.value.length === 2) {
        exportParams.params = {
          beginTslcFqrq: dateRange.value[0],
          endTslcFqrq: dateRange.value[1]
        }
      } else {
        exportParams.params = {}
      }

      // 移除分页参数
      delete exportParams.pageNum
      delete exportParams.pageSize

      const res = await api.exportTslcSqb(exportParams)

      // 创建下载链接
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `特殊流程申请列表_${new Date().getTime()}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败', error)
      ElMessage.error('导出失败')
    }
  }

  // 分页大小改变
  const handleSizeChange = (size : number) => {
    queryParams.pageSize = size
    getList()
  }

  // 当前页改变
  const handleCurrentChange = (page : number) => {
    queryParams.pageNum = page
    getList()
  }

  // 表格选择变化
  const handleSelectionChange = (selection : TslcSqbVO[]) => {
    selectedIds.value = selection.map(item => item.id)
  }

  // 新增
  const handleAdd = () => {
    resetForm()
    dialogTitle.value = '新增申请'
    isView.value = false
    dialogVisible.value = true
  }

  // 带类型的新增
  const handleAddWithType = (type : string) => {
    // 如果是退费申请类型（value=6），打开退费申请组件
    if (type === '6') {
      showRefundApplication.value = true;
      proxy.$nextTick(() => {
        refundApplicationRef.value?.open({});
      });
      return;
    }

    // 如果是终止公证申请类型（value=2），打开终止公证申请组件
    if (type === '2') {
      showTerminateNotarization.value = true;
      proxy.$nextTick(() => {
        terminateNotarizationRef.value?.open({});
      });
      return;
    }

    // 如果是不予办理申请类型（value=3），打开不予办理申请组件
    if (type === '3') {
      showRejectApplication.value = true;
      proxy.$nextTick(() => {
        rejectApplicationRef.value?.open({});
      });
      return;
    }

    // 如果是费用减免申请类型（value=5），打开费用减免申请组件
    if (type === '5') {
      showFeeReductionApplication.value = true;
      proxy.$nextTick(() => {
        feeReductionApplicationRef.value?.open({});
      });
      return;
    }

    // 如果是撤销公证申请类型（value=1），打开撤销公证申请组件
    if (type === '1') {
      showRevokeNotarization.value = true;
      proxy.$nextTick(() => {
        revokeNotarizationRef.value?.open({});
      });
      return;
    }

    // 如果是不予签发执行证书申请类型（value=4），打开不予签发执行证书申请组件
    if (type === '4') {
      showRejectEnforcementCertificate.value = true;
      proxy.$nextTick(() => {
        rejectEnforcementCertificateRef.value?.open({});
      });
      return;
    }

    // 如果是延迟归档申请类型（value=7），打开延迟归档申请组件
    if (type === '7') {
      showDelayedArchiving.value = true;
      proxy.$nextTick(() => {
        delayedArchivingRef.value?.open({});
      });
      return;
    }

    // 如果是欠费归档申请类型（value=8），打开欠费归档申请组件
    if (type === '8') {
      showArrearsArchiving.value = true;
      proxy.$nextTick(() => {
        arrearsArchivingRef.value?.open({});
      });
      return;
    }

    // 如果是先予归档申请类型（value=9），打开先予归档申请组件
    if (type === '9') {
      showPreArchiving.value = true;
      proxy.$nextTick(() => {
        preArchivingRef.value?.open({});
      });
      return;
    }

    // 其他类型走原有逻辑
    resetForm()
    form.tslcLx = type
    dialogTitle.value = '新增申请'
    isView.value = false
    dialogVisible.value = true
  }

  // 编辑：根据类型打开对应弹窗，传入 id 与编辑模式
  const handleEdit = async (row : TslcSqbVO) => {
    const type = String(row.tslcLx)
    const openWith = (fn : () => void) => { fn() }
    if (type === '6') { // 退费
      showRefundApplication.value = true
      proxy?.$nextTick(() => refundApplicationRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '2') { // 终止公证
      showTerminateNotarization.value = true
      proxy?.$nextTick(() => terminateNotarizationRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '3') { // 不予办理
      showRejectApplication.value = true
      proxy?.$nextTick(() => rejectApplicationRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '5') { // 费用减免
      showFeeReductionApplication.value = true
      proxy?.$nextTick(() => feeReductionApplicationRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '1') { // 撤销公证
      showRevokeNotarization.value = true
      proxy?.$nextTick(() => revokeNotarizationRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '4') { // 不予签发执行证书
      showRejectEnforcementCertificate.value = true
      proxy?.$nextTick(() => rejectEnforcementCertificateRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '7') { // 延迟归档
      showDelayedArchiving.value = true
      proxy?.$nextTick(() => delayedArchivingRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '8') { // 欠费归档
      showArrearsArchiving.value = true
      proxy?.$nextTick(() => arrearsArchivingRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    if (type === '9') { // 先予归档
      showPreArchiving.value = true
      proxy?.$nextTick(() => preArchivingRef.value?.open({ id: row.id, mode: 'edit' }))
      return
    }
    // 其他类型仍走内置对话框
    try {
      const res = await api.getTslcSqb(row.id)
      Object.assign(form, res.data)
      dialogTitle.value = '编辑申请'
      isView.value = false
      dialogVisible.value = true
    } catch (error) {
      console.error('获取详情失败', error)
      ElMessage.error('获取详情失败')
    }
  }
  //撤回
  const handleWithdraw = async (row : TslcSqbVO) => {
    await proxy?.$modal.confirm(`确认要撤回申请？`)
    await api.withdraw({ id: row.id });
    ElMessage.success('撤回成功')
    getList()
  }
  //查看文档
  const handleViewDocument = async (row : TslcSqbVO) => {

  }
  // 查看
  const handleView = async (row : TslcSqbVO) => {
    showDetail.value = true
    proxy?.$nextTick(() => {
      detailRef.value?.open({ id: row.id })
    })
  }

  // 删除
  const handleDelete = async (row ?: TslcSqbVO) => {
    const ids = row ? [row.id] : selectedIds.value
    if (ids.length === 0) {
      ElMessage.warning('请选择要删除的数据')
      return
    }

    try {
      await proxy?.$modal.confirm(`确认删除选中的${ids.length}条数据？`)
      await api.delTslcSqb(ids)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate()

      if (form.id) {
        // 编辑
        await api.updateTslcSqb(form)
        // ElMessage.success('编辑成功')
      } else {
        // 新增
        await api.addTslcSqb(form)
        // ElMessage.success('新增成功')
      }

      dialogVisible.value = false
      getList()
    } catch (error) {
      console.error('提交失败', error)
      ElMessage.error('提交失败')
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(form, {
      id: '',
      gzjzId: '',
      jzh: '',
      tslcZt: '',
      tslcLx: '',
      tslcFqrId: '',
      tslcFqr: '',
      tslcFqrq: '',
      gzsbh: '',
      jdsbh: '',
      jdswj: '',
      sfQh: '0',
      sfXh: '0',
      tslcSqyy: '',
      tslcSprId: '',
      tslcSpr: '',
      tslcSprq: '',
      tslcSpyj: '',
      tslcSpbwj: '',
      remark: ''
    })
    formRef.value?.clearValidate()
  }

  // 特殊流程申请成功回调
  const onRefundSuccess = () => {
    // ElMessage.success('退费申请提交成功');
    getList(); // 刷新列表
  }

  const onTerminateSuccess = () => {
    // ElMessage.success('终止公证申请提交成功');
    getList(); // 刷新列表
  }

  const onRejectSuccess = () => {
    // ElMessage.success('不予办理申请提交成功');
    getList(); // 刷新列表
  }

  const onFeeReductionSuccess = () => {
    // ElMessage.success('费用减免申请提交成功');
    getList(); // 刷新列表
  }

  const onRevokeSuccess = () => {
    // ElMessage.success('撤销公证申请提交成功');
    getList(); // 刷新列表
  }

  const onRejectEnforcementSuccess = () => {
    // ElMessage.success('不予签发执行证书申请提交成功');
    getList(); // 刷新列表
  }

  const onDelayedArchivingSuccess = () => {
    // ElMessage.success('延迟归档申请提交成功');
    getList(); // 刷新列表
  }

  const onArrearsArchivingSuccess = () => {
    // ElMessage.success('欠费归档申请提交成功');
    getList(); // 刷新列表
  }

  const onPreArchivingSuccess = () => {
    // ElMessage.success('先予归档申请提交成功');
    getList(); // 刷新列表
  }

  // 组件挂载时
  onMounted(() => {
    getList()
  })
</script>

<style scoped>
  .app-container {
    padding: 15px;
  }

  .search-form {
    background-color: #fff;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 4px;
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .table-title {
    font-size: 16px;
    font-weight: bold;
  }

  .table-actions {
    display: flex;
    gap: 10px;
  }

  .dropdown-button {
    margin-right: 10px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
