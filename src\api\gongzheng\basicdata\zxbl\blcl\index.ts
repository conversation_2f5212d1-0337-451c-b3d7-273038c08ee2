import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZxblMaterialVO, ZxblMaterialForm, ZxblMaterialQuery } from '@/api/gongzheng/basicdata/zxbl/blcl/types';

/**
 * 查询在线办理材料管理列表
 * @param query
 * @returns {*}
 */
export const listZxblMaterial = (query?: ZxblMaterialQuery): AxiosPromise<ZxblMaterialVO[]> => {
  return request({
    url: '/basicdata/gzsxZxblMaterial/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询在线办理材料管理详细
 * @param id
 */
export const getZxblMaterial = (id: string | number): AxiosPromise<ZxblMaterialVO> => {
  return request({
    url: '/basicdata/gzsxZxblMaterial/' + id,
    method: 'get'
  });
};

/**
 * 新增在线办理材料管理
 * @param data
 */
export const addZxblMaterial = (data: ZxblMaterialForm) => {
  return request({
    url: '/basicdata/gzsxZxblMaterial',
    method: 'post',
    data: data
  });
};

/**
 * 修改在线办理材料管理
 * @param data
 */
export const updateZxblMaterial = (data: ZxblMaterialForm) => {
  return request({
    url: '/basicdata/gzsxZxblMaterial',
    method: 'put',
    data: data
  });
};

/**
 * 删除在线办理材料管理
 * @param id
 */
export const delZxblMaterial = (id: string | number | Array<string | number>) => {
  return request({
    url: '/basicdata/gzsxZxblMaterial/' + id,
    method: 'delete'
  });
};

/**
 * 导出在线办理材料管理
 * @param query
 */
export const exportZxblMaterial = (query?: ZxblMaterialQuery) => {
  return request({
    url: '/basicdata/gzsxZxblMaterial/export',
    method: 'get',
    params: query
  });
};
