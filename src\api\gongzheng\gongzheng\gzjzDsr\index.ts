import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzDsrVO, GzjzDsrForm, GzjzDsrQuery } from '@/api/gongzheng/gzjzDsr/types';
import { GzjzDsrZp, GzjzDsrZpQuery } from './types';

/**
 * 查询公证卷宗-当事人v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzDsr = (query?: GzjzDsrQuery): AxiosPromise<GzjzDsrVO[]> => {
  return request({
    url: '/gongzheng/gzjzDsr/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-当事人列表 (根据卷宗ID)
 * @param query
 * @returns {*}
 */
export const listGzjzDsrByGzjz = (query?: GzjzDsrQuery): AxiosPromise<GzjzDsrVO[]> => {
  return request({
    url: '/gongzheng/gzjzDsr/listByGzjz',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-当事人v1.0详细
 * @param id
 */
export const getGzjzDsr = (id: string | number): AxiosPromise<GzjzDsrVO> => {
  return request({
    url: '/gongzheng/gzjzDsr/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-当事人v1.0
 * @param data
 */
export const addGzjzDsr = (data: GzjzDsrForm) => {
  return request({
    url: '/gongzheng/gzjzDsr',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-当事人v1.0
 * @param data
 */
export const updateGzjzDsr = (data: GzjzDsrForm) => {
  return request({
    url: '/gongzheng/gzjzDsr',
    method: 'put',
    data: data
  });
};

/**
 * 修改公证卷宗-当事人v1.0
 * @param data
 */
export const updateBySMSGzjzDsr = (id: string | number | Array<string | number>, data: GzjzDsrForm) => {
  return request({
    url: '/gongzheng/gzjzDsr/sms/' + id,
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-当事人v1.0
 * @param id
 */
export const delGzjzDsr = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzDsr/' + id,
    method: 'delete'
  });
};

/**
 * 获取可代理的当事人列表
 * @returns
 */
export const getDlrAvailable = (params: { gzjzId: string, dlrId: number }) => {
  return request({
    url: '/gongzheng/gzjzDsr/getAgentAvailable',
    method: 'get',
    params
  });
}

/**
 * 更新代理人代理关系
 */
export const updateDlrDlgx = (data: { gzjzId: string, dlrId: number, dsrIds: Array<number> }) => {
  return request({
    url: '/gongzheng/gzjzDsr/updateAgentAvailable',
    method: 'post',
    data
  });
}

/**
 * 修改当事人电话&角色类型
 */
export const updateDsrTelAndJs = (data: { id: string, js: string, contactTel: string }) => {
  return request({
    url: '/gongzheng/gzjzDsr/editByJs',
    method: 'put',
    data
  });
}

/**
 * 修改当事人照片 （可能废弃）
 * @param data
 * @returns
 */
export const updateDsrZp = (data: GzjzDsrZp) => {
  return request({
    url: '/dsr/dsrxxZrrZp',
    method: 'put',
    data
  });
};

/**
 * 新增当事人照片 （可能废弃）
 * @param data
 * @returns
 */
export const addDarZp = (data: GzjzDsrZp) => {
  return request({
    url: '/dsr/dsrxxZrrZp',
    method: 'post',
    data
  });
}

/**
 * 当事人图片上传
 * @param data
 * @returns
 */
export const uploadDsrZp = (data: GzjzDsrZp) => {
  return request({
    url: '/dsr/dsrxxZrrZp/uploadFile',
    method: 'post',
    data
  });
}

// 导出当事人照片列表
export const exportDsrZpList = () => {
  return request({
    url: '/dsr/dsrxxZrrZp/export',
    method: 'post',
  });
}

// 获取当事人照片详情
export const getDsrZpDetail = (id: string | number) => {
  return request({
    url: '/dsr/dsrxxZrrZp/' + id,
    method: 'get'
  });
}

// 查询当事人照片列表
export const queryDsrZpList = (params: GzjzDsrZpQuery) => {
  return request({
    url: '/dsr/dsrxxZrrZp/list',
    method: 'get',
    params
  });
}

/**
 * 删除当事人照片
 * @param ids
 * @returns
 */
export const deleteDsrZp = (ids: string) => {
  return request({
    url: '/dsr/dsrxxZrrZp/' + ids,
    method: 'delete',
  });
}
