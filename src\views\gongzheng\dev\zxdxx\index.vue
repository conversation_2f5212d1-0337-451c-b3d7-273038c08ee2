<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="咨询单号" prop="zxDh">
              <el-input v-model="queryParams.zxDh" placeholder="请输入咨询单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="咨询日期" prop="zxRq">
              <el-date-picker clearable
                v-model="queryParams.zxRq"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择咨询日期"
              />
            </el-form-item>
            <el-form-item label="咨询人ID" prop="zxrId">
              <el-input v-model="queryParams.zxrId" placeholder="请输入咨询人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="咨询人姓名" prop="zxrXm">
              <el-input v-model="queryParams.zxrXm" placeholder="请输入咨询人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证类别" prop="gzlb">
              <el-select v-model="queryParams.gzlb" placeholder="请选择公证类别" clearable >
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="接待人ID" prop="jdrId">
              <el-input v-model="queryParams.jdrId" placeholder="请输入接待人ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="接待人姓名" prop="jdrXm">
              <el-input v-model="queryParams.jdrXm" placeholder="请输入接待人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="使用地" prop="syd">
              <el-select v-model="queryParams.syd" placeholder="请选择使用地" clearable >
                <el-option v-for="dict in gz_sl_syd" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="咨询电话" prop="zxdh">
              <el-input v-model="queryParams.zxdh" placeholder="请输入咨询电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="其他情况" prop="qtqk">
              <el-input v-model="queryParams.qtqk" placeholder="请输入其他情况" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="咨询记录" prop="zxjl">
              <el-input v-model="queryParams.zxjl" placeholder="请输入咨询记录" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="咨询单文件地址" prop="zxdWjdz">
              <el-input v-model="queryParams.zxdWjdz" placeholder="请输入咨询单文件地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="公证事项名" prop="gzsxMc">
              <el-input v-model="queryParams.gzsxMc" placeholder="请输入公证事项名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:zxdxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:zxdxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:zxdxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:zxdxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="zxdxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="zxId" v-if="true" />
        <el-table-column label="咨询单号" align="center" prop="zxDh" />
        <el-table-column label="咨询日期" align="center" prop="zxRq" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.zxRq, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="咨询人ID" align="center" prop="zxrId" />
        <el-table-column label="咨询人姓名" align="center" prop="zxrXm" />
        <el-table-column label="公证类别" align="center" prop="gzlb">
          <template #default="scope">
            <dict-tag :options="gz_gzlb" :value="scope.row.gzlb"/>
          </template>
        </el-table-column>
        <el-table-column label="接待人ID" align="center" prop="jdrId" />
        <el-table-column label="接待人姓名" align="center" prop="jdrXm" />
        <el-table-column label="使用地" align="center" prop="syd">
          <template #default="scope">
            <dict-tag :options="gz_sl_syd" :value="scope.row.syd"/>
          </template>
        </el-table-column>
        <el-table-column label="咨询电话" align="center" prop="zxdh" />
        <el-table-column label="其他情况" align="center" prop="qtqk" />
        <el-table-column label="咨询记录" align="center" prop="zxjl" />
        <el-table-column label="咨询单文件地址" align="center" prop="zxdWjdz" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="公证事项名" align="center" prop="gzsxMc" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:zxdxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:zxdxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证-咨询单信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="zxdxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="咨询单号" prop="zxDh">
          <el-input v-model="form.zxDh" placeholder="请输入咨询单号" />
        </el-form-item>
        <el-form-item label="咨询日期" prop="zxRq">
          <el-date-picker clearable
            v-model="form.zxRq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择咨询日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="咨询人ID" prop="zxrId">
          <el-input v-model="form.zxrId" placeholder="请输入咨询人ID" />
        </el-form-item>
        <el-form-item label="咨询人姓名" prop="zxrXm">
          <el-input v-model="form.zxrXm" placeholder="请输入咨询人姓名" />
        </el-form-item>
        <el-form-item label="公证类别" prop="gzlb">
          <el-select v-model="form.gzlb" placeholder="请选择公证类别">
            <el-option
                v-for="dict in gz_gzlb"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接待人ID" prop="jdrId">
          <el-input v-model="form.jdrId" placeholder="请输入接待人ID" />
        </el-form-item>
        <el-form-item label="接待人姓名" prop="jdrXm">
          <el-input v-model="form.jdrXm" placeholder="请输入接待人姓名" />
        </el-form-item>
        <el-form-item label="使用地" prop="syd">
          <el-select v-model="form.syd" placeholder="请选择使用地">
            <el-option
                v-for="dict in gz_sl_syd"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="咨询电话" prop="zxdh">
          <el-input v-model="form.zxdh" placeholder="请输入咨询电话" />
        </el-form-item>
        <el-form-item label="其他情况" prop="qtqk">
          <el-input v-model="form.qtqk" placeholder="请输入其他情况" />
        </el-form-item>
        <el-form-item label="咨询记录" prop="zxjl">
          <el-input v-model="form.zxjl" placeholder="请输入咨询记录" />
        </el-form-item>
        <el-form-item label="咨询单文件地址" prop="zxdWjdz">
          <el-input v-model="form.zxdWjdz" placeholder="请输入咨询单文件地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="公证事项名" prop="gzsxMc">
            <el-input v-model="form.gzsxMc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Zxdxx" lang="ts">
import { listZxdxx, getZxdxx, delZxdxx, addZxdxx, updateZxdxx } from '@/api/gongzheng/gongzheng/zxdxx';
import { ZxdxxVO, ZxdxxQuery, ZxdxxForm } from '@/api/gongzheng/gongzheng/zxdxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_sl_syd, gz_gzlb } = toRefs<any>(proxy?.useDict('gz_sl_syd', 'gz_gzlb'));

const zxdxxList = ref<ZxdxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const zxdxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ZxdxxForm = {
  zxId: undefined,
  zxDh: undefined,
  zxRq: undefined,
  zxrId: undefined,
  zxrXm: undefined,
  gzlb: undefined,
  jdrId: undefined,
  jdrXm: undefined,
  syd: undefined,
  zxdh: undefined,
  qtqk: undefined,
  zxjl: undefined,
  zxdWjdz: undefined,
  remark: undefined,
  gzsxMc: undefined
}
const data = reactive<PageData<ZxdxxForm, ZxdxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    zxDh: undefined,
    zxRq: undefined,
    zxrId: undefined,
    zxrXm: undefined,
    gzlb: undefined,
    jdrId: undefined,
    jdrXm: undefined,
    syd: undefined,
    zxdh: undefined,
    qtqk: undefined,
    zxjl: undefined,
    zxdWjdz: undefined,
    gzsxMc: undefined,
    params: {
    }
  },
  rules: {
    zxId: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证-咨询单信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listZxdxx(queryParams.value);
  zxdxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  zxdxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ZxdxxVO[]) => {
  ids.value = selection.map(item => item.zxId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证-咨询单信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ZxdxxVO) => {
  reset();
  const _zxId = row?.zxId || ids.value[0]
  const res = await getZxdxx(_zxId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证-咨询单信息";
}

/** 提交按钮 */
const submitForm = () => {
  zxdxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.zxId) {
        await updateZxdxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addZxdxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ZxdxxVO) => {
  const _zxIds = row?.zxId || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证-咨询单信息编号为"' + _zxIds + '"的数据项？').finally(() => loading.value = false);
  await delZxdxx(_zxIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/zxdxx/export', {
    ...queryParams.value
  }, `zxdxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
