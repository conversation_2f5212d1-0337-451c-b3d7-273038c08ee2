export interface GzjzYzVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 遗嘱人（逗号分隔）
   */
  qzrIds: string;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds: string;

  /**
   * 共同遗嘱（字典：gz_yes_or_no）
   */
  gtyz: string;

  /**
   * 备注
   */
  remark: string;

}

export interface GzjzYzVOEdit {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 遗嘱人（逗号分隔）
   */
  qzrIds?: string[] | number[];

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string[] | number[];

  /**
   * 共同遗嘱（字典：gz_yes_or_no）
   */
  gtyz?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzYzForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 遗嘱人（逗号分隔）
   */
  qzrIds?: string | number;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string | number;

  /**
   * 共同遗嘱（字典：gz_yes_or_no）
   */
  gtyz?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface GzjzYzQuery extends PageQuery {

  /**
   * 遗嘱人（逗号分隔）
   */
  qzrIds?: string | number;

  /**
   * 代理人（逗号分隔）
   */
  dlrIds?: string | number;

  /**
   * 共同遗嘱（字典：gz_yes_or_no）
   */
  gtyz?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



