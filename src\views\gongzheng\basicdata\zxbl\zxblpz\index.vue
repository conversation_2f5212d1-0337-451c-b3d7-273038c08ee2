<template>
  <div class="p-2">
    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" label-width="140px" :model="queryParams" :inline="true">
            <el-form-item label="公证事项名称" prop="gzsxName">
              <el-input v-model="queryParams.gzsxName" placeholder="请输入公证事项名称" clearable style="width: 200px"
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否在线办理" prop="isOnlineHandle">
              <el-select v-model="queryParams.isOnlineHandle" placeholder="请选择" clearable style="width: 150px">
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否启用办理" prop="isEnableHandle">
              <el-select v-model="queryParams.isEnableHandle" placeholder="请选择" clearable style="width: 150px">
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 主体内容 -->
    <el-row :gutter="20">
      <!-- 左侧：公证事项树 -->
      <el-col :span="8">
        <el-card shadow="never">
          <template #header>
            <div class="flex justify-between items-center">
              <span>公证事项名称</span>
              <el-button type="info" plain icon="Sort" size="small" @click="handleToggleExpandAll">
                展开/折叠
              </el-button>
            </div>
          </template>
          <el-table ref="gzsxTableRef" v-loading="gzsxLoading" :data="gzsxList" row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :default-expand-all="isExpandAll"
            highlight-current-row @current-change="handleGzsxSelect" height="600">
            <el-table-column label="事项名称" align="left" prop="title" min-width="180" :show-overflow-tooltip="true" />
            <el-table-column label="事项编号" align="center" prop="code" width="100" :show-overflow-tooltip="true" />
            <el-table-column label="配置状态" align="center" width="80">
              <template #default="scope">
                <el-tag v-if="!scope.row.children || scope.row.children.length === 0"
                  :type="getConfigStatus(scope.row.id) ? 'success' : 'info'" size="small">
                  {{ getConfigStatus(scope.row.id) ? '已配置' : '未配置' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="60">
              <template #default="scope">
                <el-tooltip content="配置" placement="top" v-if="!scope.row.children || scope.row.children.length === 0">
                  <el-button link type="primary" icon="Setting" @click="handleConfigClick(scope.row)" />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 右侧：在线办理配置 -->
      <el-col :span="16">
        <el-card shadow="never">
          <template #header>
            <div class="flex justify-between items-center">
              <span>公证事项在线办理配置</span>
              <div v-if="currentGzsx">
                <!-- <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAdd"
                                     v-hasPermi="['basicdata:gzsxZxblConfig:add']"
                  :disabled="!currentGzsx"
                >
                  新增配置
                </el-button> -->
              </div>
            </div>
          </template>

          <!-- 未选择事项提示 -->
          <div v-if="!currentGzsx" class="text-center text-gray-400 py-20">
            <el-icon size="64" class="mb-4">
              <Document />
            </el-icon>
            <p class="text-lg">请在左侧选择一个公证事项进行配置</p>
          </div>

          <!-- 配置表单 -->
          <div v-else>
            <!-- 当前选中事项信息 -->
            <div class="bg-blue-50 p-4 rounded-lg mb-6">
              <h4 class="text-base font-medium text-blue-800 mb-2">当前配置事项</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div><span class="text-gray-600">事项名称：</span>{{ currentGzsx.title }}</div>
                <div><span class="text-gray-600">事项编号：</span>{{ currentGzsx.code }}</div>
              </div>
            </div>

            <!-- 配置表单 -->
            <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="140px">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="是否在线办理" prop="isOnlineHandle">
                    <el-radio-group v-model="configForm.isOnlineHandle">
                      <el-radio :value="1">是</el-radio>
                      <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否启用办理" prop="isEnableHandle">
                    <el-radio-group v-model="configForm.isEnableHandle">
                      <el-radio :value="1">是</el-radio>
                      <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 申办材料配置 -->
              <el-form-item label="申办材料配置">
                <MaterialSelector v-model="configForm.materialIds" @change="handleMaterialChange" />
              </el-form-item>

              <el-form-item label="备注" prop="remark">
                <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注信息" :rows="3" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="handleSave" :loading="saveLoading"
                  v-hasPermi="['basicdata:gzsxZxblConfig:edit']">
                  保存配置
                </el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button v-if="configForm.id" type="danger" plain @click="handleDelete"
                  v-hasPermi="['basicdata:gzsxZxblConfig:remove']">
                  删除配置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ZxblConfig" lang="ts">
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { ElForm, ElTable, ElMessageBox } from 'element-plus';
  import { Document } from '@element-plus/icons-vue';
  import { listTreeByZxbl as listGzsxTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO } from '@/api/gongzheng/basicdata/gzsx/types';
  import {
    listZxblConfig,
    getZxblConfigByGzsxId,
    addZxblConfig,
    updateZxblConfig,
    delZxblConfig
  } from '@/api/gongzheng/basicdata/zxbl/zxblpz';
  import {
    ZxblConfigVO,
    ZxblConfigForm,
    ZxblConfigQuery,
    GzsxOption,
    MaterialOption
  } from '@/api/gongzheng/basicdata/zxbl/zxblpz/types';
  import MaterialSelector from './components/MaterialSelector.vue';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  // 响应式数据
  const showSearch = ref(true);
  const gzsxLoading = ref(false);
  const saveLoading = ref(false);
  const isExpandAll = ref(false);

  // 引用
  const queryFormRef = ref<InstanceType<typeof ElForm>>();
  const configFormRef = ref<InstanceType<typeof ElForm>>();
  const gzsxTableRef = ref<InstanceType<typeof ElTable>>();

  // 公证事项相关
  const gzsxList = ref<GzsxVO[]>([]);
  const currentGzsx = ref<GzsxVO | null>(null);
  const configStatusMap = ref<Map<string | number, boolean>>(new Map());

  // 搜索参数
  const queryParams = ref<ZxblConfigQuery>({
    pageNum: 1,
    pageSize: 10,
    gzsxName: '',
    isOnlineHandle: undefined,
    isEnableHandle: undefined
  });

  // 配置表单
  const initConfigForm : ZxblConfigForm = {
    id: undefined,
    gzsxName: '',
    gzsxCode: '',
    gzsxId: undefined,
    isOnlineHandle: 0,
    isEnableHandle: 0,
    materialIds: '',
    remark: ''
  };

  const configForm = ref<ZxblConfigForm>({ ...initConfigForm });

  // 表单验证规则
  const configRules = reactive({
    isOnlineHandle: [
      { required: true, message: '请选择是否在线办理', trigger: 'change' }
    ],
    isEnableHandle: [
      { required: true, message: '请选择是否启用办理', trigger: 'change' }
    ]
  });

  // 加载公证事项树
  const loadGzsxTree = async () => {
    try {
      gzsxLoading.value = true;
      // 构建查询参数
      const searchParams = {
        title: queryParams.value.gzsxName || undefined,
        isOnlineHandle: queryParams.value.isOnlineHandle || undefined,
        isEnableHandle: queryParams.value.isEnableHandle || undefined
      };
      const res = await listGzsxTree(searchParams);
      const data = proxy?.handleTreeCode<GzsxVO>(res.data, 'code');
      if (data) {
        gzsxList.value = data;
        // 加载配置状态
        await loadConfigStatus();
      }
    } catch (error) {
      console.error('加载公证事项树失败:', error);
    } finally {
      gzsxLoading.value = false;
    }
  };

  // 加载所有配置状态
  const loadConfigStatus = async () => {
    try {
      const res = await listZxblConfig({ pageNum: 1, pageSize: 1000 });
      const configs = res.rows || [];
      configStatusMap.value.clear();
      configs.forEach(config => {
        configStatusMap.value.set(config.gzsxId, true);
      });
    } catch (error) {
      console.error('加载配置状态失败:', error);
    }
  };

  // 获取配置状态
  const getConfigStatus = (gzsxId : string | number) : boolean => {
    return configStatusMap.value.get(gzsxId) || false;
  };

  // 公证事项选择
  const handleGzsxSelect = (row : GzsxVO | null) => {
    if (!row) return;
    // 只有无子集的事项才能被选中配置
    if (row.children && row.children.length > 0) {
      proxy?.$modal.msgWarning('请选择具体的公证事项进行配置');
      return;
    }
    currentGzsx.value = row;
    loadConfigByGzsx(row);
  };

  // 操作按钮点击
  const handleConfigClick = (row : GzsxVO) => {
    // 设置当前行为选中状态
    gzsxTableRef.value?.setCurrentRow(row);
    handleGzsxSelect(row);
  };

  // 根据公证事项加载配置
  const loadConfigByGzsx = async (gzsx : GzsxVO) => {
    try {
      const res = await getZxblConfigByGzsxId(gzsx.id);
      if (res.data) {
        // 已有配置，加载现有数据
        Object.assign(configForm.value, res.data);
      } else {
        // 无配置，初始化新配置
        configForm.value = {
          ...initConfigForm,
          gzsxId: gzsx.id,
          gzsxName: gzsx.title,
          gzsxCode: gzsx.code
        };
      }
    } catch (error : any) {
      // 如果是404错误，说明没有配置，初始化新配置
      if (error?.response?.status === 404) {
        configForm.value = {
          ...initConfigForm,
          gzsxId: gzsx.id,
          gzsxName: gzsx.title,
          gzsxCode: gzsx.code
        };
      } else {
        console.error('加载配置失败:', error);
        proxy?.$modal.msgError('加载配置失败');
      }
    }
  };

  // 搜索
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    // 根据搜索条件过滤公证事项树
    loadGzsxTree();
  };

  // 重置搜索
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  };

  // 展开/折叠
  const handleToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleExpandAll(gzsxList.value, isExpandAll.value);
  };

  // 递归展开/折叠
  const toggleExpandAll = (data : GzsxVO[], status : boolean) => {
    data.forEach((item) => {
      gzsxTableRef.value?.toggleRowExpansion(item, status);
      if (item.children && item.children.length > 0) {
        toggleExpandAll(item.children, status);
      }
    });
  };

  // 新增配置
  const handleAdd = () => {
    if (!currentGzsx.value) return;

    configForm.value = {
      ...initConfigForm,
      gzsxId: currentGzsx.value.id,
      gzsxName: currentGzsx.value.title,
      gzsxCode: currentGzsx.value.code
    };

    nextTick(() => {
      configFormRef.value?.clearValidate();
    });
  };

  // 材料变化处理
  const handleMaterialChange = (materials : MaterialOption[]) => {
    // 这里可以做一些额外的处理，比如显示选中的材料信息
    console.log('选中的材料:', materials);
  };

  // 保存配置
  const handleSave = async () => {
    if (!configFormRef.value) return;

    try {
      const valid = await configFormRef.value.validate();
      if (!valid) return;

      saveLoading.value = true;

      if (configForm.value.id) {
        // 更新
        await updateZxblConfig(configForm.value);
        proxy?.$modal.msgSuccess('更新成功');
      } else {
        // 新增
        await addZxblConfig(configForm.value);
        proxy?.$modal.msgSuccess('保存成功');
        // 重新加载配置以获取ID
        if (currentGzsx.value) {
          await loadConfigByGzsx(currentGzsx.value);
        }
      }

      // 更新配置状态
      if (currentGzsx.value) {
        configStatusMap.value.set(currentGzsx.value.id, true);
      }
    } catch (error) {
      console.error('保存失败:', error);
      proxy?.$modal.msgError('保存失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 重置表单
  const handleReset = () => {
    if (currentGzsx.value) {
      loadConfigByGzsx(currentGzsx.value);
    }
  };

  // 删除配置
  const handleDelete = async () => {
    if (!configForm.value.id) return;

    try {
      await ElMessageBox.confirm('确认删除该配置吗？', '提示', {
        type: 'warning'
      });

      await delZxblConfig(configForm.value.id);
      proxy?.$modal.msgSuccess('删除成功');

      // 更新配置状态
      if (currentGzsx.value) {
        configStatusMap.value.set(currentGzsx.value.id, false);
      }

      // 重置表单
      if (currentGzsx.value) {
        configForm.value = {
          ...initConfigForm,
          gzsxId: currentGzsx.value.id,
          gzsxName: currentGzsx.value.title,
          gzsxCode: currentGzsx.value.code
        };
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        proxy?.$modal.msgError('删除失败');
      }
    }
  };

  // 初始化
  onMounted(() => {
    loadGzsxTree();
  });
</script>

<style scoped>
  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .gap-4 {
    gap: 1rem;
  }

  .text-center {
    text-align: center;
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  .text-gray-600 {
    color: #6b7280;
  }

  .text-blue-800 {
    color: #1e40af;
  }

  .py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .p-4 {
    padding: 1rem;
  }

  .bg-blue-50 {
    background-color: #eff6ff;
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .text-base {
    font-size: 1rem;
  }

  .font-medium {
    font-weight: 500;
  }

  .text-lg {
    font-size: 1.125rem;
  }

  .text-sm {
    font-size: 0.875rem;
  }
</style>
