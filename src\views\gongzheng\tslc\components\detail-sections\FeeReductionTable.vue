<template>
  <el-table :data="items" border stripe style="width: 100%">
    <el-table-column label="费用类型" prop="fylx">
      <template #default="scope">
        {{ dictMapFormat(gz_sf_lb, scope.row.fylx) }}
      </template>
    </el-table-column>
    <el-table-column label="公证事项" prop="gzjzGzsx" />
    <el-table-column label="公证书编号" prop="gzsbh" />
    <el-table-column label="应收" prop="fyys" width="100" align="right" />
    <el-table-column label="已收" prop="fyss" width="100" align="right" />
    <el-table-column label="已退" prop="fytf" width="100" align="right" />
    <el-table-column label="减免" prop="fyjm" width="100" align="right" />
    <el-table-column label="收费状态" prop="sfzt" width="100">
      <template #default="scope">
        {{ dictMapFormat(gz_sfzt, scope.row.sfzt) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
  interface Props {
    items : any[];
  }
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_sf_lb, gz_sfzt, gz_gzlb, gz_tslc_tffs } = toRefs<any>(proxy?.useDict('gz_tslc_tffs', 'gz_sf_lb', 'gz_sfzt', 'gz_gzlb'));

  import { dictMapFormat } from '@/utils/ruoyi';
  defineProps<Props>();
</script>
