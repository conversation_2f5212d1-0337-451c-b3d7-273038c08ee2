export interface GzjzDsrVO {
  /**
   * ID
   */
  id ?: string | number;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 当事人类型
   */
  dsrLx ?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js ?: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk ?: string;

  /**
   * 备注
   */
  remark ?: string;
   /**
   * 是否短信通知
   */
  sfdxtz ?: string;

  // ============ 以下是显示用的扩展属性 ===============
  /**
   * 姓名
   */
  name ?: string;
  /**
   * 性别
   */
  gender ?: string | number;
  /**
   * 性别 (接口字段)
   */
  sex ?: string;
  /**
   * 证件类型
   */
  idType ?: string | number;
  /**
   * 证件类型 (接口字段)
   */
  certificateType ?: string;
  /**
   * 证件号码
   */
  idNumber ?: string;
  /**
   * 证件号码 (接口字段)
   */
  certificateNo ?: string;
  /**
   * 出生日期
   */
  birthDate ?: string;
  /**
   * 住址
   */
  address ?: string;
  /**
   * 联系电话
   */
  phone ?: string;
  /**
   * 联系电话 (接口字段)
   */
  contactTel ?: string;
  /**
   * 类型
   */
  type ?: string | number;

  // ============ 兼容原有字段 ===============
  /**
   * 姓名 (兼容字段)
   */
  xm ?: string;
  /**
   * 性别 (兼容字段)
   */
  xb ?: string;
  /**
   * 证件号码 (兼容字段)
   */
  zjhm ?: string;
  /**
   * 出生日期 (兼容字段)
   */
  csrq ?: string;
  /**
   * 住址 (兼容字段)
   */
  zz ?: string;

  // ============ 新增接口返回的对象字段 ===============
  /**
   * 个人信息对象 (当dsrLx为'1'时有数据)
   */
  zrrBo ?: {
    id ?: string | number;
    slbh ?: string;
    xm ?: string;
    xb ?: string;
    lxdh ?: string;
    zz ?: string;
    zjlx ?: string;
    zjhm ?: string;
    dsrlb ?: string;
    zp ?: string;
    gj ?: string;
    mz ?: string;
    csrq ?: string;
    remark ?: string;
    khh ?: string;
    cym ?: string;
    ywm ?: string;
    dzyj ?: string;
    hyzk ?: number;
    gzdw ?: string;
    wxh ?: string;
    khhmc ?: string;
    khhzh ?: string;
    pjdj ?: number;
    lxzz ?: string;
    zjzt ?: boolean;
    createDept ?: number;
    createBy ?: number;
    createTime ?: string;
    updateBy ?: number;
    updateTime ?: string;
    params ?: any;
  };

  /**
   * 单位信息对象 (当dsrLx为'2'时有数据)
   */
  frhzzBo ?: {
    dwmc ?: string;
    dwszd ?: string;
    zjlx ?: string;
    zjhm ?: string;
    lxdh ?: string;
    fddbr ?: string;
    fddbrxb ?: string;
    fddbrlxdh ?: string;
    fddbrzw ?: string;
    fddbrzjlx ?: string;
    fddbrzjhm ?: string;
    dsrlb ?: string;
    fddbrzp ?: string;
    fddbrzz ?: string;
    remark ?: string;
    hyhm ?: string;
    ywmc ?: string;
    fzrzjlx ?: string;
    fzrzjhm ?: string;
    fzrxm ?: string;
    fzrcsrq ?: string;
    fzrwxh ?: string;
    fzrdzyj ?: string;
    khh ?: string;
    khzh ?: string;
    zjzt ?: boolean;
  };
}

export interface GzjzDsrForm extends BaseEntity {
  /**
   * ID
   */
  id ?: string | number;

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 当事人类型
   */
  dsrLx ?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js ?: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk ?: string;

  /**
   * 备注
   */
  remark ?: string;
  /**
   * 是否短信通知
   */
  sfdxtz ?: string;

  zrrBo ?: Object;
  frhzzBo ?: Object;
}

export interface GzjzDsrQuery extends PageQuery {

  /**
   * 当事人ID
   */
  dsrId ?: string | number;

  /**
   * 当事人类型
   */
  dsrLx ?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 角色（申请人、当事人、关系人、代理人、证人、被继承人）
   */
  js ?: string;

  /**
   * 是否读卡（0否，1是）
   */
  sfdk ?: string;

  /**
   * 是否短信通知
   */
  sfdxtz ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}

export interface GzjzDsrZp extends BaseEntity {
  /**
   * 卷宗id
   */
  gzjzId: string | number;
  /**
   * 图片base64
   */
  zp: string;
  /**
   * 当事人id
   */
  dsrId: string | number;
}

export interface GzjzDsrZpQuery extends PageQuery {
  /**
   * 卷宗id
   */
  gzjzId: string | number;
  /**
   * 当事人id
   */
  dsrId: string | number;
}


