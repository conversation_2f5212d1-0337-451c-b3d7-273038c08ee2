// BaseEntity 和 PageQuery 已在全局类型中定义

export interface GzjzGzsxVO {
  /**
   * ID
   */
  id : string | number;

  /**
   * 公证事项ID
   */
  gzsxId : string | number;

  /**
   * 公证事项名称
   */
  gzsxMc : string;

  /**
   * 关系人ID
   */
  gxrId ?: string | number;

  /**
   * 关系人
   */
  gxrMc ?: string;

  /**
   * 公证书ID
   */
  gzsId ?: string | number;

  /**
   * 公证书编号
   */
  gzsBh ?: string;

  /**
   * 公证书名称
   */
  gzsMc ?: string;

  /**
   * 合成公证书ID
   */
  hcGzsId ?: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc ?: string;

  /**
   * 公证书份数
   */
  gzsFs ?: number;

  /**
   * 是否备案（0否，1是）
   */
  sfBa ?: string;

  /**
   * 备案信息ID
   */
  baxxId ?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId : string | number;

  /**
   * 备注
   */
  remark ?: string;

  /**
   * 公证件数
   */
  gzjs ?: number;

  /**
   * 是否取双号
   */
  sfsh ?: string;

  /**
   * 公益类服务（字典）
   */
  gylfw ?: string;

  /**
   * 新型公证业务（字典）
   */
  xxgzyw ?: string;

  /**
   * 涉疫情灾情公证（字典）
   */
  syqzqgz ?: string;

  /**
   * 是否上门办证服务
   */
  sfsmbz ?: string;

  /**
   * 是否最多跑一次服务
   */
  sfzdpyc ?: string;

  /**
   * 是否一带一路建设服务
   */
  sfydyljs ?: string;

  /**
   * 是否在线受理服务
   */
  sfzxsl ?: string;

  /**
   * 是否无接触办证服务
   */
  sfwjcbz ?: string;

  /**
   * 公证费
   */
  gzf ?: number;

  /**
   * 英文翻译费
   */
  ywfyf ?: number;

  /**
   * 小语种翻译费
   */
  xyzfyf ?: number;

  /**
   * 其他费用
   */
  qtfy ?: number;

  // ======== 显示用字段 ========
  /**
   * 排序
   */
  sequence ?: number;

  /**
   * 公证事项
   */
  matter ?: string;

  /**
   * 关系人
   */
  relatedPerson ?: string;

  /**
   * 公证书编号
   */
  notaryNumber ?: string;

  /**
   * 份数
   */
  copies ?: number;

  /**
   * 公证费
   */
  notaryFee ?: number;

  /**
   * 副本费
   */
  copyFee ?: number;

  /**
   * 实收副本费
   */
  actualCopyFee ?: number;

  /**
   * 小语种翻译费
   */
  translationFee ?: number;

  /**
   * 其他费用
   */
  otherFee ?: number;

  /**
   * 小计
   */
  subtotal ?: number;

  /**
   * 状态
   */
  status ?: string;
}

export interface GzjzGzsxForm extends BaseEntity {
  /**
   * ID
   */
  id ?: string | number;

  /**
   * 公证事项ID
   */
  gzsxId : string | number;

  gzsxIds ?: Array<string | number>;

  /**
   * 公证事项名称
   */
  gzsxMc ?: string;

  /**
   * 关系人ID
   */
  gxrId ?: string | number;

  /**
   * 关系人
   */
  gxrMc ?: string;

  /**
   * 公证书ID
   */
  gzsId ?: string | number;

  /**
   * 公证书编号
   */
  gzsBh ?: string;

  /**
   * 公证书类别ID
   */
  gzlbValue ?: string | number;

  /**
   * 公证书名称
   */
  gzsMc ?: string;

  /**
   * 合成公证书ID
   */
  hcGzsId ?: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc ?: string;

  /**
   * 是否合成公证书
   */
  sfhcgzs ?: string;

  /**
   * 公证书份数
   */
  gzsFs ?: number;

  /**
   * 是否备案（0否，1是）
   */
  sfBa ?: string;

  /**
   * 备案信息ID
   */
  baxxId ?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId : string | number;

  /**
   * 备注
   */
  remark ?: string;

  /**
   * 公证件数
   */
  gzjs ?: number;

  /**
   * 是否取双号
   */
  sfsh ?: string;

  /**
   * 公益类服务（字典）
   */
  gylfw ?: string;

  /**
   * 新型公证业务（字典）
   */
  xxgzyw ?: string;

  /**
   * 涉疫情灾情公证（字典）
   */
  syqzqgz ?: string;

  /**
   * 是否上门办证服务
   */
  sfsmbz ?: string;

  /**
   * 是否最多跑一次服务
   */
  sfzdpyc ?: string;

  /**
   * 是否一带一路建设服务
   */
  sfydyljs ?: string;

  /**
   * 是否在线受理服务
   */
  sfzxsl ?: string;

  /**
   * 是否无接触办证服务
   */
  sfwjcbz ?: string;

  // ======== 费用相关字段 ========
  /**
   * 公证费
   */
  notaryFee ?: number;

  /**
   * 副本费
   */
  copyFee ?: number;

  /**
   * 实收副本费
   */
  actualCopyFee ?: number;

  /**
   * 小语种翻译费
   */
  translationFee ?: number;

  /**
   * 其他费用
   */
  otherFee ?: number;

  /**
   * 小计
   */
  subtotal ?: number;

  /**
   * 状态
   */
  status ?: string;

  /**
   * 关系人动态表单json string信息
   */
  gxrJson ?: string;

  /**
   * 关系人动态表单json object信息
   */
  gxrJsonObject ?: Array<GxrJsonObject>;

  /**
   * 死亡地点
   */
  swdd ?: string;

  /**
   * 死亡日期
   */
  swrq ?: string;

  /**
   * 代理人
   */
  dlr ?: string;

  /**
   * 继承人
   */
  jcr ?: string;

  /**
   * 被继承人
   */
  bjcr ?: string;

  /**
   * 放弃继承人
   */
  fqjcr ?: string;
}

export interface GxrJsonObject {
  label ?: string;
  valKey ?: string;
  value ?: string | number;
  type ?: string;
}

export interface GzjzGzsxQuery extends PageQuery {
  /**
   * 公证事项ID
   */
  gzsxId ?: string | number;

  /**
   * 公证事项名称
   */
  gzsxMc ?: string;

  /**
   * 关系人ID
   */
  gxrId ?: string | number;

  /**
   * 关系人
   */
  gxrMc ?: string;

  /**
   * 公证书ID
   */
  gzsId ?: string | number;

  /**
   * 公证书编号
   */
  gzsBh ?: string;

  /**
   * 公证书名称
   */
  gzsMc ?: string;

  /**
   * 公证卷宗ID
   */
  gzjzId ?: string | number;

  /**
   * 是否合成公证书（0否，1是）
   */
  sfHc ?: string;

  /**
   * 是否备案（0否，1是）
   */
  sfBa ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}

// 兼容旧版本类型定义
export interface NotaryMatterData extends GzjzGzsxVO {
  // 保持向后兼容
}


export interface GzjzGzsxChangeQuery extends PageQuery {
  jzbh : string;
  gzsxMc : string;
  gzybm : number;
  gzsNf : number;
  gzsZh : number;
  gzsLs : number;
  lczt : string;
}
export interface GzjzGzsxChangeVO {
  id : string | number;
  jzbh : string;
  gzsxMc : string;
  gzsBh : string;
  lb : string;
  gzsMc : string;
  gzyxm : string;
  lczt : string;
  lcztId:string;
  czsj : string;
  slrq : string;
  gzjzId : string | number;
  gzsbmId : string | number;
  nf : string;
  ls : number;
  zh : string;
  zhCode : string;
}
