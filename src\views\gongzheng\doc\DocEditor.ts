import { filemakerctrl, POBrowser } from 'js-pageoffice';
import { getToken } from '@/utils/auth';
import { EnumDocType, EnumDocActionType, EnumDocFileType } from "./enumType";
import { DocGenParams, GenerateCallBack, DocOpenParams, UserDocGenParams } from './type';
import qs from 'qs';

const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;

export interface DocParams {
  /**
   * 生成文档类型
   */
  type : EnumDocType | string;

  /**
   * 文档操作类型：不传默认是生成，编辑edit，查看view
   */
  action ?: EnumDocActionType;
  /**
   * 文件类别
   */
  fjlb : EnumDocFileType;
  /**
   * 业务id
   */
  bizId : string | number;
  /**
   * 额外参数
   */
  extraParams ?: {
    /**
     * 编辑与查看时需要添加 后台文件保存路径字段
     */
    bclj ?: string
  } & Record<string, any>;

  [key : string] : any;
}

/**
 * 调用 pageOffice 打开文档 旧
 * @param params
 * @returns
 */
export const openDocToPageOffice = (params : DocParams) => {
  const token = getToken();
  if (!token) {
    ElMessage.error("用户信息失效，请重新登录");
    return;
  }

  ElMessage.success("正在打开文档，请稍候...");
  POBrowser.setProxyBaseAPI(baseUrl)
  POBrowser.setHeader("Authorization", "Bearer " + token);
  POBrowser.setStorage("Admin-Token", token);
  POBrowser.openWindow("/backoffice/office", 'width=1300px;height=900px;', JSON.stringify(params));
}

/**
 * 生成文档 旧
 * @param params
 */
export const buildDoc = (params : DocParams) => {
  if (Object.hasOwn(params, 'action')) {
    delete params.action;
    console.warn('生成文档时，不需要传递action')
  }
  openDocToPageOffice(params);
}

export interface EditDocParams extends DocParams {
  extraParams : { bclj : string } & Record<string, any>;
}

/**
 * 编辑文档 旧
 * @param params
 */
export const editDoc = (params : EditDocParams) => {
  params = {
    ...params,
    action: EnumDocActionType.EDIT
  }

  openDocToPageOffice(params);
}

/**
 * 查看文档 旧
 * @param params
 */
export const showDoc = (params : EditDocParams) => {
  params = {
    ...params,
    action: EnumDocActionType.VIEW
  }

  openDocToPageOffice(params);
}


/**========================================== 以下为优化版本 ==========================================**/

const POBSetting = () => {
  return new Promise<void>((resolve, reject) => {
    try {
      POBrowser.setProxyBaseAPI(baseUrl);
      POBrowser.setHeader("Authorization", "Bearer " + getToken());
      POBrowser.setStorage("Admin-Token", getToken());
      POBrowser.setHeader("clientid", import.meta.env.VITE_APP_CLIENT_ID);

      resolve();
    } catch (err : any) {
      console.error('初始化失败', err);
      reject('初始化失败');
    }
  })
}
// 检查字符串是否为有效的JSON格式
const isJsonString = (str) => {
  try {
    // 尝试解析JSON
    const obj = JSON.parse(str);
    // 确保解析结果是对象或数组（排除null的情况）
    return typeof obj === 'object' && obj !== null;
  } catch (e) {
    // 解析失败，不是有效的JSON
    return false;
  }
}
const isArray = (value) => {
  return Object.prototype.toString.call(value) === '[object Array]';
}

const isObject = (value) => {
  return value !== null && (typeof value === 'object' || Array.isArray(value))
}

const genUrlParams = (obj : Record<string, any>) : string => {
  const urlParams = new URLSearchParams();
  const { extraParams } = obj;
  delete obj.extraParams;
  Object.entries(obj).forEach(([key, value]) => {
    urlParams.append(key, String(value));
  });

  if (extraParams) {
    Object.entries(extraParams).forEach(([key, value]) => {
      if (isObject(value)) {
        urlParams.append(`extraParams[${key}]`, JSON.stringify(value));
      } else {
        urlParams.append(`extraParams[${key}]`, String(value));
      }

    });
  }
  return urlParams.toString();
  // return qs.stringify(obj, {
  //   arrayFormat: 'brackets',
  //   encodeValuesOnly: true,
  //   allowDots: false
  // });
}

const genTaskID = (len : number = 8) => {
  const timeStemp = new Date().getTime().toString();
  // const subStemp = timeStemp.substring(timeStemp.length - 5);

  // 定义随机字符串的字符集
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomStr = '';

  // 生成指定长度的随机字符串
  for (let i = 0; i < len; i++) {
    const ri = Math.floor(Math.random() * chars.length);
    randomStr += chars.charAt(ri);
  }

  return `${timeStemp}_${randomStr}`;
}

const docBuildController = (params: DocGenParams, cb?: GenerateCallBack) => {
  return new Promise((resolve, reject) => {
    try {
      const genParamsStr = genUrlParams(params);
      const saveParamsStr = genUrlParams({ taskId: params.taskId });

      console.log('生成文档参数', params, genParamsStr)

      const { success, error, progress } = cb || {}

      // 设置用于保存文件的服务器端controller地址,该地址需从"/"开始，指向服务器端根目录
      filemakerctrl.SaveFilePage = `/wordgenerate/document/generateSave?${saveParamsStr}`;
      filemakerctrl.CallFileMaker({
        // url：指向服务器端FileMakerCtrl打开文件的controller地址，该地址需从"/"开始，指向服务器端根目录
        url: `/wordgenerate/document/generateBanked?${genParamsStr}`,
        success: (res : any) => {//res：获取服务器端fs.setCustomSaveResult设置的保存结果
          const resJson = JSON.parse(res)
          console.log(resJson)
          success && success(resJson);
          resolve(resJson);
        },
        progress: (pos : any) => {
          progress && progress(pos);
        },
        error: (err : any) => {
          error && error(err);
          reject(err);
        },
      });
    } catch (err : any) {
      console.error('生成异常', err)
      reject(err);
    }
  })
}

const pdfBuilder = (wordPath: string, cb?: GenerateCallBack) => {
  return new Promise((resolve, reject) => {
    try {
      const taskId = genTaskID();
      const genParamsStr = genUrlParams({ wordPath, taskId });
      const saveParamsStr = genUrlParams({ taskId });

      console.log('PDF 生成文档参数', genParamsStr, genParamsStr)

      const { success, error, progress } = cb || {}

      // 设置用于保存文件的服务器端controller地址,该地址需从"/"开始，指向服务器端根目录
      filemakerctrl.SaveFilePage = `/wordgenerate/document/generateSave?${saveParamsStr}`;
      filemakerctrl.CallFileMaker({
        // url：指向服务器端FileMakerCtrl打开文件的controller地址，该地址需从"/"开始，指向服务器端根目录
        url: `/wordgenerate/document/wordConvertPdf?${genParamsStr}`,
        success: (res : any) => {//res：获取服务器端fs.setCustomSaveResult设置的保存结果
          const resJson = JSON.parse(res)
          console.log(resJson)
          success && success(resJson);
          resolve(resJson);
        },
        progress: (pos : any) => {
          progress && progress(pos);
        },
        error: (err : any) => {
          error && error(err);
          reject(err);
        },
      });
    } catch (err : any) {
      console.error('PDF 生成异常', err)
      reject(err);
    }
  })
}

/**
 * 新的生成文档 会返回生成后的文件信息
 * @param params
 * @param cb
 * @returns
 */
export const docGenerator = (params: UserDocGenParams, cb?: GenerateCallBack) => {
  return new Promise((resolve, reject) => {
    POBSetting().then(() => {
      const taskId = genTaskID();

      const p : DocGenParams = {
        ...params,
        taskId,
        action: EnumDocActionType.GEN,
      }
      docBuildController(p, cb).then((res) => {
        resolve(res);
      }).catch((err) => {
        console.error('调用生成异常', err)
        reject(err);
      })
    }).catch((err : any) => {
      console.error('生成配置异常', err)
      reject(err);
    })
  })
}

/**
 * word转pdf
 * @param wordPath
 * @param cb
 * @returns
 */
export const wordToPdf = (wordPath: string, cb?: GenerateCallBack) => {
  return new Promise((resolve, reject) => {
    POBSetting().then(() => {
      pdfBuilder(wordPath, cb).then((res) => {
        resolve(res);
      }).catch((err) => {
        console.error('调用PDF 生成异常', err)
        reject(err);
      });
    }).catch((err : any) => {
      console.error('PDF 生成配置异常', err)
      reject(err);
    })
  })
}

export const openDoc = (params: DocOpenParams) => {
  const token = getToken();
  if (!token) {
    ElMessage.error("用户信息失效，请重新登录");
    return;
  }

  ElMessage.success("正在打开文档，请稍候...");
  POBrowser.setProxyBaseAPI(baseUrl)
  POBrowser.setHeader("Authorization", "Bearer " + token);
  POBrowser.setStorage("Admin-Token", token);
  POBrowser.openWindow("/backoffice/office", 'width=1300px;height=900px;', JSON.stringify(params));
}

/**
 * 已编辑模式打开pageOffice文档
 * @param path 生成文档时服务器返回的保存路径
 */
export const docOpenEdit = (path : string) => {
  openDoc({ path, action: EnumDocActionType.EDIT })
}

/**
 * 查看模式打开pageOffice文档
 * @param path 生成文档时服务器返回的保存路径
 */
export const docOpenShow = (path : string) => {
  openDoc({ path, action: EnumDocActionType.VIEW })
}
