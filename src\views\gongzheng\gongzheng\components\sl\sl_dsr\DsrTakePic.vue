<template>
  <gz-dialog v-model="visible" :title="title" fullscreen show-close destroy-on-close>
    <el-row :gutter="10" class="h-full">
      <el-col :span="10">
        <el-card class="h-full">
          <template #header>
            <strong>当事人列表</strong>
          </template>
          <el-table :data="dsrState.dsrListData" @current-change="dsrSelectChange" v-loading="dsrState.dsrLoading" highlight-current-row border stripe size="small" row-key="dsrId">
            <el-table-column type="index" label="#" width="55" align="center"/>
            <el-table-column label="姓名" prop="name" width="120" align="center" show-overflow-tooltip >
              <template #default="scope">
                {{ scope.row.name || scope.row.xm }}
              </template>
            </el-table-column>
            <el-table-column label="证件类型" prop="certificateType" width="120" align="center" show-overflow-tooltip >
              <template #default="scope">
                <dict-tag v-if="scope.row.dsrLx==='1'" :options="gz_gr_zjlx" :value="scope.row.certificateType" />
                <dict-tag v-if="scope.row.dsrLx==='2'" :options="gz_jg_zjlx" :value="scope.row.certificateType" />
              </template>
            </el-table-column>
            <el-table-column label="证件号码" prop="idNumber" align="center" show-overflow-tooltip >
              <template #default="scope">
                {{ scope.row.idNumber || scope.row.zjhm ||scope.row.certificateNo }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="14">
        <div class="flex flex-col h-full gap-10px">
          <el-card class="flex-1">
            <template #header>
              <div class="flex items-center flex-nowrap gap-10px justify-between">
                <div class="flex items-center flex-nowrap gap-10px">
                  <strong class="mr-20px">照片列表</strong>
                  <el-tag v-show="dsrState.activeDsr" size="small">{{ dsrState.activeDsr?.name || dsrState.activeDsr?.xm || '-' }}</el-tag>
                  <el-tag v-show="dsrState.activeDsr" size="small">{{ dsrState.activeDsr?.idNumber || dsrState.activeDsr?.zjhm || dsrState.activeDsr?.certificateNo || '-' }}</el-tag>
                </div>
                <div class="flex items-center gap-10px">
                  <el-checkbox @change="imgCheckAllChange" v-model="dsrImgState.checkAllFlag" :indeterminate="dsrImgState.imgCheckIsI">全选</el-checkbox>
                  <el-button @click="delCheckedDsrPic" type="danger" size="small">删除</el-button>
                  <el-button @click="startTakePic" type="primary" size="small">拍照</el-button>
                </div>
              </div>
            </template>
            <div class="flex flex-wrap gap-10px">
              <el-checkbox-group @change="imgCheckChange" v-model="dsrImgState.checkedImgIds" class="flex flex-wrap gap-2 p-6px">
                <div
                  v-for="(item, index) in dsrState.activeDsrImgList"
                  :key="item.id"
                  class="box-sd object-contain min-w-100px max-w-100px max-h-120px max-w-120px relative flex items-center justify-center bg-gray-300 rounded-lg overflow-hidden p-1px"
                >
                  <span class="absolute top-0px left-8px z-50 p-2px">
                    <el-checkbox :value="item.id" />
                  </span>
                  <span class="absolute top-8px right-8px z-50 p-0 rounded hover:bg-gray-50 bg-gray-200">
                    <el-button type="danger" link icon="Delete" @click="delCurDsrPic(item.id)" />
                  </span>
                  <el-image
                    class="object-contain min-w-100px max-w-100px max-h-120px max-w-120px"
                    :src="item.visitUrl"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="dsrState.activeDsrImgList.map(item => item.visitUrl)"
                    show-progress
                    :initial-index="index"
                    fit="cover"
                  />
                </div>
              </el-checkbox-group>
            </div>
          </el-card>
          <el-card>
            <el-table :data="picDocTypeList" v-loading="dsrState.dsrDocLoading" max-height="200" size="small" border stripe>
              <el-table-column type="index" label="#" width="55" align="center" />
              <el-table-column prop="typeName" label="文档类型" width="160" show-overflow-tooltip/>
              <!-- <el-table-column prop="dsrId" label="当事人" align="center" width="140">
                <template #default="{ row }">
                  {{ findDsrName(row.dsrId) }}
                </template>
              </el-table-column> -->
              <el-table-column label="文件名称">
                <template #default="{ row, column }">
                  <div class="flex flex-wrap gap-4px">
                    <el-tag v-for="item in row.docList" :key="item.id">
                      <el-button type="primary" link @click="handleOpen(item)">{{item.wbmc}}</el-button>
                      <el-button type="danger" link icon="Delete" @click="handleDelete(item)"></el-button>
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                  <el-button v-if="dsrState.activeDsr" @click="handleGenBtn(row)" type="primary" link size="small">生成</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <el-dialog v-model="genState.visible" :title="genState.title" @closed="genClosed" draggable :modal="false" show-close destroy-on-close width="400">
        <div class="h-100px flex flex-col items-center justify-center gap-16px">
          <div class="flex items-center justify-center gap-10px">
            <strong>文档模板：</strong>
            <el-select v-model="genState.mbId" default-first-option filterable style="width: 200px;">
              <el-option v-for="item in genState.typeData" :key="item.id" :label="item.wdMc" :value="item.id" />
            </el-select>
          </div>
          <div v-if="genState.warningShow" class="flex items-center justify-end">
            <el-text type="danger">未查询到模板，请添加模板后重试或上传本地文档文件</el-text>
          </div>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-10px">
            <el-button type="primary" @click="comfirmGen" :loading="genState.loading" :disabled="genState.loading">确认生成</el-button>
            <el-button @click="genClose">关闭</el-button>
          </div>
        </template>
      </el-dialog>

    <TakePic v-if="dsrState.takePicShow" v-model="dsrState.takePicShow" @comfirm="takePicConfirm" />

    <template #footer>
      <div class="flex justify-end items-center gap-10px">
        <!-- <el-button>设置签署位</el-button> -->
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { deleteDsrZp, listGzjzDsrByGzjz, queryDsrZpList, uploadDsrZp } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery, GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { addGzjzZmclxx, listGzjzZmclxx } from '@/api/gongzheng/gongzheng/gzjzZmclxx';
import { ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue';
import TakePic from '@/views/gongzheng/components/takePic/index.vue';
import { docGenerator, docOpenEdit } from '@/views/gongzheng/doc/DocEditor';
import { DocGenParams, UserDocGenParams } from '@/views/gongzheng/doc/type';
import { EnumDocFileType, EnumDocType } from '@/views/gongzheng/doc/enumType';
import { addZjclFile } from '@/api/gongzheng/gongzheng/gzjzZmclxxMx';
import { delEvidenceFile } from '@/api/gongzheng/gongzheng/zjcl';
import { picDocTableData } from '../preset_data';
import { addGzjzWjccxx, delGzjzWjccxx, listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { GzjzWjccxxForm } from '@/api/gongzheng/gongzheng/gzjzWjccxx/types';
import { formatDate } from '@/utils/ruoyi';
import { queryMbFiles } from '@/api/gongzheng/mb/mbWd';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gr_zjlx, gz_jg_zjlx, gz_dsr_jslx } = toRefs<any>(proxy?.useDict('gz_jg_zjlx', 'gz_gr_zjlx', 'gz_dsr_jslx'));

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const props = withDefaults(defineProps<Props>(), {
  title: '拍照',
});

const emit = defineEmits(['update:modelValue', 'close']);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

const presetDocnames = ref(['信息表', '认证/识别记录', '现场记录']);
const picDocTypeList = ref([]);

const dsrState = reactive({
  dsrListData: [],
  dsrLoading: false,
  activeDsr: null,
  activeDsrImgList: [],
  activeDsrDocList: [],
  dsrDocLoading: false,

  takePicShow: false,
})

const dsrImgState = reactive({
  checkAllFlag: false,
  imgCheckIsI: false,
  checkedImgIds: [] as string[],
});

const genState = reactive({
  visible: false,
  loading: false,
  title: '',
  sxRow: null,
  typeData: [],
  mbId: '',
  warningShow: false
})

// 查询当事人当前预设文档列表，需过滤不需要的文档
async function queryDsrDocList() {
  let dsrDoc = [];
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value?.id || currentRecordId.value,
      dsrId: dsrState.activeDsr?.dsrId || null,
      fjlb: '9',   // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
      pageNum: 1,
      pageSize: 200
    }
    const res = await listGzjzZmclxx(params);
    if (res.code === 200) {
      dsrDoc = res.data || [];
    }
  } catch (err: any) {
    console.error(`获取当事人文档列表失败: ${err.message}`);
  }

  return dsrDoc;
}

async function addDsrDocTypeList(zmmc: string) {
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      dsrId: dsrState.activeDsr?.dsrId || null,
      zmmc,
      fjlb: '9', // 附件类别(1过程文档，2证据材料，3其他材料， 4代书(文书)，9其他材料)
    }
    const res = await addGzjzZmclxx(params);
    if (res.code === 200) {
      console.log('添加完成');
    }
  } catch (err: any) {
    console.error(`添加当事人文档类型失败: ${err.message}`);
  }
}

// 加载当事人相关文档列表
async function loadDsrDocList() {
  try {
    dsrState.dsrDocLoading = true;
    const typeNames = [...presetDocnames.value].map(name => `${dsrState.activeDsr.name}-${name}`);
    const docList1 = await queryDsrDocList();
    const docList2 = docList1.filter((doc: any) => {
      if(typeNames.includes(doc.zmmc)) {
        typeNames.splice(typeNames.indexOf(doc.zmmc), 1);
        return true;
      }
      return false;
    });

    if (typeNames.length > 0) {
      await addDsrDocTypeList(typeNames.join(','));
      const newDocList = await queryDsrDocList();
      dsrState.activeDsrDocList = newDocList.filter((doc: any) => presetDocnames.value.map(name => `${dsrState.activeDsr.name}-${name}`).includes(doc.zmmc));
    } else {
      dsrState.activeDsrDocList = docList2;
    }
  } catch (err: any) {
    console.error(`加载当事人文档列表失败: ${err.message}`);
    ElMessage.error(`加载当事人相关文档列表失败`);
  } finally {
    dsrState.dsrDocLoading = false;
  }
}

// 加载当事人列表
async function loadDsrList() {
  try {
    dsrState.dsrLoading = true;
    const query : GzjzDsrQuery = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value!,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzjzDsrByGzjz(query);
    if (res.code === 200) {
      dsrState.dsrListData = res.rows || []
    }
  } catch (err: any) {
    console.error(`加载当事人列表失败: ${err.message}`);
    ElMessage.error(`加载当事人列表失败`);
  } finally {
    dsrState.dsrLoading = false;
  }
}

// 加载当事人照片列表
async function loadDsrImgList() {
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value!, // 公证卷宗ID
      dsrId: dsrState.activeDsr.dsrId,
      pageNum: 1,
      pageSize: 100,
    }
    const res = await queryDsrZpList(params);
    if(res.code === 200 && res.rows.length > 0) {
      dsrState.activeDsrImgList = res.rows || [];
    } else {
      dsrState.activeDsrImgList = [];
    }
  } catch (err: any) {
    console.log('获取当事人相关照片列表失败:', err);
    ElMessage.error('获取当事人相关照片失败');
  }
}

function findDsrName(dsrId: string) {
  const dsr = dsrState.dsrListData.find((item) => item.dsrId == dsrId);
  return dsr?.name || dsrId || '';
}

// 选择当事人时，加载相关照片和文档列表
function dsrSelectChange(row: any) {
  dsrState.activeDsr = row;
  dsrState.activeDsrImgList = [];
  dsrState.activeDsrDocList = [];

  dsrImgState.checkedImgIds = [];
  dsrImgState.checkAllFlag = false;
  if (row) {
    loadDsrImgList();
    loadDocList();
  }
}

function startTakePic() {
  dsrState.takePicShow = true;
}

// 拍照确认并上传
async function takePicConfirm(data: { base64: string, file: File }) {
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value!, // 公证卷宗ID
      dsrId: dsrState.activeDsr.dsrId,
      zp: data.base64, // base64
    }
    const res = await uploadDsrZp(params);
    if (res.code === 200) {
      ElMessage.success('上传当事人照片成功');
      loadDsrImgList();
    }
  } catch (err: any) {
    console.error(`上传当事人照片失败: ${err.message}`);
    ElMessage.error(`上传当事人照片失败`);
  } finally {
    dsrState.takePicShow = false;
  }

}

function imgCheckAllChange(val: CheckboxValueType) {
  dsrImgState.checkedImgIds = val ? dsrState.activeDsrImgList.map((item) => item.id) : [];
  dsrImgState.imgCheckIsI = false;
}

function imgCheckChange(vals: CheckboxValueType[]) {
  const count = vals.length;
  dsrImgState.checkAllFlag = vals.length === dsrState.activeDsrImgList.length;
  dsrImgState.imgCheckIsI = vals.length > 0 && vals.length < dsrState.activeDsrImgList.length;
}

function delCurDsrPic(id: string) {
  ElMessageBox.confirm('确定要删除该的照片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteDsrZp(id);
    if(res.code === 200) {
      ElMessage.success('删除照片成功');
      loadDsrImgList();
    }
  }).catch(() => {
    console.log('取消删除');
  });
}

// 删除已选照片
function delCheckedDsrPic() {
  if (dsrImgState.checkedImgIds.length === 0) {
    ElMessage.warning('请先选择要删除的照片');
    return;
  }
  ElMessageBox.confirm('确定要删除选中的照片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteDsrZp(dsrImgState.checkedImgIds.join(','));
    if(res.code === 200) {
      ElMessage.success('删除照片成功');
      loadDsrImgList();
      dsrImgState.checkedImgIds = [];
      dsrImgState.checkAllFlag = false;
    }
  }).catch(() => {
    console.log('取消删除');
  });
}

function openDoc(item: any) {
  try {
    docOpenEdit(item.bclj);
  } catch (err: any) {
    ElMessage.error(`打开文档失败`);
    console.error(`打开文档失败: ${err.message}`);
  }
}
// function delDoc(item: any) {
//   delCurDoc(item.id);
// }
function genDoc(row: any) {
  try {
    let type;
    if (row.zmmc === `${dsrState.activeDsr.name}-信息表`) {
      type = EnumDocType.DSR_INFO;
    } else if (row.zmmc === `${dsrState.activeDsr.name}-认证/识别记录`) {
      type = EnumDocType.DSR_INFO;
    } else {
      type = EnumDocType.DSR_INFO;
    }

    let params: UserDocGenParams = {
      bizId: (props.gzjzId || curGzjz.value.id || currentRecordId.value) as string,
      type,
      fjlb: EnumDocFileType.QT,
      extraParams: {
        dsrId: dsrState.activeDsr.dsrId,
        dsrLx: dsrState.activeDsr.dsrLx,
      }
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在生成文档，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
      fullscreen: true
    })

    console.log('产成文档', row)

    docGenerator(params).then((res: any) => {
      console.log('产成文档2', row)
      addDoc(row.id, [
        {
          xxmc: `${row.zmmc}_${new Date().getTime()}${res.data.fileSuffix}`,
          bclj: res.data.fileName!
        }
      ]);
    }).catch((err: any) => {
      ElMessage.error(`生成文档失败`);
      console.error(`生成文档失败: ${err.message}`);
    }).finally(() => {
      loading.close();
    })
  } catch (err: any) {
    ElMessage.error(`处理生成文档失败`);
    console.error(`处理生成文档失败: ${err.message}`);
  }
}

async function addDoc(docTypeId: string, zmcl: any[]) {
  try {
    const params = {
      gzjzId: props.gzjzId || curGzjz.value.id || currentRecordId.value,
      parentId: docTypeId,
      files: zmcl
    }
    const res = await addZjclFile(params);
    if (res.code === 200) {
      ElMessage.success(`已成功添加 ${zmcl.length} 个文档材料`);
      // loadDsrDocList();
    }
  } catch (err: any) {
    ElMessage.error(`添加文档材料失败`)
    console.error(`添加文档材料失败: ${err.message}`);
  } finally {

  }
}

function delCurDoc(id: string | number | Array<string | number>) {
  ElMessageBox.confirm(Array.isArray(id) ? '确定删除选中的文档吗？' : '确定删除当前文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await delEvidenceFile(id);
      if (res.code === 200) {
        ElMessage.success(`已成功删除 ${Array.isArray(id) ? id.length : 1} 个文档`);
        loadDsrDocList();
      }
    } catch (err: any) {
      ElMessage.error(`删除文档失败`);
      console.log(`删除文档失败`, err)
    }
  }).catch(() => {})
}

/**==========================  ==========================**/
// 文件打开
const handleOpen = (data: any) => {
  // 获取文件名后缀
  // const ext = data.xxmc.substring(data.xxmc.lastIndexOf('.') + 1).toLowerCase();
  const obj = JSON.parse(data.wblj || '{}');
  if (obj.path) {
    docOpenEdit(obj.path)
  }
}

// 文件删除
const handleDelete = (data: any) => {
  ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDoc(data.id)
  }).catch(() => {
    // ElMessage.info('已取消删除')
  })
}

// 删除关联文档
const delDoc = async (id: number | string) => {
  delGzjzWjccxx(id).then((res) => {
    if(res.code === 200) {
      ElMessage.success('删除成功');
      loadDocList()
    }
  }).catch((err: any) => {
    ElMessage.success('删除失败');
    console.log('删除失败', err)
  })
}

// 文档文件列表查询
const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      dsrId: dsrState.activeDsr.dsrId,
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.log('文档文件列表查询失败', err)
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  const tbList = picDocTableData.map(item => {
      const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    source = noMatches;
    item.docList = matches;
    return item;
  })

  picDocTypeList.value = tbList
}

interface FilterRes<T> {
  matches: T[];
  noMatches: T[];
}

function nodeFilter<T>(data: Array<T>, match: (node: T) => boolean = () => false): FilterRes<T> {
  let y: Array<T> = [], n: Array<T> = [];
  data.forEach((item, index) => {
    if (match(item)) {
      y.push(item);
    } else {
      n.push(item);
    }
  });
  return {
    matches: y,
    noMatches: n
  }
}

const handleGenBtn = (row: any) => {
  if(row.typeCode === 6 && dsrImgState.checkedImgIds.length !== 1) {
    ElMessage.warning('请选择一张照片,进行生成信息表');
    return;
  }

  if(row.typeCode === 94 && dsrImgState.checkedImgIds.length !== 1) {
    ElMessage.warning('请选择一张照片,进行识别记录生成');
    return;
  }
  if(row.typeCode === 23 && dsrImgState.checkedImgIds.length !== 1) {
    ElMessage.warning('请选择一张照片,进行现场记录生成');
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在获取文档模版，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.2)',
    fullscreen: true
  })
  queryMbFiles({ wdLb: row.typeCode }).then((res) => {
    if (res.code === 200) {
      if(!res.data || res.data.length === 0) {
        ElMessage.error('模版为空，请上传模版后重试或选择本地上传文档')
      } else {
        genState.typeData = res.data;
        genState.mbId = res.data[0].id;
        genState.sxRow = row;
        genState.visible = true;
        genState.title = `${row.typeName} 文档生成`;
      }
    }
  }).catch((err: any) => {
    console.log('查询模版文件异常', err);
  }).finally(() => {
    loading.close();
  })
}

// 确认生成文档
const comfirmGen = () => {
  if (!genState.mbId) {
    ElMessage.warning('未选择生成指定模板')
    return;
  }

  // const subIns = SubRef.value;
  // const dsrIds = (subIns?.getSelectedDsr() || []).map((dsr: GzjzDsrVO) => dsr.dsrId)

  let params: UserDocGenParams = {
    bizId: (props.gzjzId || curGzjz.value.id || currentRecordId.value) as string,
    // type: genState.sxRow.typeCodeStr,
    // fjlb: EnumDocFileType.QT,
    mbWdId: genState.mbId,
    extraParams: {
      dsrId: dsrState.activeDsr.dsrId,
      dsrLx: dsrState.activeDsr.dsrLx,
      // gzxsId: gzsxState.gzsxId,
      // dsrIds: dsrIds.join(',')
    }
  }

  // 加一个现场照片的参数
  if (genState.sxRow.typeCode === 6 || genState.sxRow.typeCode === 94|| genState.sxRow.typeCode === 23) {
    const imgInfo = dsrState.activeDsrImgList.find(item => item.id === dsrImgState.checkedImgIds[0])
    if (!imgInfo) {
      ElMessage.warning('未找到照片信息')
      return;
    }
    params.extraParams['xczpId'] = imgInfo.ossId;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成文档，请稍等...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
  genState.loading = true;

  docGenerator(params, {
    success: (res) => {
      const { ossId, fileName: path, fileSuffix } = res.data
      // const gzsxName = gzsxState.listData.find((item) => item.gzsxId === gzsxState.gzsxId)?.gzsxMc || ''
      const fileName = `${genState.sxRow.typeName}_${formatDate(new Date(), 'YYYYMMDDhhmmss')}${fileSuffix}`
      const docInfo: GzjzWjccxxForm = {
        wbmc: fileName,
        wblj: JSON.stringify({
          ossId,
          path,
          fileSuffix,
          fileName,
          typeCode: genState.sxRow.typeCode,
          typeName: genState.sxRow.typeName,
          mbWdId: genState.mbId
        }),
        lx: genState.sxRow.typeCode,
        // gzjzGzsxId: gzsxState.gzsxId,
        // gzsx: gzsxName,
      }
      relateDoc(docInfo);
      genState.visible = false;
    }
  }).catch((err: any) => {
    console.error('文档生成失败', err)
    ElMessage.error('生成失败,'+err)
  }).finally(() => {
    genState.loading = false;
    loading.close()
  })
}

// 添加生成后的文档（关联生成文档）
const relateDoc = async (docInfo: GzjzWjccxxForm) => {
  try {
    const params = {
      ...docInfo,
      gzjzId: props.gzjzId ||  curGzjz.value.id || currentRecordId.value,
      dsrId: dsrState.activeDsr.dsrId,
      // wbmc: '',   // 文本名称
      // wblj: '',   // 文本路径
      // lx: '',     // 类型
      // ywmc: '',   // 译文名称
      // ywlj: '',   // 译文路径
      // gzsbh: '',  // 公证书编号
      // gzsx: '',   // 公证事项（事务）
      // gzjzGzsxId: '', // 公证卷宗-公证事项ID
      // remark: '', // 备注
      // sfFy: '',   // 是否翻译（0否，1是）
    }
    const res = await addGzjzWjccxx(params);
    if(res.code === 200) {
      ElMessage.success('生成文件添加成功')
      loadDocList()
    }
  } catch (err: any) {
    console.log('关联生成文档错误', err)
    ElMessage.error('添加文档异常')
  }
}

const genClose = () => {
  genState.loading = false;
  genState.visible = false;
}

const genClosed = () => {
  genState.sxRow = null
  genState.mbId = ''
  genState.warningShow = false;
}

/**==========================  ==========================**/

watch(() => props.modelValue, (val) => {
  if (val) {
    // 打开时加载当事人列表
    loadDsrList();
  } else {
    // 关闭时重置状态
    dsrState.dsrListData = [];
    dsrState.activeDsr = null;
    dsrState.activeDsrImgList = [];
    dsrState.activeDsrDocList = [];

    dsrImgState.checkAllFlag = false;
    dsrImgState.imgCheckIsI = false;
    dsrImgState.checkedImgIds = [];

    picDocTypeList.value = [];
  }
})

onMounted(() => {
  loadDsrList();
})
</script>
