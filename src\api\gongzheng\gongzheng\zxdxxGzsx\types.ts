export interface ZxdxxGzsxVO {
  /**
   * ID
   */
  zxsxId: string | number;

  /**
   * 咨询ID
   */
  zxId: string | number;

  /**
   * 公证事项ID
   */
  gzsxId: string | number;

  /**
   * 公证事项名称
   */
  gzsxMc: string;

  /**
   * 备注
   */
  remark: string;

}

export interface ZxdxxGzsxForm extends BaseEntity {
  /**
   * ID
   */
  zxsxId?: string | number;

  /**
   * 咨询ID
   */
  zxId?: string | number;

  /**
   * 公证事项ID
   */
  gzsxId?: string | number;

  /**
   * 公证事项名称
   */
  gzsxMc?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface ZxdxxGzsxQuery extends PageQuery {

  /**
   * 咨询ID
   */
  zxId?: string | number;

  /**
   * 公证事项ID
   */
  gzsxId?: string | number;

  /**
   * 公证事项名称
   */
  gzsxMc?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



