import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxHmdJdwyVO, DsrxxHmdJdwyForm, DsrxxHmdJdwyQuery } from '@/api/gongzheng/dsr/dsrxxHmdJdwy/types';

/**
 * 查询当事人-借贷违约列表
 * @param query
 * @returns {*}
 */

export const listDsrxxHmdJdwy = (query?: DsrxxHmdJdwyQuery): AxiosPromise<DsrxxHmdJdwyVO[]> => {
  return request({
    url: '/dsr/dsrxxHmdJdwy/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人-借贷违约详细
 * @param id
 */
export const getDsrxxHmdJdwy = (id: string | number): AxiosPromise<DsrxxHmdJdwyVO> => {
  return request({
    url: '/dsr/dsrxxHmdJdwy/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人-借贷违约
 * @param data
 */
export const addDsrxxHmdJdwy = (data: DsrxxHmdJdwyForm) => {
  return request({
    url: '/dsr/dsrxxHmdJdwy',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人-借贷违约
 * @param data
 */
export const updateDsrxxHmdJdwy = (data: DsrxxHmdJdwyForm) => {
  return request({
    url: '/dsr/dsrxxHmdJdwy',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人-借贷违约
 * @param id
 */
export const delDsrxxHmdJdwy = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxHmdJdwy/' + id,
    method: 'delete'
  });
};
