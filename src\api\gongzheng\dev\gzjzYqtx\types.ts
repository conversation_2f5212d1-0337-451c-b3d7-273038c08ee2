export interface GzjzYqtxVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq: string;

  /**
   * 提醒-制证要求
   */
  txZzyq: string;

  /**
   * 提醒-发证提醒
   */
  txFztx: string;

  /**
   * 提醒-收费提醒
   */
  txSftx: string;

}

export interface GzjzYqtxForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq?: string;

  /**
   * 提醒-制证要求
   */
  txZzyq?: string;

  /**
   * 提醒-发证提醒
   */
  txFztx?: string;

  /**
   * 提醒-收费提醒
   */
  txSftx?: string;

}

export interface GzjzYqtxQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 提醒-翻译要求
   */
  txFyyq?: string;

  /**
   * 提醒-制证要求
   */
  txZzyq?: string;

  /**
   * 提醒-发证提醒
   */
  txFztx?: string;

  /**
   * 提醒-收费提醒
   */
  txSftx?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



