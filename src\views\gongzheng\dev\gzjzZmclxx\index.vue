<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
              <el-input v-model="queryParams.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证明名称" prop="zmmc">
              <el-input v-model="queryParams.zmmc" placeholder="请输入证明名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="有效期" prop="yxq">
              <el-input v-model="queryParams.yxq" placeholder="请输入有效期" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证明材料来源" prop="zmclly">
              <el-input v-model="queryParams.zmclly" placeholder="请输入证明材料来源" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排序序号(从 1 开始自动生成)" prop="pxxh">
              <el-input v-model="queryParams.pxxh" placeholder="请输入排序序号(从 1 开始自动生成)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="盖章信息" prop="gzxx">
              <el-input v-model="queryParams.gzxx" placeholder="请输入盖章信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否为公证文书(0：否，1：是)" prop="sfwgzws">
              <el-input v-model="queryParams.sfwgzws" placeholder="请输入是否为公证文书(0：否，1：是)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['temp:gzjzZmclxx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['temp:gzjzZmclxx:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['temp:gzjzZmclxx:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['temp:gzjzZmclxx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzjzZmclxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="公证卷宗ID(关联公证卷宗)" align="center" prop="gzjzId" />
        <el-table-column label="证明名称" align="center" prop="zmmc" />
        <el-table-column label="有效期" align="center" prop="yxq" />
        <el-table-column label="证明材料来源" align="center" prop="zmclly" />
        <el-table-column label="排序序号(从 1 开始自动生成)" align="center" prop="pxxh" />
        <el-table-column label="盖章信息" align="center" prop="gzxx" />
        <el-table-column label="是否为公证文书(0：否，1：是)" align="center" prop="sfwgzws" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['temp:gzjzZmclxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['temp:gzjzZmclxx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证卷宗-公证证明材料信息-主信息v1.0对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzjzZmclxxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证卷宗ID(关联公证卷宗)" prop="gzjzId">
          <el-input v-model="form.gzjzId" placeholder="请输入公证卷宗ID(关联公证卷宗)" />
        </el-form-item>
        <el-form-item label="证明名称" prop="zmmc">
          <el-input v-model="form.zmmc" placeholder="请输入证明名称" />
        </el-form-item>
        <el-form-item label="有效期" prop="yxq">
          <el-input v-model="form.yxq" placeholder="请输入有效期" />
        </el-form-item>
        <el-form-item label="证明材料来源" prop="zmclly">
          <el-input v-model="form.zmclly" placeholder="请输入证明材料来源" />
        </el-form-item>
        <el-form-item label="排序序号(从 1 开始自动生成)" prop="pxxh">
          <el-input v-model="form.pxxh" placeholder="请输入排序序号(从 1 开始自动生成)" />
        </el-form-item>
        <el-form-item label="盖章信息" prop="gzxx">
          <el-input v-model="form.gzxx" placeholder="请输入盖章信息" />
        </el-form-item>
        <el-form-item label="是否为公证文书(0：否，1：是)" prop="sfwgzws">
          <el-input v-model="form.sfwgzws" placeholder="请输入是否为公证文书(0：否，1：是)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzjzZmclxx" lang="ts">
import { listGzjzZmclxx, getGzjzZmclxx, delGzjzZmclxx, addGzjzZmclxx, updateGzjzZmclxx } from '@/api/gongzheng/dev/gzjzZmclxx';
import { GzjzZmclxxVO, GzjzZmclxxQuery, GzjzZmclxxForm } from '@/api/gongzheng/dev/gzjzZmclxx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gzjzZmclxxList = ref<GzjzZmclxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gzjzZmclxxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GzjzZmclxxForm = {
  id: undefined,
  gzjzId: undefined,
  zmmc: undefined,
  yxq: undefined,
  zmclly: undefined,
  pxxh: undefined,
  gzxx: undefined,
  sfwgzws: undefined,
}
const data = reactive<PageData<GzjzZmclxxForm, GzjzZmclxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    gzjzId: undefined,
    zmmc: undefined,
    yxq: undefined,
    zmclly: undefined,
    pxxh: undefined,
    gzxx: undefined,
    sfwgzws: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公证卷宗-公证证明材料信息-主信息v1.0列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGzjzZmclxx(queryParams.value);
  gzjzZmclxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  gzjzZmclxxFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GzjzZmclxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公证卷宗-公证证明材料信息-主信息v1.0";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GzjzZmclxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGzjzZmclxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公证卷宗-公证证明材料信息-主信息v1.0";
}

/** 提交按钮 */
const submitForm = () => {
  gzjzZmclxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGzjzZmclxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGzjzZmclxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GzjzZmclxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公证卷宗-公证证明材料信息-主信息v1.0编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGzjzZmclxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('temp/gzjzZmclxx/export', {
    ...queryParams.value
  }, `gzjzZmclxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
