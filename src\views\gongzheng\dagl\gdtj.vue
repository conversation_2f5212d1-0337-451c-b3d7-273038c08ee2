<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
      <el-form-item label="归档日期：" prop="archiveDate">
        <el-date-picker
          v-model="queryParams.archiveStartDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
        至
        <el-date-picker
          v-model="queryParams.archiveEndDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="受理日期：" prop="receiveDate">
        <el-date-picker
          v-model="queryParams.receiveStartDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
        至
        <el-date-picker
          v-model="queryParams.receiveEndDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="保管期限：" prop="storageLimit">
        <el-select v-model="queryParams.storageLimit" placeholder="请选择" clearable style="width: 180px">
          <el-option label="请选择" value="" />
          <el-option v-for="item in storageLimitOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公证类别：" prop="notaryType">
        <el-select v-model="queryParams.notaryType" placeholder="请选择" clearable style="width: 180px">
          <el-option label="请选择" value="" />
          <el-option v-for="item in notaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="公证事项：" prop="notaryItem">
        <el-input v-model="queryParams.notaryItem" placeholder="请输入公证事项" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">统计</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button type="info" icon="Printer" @click="handlePrint">备注</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" icon="Printer" @click="handleFlashPrint">Flash打印</el-button>
      <el-button type="primary" icon="Download" @click="handleExport">导出Excel</el-button>
    </div>

    <!-- 统计结果区域 -->
    <div class="statistics-container" v-loading="loading">
      <!-- 加载中提示 -->
      <div class="loading-text" v-if="loading">
        <div class="loading-spinner"></div>
        <span>报表加载中...</span>
      </div>
      
      <!-- 统计结果内容 -->
      <div class="statistics-content" v-else>
        <!-- 这里可以放置实际的统计图表或表格 -->
        <div class="empty-text" v-if="!hasData">
          请先进行查询统计
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Printer, Download } from '@element-plus/icons-vue'

// 加载状态
const loading = ref(false)
const hasData = ref(false)

// 保管期限选项
const storageLimitOptions = ref([
  { value: '1', label: '永久' },
  { value: '2', label: '30年' },
  { value: '3', label: '10年' }
])

// 公证类别选项
const notaryTypeOptions = ref([
  { value: '1', label: '国内民事' },
  { value: '2', label: '涉外民事' },
  { value: '3', label: '经济' }
])

// 查询参数
const queryParams = reactive({
  archiveStartDate: '',
  archiveEndDate: '',
  receiveStartDate: '',
  receiveEndDate: '',
  storageLimit: '',
  notaryType: '',
  notaryItem: ''
})

// 统计按钮操作
const handleQuery = () => {
  loading.value = true
  // 这里应该是实际的API调用
  setTimeout(() => {
    // 模拟API返回数据
    hasData.value = true
    loading.value = false
    ElMessage.success('统计完成')
  }, 1500)
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.archiveStartDate = ''
  queryParams.archiveEndDate = ''
  queryParams.receiveStartDate = ''
  queryParams.receiveEndDate = ''
  queryParams.storageLimit = ''
  queryParams.notaryType = ''
  queryParams.notaryItem = ''
  hasData.value = false
}

// Flash打印
const handleFlashPrint = () => {
  if (!hasData.value) {
    ElMessage.warning('请先进行统计')
    return
  }
  ElMessage.success('Flash打印')
  // 实际Flash打印逻辑
}

// 导出Excel
const handleExport = () => {
  if (!hasData.value) {
    ElMessage.warning('请先进行统计')
    return
  }
  ElMessage.success('导出Excel')
  // 实际导出Excel逻辑
}

// 备注
const handlePrint = () => {
  ElMessage.info('备注信息')
  // 实际备注逻辑
}

// 组件挂载时
onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.app-container {
  padding: 15px;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.statistics-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  position: relative;
  min-height: 400px;
}

.loading-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statistics-content {
  height: 100%;
}

.empty-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 16px;
}
</style> 