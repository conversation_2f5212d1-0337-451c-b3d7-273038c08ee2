<template>
  <GzDialog v-model="visible" :title="title" @closed="closed" show-close destroy-on-close fullscreen>
    <el-tabs v-model="activeName" @tab-change="tabChange" type="border-card">
      <el-tab-pane label="卷宗信息" name="jzxx">
        <div class="flex flex-col gap-14px w-full rounded-md">
          <Base v-loading="detailState.loading" :data="detailState.detail" />
          <DsrList v-loading="detailState.loading" :data="detailState.detail.dsrVoList"/>
          <GzsxList v-loading="detailState.loading" :data="detailState.detail.gzsxVoList" />
          <SfmxList v-loading="detailState.loading" :data="detailState.detail.sfxxVoList" />
          <SfkpList v-loading="detailState.loading" :data="detailState.detail.sfjfpVoList"/>
          <OtherDocList v-loading="detailState.loading" :data="docList" />

          <WsList v-loading="detailState.loading" />
          <SwpageList v-loading="detailState.loading" />
          <SubList v-loading="detailState.loading" />
          <BzList v-loading="detailState.loading" />

          <SdList v-loading="detailState.loading" />
          <PjList v-loading="detailState.loading" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="日志" name="log">
        <div v-if="activeName === 'log'" class="flex flex-col gap-14px w-full rounded-md">
          <LcLog />
          <CzLog />
          <DxLog />
        </div>
      </el-tab-pane>
    </el-tabs>

    <NewZjcl v-if="zjclVisible" v-model="zjclVisible" :gzjz-id="gzjzId" read-only/>

    <template #footer>
      <div class="flex justify-end items-center gap-10px">
        <el-button @click="showZjcl" >证据材料</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </GzDialog>
</template>

<script setup lang="ts">
import { gzjzDetail } from '@/api/gongzheng/gongzheng/gzjz';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { ref, reactive, computed, watch, onBeforeMount, onMounted} from 'vue';
import Base from './detail/Base.vue';
import DsrList from './detail/DsrList.vue';
import GzsxList from './detail/GzsxList.vue';
import SfmxList from './detail/SfmxList.vue';
import SfkpList from './detail/SfkpList.vue';
import SwpageList from './detail/SwpageList.vue';
import SubList from './detail/SubList.vue';
import BzList from './detail/BzList.vue';
import SdList from './detail/SdList.vue';
import PjList from './detail/PjList.vue';
import WsList from './detail/WsList.vue';
import OtherDocList from './detail/OtherDocList.vue';
import NewZjcl from '@/views/gongzheng/gongzheng/components/sl/new_zjcl/index.vue'
// import CzrzList from '@/views/gongzheng/gongzheng/components/czrz_list.vue'
import LcLog from './log/LcLog.vue';
import CzLog from './log/CzLog.vue';
import DxLog from './log/DxLog.vue';
import { wdndDocTableData, blDocTableData, wsDocTableData, picDocTableData, qfgDocTableData, type DocListItem } from '@/views/gongzheng/gongzheng/components/sl/preset_data'
import { listGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { nodeFilter } from '@/utils/ruoyi';

interface Detail extends GzjzJbxxVO {
  [k: string]: any;
}

interface Props {
  modelValue?: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '详情',
});

const emit = defineEmits(['update:modelValue']);

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
  // 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const detailState = reactive({
  loading: false,
  detail: {} as Detail,
})

const docList = ref<DocListItem[]>([
  ...wdndDocTableData,
  ...qfgDocTableData,
  ...blDocTableData,
  ...wsDocTableData,
  ...picDocTableData,
])

const activeName = ref('jzxx')

const zjclVisible = ref(false)

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
});

const otherDoc = computed(() => {
  return [
    ...(detailState.detail.ndwdVoList || []),
    ...(detailState.detail.blVoList || []),
    ...(detailState.detail.wsVoList || []),
  ]
})

const showZjcl = () => {
  zjclVisible.value = true;
}

const close = () => {
  emit('update:modelValue', false);
}

const closed = () => {

}

const reset = () => {

}

const loadDetail = async () => {
  if (!props.gzjzId && !currentRecordId.value && !curGzjz.value) {
    ElMessage.error('未获取到卷宗信息编号');
    console.log('loadDetail', props.gzjzId, currentRecordId.value, curGzjz.value)
    return;
  }
  try {
    detailState.loading = true;
    const res = await gzjzDetail(props.gzjzId || currentRecordId.value || curGzjz.value.id);
    if (res.code === 200) {
      // ElMessage.success('获取卷宗信息成功');
      detailState.detail = res.data;

      placeGzs();
    }
  } catch (err: any) {
    ElMessage.error('获取卷宗信息失败');
    console.log(err);
  } finally {
    detailState.loading = false;
  }
}

const placeGzs = () => {
  try {
    let gzsList = nodeFilter(detailState.detail.wjccxxVoList, (node: any) => {
      return (node.lx == 3);
    }).matches;

    detailState.detail.gzsxVoList = detailState.detail.gzsxVoList.map(gzsx => {
      const { matches, noMatches } = nodeFilter(gzsList, (node) => {
        return (node.gzjzGzsxId == gzsx.id);
      });

      if(matches.length > 0) {
        gzsx.gzsFile = matches[0];
      } else {
        gzsx.gzsFile = null;
      }
      gzsList = noMatches;

      return gzsx;
    })
  } catch (err: any) {
    console.error('分配公证书失败', err)
  }
}

const loadDocList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      gzjzId: curGzjz.value.id || currentRecordId.value
    }
    const res = await listGzjzWjccxx(params);
    if(res.code === 200) {
      placeDoc((res.rows || []))
    }
  } catch (err: any) {
    console.error('文档文件列表查询失败', err)
  }
}

// 分配文档文件
const placeDoc = (list: any[]) => {
  //docList
  let source: any[] = list
  docList.value.forEach(item => {
    const { matches, noMatches } = nodeFilter(source, (node) => {
      const obj = JSON.parse(node.wblj);
      if (!obj) {
        return false;
      }
      return (item.typeCode == node.lx) || (item.typeCode === obj.typeCode);
    });

    item.docList = matches;
    source = noMatches;
  })
}

const tabChange = (tabName: string) => {
  switch (tabName) {
    case 'jzxx':
      loadDetail();
    break;
  }
}

onBeforeMount(() => {
  // loadDetail();
})


onMounted(() => {
  loadDetail();
  // loadDocList();
})

</script>
