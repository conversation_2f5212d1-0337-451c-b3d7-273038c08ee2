<template>
  <div v-loading="linkDsrState.loading">
    <el-form :model="fqInfoFrom" :rules="rules" ref="fqFormRef" label-width="140" label-suffix=":">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="债务人" prop="zwrIds">
            <el-select v-model="fqInfoFrom.zwrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="债权人" prop="zqrIds">
            <el-select v-model="fqInfoFrom.zqrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="代理人" prop="dlrIds">
            <el-select v-model="fqInfoFrom.dlrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="担保人" prop="dbrIds">
            <el-select v-model="fqInfoFrom.dbrIds" multiple>
              <el-option v-for="item in linkDsrState.dsrList" :key="item.id" :value="item.dsrId" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="出借人" prop="cjrLx">
            <el-select v-model="fqInfoFrom.cjrLx">
              <el-option v-for="item in gz_cjr_lx" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="担保方式" prop="dbfs">
            <el-checkbox-group v-model="fqInfoFrom.dbfs">
              <el-checkbox v-for="item in gz_dbfs" :key="item.value" :value="item.value" :label="item.label" />
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同金额币种" prop="htJebz">
            <el-select v-model="fqInfoFrom.htJebz">
              <el-option v-for="item in gz_tc_bz" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同（协议）金额" prop="htJe">
            <el-input v-model="fqInfoFrom.htJe" @input="jeOnInput">
              <!-- <template #suffix>
                元
              </template> -->
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同利率类型" prop="htLllx">
            <el-select v-model="fqInfoFrom.htLllx">
              <el-option v-for="item in gz_ht_lllx" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同利率" prop="htJkll">
            <el-input v-model="fqInfoFrom.htJkll" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同期限起" prop="jkqxStart">
            <el-date-picker
              v-model="fqInfoFrom.jkqxStart"
              type="date"
              placeholder="合同期限起"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同期限止" prop="jkqxEnd">
            <el-date-picker
              v-model="fqInfoFrom.jkqxEnd"
              type="date"
              placeholder="合同期限止"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同编号" prop="jkbh">
            <el-input v-model="fqInfoFrom.jkbh" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { GzjzFqForm, GzjzFqFormEdit } from '@/api/gongzheng/bzfz/gzjzFq/types';
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';

interface Props {
  modelValue: Record<string, any>;
}

const props = defineProps<Props>();

const emit = defineEmits(['update:modelValue']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_cjr_lx, gz_dbfs, gz_tc_bz, gz_ht_lllx } = toRefs<any>(proxy?.useDict('gz_cjr_lx', 'gz_dbfs', 'gz_tc_bz', 'gz_ht_lllx'))

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));
// 由父组件传入的卷宗信息
const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));

const fqInfoFrom = computed<GzjzFqFormEdit>({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// const fqInfoFrom = ref({
//   zwrIds: '',
//   zqrIds: '',
//   dlrIds: '',
//   dbrIds: '',
//   cjrLx: '',
//   dbfs: [],
//   htJebz: '',
//   htJe: '',
//   htLllx: '',
//   htJkll: '',
//   jkqxStart: '',
//   jkqxEnd: '',
//   jkbh: ''
// });

const fqFormRef = ref<ElFormInstance>(null);

const linkDsrState = reactive({
  dsrList: [],
  loading: false,
})

const rules = {
  zwrIds: [
    { required: true, message: '请选择债务人', trigger: 'change' }
  ],
  zqrIds: [
    { required: true, message: '请选择债权人', trigger: 'change' }
  ],
  dlrIds: [
    // { required: true, message: '请选择代理人', trigger: 'change' }
  ],
  dbrIds: [
    // { required: true, message: '请选择担保人', trigger: 'change' }
  ],
  cjrLx: [
    { required: true, message: '请选择创建人类型', trigger: 'change' }
  ],
  dbfs: [
    { required: true, message: '请选择担保方式', trigger: 'change' }
  ],
  htJebz: [
    { required: true, message: '请选择合同金额币种', trigger: 'change' }
  ],
  htJe: [
    { required: true, message: '请输入合同（协议）金额', trigger: 'blur' }
  ],
  htLllx: [
    { required: true, message: '请选择合同利率类型', trigger: 'change' }
  ],
  htJkll: [
    { required: true, message: '请输入合同利率', trigger: 'blur' }
  ],
  jkqxStart: [
    { required: true, message: '请选择合同期限起', trigger: 'change' }
  ],
  jkqxEnd: [
    { required: true, message: '请选择合同期限止', trigger: 'change' }
  ],
  jkbh: [
    // { required: true, message: '请输入合同编号', trigger: 'blur' }
  ]
}

const oldHtJe = ref('')
const jeOnInput = (val: any) => {
  const reg = /^\d+\.?\d*$/
  if(val) {
    if(reg.test(val)) {
      fqInfoFrom.value.htJe = val
      oldHtJe.value = val
    } else {
      fqInfoFrom.value.htJe = oldHtJe.value
    }
  } else {
    oldHtJe.value = ''
  }
}

// 加载当事人列表
const loadDsrList = async () => {
  if (!curGzjz.value.id && !currentRecordId.value) {
    linkDsrState.dsrList = []
    return;
  };
  linkDsrState.loading = true;
  try {
    const params: GzjzDsrQuery = {
      gzjzId: curGzjz.value.id || currentRecordId.value,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    }
    const res = await listGzjzDsrByGzjz(params);
      if (res && res.code === 200) {
        linkDsrState.dsrList = (res.rows || []).reduce((doneArr, curItem) => {
          const found = doneArr.find(i => {
            return i.dsrId == curItem.dsrId
          })
          if(!found) {
            doneArr.push(curItem)
          }
          return doneArr;
        }, []);
      }
  } catch (err: any) {
    console.log('加载当事人列表失败', err);
    ElMessage.error('加载当事人列表失败');
  } finally {
    linkDsrState.loading = false;
  }
}

const validate = async () => {
  return fqFormRef.value?.validate();
}

defineExpose({
  validate
})

onMounted(() => {
  loadDsrList();
})

</script>
