import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ArchiveFormData, ArchiveQuery, ArchiveResponse } from './types';

/**
 * 提交归档
 * @param data 归档数据
 */
export const submitArchive = (data : ArchiveFormData) : AxiosPromise<ArchiveResponse> => {
  return request({
    url: '/gongzheng/gzjzJbxx/initiatePutaway',
    method: 'post',
    data: {
      id: data.id,
      sftg: data.sftg,
      lcyj: data.lcyj,
      dahh: data.dahh,
      bgqx: data.bgqx,
      dabh: data.dabh,
      sfmj: data.sfmj,
      jyfs: data.jyfs,
      lczt: data.lczt,
      gzGzjzArchiveAttachmentsBos: data.gzGzjzArchiveAttachmentsBos
    }
  });
};

/**
 * 获取归档信息
 * @param query 查询参数
 */
export const getArchiveInfo = (query : ArchiveQuery) : AxiosPromise<ArchiveResponse> => {
  return request({
    url: '/gongzheng/gzjzJbxx/getArchiveInfo',
    method: 'get',
    params: query
  });
};

/**
 * 生成卷宗封皮
 * @param gzjzId 卷宗ID
 */
export const generateCaseCover = (gzjzId : string | number) : AxiosPromise<ArchiveResponse> => {
  return request({
    url: '/gongzheng/gzjzJbxx/generateCaseCover',
    method: 'post',
    data: { gzjzId }
  });
};
