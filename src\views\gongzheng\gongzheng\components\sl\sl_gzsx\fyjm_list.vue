<template>
  <div>
    <el-form :model="formData" ref="formRef" label-width="130px" inline style="margin-top: 10px;">
      <el-row>
        <el-col :span="8">
          <el-form-item label="申请人：" prop="applicant">
            <el-input v-model="formData.applicant" :readonly="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请日期：" prop="applicationDate">
            <el-date-picker v-model="formData.applicationDate" type="date" placeholder="申请日期" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" :readonly="true"  />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人：" prop="approver">
            <el-select v-model="formData.approver" placeholder="请选择审批人" clearable>
              <el-option v-for="dict in tsjzspy" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="申请原因：" prop="reason" style="width: 100%;">
            <el-input v-model="formData.reason" type="textarea" style="width: 100%;" placeholder="请输入费用减免申请原因" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div style="margin-top: 10px;">
      <h3>收费列表</h3>
      <el-table :data="feeList" :summary-method="getSummaries" style="width: 100%" border highlight-current-row
        height="300px" size="small" show-summary @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column label="费用类型" prop="fylx" align="center">
          <template #default="scope">
            {{ getDictLabel(gz_sf_lb, scope.row.fylx) || scope.row.fylx }}
          </template>
        </el-table-column>
        <el-table-column label="公证事项" prop="gzsxMc" align="center" />
        <el-table-column label="应收金额" prop="fyys" align="center">
          <template #default="scope">
            <span>¥{{ scope.row.fyys?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已收金额" prop="fyss" align="center">
          <template #default="scope">
            <span>¥{{ scope.row.fyss?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="减免金额" prop="fyjm" align="center">
          <template #default="scope">
            <el-input-number v-model="scope.row.fyjm" :min="0" :max="scope.row.fyys" :precision="2" size="small"
              style="width: 100px" />
          </template>
        </el-table-column>
        <el-table-column label="收费状态" prop="sfzt" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.sfzt === '2' ? 'success' : 'danger'" size="small">
              {{ scope.row.sfzt === '2' ? '已完成' : '未收费' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, getCurrentInstance, toRefs, watch } from 'vue'
  import { ElMessage } from 'element-plus';
  import type { ComponentInternalInstance } from 'vue'
  import type { GzjzGzsxSfxxVO } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx/types'
  import { useUserStore } from '@/store/modules/user'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_gylfw, gz_sf_lb } = toRefs<any>(proxy?.useDict('gz_gylfw', 'gz_sf_lb'))
  const { tsjzspy } = toRefs<any>(proxy?.useRoleUser('tsjzspy'));
  interface Props {
    formData ?: any
    mode ?: 'add' | 'edit' | 'view'
    selectedMatter ?: any // 选中的公证事项
  }

  interface Emits {
    (e : 'save', data : any) : void
    (e : 'cancel') : void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const formRef = ref()
  const feeList = ref<GzjzGzsxSfxxVO[]>([])
  const selectedFees = ref<GzjzGzsxSfxxVO[]>([])
  const userStore = useUserStore()
  // 表单数据
  const formData = reactive({
    applicant: userStore.nickname, // 申请人
    applicationDate: new Date().toISOString().split('T')[0], // 申请日期
    approver: '', // 审批人
    reason: '' // 申请原因
  })

  // 字典标签获取方法
  const getDictLabel = (dictData : any[], value : string) => {
    const dict = dictData.find(item => item.value === value)
    return dict ? dict.label : value
  }


  // 初始化数据
  const initializeData = () => {
    if (props.selectedMatter.length==0) {
      ElMessage.error('请选择减免费用事项')
      return;
    }
    // 从选中的公证事项获取收费列表
    if (props.selectedMatter) {
      console.log(props.selectedMatter)
      feeList.value = props.selectedMatter
      console.log( feeList.value)
    }
    // 自动填充申请日期为当天
    const today = new Date()
    formData.applicationDate = today.toISOString().split('T')[0]

  }

  // 表格选择变化
  const handleSelectionChange = (selection : GzjzGzsxSfxxVO[]) => {
    selectedFees.value = selection
  }

  // 合计计算
  const getSummaries = (param : any) => {
    const { columns, data } = param
    const sums : string[] = []
    columns.forEach((column : any, index : number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (column.property === 'fyys' || column.property === 'fyss' || column.property === 'fyjm') {
        const values = data.map((item : any) => Number(item[column.property]))
        if (!values.every((value : number) => Number.isNaN(value))) {
          sums[index] = `¥${values.reduce((prev : number, curr : number) => {
            const value = Number(curr)
            if (!Number.isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0).toFixed(2)}`
        } else {
          sums[index] = ''
        }
      } else {
        sums[index] = ''
      }
    })
    return sums
  }

  // 监听props变化
  watch(() => props.selectedMatter, (newMatter) => {
    if (newMatter) {
      initializeData()
    }
  }, { immediate: true })

  onMounted(() => {
    initializeData()
  })

  // 暴露方法
  defineExpose({
    formData,
    feeList,
    selectedFees
  })
</script>

<style scoped>
  :deep(.el-form-item) {
    margin-bottom: 15px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th) {
    background-color: #fafafa;
    font-weight: 600;
  }
</style>
