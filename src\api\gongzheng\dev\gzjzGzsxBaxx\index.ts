import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzGzsxBaxxVO, GzjzGzsxBaxxForm, GzjzGzsxBaxxQuery } from '@/api/gongzheng/dev/gzjzGzsxBaxx/types';

/**
 * 查询公证卷宗-公证事项-备案信息v1.0列表
 * @param query
 * @returns {*}
 */

export const listGzjzGzsxBaxx = (query?: GzjzGzsxBaxxQuery): AxiosPromise<GzjzGzsxBaxxVO[]> => {
  return request({
    url: '/gongzheng/gzjzGzsxBaxx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公证卷宗-公证事项-备案信息v1.0详细
 * @param id
 */
export const getGzjzGzsxBaxx = (id: string | number): AxiosPromise<GzjzGzsxBaxxVO> => {
  return request({
    url: '/gongzheng/gzjzGzsxBaxx/' + id,
    method: 'get'
  });
};

/**
 * 新增公证卷宗-公证事项-备案信息v1.0
 * @param data
 */
export const addGzjzGzsxBaxx = (data: GzjzGzsxBaxxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsxBaxx',
    method: 'post',
    data: data
  });
};

/**
 * 修改公证卷宗-公证事项-备案信息v1.0
 * @param data
 */
export const updateGzjzGzsxBaxx = (data: GzjzGzsxBaxxForm) => {
  return request({
    url: '/gongzheng/gzjzGzsxBaxx',
    method: 'put',
    data: data
  });
};

/**
 * 删除公证卷宗-公证事项-备案信息v1.0
 * @param id
 */
export const delGzjzGzsxBaxx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzGzsxBaxx/' + id,
    method: 'delete'
  });
};
