<template>
  <!-- 信息送达列表 -->
  <div>
    <div class="action-wrap">
      <el-button type="primary" @click="addSdxx">新增</el-button>
      <el-button type="danger" @click="delSdxx" :disabled="multiple" :loading="sdxxBtnLoading">删除</el-button>
    </div>
    <el-table v-loading="sdxxLoading" :data="sdxxList" @selection-change="handleSelectionChange" style="height: 200px;" border size="small">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column prop="tzfs" label="送达方式" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ dictMapFormat(gz_fzxx_sdfs, row.tzfs) }}
        </template>
      </el-table-column>
      <el-table-column prop="sdrVo.xm" label="送大人" align="center" show-overflow-tooltip/>
      <el-table-column prop="sdrVo.zjlx" label="证件类型" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ dictMapFormat(gz_gr_zjlx, row.sdrVo.zjlx) }}
        </template>
      </el-table-column>
      <el-table-column prop="sdrVo.zjhm" label="证件号码" align="center" show-overflow-tooltip/>
      <el-table-column prop="lxdh" label="联系电话" align="center" show-overflow-tooltip/>
      <el-table-column prop="lqdd" label="领取地点" align="center" show-overflow-tooltip/>
      <el-table-column prop="lqxx" label="领取信息" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ dictMapFormat(gz_fzxx_lqxx, row.lqxx) }}
        </template>
      </el-table-column>
      <el-table-column prop="sdlx" label="送达类型" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          {{ dictMapFormat(gz_fzxx_sdlx, row.sdlx) }}
        </template>
      </el-table-column>
    </el-table>
    <sdxx-form ref="SdxxFormRef" @callback="callback"></sdxx-form>
  </div>
</template>

<script setup name="TableBySdxx" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue';
  import type { ComponentInternalInstance, Ref } from 'vue';
  import type { GzjzGzsxVO } from '@/api/gongzheng/gongzheng/gzjzGzsx/types';
  import { listGzjzSdxx, addGzjzSdxx, updateGzjzSdxx, delGzjzSdxx } from '@/api/gongzheng/gongzheng/gzjzSdxx';
  import type { GzjzSdxxVO, GzjzSdxxForm, GzjzSdxxQuery } from '@/api/gongzheng/gongzheng/gzjzSdxx/types';
  import SdxxForm from './popForm.vue';
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_fzxx_sdfs, gz_fzxx_sdlx, gz_gr_zjlx, gz_fzxx_lqxx } = toRefs<any>(proxy?.useDict('gz_fzxx_sdfs', 'gz_fzxx_sdlx', 'gz_gr_zjlx', 'gz_fzxx_lqxx'));

  const SdxxFormRef = ref<InstanceType<typeof SdxxForm>>(null);

  // 获取父组件的数据
  const gzcAddr = inject<Ref<string>>('gzcAddr', ref(null));
  const view = inject<Ref<boolean>>('view', ref(false));
  const gzjzId = inject<Ref<string | number>>('gzjzId', ref(null));
  const gzsxList = inject<Ref<GzjzGzsxVO[]>>('gzsxList', ref(null));

  // 提供给子组件的数据和方法
  provide('gzcAddr', gzcAddr);
  provide('gzjzId', gzjzId);
  provide('gzsxList', gzsxList);

  const sdxxLoading = ref(false);
  const sdxxBtnLoading = ref(false);
  const sdxxList = ref<GzjzSdxxVO[]>([]);

  // 获取送达信息列表
  const getSdxxList = async () => {
    sdxxLoading.value = true;
    try {
      const res = await listGzjzSdxx({
        gzjzId: gzjzId.value,
        pageNum: 1,
        pageSize: 1000
      });
      sdxxList.value = res.rows || [];
    } catch (error) {
      console.error('获取送达信息失败:', error);
      proxy?.$modal.msgError('获取送达信息失败');
    } finally {
      sdxxLoading.value = false;
    }
  };

  const ids = ref<Array<string | number>>([]);
  const multiple = ref(true);
  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzjzSdxxVO[]) => {
    ids.value = selection.map((item : GzjzSdxxVO) => item.id);
    multiple.value = !selection.length;
  };

  // 新增送达信息
  const addSdxx = () => {
    SdxxFormRef.value.open(gzjzId.value);
  }
  // 编辑送达信息
  const editSdxx = (row ?: GzjzSdxxVO) => {
    SdxxFormRef.value.open(gzjzId.value, row);
  }

  // 删除送达信息
  const delSdxx = async (row ?: GzjzSdxxVO) => {
    const _ids = row?.id || ids.value;
    sdxxBtnLoading.value = true;
    await proxy?.$modal.confirm('是否确认删除选中的数据项？').finally(() => sdxxBtnLoading.value = false);
    await delGzjzSdxx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getSdxxList();
  }

  const loadData = () => {
    getSdxxList();
  }
  const rest = () => {
    sdxxLoading.value = false;
    sdxxBtnLoading.value = false;
    sdxxList.value = [];
  }

  // 监听
  watch(() => view.value, (newVal) => {
    if(newVal){
      loadData();
    }else{
      rest();
    }
  });

  const callback = (options ?: any) => {
    if(options?.success){
      // 成功
    }else{
      // 失败
    }
    getSdxxList();
  }
  // 暴露方法给父组件
  defineExpose({
    loadData
  });

  onMounted(() => {
    loadData();
  });
</script>

<style scoped>
  .action-wrap {
    /* float: right; */
    margin-bottom: 10px;
  }
</style>
