<template>
  <div class="notary-matters">
    <div class="section-header">
      <h3>公证事项(费用合计：¥{{ totalFee }})</h3>
      <div class="header-actions">
        <!-- <el-button @click="handleReceiveInvoice">收费置顶</el-button> -->
        <el-button type="primary" @click="handleAdd">新增公证事项</el-button>
        <el-button v-if="currentRecord?.lczt === '04'" type="primary" @click="gzsxHandleNumber"
          :loading="takeNumberLoading">取号</el-button>
        <el-button @click="handlePrintReceipt" v-has-permi="['tslc:fq:edit']">费用减免</el-button>
        <el-button @click="handleViewFeeDetail">批量修改费用</el-button>
        <el-button type="danger" @click="handleDelete" :disabled="selectedRows.length === 0">删除</el-button>
      </div>
    </div>

    <el-table :data="listData" style="width: 100%" border highlight-current-row
      @selection-change="handleSelectionChange" height="300px" size="small" v-loading="loading">
      <el-table-column type="selection" width="55" align="center" />
      <!-- -->
      <!-- <el-table-column label="排序" prop="index" width="60" align="center" /> -->
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <div class="flex flex-wrap">
            <span>
              <el-button link type="primary" size="small" @click="handleEdit(scope.row)">
                编辑
              </el-button>
            </span>
            <span>
              <el-button link type="primary" size="small" @click="handleSf(scope.row)">
                收费
              </el-button>
            </span>
            <template v-if="isFqJd(scope.row)">
              <span>
                <el-button link type="primary" size="small" @click="openJd(scope.row)">
                  借贷
                </el-button>
              </span>
              <span>
                <el-button link type="primary" size="small" @click="openFq(scope.row)">
                  赋强
                </el-button>
              </span>
            </template>
            <span v-if="isYz(scope.row)">
              <el-button link type="primary" size="small" @click="openYz(scope.row)">
                遗嘱
              </el-button>
            </span>
            <template v-if="isTcx(scope.row)">
              <span>
                <el-button link type="primary" size="small" @click="openTcx(scope.row)">
                  提存项
                </el-button>
              </span>
            </template>
            <!--  <el-button link type="info" size="small" @click="handleView(scope.row)">
            查看
          </el-button> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column label="公证事项" prop="gzsxMc" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column label="关系人" prop="gxrMc" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column label="公证书编号" prop="gzsBh" min-width="120" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="公证书名称" prop="gzsMc" min-width="120" align="center" /> -->
      <el-table-column label="份数" prop="gzsFs" min-width="100" align="center" />
      <!-- <el-table-column label="公证件数" prop="gzjs" width="100" align="center" /> -->
      <el-table-column label="公证费" prop="gzf" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.notaryFee || row.gzf || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="副本费" prop="fbf" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.copyFee || row.fbf || '0' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="实收副本费" prop="actualCopyFee" width="100" align="center" /> -->
      <el-table-column label="英文翻译费" prop="ywfyf" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.ywfyf || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="小语种翻译费" prop="xyzfyf" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.xyzfyf || row.translationFee || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="其他费用" prop="qtfy" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.qtfy || row.otherFee || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="小计" prop="xjfy" min-width="80" align="center" />
      <!-- <el-table-column label="是否备案" prop="sfBa" width="100" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.sfBa === '1' ? 'success' : 'info'" size="small">
            {{ scope.row.sfBa === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="状态" prop="status" min-width="100" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.sfzt === '已完成' ? 'success' : 'danger'" size="small">
            {{ scope.row.sfzt || '未收费' }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" prop="remark" min-width="120" align="center" show-overflow-tooltip /> -->
    </el-table>

    <!-- 添加/编辑/查看对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800" @close="handleDialogClose" destroy-on-close>
      <NotaryMatterForm v-if="dialogVisible" :form-data="currentMatter" :mode="dialogMode" @save="handleSave"
        :saving="curMatterSaving" @cancel="handleCancel" />
    </el-dialog>

    <!-- 收费管理对话框 -->
    <el-dialog v-model="dialogVisible2" :title="dialogTitle2" width="1000" @close="handleDialogClose2">
      <SfList v-if="dialogVisible2" :gzsx-id="currentMatter2?.id" :gzjz-id="currentRecordId" :visible="dialogVisible2"
        @confirm="submitForm2" @update:visible="dialogVisible2 = $event" @fee-changed="handleFeeChanged" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel3">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 费用减免申请对话框（新） -->
    <FeeReductionDialog ref="feeReduceRef" :record-id="currentRecordId" :record="currentRecord"
      @success="onFeeReduceSuccess" />




    <!-- 批量设置费用对话框 -->
    <el-dialog v-model="dialogVisible4" :title="dialogTitle4" width="500" @close="cancel5">
      <el-form :model="plfyForm" ref="plfyFormRef" label-width="130px" style="margin-top: 10px;">
        <el-form-item label="费用类型" prop="fylx">
          <el-select v-model="plfyForm.fylx" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="dict in gz_sf_lb" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="费用金额" prop="fy">
          <el-input-number v-model="plfyForm.fy" :min="0" :precision="2" placeholder="请输入费用金额" style="width: 100%" />
        </el-form-item>
        <!-- <el-form-item label="计价方式" prop="jjfs">
          <el-select v-model="plfyForm.jjfs" placeholder="请选择计价方式" clearable style="width: 100%">
            <el-option v-for="dict in gz_jjfs" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm4">保存</el-button>
          <el-button @click="cancel5">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 借贷 -->
    <JdModel ref="jdRef" />
    <!-- 赋强 -->
    <FqModel ref="fqRef" />
    <!-- 遗嘱 -->
    <YzModel ref="yzRef" />
    <!--提存项-->
    <TcxModel ref="tcxRef" />

  </div>
</template>

<script setup lang="ts">
  import { ref, computed, inject, watch, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { Ref, ComponentInternalInstance } from 'vue'
  import NotaryMatterForm from './NotaryMatterForm.vue'
  import SfList from './sf_list.vue'
  import FeeReductionDialog from './FeeReductionDialog.vue'
  import FyjmList from './fyjm_list.vue'
  import JdModel from './jd/index.vue'
  import FqModel from './fq/index.vue'
  import YzModel from './yz/index.vue'
  import type { GzjzGzsxVO, GzjzGzsxForm, GzjzGzsxQuery } from '@/api/gongzheng/gongzheng/gzjzGzsx/types'
  import { listGzjzGzsx, addGzjzGzsx, updateGzjzGzsx, delGzjzGzsx, getGzjzGzsx, gzsxTakeNumber } from '@/api/gongzheng/gongzheng/gzjzGzsx'
  import { updateGzsxFees } from '@/api/gongzheng/gongzheng/gzjzGzsxSfxx'
  import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types'
  import { jdfq_data, yz_data } from './data_preset'
  import TcxModel from './tcx/index.vue'
  import {tcx_gzsx_data} from './tcx_preset';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance
  const { gz_sf_lb, gz_jjfs } = toRefs<any>(proxy?.useDict ? proxy.useDict('gz_sf_lb', 'gz_jjfs') : { gz_sf_lb: ref([]), gz_jjfs: ref([]) })

  interface Props {
    data ?: GzjzGzsxVO[]
  }

  interface Emits {
    (e : 'update:data', value : GzjzGzsxVO[]) : void
    (e : 'add-matter') : void
    (e : 'edit-matter', matter : GzjzGzsxVO) : void
    (e : 'delete-matter', ids : number[]) : void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 获取当前卷宗ID
  const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))
  const currentRecord = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null))

  console.log('currentRecord: ', currentRecord)

  // 主对话框状态
  const dialogVisible = ref(false)
  const dialogMode = ref<'add' | 'edit' | 'view'>('add')
  const currentMatter = ref<GzjzGzsxForm>({} as GzjzGzsxForm)
  const curMatterSaving = ref(false);
  const selectedRows = ref<GzjzGzsxVO[]>([])

  // 收费对话框状态
  const dialogVisible2 = ref(false)
  const dialogMode2 = ref<'add' | 'edit' | 'view'>('add')
  const currentMatter2 = ref<any>({})

  // 费用减免（新弹窗）
  const feeReduceRef = ref<InstanceType<typeof FeeReductionDialog> | null>(null)

  // 批量费用设置对话框状态
  const dialogVisible4 = ref(false)
  const plfyForm = ref({
    fylx: null,
    fy: null,
    jjfs: null
  })
  const plfyFormRef = ref()

  const jdRef = ref(null)
  const fqRef = ref(null)
  const yzRef = ref(null)

  // 列表数据和加载状态
  const listData = ref<GzjzGzsxVO[]>([])
  const loading = ref(false)

  const takeNumberLoading = ref(false)

  // 计算属性
  const dialogTitle = computed(() => {
    const titles = {
      add: '新增公证事项',
      edit: '编辑公证事项',
      view: '查看公证事项'
    }
    return titles[dialogMode.value]
  })

  const dialogTitle2 = computed(() => {
    const titles = {
      add: '新增费用管理',
      edit: '编辑费用管理',
      view: '查看费用管理'
    }
    return titles[dialogMode2.value]
  })

  // 已移除旧的费用减免弹窗标题

  const dialogTitle4 = computed(() => {
    return '批量设置费用'
  })

  // 计算总费用
  const totalFee = computed(() => {
    return listData.value.reduce((total, item) => {
      const subtotal = Number(item.xjfy) || 0
      return total + subtotal
    }, 0).toFixed(2)
  })

  // 加载数据
  const loadData = async () => {
    if (!currentRecordId.value) {
      listData.value = []
      return
    }

    try {
      loading.value = true
      const queryParams : GzjzGzsxQuery = {
        gzjzId: currentRecordId.value,
        pageNum: 1,
        pageSize: 1000 // 获取所有数据
      }

      console.log('正在加载公证事项数据，查询参数：', queryParams)

      const response = await listGzjzGzsx(queryParams)
      console.log('公证事项API响应：', response)

      if (response && response.code === 200) {
        listData.value = response.rows || []
        console.log('>>>>>> ', listData.value.map(i => {
          return {
            gzsxId: i.gzsxId,
            gzsxMc: i.gzsxMc
          }
        }))
        emit('update:data', listData.value)
      } else {
        const errorMsg = response?.msg || '获取公证事项列表失败'
        console.warn('公证事项列表获取失败:', errorMsg)
        ElMessage.warning(errorMsg)
        listData.value = [] // 确保清空数据
      }
    } catch (error : any) {
      console.error('加载公证事项列表失败:', error)

      // 检查是否是网络错误或API不存在
      if (error?.message?.includes('404') || error?.message?.includes('Not Found')) {
        console.warn('公证事项API接口不存在，使用空数据')
        listData.value = []
      } else if (error?.message?.includes('Cannot read properties')) {
        console.warn('API响应格式异常，使用空数据')
        listData.value = []
      } else {
        ElMessage.error('加载公证事项列表失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 表格选择变化
  const handleSelectionChange = (rows : GzjzGzsxVO[]) => {
    selectedRows.value = rows
  }

  // 新增公证事项
  const handleAdd = () => {
    if (!currentRecordId.value) {
      ElMessage.error('请先保存基本信息后再添加公证事项')
      return
    }

    currentMatter.value = {
      gzsxId: '',
      gzjzId: currentRecordId.value,
      gzsxMc: '',
      gxrMc: '',
      gzsBh: '',
      gzsMc: '',
      gzsFs: 1,
      gzjs: 1,
      sfBa: '0',
      remark: ''
    }
    dialogMode.value = 'add'
    dialogVisible.value = true
    emit('add-matter')
  }

  // 取号
  const gzsxHandleNumber = async () => {
    if (!currentRecordId.value) {
      ElMessage.error('请先保存基本信息后再取号')
      return
    }

    try {
      takeNumberLoading.value = true;
      const res = await gzsxTakeNumber({ gzjzId: currentRecordId.value });
      if (res && res.code === 200) {
        ElMessage.success('取号成功');
      }
    } catch (err : any) {
      console.error('取号失败:', err);
      ElMessage.error('取号失败');
    } finally {
      await loadData() // 重新加载数据
      takeNumberLoading.value = false;
    }
  }

  const stringToBoolean = <T>(obj : T, keys : string[], from : string = '1,0') => {
    const [trueStr, falseStr] = from.split(',')
    keys.forEach(key => {
      if (obj[key] === trueStr) {
        obj[key] = true
      } else {
        obj[key] = false
      }
    });
    return obj
  }

  // 编辑公证事项
  const handleEdit = async (matter : GzjzGzsxVO) => {
    try {
      const response = await getGzjzGzsx(matter.id)
      if (response && response.code === 200) {
        const data = stringToBoolean(response.data, ['sfsh', 'sfsmbz', 'sfzdpyc', 'sfydyljs', 'sfzxsl', 'sfwjcbz'])
        currentMatter.value = data
        dialogMode.value = 'edit'
        dialogVisible.value = true
        emit('edit-matter', matter)
      } else {
        ElMessage.warning(response?.msg || '获取公证事项详情失败')
      }
    } catch (error : any) {
      console.error('获取公证事项详情失败:', error)
      if (error?.message?.includes('404')) {
        ElMessage.warning('公证事项详情接口不存在')
      } else {
        ElMessage.error('获取公证事项详情失败')
      }
    }
  }

  // 查看公证事项
  const handleView = async (matter : GzjzGzsxVO) => {
    try {
      const response = await getGzjzGzsx(matter.id)
      if (response && response.code === 200) {
        currentMatter.value = { ...response.data }
        dialogMode.value = 'view'
        dialogVisible.value = true
      } else {
        ElMessage.warning(response?.msg || '获取公证事项详情失败')
      }
    } catch (error : any) {
      console.error('获取公证事项详情失败:', error)
      if (error?.message?.includes('404')) {
        ElMessage.warning('公证事项详情接口不存在')
      } else {
        ElMessage.error('获取公证事项详情失败')
      }
    }
  }

  // 删除公证事项
  const handleDelete = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要删除的公证事项')
      return
    }

    const ids = selectedRows.value.map(row => row.id).join(',')

    ElMessageBox.confirm('确定要删除选中的公证事项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const response = await delGzjzGzsx(ids)
        if (response && response.code === 200) {
          ElMessage.success('删除成功')
          await loadData() // 重新加载数据
          emit('delete-matter', selectedRows.value.map(row => Number(row.id)))
        } else {
          ElMessage.warning(response?.msg || '删除失败')
        }
      } catch (error : any) {
        console.error('删除公证事项失败:', error)
        if (error?.message?.includes('404')) {
          ElMessage.warning('删除接口不存在')
        } else {
          ElMessage.error('删除失败')
        }
      }
    })
  }

  // 收费置顶功能
  const handleReceiveInvoice = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要置顶的公证事项')
      return
    }

    ElMessageBox.confirm(
      `确定将选中的${selectedRows.value.length}条公证事项置顶显示？置顶后将在列表中优先显示。`,
      '收费置顶确认',
      {
        confirmButtonText: '确定置顶',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        // 更新置顶状态，这里可以调用后端接口
        selectedRows.value.forEach(row => {
          row.status = '置顶' // 使用status字段表示置顶状态
          row.remark = (row.remark || '') + '[置顶]'
        })

        // 重新排序，将置顶的放在前面
        listData.value.sort((a, b) => {
          if (a.status === '置顶' && b.status !== '置顶') return -1
          if (a.status !== '置顶' && b.status === '置顶') return 1
          return 0
        })

        ElMessage.success('置顶成功，已将选中公证事项置顶显示')
        selectedRows.value = []
      })
      .catch(() => {
        ElMessage.info('已取消置顶操作')
      })
  }

  // 收费功能
  const handleSf = (matter : GzjzGzsxVO) => {
    currentMatter2.value = matter
    dialogMode2.value = 'add'
    dialogVisible2.value = true
  }

  // 借贷
  const openJd = (row: any) => {
    jdRef.value?.open({
      gzjzId: currentRecordId.value,
      gzjzGzsxInfo: row
    })
  }

  // 赋强
  const openFq = (row: any) => {
    fqRef.value?.open({
      gzjzId: currentRecordId.value,
      gzjzGzsxInfo: row
    })
  }

  // 遗嘱
  const openYz = (row: any) => {
    yzRef.value?.open({
      gzjzId: currentRecordId.value,
      gzjzGzsxInfo: row
    })
  }

  // 费用减免功能
  const handlePrintReceipt = () => {
    if (!currentRecordId.value) {
      ElMessage.warning('请先保存基本信息后再申请费用减免')
      return
    }
    feeReduceRef.value?.open()
  }

  // 批量修改费用功能
  const handleViewFeeDetail = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要修改费用的公证事项')
      return
    }
    dialogVisible4.value = true
  }

  // 主对话框事件
  const handleDialogClose = () => {
    dialogVisible.value = false
  }

  const handleSave = async (matter : GzjzGzsxForm) => {
    try {
      curMatterSaving.value = true
      if (dialogMode.value === 'add') {
        const response = await addGzjzGzsx(matter)
        if (response && response.code === 200) {
          ElMessage.success('新增成功')
          loadData() // 重新加载数据
        } else {
          ElMessage.warning(response?.msg || '新增失败')
        }
      } else if (dialogMode.value === 'edit') {
        const response = await updateGzjzGzsx(matter)
        if (response && response.code === 200) {
          ElMessage.success('修改成功')
          loadData() // 重新加载数据
        } else {
          ElMessage.warning(response?.msg || '修改失败')
        }
      }
      dialogVisible.value = false
    } catch (error : any) {
      console.error('保存公证事项失败:', error)
      if (error?.message?.includes('404')) {
        ElMessage.warning('保存接口不存在')
      } else {
        ElMessage.error('操作失败')
      }
    } finally {
      curMatterSaving.value = false
    }
  }

  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 收费对话框事件
  const handleDialogClose2 = () => {
    dialogVisible2.value = false
  }

  const handleSave2 = (data : any) => {
    console.log('保存收费数据:', data)
    ElMessage.success('收费信息保存成功')
  }

  const handleCancel2 = () => {
    dialogVisible2.value = false
  }

  const submitForm2 = () => {
    ElMessage.success('收费操作完成')
    dialogVisible2.value = false
  }

  const cancel3 = () => {
    dialogVisible2.value = false
  }

  // 判断赋强、借贷
  const isFqJd = (row : any) => {
    return !!(jdfq_data.find(item => item.gzsxId === row.gzsxId))
  }
  // 判断遗嘱
  const isYz = (row : any) => {
    return !!(yz_data.find(item => item.gzsxId === row.gzsxId))
  }
  //提存项判断
  const tcxRef = ref(null)
  const isTcx = (row : any) => {
    return !!(tcx_gzsx_data.find(item => item.gzsxId === row.gzsxId))
  }
  const openTcx = (row : any) => {
    tcxRef.value?.open({
      gzjzId: currentRecordId.value,
      gzjzGzsxInfo: row
    })
  }

  // 监听收费变化
  const handleFeeChanged = (totalFee : number) => {
    // 更新对应公证事项的费用
    if (currentMatter2.value && currentMatter2.value.id) {
      const matter = listData.value.find(item => item.id === currentMatter2.value.id)
      if (matter) {
        matter.subtotal = totalFee
      }
    }

    loadData()
  }

  const onFeeReduceSuccess = () => {
    loadData()
  }

  // 批量费用设置事件
  const submitForm4 = () => {
    if (!plfyForm.value.fylx || !plfyForm.value.fy) {
      ElMessage.warning('请填写完整的费用信息')
      return
    }

    const { fylx, fy, jjfs } = plfyForm.value

    const params = {
      gzjzId: currentRecordId.value,
      gzjzGzsxIds: selectedRows.value.map(row => row.id),
      fylx,
      fyys: fy,
    }

    updateGzsxFees(params).then(res => {
      if (res.code === 200) {
        loadData();
        ElMessage.success('费用更新成功')
        dialogVisible4.value = false

        plfyForm.value.fylx = null
        plfyForm.value.fy = 0
      }
    })
  }

  const cancel5 = () => {
    dialogVisible4.value = false
    // 重置表单
    plfyForm.value = {
      fylx: null,
      fy: null,
      jjfs: null
    }
  }

  // 监听卷宗ID变化，重新加载数据
  watch(currentRecordId, (newId) => {
    if (newId) {
      loadData()
    } else {
      listData.value = []
    }
  }, { immediate: true })

  // 监听外部数据变化
  watch(() => props.data, (newData) => {
    if (newData && Array.isArray(newData)) {
      listData.value = newData
    }
  }, { immediate: true })

  // 组件挂载时加载数据
  onMounted(() => {
    if (currentRecordId.value) {
      loadData()
    }
  })

  // 暴露方法给父组件
  defineExpose({
    loadData,
    handleAdd,
    handleEdit,
    handleView,
    handleDelete
  })
</script>

<style scoped>
  .notary-matters {
    margin: 20px 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .header-actions .el-button {
    padding: 8px 15px;
    font-size: 12px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.el-table .el-button--small) {
    padding: 2px 8px;
    font-size: 11px;
  }

  :deep(.el-tag--small) {
    font-size: 11px;
  }

  .dialog-footer {
    text-align: center;
  }
</style>
