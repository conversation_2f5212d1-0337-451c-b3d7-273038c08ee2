import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DsrxxZrrZpVO, DsrxxZrrZpForm, DsrxxZrrZpQuery, DsrxxZrrZpUploadForm } from '@/api/gongzheng/dsr/dsrxxZrrZp/types';

/**
 * 查询当事人照片列表
 * @param query
 * @returns {*}
 */

export const listDsrxxZrrZp = (query ?: DsrxxZrrZpQuery) : AxiosPromise<DsrxxZrrZpVO[]> => {
  return request({
    url: '/dsr/dsrxxZrrZp/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询当事人照片详细
 * @param id
 */
export const getDsrxxZrrZp = (id : string | number) : AxiosPromise<DsrxxZrrZpVO> => {
  return request({
    url: '/dsr/dsrxxZrrZp/' + id,
    method: 'get'
  });
};

/**
 * 新增当事人照片
 * @param data
 */
export const addDsrxxZrrZp = (data : DsrxxZrrZpForm) => {
  return request({
    url: '/dsr/dsrxxZrrZp',
    method: 'post',
    data: data
  });
};

/**
 * 修改当事人照片
 * @param data
 */
export const updateDsrxxZrrZp = (data : DsrxxZrrZpForm) => {
  return request({
    url: '/dsr/dsrxxZrrZp',
    method: 'put',
    data: data
  });
};

/**
 * 删除当事人照片
 * @param id
 */
export const delDsrxxZrrZp = (id : string | number | Array<string | number>) => {
  return request({
    url: '/dsr/dsrxxZrrZp/' + id,
    method: 'delete'
  });
};

/**
 * 图片上传
 */
export const uploadFile = (data : DsrxxZrrZpUploadForm) => {
  return request({
    url: '/dsr/dsrxxZrrZp/uploadFile',
    method: 'post',
    data: data
  });
};
