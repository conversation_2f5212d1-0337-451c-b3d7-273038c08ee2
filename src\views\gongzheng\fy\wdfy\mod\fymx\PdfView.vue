<template>
  <div class="pdf-view h-full shadow-[0_0_10px_rgba(0,0,0,0.2)] rounded overflow-hidden">
    <div v-if="$slots.title || title" class="pdf-view-header h-32px flex items-center px-10px">
      <slot name="title"><strong>{{ title }}</strong></slot>
    </div>
    <div class="p-6px" :class="($slots.title || title) ? 'h-[calc(100%-32px)]' : 'h-full'">
      <iframe class="h-full w-full border-none overflow-auto" :src="src"></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">

interface Props {
  src?: string;
  title?: string;
}

const props = defineProps<Props>();

</script>

<style scoped>
.pdf-view {
  border: 1px solid rgba(0, 0, 0, 0.2);
}
.pdf-view-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
</style>