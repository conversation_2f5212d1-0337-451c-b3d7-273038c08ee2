export interface GzsxJdVo {
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 公证员ID
   */
  gzyId?: string | number;

  /**
   * 公证员
   */
  gzyXm?: string;

  /**
   * 贷款银行（字典：gz_tc_khh）
   */
  dkxh?: string;

  /**
   * 借贷日期
   */
  jdrq?: string;

  /**
   * 到期日期
   */
  dqrq?: string;

  /**
   * 合同签署日期
   */
  htqsrq?: string;

  /**
   * 借款金额（万元）
   */
  jkje?: string;

  /**
   * 有无担保（0无，1有）
   */
  ywdb?: string;

  /**
   * 婚姻状况（字典：gz_dsr_hyzt）
   */
  hyzk?: string;

  /**
   * 备注
   */
  remark?: string;

  dsrList?: string;
}

export interface JdKhVo {
  /**
   * 当事人ID
   */
  dsrId?: number;

  /**
   * 当事人姓名
   */
  name?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 当事人地址
   */
  addr?: string;

  /**
   * 放款人 0否 1是
   */
  isLender?: string;

  /**
   * 抵押人 0否 1是
   */
  isMortgagor?: string;

  /**
   * 借款人 0否 1是
   */
  isBorrower?: string;
}