<template>
  <!-- 短信预设对话框 -->
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="1200" @close="cancel">
    <div>
      <h3>短信接收人<span style="margin-left: 10px; color: red; font-size: 12px;">(注:本列表只会显示填有联系电话的人员，如果是联系电话显示红色说明号码有误或短信可能无法送达)</span></h3>
      <TableBySms ref="TableBySmsRef" ></TableBySms>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-loading="btnLoading" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SetSmsIndex" lang="ts">
  import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
  import type { ComponentInternalInstance, Ref } from 'vue'
  import { listGzjzDsrByGzjz, updateBySMSGzjzDsr } from '@/api/gongzheng/gongzheng/gzjzDsr'
  import type { GzjzDsrVO, GzjzDsrForm } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
  import TableBySms from './tableBySms.vue';
  import { clearEmptyProperty, dictMapFormat } from '@/utils/ruoyi';

  const dialog = reactive<DialogOption>({
    visible: false,
    title: '短信推送'
  });

  const TableBySmsRef = ref<InstanceType<typeof TableBySms>>(null);

  const view = ref(false);
  const gzjzId = ref<string | number>(null);
  // 提供给子组件的数据和方法
  provide('view', view);
  provide('gzjzId', gzjzId);

  const btnLoading = ref(false);

  // 提交保存
  const submitForm = async () => {
    btnLoading.value = true;
    const result = await TableBySmsRef.value.submitForm();
    btnLoading.value = false
    if(!result.success){
      proxy?.$modal.msgWarning(result.msssage);
      return;
    }
    emit('callback', result);
    dialog.visible = false;
    view.value = false;
  }
  // 取消
  const cancel = () => {
    dialog.visible = false;
    view.value = false;
    emit('callback', {success: true});
  }
  // 打开
  const open = (_gzjzId: string | number) => {
    console.log('SetSMSIndex.gzjzId', _gzjzId);
    if(!_gzjzId){
      proxy?.$modal.msgWarning("卷宗编号为空！");
      return;
    }
    gzjzId.value = _gzjzId;
    view.value = true;
    dialog.visible = true;
  }
  // 回调方法
  const emit = defineEmits(['callback']);
  // 暴露方法给父组件
  defineExpose({
    open,
    cancel
  });

  onMounted(() => {
  });
</script>

<style>
</style>
