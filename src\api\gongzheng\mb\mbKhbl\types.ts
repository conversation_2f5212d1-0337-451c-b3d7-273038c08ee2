export interface MbKhblVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 模板基础信息ID
   */
  mbId: string | number;

  /**
   * 业务ID
   */
  ywId: string | number;

  /**
   * 客户角色
   */
  khjs: string;

  /**
   * 客户角色样式类型
   */
  khjsYs: number;

  /**
   * 客户属性
   */
  khsx: number;

  /**
   * 日期格式
   */
  rqgs: number;

  /**
   * 姓名
   */
  xm: number;

  /**
   * 民族
   */
  mz: number;

  /**
   * 性别
   */
  xb: number;

  /**
   * 出生日期
   */
  crrq: number;

  /**
   * 证件名称
   */
  zjmc: number;

  /**
   * 国际
   */
  gj: number;

  /**
   * 住址
   */
  zz: number;

  /**
   * 联系地址
   */
  lxdz: number;

  /**
   * 工作单位
   */
  gzdw: number;

  /**
   * 身前往
   */
  sqw: number;

  /**
   * 备注
   */
  remark: string;

}

export interface MbKhblForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 业务ID
   */
  ywId?: string | number;

  /**
   * 客户角色
   */
  khjs?: string;

  /**
   * 客户角色样式类型
   */
  khjsYs?: number;

  /**
   * 客户属性
   */
  khsx?: number;

  /**
   * 日期格式
   */
  rqgs?: number;

  /**
   * 姓名
   */
  xm?: number;

  /**
   * 民族
   */
  mz?: number;

  /**
   * 性别
   */
  xb?: number;

  /**
   * 出生日期
   */
  crrq?: number;

  /**
   * 证件名称
   */
  zjmc?: number;

  /**
   * 国际
   */
  gj?: number;

  /**
   * 住址
   */
  zz?: number;

  /**
   * 联系地址
   */
  lxdz?: number;

  /**
   * 工作单位
   */
  gzdw?: number;

  /**
   * 身前往
   */
  sqw?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface MbKhblQuery extends PageQuery {

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 业务ID
   */
  ywId?: string | number;

  /**
   * 客户角色
   */
  khjs?: string;

  /**
   * 客户角色样式类型
   */
  khjsYs?: number;

  /**
   * 客户属性
   */
  khsx?: number;

  /**
   * 日期格式
   */
  rqgs?: number;

  /**
   * 姓名
   */
  xm?: number;

  /**
   * 民族
   */
  mz?: number;

  /**
   * 性别
   */
  xb?: number;

  /**
   * 出生日期
   */
  crrq?: number;

  /**
   * 证件名称
   */
  zjmc?: number;

  /**
   * 国际
   */
  gj?: number;

  /**
   * 住址
   */
  zz?: number;

  /**
   * 联系地址
   */
  lxdz?: number;

  /**
   * 工作单位
   */
  gzdw?: number;

  /**
   * 身前往
   */
  sqw?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



