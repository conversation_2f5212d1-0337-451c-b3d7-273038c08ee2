export interface MbWdVO {
  /**
   * 序号
   */
  id: string | number;

  /**
   * 模板基础信息ID
   */
  mbId: string | number;

  /**
   * 文档名称
   */
  wdMc: string;

  /**
   * 文档类别
   */
  wdLb: number;

  /**
   * 文档地址
   */
  wdDz: string;

  /**
   * 业务ID
   */
  ywId: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 是否默认
   */
  isDefault: number;

}

export interface MbWdForm extends BaseEntity {
  /**
   * 序号
   */
  id?: string | number;

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 文档名称
   */
  wdMc?: string;

  /**
   * 文档类别
   */
  wdLb?: number;

  /**
   * 文档地址
   */
  wdDz?: string;

  /**
   * 业务ID
   */
  ywId?: string | number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 是否默认
   */
  isDefault?: number;

}

export interface MbWdQuery extends PageQuery {

  /**
   * 模板基础信息ID
   */
  mbId?: string | number;

  /**
   * 文档名称
   */
  wdMc?: string;

  /**
   * 文档类别
   */
  wdLb?: number;

  /**
   * 文档地址
   */
  wdDz?: string;

  /**
   * 业务ID
   */
  ywId?: string | number;

  /**
   * 是否默认
   */
  isDefault?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



