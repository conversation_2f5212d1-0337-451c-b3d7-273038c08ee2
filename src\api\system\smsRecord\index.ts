import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SmsRecordVO, SmsRecordForm, SmsRecordQuery } from '@/api/system/smsRecord/types';

/**
 * 查询短信发送日志列表
 * @param query
 * @returns {*}
 */

export const listSmsRecord = (query?: SmsRecordQuery): AxiosPromise<SmsRecordVO[]> => {
  return request({
    url: '/system/smsRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询短信发送日志详细
 * @param id
 */
export const getSmsRecord = (id: string | number): AxiosPromise<SmsRecordVO> => {
  return request({
    url: '/system/smsRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增短信发送日志
 * @param data
 */
export const addSmsRecord = (data: SmsRecordForm) => {
  return request({
    url: '/system/smsRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改短信发送日志
 * @param data
 */
export const updateSmsRecord = (data: SmsRecordForm) => {
  return request({
    url: '/system/smsRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除短信发送日志
 * @param id
 */
export const delSmsRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/smsRecord/' + id,
    method: 'delete'
  });
};
