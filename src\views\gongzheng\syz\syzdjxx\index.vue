<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-form-item label="水印纸编号" prop="syzQz">
              <el-select v-model="queryParams.syzQz" placeholder="前缀" clearable style="width: 80px" >
                <el-option v-for="dict in gz_syz_qhz" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
              <el-input v-model="queryParams.szbh" placeholder="请输入首张号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="入库时间" style="width: 345px">
              <el-date-picker
                v-model="dateRangeRksj"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:syzdjxx:add']">登记入库</el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:syzdjxx:edit']">修改</el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:syzdjxx:remove']">删除</el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:syzdjxx:export']">导出</el-button>-->
<!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table border v-loading="loading" :data="syzdjxxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="首张编号" align="center" prop="szbh">
          <template #default="scope">
            {{scope.row.syzQz + '' + scope.row.szbh + '' + scope.row.syzHz}}
          </template>
        </el-table-column>
        <el-table-column label="末张编号" align="center" prop="mzbh">
          <template #default="scope">
            {{scope.row.syzQz + '' + scope.row.mzbh + '' + scope.row.syzHz}}
          </template>
        </el-table-column>
<!--        <el-table-column label="水印纸前缀" align="center" prop="syzQz">
          <template #default="scope">
            <dict-tag :options="gz_syz_qhz" :value="scope.row.syzQz"/>
          </template>
        </el-table-column>
        <el-table-column label="水印纸后缀" align="center" prop="syzHz">
          <template #default="scope">
            <dict-tag :options="gz_syz_qhz" :value="scope.row.syzHz"/>
          </template>
        </el-table-column>
        <el-table-column label="首张号码" align="center" prop="szbh" />
        <el-table-column label="末张号码" align="center" prop="mzbh" />-->
        <el-table-column label="入库张数" align="center" prop="rkzs" />
        <el-table-column label="已使用张数" align="center" prop="syzs" />
        <el-table-column label="作废张数" align="center" prop="zfzs" />
        <el-table-column label="入库时间" align="center" prop="rksj" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.rksj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
<!--        <el-table-column label="入库操作人" align="center" prop="rkczr" />-->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
<!--            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:syzdjxx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['gongzheng:syzdjxx:remove']"></el-button>
            </el-tooltip>-->
            <el-button type="primary"  link @click="handleRecipient(scope.row, '领用')" v-hasPermi="['gongzheng:syzdjxx:receive']">领用</el-button>
            <el-button type="primary"  link @click="handleRecipient(scope.row, '分配')" v-hasPermi="['gongzheng:syzdjxx:distribute']">分配</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改水印纸入库对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
      <el-form ref="syzdjxxFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="水印纸前缀" prop="syzQz">
              <el-select v-model="form.syzQz" placeholder="请选择">
                <el-option
                  v-for="dict in gz_syz_qhz"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水印纸后缀" prop="syzHz">
              <el-select v-model="form.syzHz" placeholder="请选择">
                <el-option
                  v-for="dict in gz_syz_qhz"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首张号码" prop="szbh">
              <el-input v-model="form.szbh" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="末张号码" prop="mzbh">
              <el-input v-model="form.mzbh" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库张数" prop="jsrkzs">
              <el-input v-model="jsrkzs" placeholder="" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="dialog2.title" v-model="dialog2.visible" width="800px" append-to-body>
      <el-form ref="syzdjxxFormRef2" :model="form2" :rules="rules" label-width="120px">
        <el-form-item label="登记信息ID" prop="djxxId" style="display: none;" >
          <el-input v-model="form2.djxxId" placeholder="登记信息ID"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="可领首张编号" prop="szbh">
              <el-input v-model="form2.szbh" placeholder="请输入" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="末张编号" prop="mzbh">
              <el-input v-model="form2.mzbh" placeholder="请输入" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可领取张数" prop="klqzs">
              <el-input v-model="form2.klqzs" placeholder="请输入" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领取张数" prop="lyzs">
              <el-input-number v-model="form2.lyzs" min="0" :max="form2.klqzs" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dialog2.title=='分配'">
            <el-form-item label="领用人" prop="lyrId">
              <el-select v-model="form2.lyrId" placeholder="请选择">
                <el-option
                  v-for="dict in gzy"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
              <!-- <SelectUserByRole ref="selectUesrByRoleRef" :value="form2.lyrId" @onSelect="handleSelectUser"></SelectUserByRole> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm2">确 定</el-button>
          <el-button @click="cancel2">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Syzdjxx" lang="ts">
import { listSyzdjxx, getSyzdjxx, delSyzdjxx, addSyzdjxx, updateSyzdjxx,queryAvailable,receive,distribute } from '@/api/gongzheng/syz/syzdjxx';
import { SyzdjxxVO, SyzdjxxQuery, SyzdjxxForm } from '@/api/gongzheng/syz/syzdjxx/types';
import SelectUserByRole from '@/views/gongzheng/components/SelectUserByRole.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_syz_qhz } = toRefs<any>(proxy?.useDict('gz_syz_qhz'));
const { gzy } = toRefs<any>(proxy?.useRoleUser('gzy'));

const syzdjxxList = ref<SyzdjxxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeRksj = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const syzdjxxFormRef = ref<ElFormInstance>();

const syzdjxxFormRef2 = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog2 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SyzdjxxForm = {
  id: undefined,
  syzQz: undefined,
  syzHz: undefined,
  szbh: undefined,
  mzbh: undefined,
  rkzs: undefined,
  syzs: undefined,
  zfzs: undefined,
  rksj: undefined,
  rkczr: undefined
}

const initFormData2 = {
  djxxId: undefined,
  szbh: undefined,
  mzbh: undefined,
  klqzs: undefined,
  lyzs: undefined,
  lyrId: undefined
}

const form2 = ref({...initFormData2});

const data = reactive<PageData<SyzdjxxForm, SyzdjxxQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    syzQz: undefined,
    szbh: undefined,
    params: {
      rksj: undefined,
    }
  },
  rules: {
    id: [
      { required: true, message: "ID不能为空", trigger: "blur" }
    ],
    syzQz: [
      { required: true, message: "水印纸前缀不能为空", trigger: "blur" }
    ],
    szbh: [
      { required: true, message: "首张号码不能为空", trigger: "blur" }
    ],
    syzHz: [
      { required: true, message: "水印纸后缀不能为空", trigger: "blur" }
    ],
    mzbh: [
      { required: true, message: "末张号码不能为空", trigger: "blur" }
    ],
    lyzs: [
      { required: true, message: "领取张数不能为空", trigger: "blur" }
    ],
    lyrId: [
      { required: true, message: "领取人不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const jsrkzs =  computed({
  get() {
    // console.log(data.form.mzbh, 'mzbh')
    if (data.form.mzbh && data.form.szbh) {
      return data.form.mzbh - data.form.szbh + 1;
    }
  },
  set() {}
});

/** 查询水印纸入库列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeRksj.value, 'Rksj');
  const res = await listSyzdjxx(queryParams.value);
  syzdjxxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  syzdjxxFormRef.value?.resetFields();
}
/** 取消按钮 */
const cancel2 = () => {
  reset2();
  dialog2.visible = false;
}

/** 表单重置 */
const reset2 = () => {
  form2.value = {...initFormData2};
  syzdjxxFormRef2.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeRksj.value = ['', ''];
  queryParams.value.szbh = undefined;
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: SyzdjxxVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加水印纸入库";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: SyzdjxxVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getSyzdjxx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改水印纸入库";
}



/** 领用 分配 */
const handleRecipient = async (row?: SyzdjxxVO, title) =>{
  console.log(row, 'SyzdjxxVO')
  reset2();
  const _id = row?.id || ids.value[0]
  const res = await getSyzdjxx(_id);
  const resmx = await queryAvailable(_id);
  console.log('mx', resmx);
  const data2 = {
    djxxId: resmx.data.djxxId,
    szbh: resmx.data.szbh,
    mzbh: resmx.data.mzbh,
    klqzs: resmx.data.klqzs,
    lqzs: undefined,
    lyrId: undefined,
  }
  console.log('data2', data2);
  Object.assign(form2.value, data2);
  dialog2.visible = true;
  dialog2.title = title;
}

/** 领用 分配 提交 */
const submitForm2 = () => {

  syzdjxxFormRef2.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      // console.log('form2.value', form2.value);
      const data = {
        id: form2.value.djxxId,
        lyzs: form2.value.lyzs?form2.value.lyzs:0,
        lyrId: form2.value.lyrId
      }
      if(data.lyzs == 0){
        proxy?.$modal.alertWarning("领取张数不能为 0 !");
        buttonLoading.value = false
        return false;
      }
      // console.log('data', data);
      if (form2.value.lyrId) {
        await distribute(data).finally(() =>  buttonLoading.value = false);
      } else {
        await receive(data).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog2.visible = false;
      await getList();
    }
  });
}

/** 提交按钮 */
const submitForm = () => {

  syzdjxxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.rkzs = jsrkzs;
      console.log(form.value, 'form.value')
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSyzdjxx(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addSyzdjxx(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: SyzdjxxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除水印纸入库编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delSyzdjxx(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('gongzheng/syzdjxx/export', {
    ...queryParams.value
  }, `syzdjxx_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
