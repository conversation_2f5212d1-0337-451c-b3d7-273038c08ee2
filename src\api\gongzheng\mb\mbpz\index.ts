// 变量配置API service
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Variable, VariableQuery, VariableSaveParams } from './types';

// 查询变量列表
export const listVariables = (query: VariableQuery): AxiosPromise<{ records: Variable[]; total: number }> => {
  return request({
    url: '/mb/mbBl/list',
    method: 'get',
    params: query
  });
};

// 新增变量
export const addVariable = (data: VariableSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbBl',
    method: 'post',
    data
  });
};

// 编辑变量
export const updateVariable = (data: VariableSaveParams): AxiosPromise<any> => {
  return request({
    url: '/mb/mbBl',
    method: 'put',
    data
  });
};

// 删除变量
export const deleteVariable = (ids: string): AxiosPromise<any> => {
  return request({
    url: `/mb/mbBl/${ids}`,
    method: 'delete'
  });
};
