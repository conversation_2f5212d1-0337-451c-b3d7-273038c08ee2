export interface GzjzJdVO {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 公证员ID
   */
  gzyId?: string | number;

  /**
   * 公证员
   */
  gzyXm?: string;

  /**
   * 贷款银行（字典：gz_tc_khh）
   */
  dkxh?: string;

  /**
   * 借贷日期
   */
  jdrq?: string;

  /**
   * 到期日期
   */
  dqrq?: string;

  /**
   * 合同签署日期
   */
  htqsrq?: string;

  /**
   * 借款金额（万元）
   */
  jkje?: number | string;

  /**
   * 有无担保（0无，1有）
   */
  ywdb?: string;

  /**
   * 婚姻状况（字典：gz_dsr_hyzt）
   */
  hyzk?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}
   */
  dsrList?: string;

}

export interface JdKhVO {
  /**
   * 当事人ID
   */
  dsrId?: string | number;

  /**
   * 当事人姓名
   */
  name?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 当事人地址
   */
  addr?: string;

  /**
   * 放款人 0否 1是
   */
  isLender?: string;

  /**
   * 抵押人 0否 1是
   */
  isMortgagor?: string;

  /**
   * 借款人 0否 1是
   */
  isBorrower?: string;
}

export interface GzjzJdForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 公证员ID
   */
  gzyId?: string | number;

  /**
   * 公证员
   */
  gzyXm?: string;

  /**
   * 贷款银行（字典：gz_tc_khh）
   */
  dkxh?: string;

  /**
   * 借贷日期
   */
  jdrq?: string;

  /**
   * 到期日期
   */
  dqrq?: string;

  /**
   * 合同签署日期
   */
  htqsrq?: string;

  /**
   * 借款金额（万元）
   */
  jkje?: string | number;

  /**
   * 有无担保（0无，1有）
   */
  ywdb?: string;

  /**
   * 婚姻状况（字典：gz_dsr_hyzt）
   */
  hyzk?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}
   */
  dsrList?: string;

}

export interface GzjzJdQuery extends PageQuery {

  /**
   * 公证卷宗ID
   */
  gzjzId?: string | number;

  /**
   * 公证卷宗公证事项ID
   */
  gzjzGzsxId?: string | number;

  /**
   * 公证员ID
   */
  gzyId?: string | number;

  /**
   * 公证员
   */
  gzyXm?: string;

  /**
   * 贷款银行（字典：gz_tc_khh）
   */
  dkxh?: string;

  /**
   * 借贷日期
   */
  jdrq?: string;

  /**
   * 到期日期
   */
  dqrq?: string;

  /**
   * 合同签署日期
   */
  htqsrq?: string;

  /**
   * 借款金额（万元）
   */
  jkje?: number;

  /**
   * 有无担保（0无，1有）
   */
  ywdb?: string;

  /**
   * 婚姻状况（字典：gz_dsr_hyzt）
   */
  hyzk?: string;

  /**
   * 客户列表，结构{dsrId=当事人ID,name=姓名,phone=电话,addr=地址,isLender=放款人,isMortgagor=抵押人,isBorrower=借款人}
   */
  dsrList?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



