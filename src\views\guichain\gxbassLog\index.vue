<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="交易哈希" prop="txHash">
              <el-input v-model="queryParams.txHash" placeholder="请输入交易哈希" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="上链描述" prop="chainDec">
              <el-input v-model="queryParams.chainDec" placeholder="请输入上链描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
           <!-- <el-form-item label="业务分类" prop="bizClassify">
              <el-input v-model="queryParams.bizClassify" placeholder="请输入业务分类" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" border :data="gxbassLogList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="业务ID" align="center" prop="bizId" width="200px" show-overflow-tooltip/>
       <!-- <el-table-column label="业务分类" align="center" prop="bizClassify">
          <template #default="scope">
            <dict-tag :options="gz_chain_biz_classify" :value="scope.row.bizClassify" />
          </template>
        </el-table-column> -->
        <el-table-column label="交易哈希" align="center" prop="txHash" width="200px" />
        <el-table-column label="上链描述" align="center" prop="chainDec" width="200px" show-overflow-tooltip/>
        <el-table-column label="上链数据" align="center" prop="chainData" width="200px" show-overflow-tooltip/>
        <el-table-column label="上链文件" align="center" prop="ossId" >
          <template #default="scope">
              {{scope.row.ossId!=null?scope.row.ossId:'--'}}
            </template>
          </el-table-column>
        <el-table-column label="存证类型" align="center" prop="chainType">
          <template #default="scope">
            <dict-tag :options="gz_chain_chain_type" :value="scope.row.chainType" />
          </template>
        </el-table-column>
        <el-table-column label="虚拟通道ID" align="center" prop="channelId" width="200px" show-overflow-tooltip/>
        <el-table-column label="加密方式" align="center" prop="encryptMode">
          <template #default="scope">
          虚拟通道
          </template>
        </el-table-column>
        <el-table-column label="上链时间" align="center" prop="createTime" width="140px" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

  </div>
</template>

<script setup name="GxbassLog" lang="ts">
  import { listGxbassLog, getGxbassLog, delGxbassLog, addGxbassLog, updateGxbassLog } from '@/api/guichian/gxbassLog';
  import { GxbassLogVO, GxbassLogQuery, GxbassLogForm } from '@/api/guichian/gxbassLog/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_chain_encrypt_mode,gz_chain_biz_classify,gz_chain_chain_type } = toRefs<any>(proxy?.useDict('gz_chain_chain_type','gz_chain_encrypt_mode','gz_chain_biz_classify'));
  const gxbassLogList = ref<GxbassLogVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const gxbassLogFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GxbassLogForm = {
    id: undefined,
    chainDec: undefined,
    chainData: undefined,
    ossId: undefined,
    chainType: undefined,
    channelId: undefined,
    encryptMode: undefined,
    txHash: undefined,
    remark: undefined,
    bizId: undefined,
    bizClassify: undefined
  }
  const data = reactive<PageData<GxbassLogForm, GxbassLogQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      chainDec: undefined,
      chainData: undefined,
      ossId: undefined,
      chainType: undefined,
      channelId: undefined,
      encryptMode: undefined,
      txHash: undefined,
      bizId: undefined,
      bizClassify: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公证-桂链-上链日志列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listGxbassLog(queryParams.value);
    gxbassLogList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gxbassLogFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GxbassLogVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加公证-桂链-上链日志";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GxbassLogVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getGxbassLog(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改公证-桂链-上链日志";
  }

  /** 提交按钮 */
  const submitForm = () => {
    gxbassLogFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGxbassLog(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGxbassLog(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GxbassLogVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除公证-桂链-上链日志编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGxbassLog(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('guichain/gxbassLog/export', {
      ...queryParams.value
    }, `gxbassLog_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
