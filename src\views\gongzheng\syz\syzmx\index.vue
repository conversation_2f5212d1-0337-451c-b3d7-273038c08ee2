<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="水印纸编号" prop="syzbh">
              <el-input v-model="queryParams.syzbh" placeholder="请输入" clearable @keyup.enter="handleQuery" style="width: 240px" />
            </el-form-item>
            <el-form-item label="领用人" prop="lyrId">
              <SelectUserByRole ref="selectUesrByRoleRef" :value="queryParams.lyrId" @onSelect="handleSelectUser"></SelectUserByRole>
            </el-form-item>
            <el-form-item label="使用时间" style="width: 345px">
              <el-date-picker
                clearable
                v-model="dateRangeSysj"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="入库时间" style="width: 345px">
              <el-date-picker
                clearable
                v-model="dateRangeDjrlsj"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="作废时间" style="width: 345px" >
              <el-date-picker
                clearable
                v-model="dateRangeZfsj"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="销毁时间" style="width: 345px">
              <el-date-picker
                clearable
                v-model="dateRangeXhsj"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="使用状态" prop="syzt">
              <el-select v-model="queryParams.syzt" placeholder="请选择" clearable style="width: 240px" >
                <el-option v-for="dict in gz_syz_zt" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="公证书编号" prop="gzsBh">
              <el-input v-model="queryParams.gzsBh" placeholder="请输入" clearable @keyup.enter="handleQuery" style="width: 240px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">
                      <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['gongzheng:syzmx:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                      <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['gongzheng:syzmx:edit']">修改</el-button>
                    </el-col>-->
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['gongzheng:syzmx:remove']">删除</el-button>
          </el-col>
          <!--          <el-col :span="1.5">
                      <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['gongzheng:syzmx:export']">导出</el-button>
                    </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table border v-loading="loading" :data="syzmxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['gongzheng:syzmx:edit']"></el-button>
            </el-tooltip>
            <el-button type="primary" link @click="handleInvalidation(scope.row)" v-if="scope.row.syzt=='1'" v-hasPermi="['gongzheng:syzmx:invalidation']">作废</el-button>
            <el-button type="primary" link @click="handleDestruction(scope.row)" v-if="scope.row.syzt=='2'" v-hasPermi="['gongzheng:syzmx:destruction']" style="color: red;">销毁</el-button>
          </template>
        </el-table-column>
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="水印纸编号" align="center" prop="syzbh" />
        <el-table-column label="使用地" align="center" prop="syd" show-overflow-tooltip />
        <el-table-column label="领用人" align="center" prop="lyr" />
        <el-table-column label="领用时间" align="center" prop="lysj" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lysj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公证书编号" align="center" prop="gzsBh" show-overflow-tooltip />
        <el-table-column label="入库时间" align="center" prop="djrlsj" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.djrlsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用人" align="center" prop="syr" />
        <el-table-column label="使用时间" align="center" prop="sysj" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="作废时间" align="center" prop="zfsj" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.zfsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销毁时间" align="center" prop="xhsj" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.xhsj, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" align="center" prop="syzt">
          <template #default="scope">
            <dict-tag :options="gz_syz_zt" :value="scope.row.syzt"/>
          </template>
        </el-table-column>

      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改水印纸管理-水印纸明细对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="syzmxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="水印纸编号" prop="syzbh">
          <el-input v-model="form.syzbh" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="水印纸流水" prop="syzls">
          <el-input v-model="form.syzls" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="使用地" prop="syd">
          <el-input v-model="form.syd" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="领用人ID" prop="lyrId">
          <el-input v-model="form.lyrId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="领用人" prop="lyr">
          <el-input v-model="form.lyr" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="使用人" prop="syr">
          <el-input v-model="form.syr" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="领用时间" prop="lysj">
          <el-date-picker clearable
                          v-model="form.lysj"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用时间" prop="sysj">
          <el-date-picker clearable
                          v-model="form.sysj"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="登记信息编号" prop="djxxId">
          <el-input v-model="form.djxxId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="入库时间" prop="djrlsj">
          <el-date-picker clearable
                          v-model="form.djrlsj"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="作废时间" prop="zfsj">
          <el-date-picker clearable
                          v-model="form.zfsj"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="销毁时间" prop="xhsj">
          <el-date-picker clearable
                          v-model="form.xhsj"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用状态" prop="syzt">
          <el-select v-model="form.syzt" placeholder="请选择使用状态">
            <el-option
              v-for="dict in gz_syz_zt"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="公证书编号" prop="gzsBh">
          <el-input v-model="form.gzsBh" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Syzmx" lang="ts">
  import { listSyzmx, getSyzmx, delSyzmx, addSyzmx, updateSyzmx, invalidation, destruction } from '@/api/gongzheng/syz/syzmx';
  import { SyzmxVO, SyzmxQuery, SyzmxForm } from '@/api/gongzheng/syz/syzmx/types';
  import SelectUserByRole from '@/views/gongzheng/components/SelectUserByRole.vue'


  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_syz_zt } = toRefs<any>(proxy?.useDict('gz_syz_zt'));

  const syzmxList = ref<SyzmxVO[]>([]);

  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const dateRangeSysj = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeDjrlsj = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeZfsj = ref<[DateModelType, DateModelType]>(['', '']);
  const dateRangeXhsj = ref<[DateModelType, DateModelType]>(['', '']);


  const queryFormRef = ref<ElFormInstance>();
  const syzmxFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData: SyzmxForm = {
    syzbh: undefined,
    syzls: undefined,
    syd: undefined,
    lyrId: undefined,
    lyr: undefined,
    syrId: undefined,
    syr: undefined,
    lysj: undefined,
    sysj: undefined,
    djxxId: undefined,
    djrlsj: undefined,
    zfsj: undefined,
    xhsj: undefined,
    syzt: undefined,
    remark: undefined,
    gzsBh: undefined
  }
  const data = reactive<PageData<SyzmxForm, SyzmxQuery>>({
    form: {...initFormData},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      syzbh: undefined,
      lyrId: undefined,
      syrId: undefined,
      syzt: undefined,
      gzsBh: undefined,
      params: {
        sysj: undefined,
        djrlsj: undefined,
        zfsj: undefined,
        xhsj: undefined,
      }
    },
    rules: {
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询水印纸管理-水印纸明细列表 */
  const getList = async () => {
    loading.value = true;
    queryParams.value.params = {};
    proxy?.addDateRange(queryParams.value, dateRangeSysj.value, 'Sysj');
    proxy?.addDateRange(queryParams.value, dateRangeDjrlsj.value, 'Djrlsj');
    proxy?.addDateRange(queryParams.value, dateRangeZfsj.value, 'Zfsj');
    proxy?.addDateRange(queryParams.value, dateRangeXhsj.value, 'Xhsj');
    const res = await listSyzmx(queryParams.value);
    syzmxList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = {...initFormData};
    syzmxFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    dateRangeSysj.value = ['', ''];
    dateRangeDjrlsj.value = ['', ''];
    dateRangeZfsj.value = ['', ''];
    dateRangeXhsj.value = ['', ''];
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection: SyzmxVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 选择用户 */
  const handleSelectUser = (value) => {
    if(value) {
      queryParams.value.lyrId = value;
    }
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加水印纸明细";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row?: SyzmxVO) => {
    reset();
    const _id = row?.id || ids.value[0]
    const res = await getSyzmx(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改水印纸明细";
  }

  /** 提交按钮 */
  const submitForm = () => {
    syzmxFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateSyzmx(form.value).finally(() =>  buttonLoading.value = false);
        } else {
          await addSyzmx(form.value).finally(() =>  buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row?: SyzmxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除水印纸明细编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delSyzmx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 作废按钮操作 */
  const handleInvalidation = async (row?: SyzmxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认作废水印纸编号为"' + row.syzbh + '"的数据项？').finally(() => loading.value = false);
    await invalidation(_ids);
    proxy?.$modal.msgSuccess("作废成功");
    await getList();
  }

  /** 销毁按钮操作 */
  const handleDestruction = async (row?: SyzmxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认销毁水印纸编号为"' + row.syzbh + '"的数据项？').finally(() => loading.value = false);
    await destruction(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('gongzheng/syzmx/export', {
      ...queryParams.value
    }, `syzmx_${new Date().getTime()}.xlsx`)
  }

  onMounted(() => {
    getList();
  });
</script>
