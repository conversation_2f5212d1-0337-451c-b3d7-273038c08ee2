<!--
  页面名称：模板管理-主页面
  功能：展示模板列表及相关操作
-->
<template>
  <div class="template-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card style="min-height: 768px; max-height: 1060px;">
          <el-form ref="gzlbpzFormRef" label-width="80px">
            <el-form-item label="公证事项" prop="gzmc">
              <el-input v-model="gzmc" placeholder="请输入模板名称" clearable @input="handleChangeCategory"
                style="width:200px" />
            </el-form-item>
            <div style="min-height: 568px; max-height: 860px; overflow: hidden;">
              <el-tree class="treeMainCss" ref="gzsxTableRef" :loading="gzsxLoading" :data="gzsxList" default-expand-all
                :default-checked-keys="defaultCheckedKeys" node-key="id" highlight-current :props="defaultProps"
                @node-click="handleNodeClick">
              </el-tree>
            </div>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="18">

        <el-card v-if="gzsxId">
          <div class="search-bar">
            <div class="search-item">
              <span class="label">模板名称:</span>
              <el-input v-model="searchForm.title" placeholder="请输入" style="width: 180px;"></el-input>
            </div>
            <div class="search-item">
              <span class="label">模板类型:</span>
              <el-select v-model="searchForm.templateType" placeholder="请选择" style="width: 180px;">
                <el-option v-for="dict in gz_mb_mblx" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </div>
            <div class="search-item">
              <span class="label">文档类别:</span>
              <el-select v-model="searchForm.wdLb" placeholder="请选择" style="width: 180px;">
                <el-option v-for="dict in gz_mb_wdlb" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </div>
            <div class="search-buttons">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </div>
          </div>

          <div class="template-list">
            <div class="list-header">
              <span class="title">公证模板</span>
              <div class="header-buttons">
                <el-button type="primary" size="small" @click="handleAddTemplate">新增模板</el-button>
                <el-button type="danger" size="small" @click="handleDelete">删除</el-button>
              </div>
            </div>

            <el-table :data="templateList" v-loading="loading" border stripe @selection-change="handleSelectionChange"
              style="width: 100%">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column prop="operation" label="操作" align="center" width="250">
                <template #default="scope">
                  <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                  <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
                  <el-button type="primary" link @click="handleCopy(scope.row)">复制</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="模板名称" align="center" />
              <el-table-column prop="templateType" label="模板类型" align="center">
                <template #default="scope">
                  <dict-tag :options="gz_mb_mblx" :value="scope.row.templateType" />
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination background layout="prev, pager, next" :total="total" :current-page="currentPage"
                :page-size="pageSize" @current-change="handleCurrentChange" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增模板对话框 -->
    <el-dialog v-model="addDialogVisible" title="新增模板" width="70%" destroy-on-close>
      <div class="dialog-content">
        <div class="form-header">
          <div class="form-item">
            <span class="label">公证事项：</span>
            <span class="value">{{gzsxName}}</span>
          </div>
          <div class="form-item">
            <span class="label">A 模板名称：</span>
            <el-input v-model="templateForm.title" placeholder="请输入模板名称" style="width: 200px;"></el-input>
          </div>
          <div class="form-item">
            <span class="label">模板类型：</span>
            <el-select v-model="templateForm.templateType" placeholder="请选择模板类型" style="width:180px">
              <el-option v-for="dict in gz_mb_mblx" :key="dict.value" :label="dict.label" :value="Number(dict.value)" />
            </el-select>
          </div>
          <div class="form-item">
            <el-button type="primary" @click="handleSave">保存</el-button>
          </div>
        </div>

        <div class="template-documents" v-if="currentTemplateId">
          <div class="documents-header">
            <span>模板文档</span>
            <el-button type="primary" size="small" @click="handleAddDocument" :disabled="!currentTemplateId">
              添加文档
            </el-button>
          </div>
          <div class="documents-content">
            <el-table :data="documentList" border stripe>
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="wdMc" label="文档名称" align="center" />
              <el-table-column prop="wdLb" label="文档类别" align="center">
                <template #default="scope">
                  <dict-tag :options="gz_mb_wdlb" :value="scope.row.wdLb" />
                </template>
              </el-table-column>
              <el-table-column prop="isDefault" label="是否默认" align="center">
                <template #default="scope">
                  <span>{{ scope.row.isDefault === 1 ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="operation" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button type="primary" link size="small" @click="handleEditDocument(scope.row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="handleDeleteDocument(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- <div class="tab-container">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="系统变量" name="system">
                <div class="variable-container">
                  <div class="variable-list">
                    <div class="variable-name">
                      <el-input v-model="variableName" placeholder="变量名" />
                      <el-button type="primary" @click="handleAddVariable">添加变量</el-button>
                    </div>
                    <div class="variable-table">
                      <el-table :data="systemVariables" border stripe>
                        <el-table-column prop="type" label="变量类型" align="center" />
                        <el-table-column prop="name" label="变量名称" align="center" />
                        <el-table-column prop="operation" label="操作" align="center" width="80">
                          <template #default="scope">
                            <el-button type="primary" link>
                              添加
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="自定义变量" name="custom">
                <div class="variable-container">
                  <!-- 自定义变量内容 -->
        <!--   </div>
              </el-tab-pane>
              <el-tab-pane label="客户变量" name="client">
                <div class="variable-container">
                  <div class="client-search">
                    <div class="client-form">
                      <div class="form-item">
                        <span class="label">客户角色：</span>
                        <el-select v-model="clientForm.role" placeholder="请选择" style="width: 180px;">
                          <el-option label="申请人" value="申请人"></el-option>
                          <el-option label="代理人" value="代理人"></el-option>
                        </el-select>
                      </div>
                      <div class="form-item">
                        <span class="label">客户属性：</span>
                        <el-select v-model="clientForm.type" placeholder="请选择" style="width: 180px;">
                          <el-option label="个人" value="个人"></el-option>
                          <el-option label="企业" value="企业"></el-option>
                        </el-select>
                      </div>
                      <div class="form-item">
                        <span class="label">日期格式：</span>
                        <el-select v-model="clientForm.dateFormat" placeholder="请选择" style="width: 180px;">
                          <el-option label="无格式" value="无格式"></el-option>
                          <el-option label="中文日期" value="中文日期"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="client-checkboxes">
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.all">全选</el-checkbox>
                        <el-checkbox v-model="clientFields.name">姓名</el-checkbox>
                        <el-checkbox v-model="clientFields.gender">性别</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.nationality">民族</el-checkbox>
                        <el-checkbox v-model="clientFields.birthdate">出生日期</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.birthdateWithoutYear">出生日期(不带出生年)</el-checkbox>
                        <el-checkbox v-model="clientFields.birthplace">西藏</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.idType">证件名称</el-checkbox>
                        <el-checkbox v-model="clientFields.idNumber">证件号码</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.address">住址</el-checkbox>
                        <el-checkbox v-model="clientFields.contactAddress">联系地址</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.phone">联系电话</el-checkbox>
                        <el-checkbox v-model="clientFields.workplace">工作单位</el-checkbox>
                      </div>
                      <div class="checkbox-row">
                        <el-checkbox v-model="clientFields.title">职称</el-checkbox>
                      </div>
                    </div>
                    <div class="client-actions">
                      <el-button type="primary">确定</el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs> -->
        <!-- </div> -->
        <!--    </div> -->
      </div>
    </el-dialog>

    <!-- 编辑模板对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑模板" width="70%" destroy-on-close>
      <div class="dialog-content">
        <el-form :model="editForm" label-width="120px" class="edit-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="title">
                <el-input v-model="editForm.title" placeholder="请输入模板名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板类型" prop="templateType">
                <el-select v-model="editForm.templateType" placeholder="请选择模板类型" style="width: 100%">
                  <el-option v-for="dict in gz_mb_mblx" :key="dict.value" :label="dict.label"
                    :value="Number(dict.value)" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 文档管理区域 -->
        <div class="template-documents" style="margin-top: 20px;">
          <div class="documents-header">
            <span>模板文档</span>
            <el-button type="primary" size="small" @click="handleAddDocument">
              添加文档
            </el-button>
          </div>
          <div class="documents-content">
            <el-table :data="documentList" border stripe>
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="wdMc" label="文档名称" align="center" />
              <el-table-column prop="wdLb" label="文档类别" align="center">
                <template #default="scope">
                  <dict-tag :options="gz_mb_wdlb" :value="scope.row.wdLb" />
                </template>
              </el-table-column>
              <el-table-column prop="isDefault" label="是否默认" align="center">
                <template #default="scope">
                  <span>{{ scope.row.isDefault === 1 ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="operation" label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button type="primary" link size="small" @click="handleEditDocument(scope.row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="handleDeleteDocument(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看模板对话框 -->
    <el-dialog v-model="viewDialogVisible" title="查看模板" width="70%" destroy-on-close>
      <div class="dialog-content">
        <el-form :model="wdQueryForm" label-width="120px" class="view-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称">
                {{viewForm.title}}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="文档类别" prop="wdLb">
                <!-- <el-input :value="gz_mb_mblx.find(item => item.value == viewForm.templateType)?.label || ''" readonly /> -->
                <el-select v-model="wdQueryForm.wdLb" placeholder="请选择">
                  <el-option v-for="dict in gzwdlb" :key="dict.value" :label="dict.label"
                    :value="Number(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div class="form-item">
                <el-button type="primary" @click="handleWdQuery">查询</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>

        <!-- 文档列表区域 -->
        <div class="template-documents" style="margin-top: 20px;">
          <div class="documents-header">
            <span>模板文档</span>
          </div>
          <div class="documents-content">
            <el-table :data="documentList" border stripe>
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="wdMc" label="文档名称" align="center" />
              <el-table-column prop="wdLb" label="文档类别" align="center">
                <template #default="scope">
                  <dict-tag :options="gz_mb_wdlb" :value="scope.row.wdLb" />
                </template>
              </el-table-column>
              <el-table-column prop="isDefault" label="是否默认" align="center">
                <template #default="scope">
                  <span>{{ scope.row.isDefault === 1 ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="wdDz" label="文件" align="center">
                <template #default="scope">
                  <el-button type="primary" link size="small" v-if="scope.row.wdDz"
                    @click="handleDownload(scope.row)">下载</el-button>
                    <el-button type="text" size="small" @click="handleViewWord(scope.row)">查看</el-button>
                    <el-button type="text" size="small" @click="handleEditWord(scope.row)">编辑文档</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文档上传对话框 -->
    <el-dialog v-model="documentDialogVisible" :title="isEditingDocument ? '编辑文档' : '添加文档'" width="600px"
      destroy-on-close>
      <el-form :model="documentForm" label-width="100px">
        <el-form-item label="文档名称" required>
          <el-input v-model="documentForm.wdMc" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档类别" required>
          <el-select v-model="documentForm.wdLb" placeholder="请选择文档类别" style="width: 100%">
            <el-option v-for="dict in gz_mb_wdlb" :key="dict.value" :label="dict.label" :value="Number(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档文件" required>
          <el-upload ref="uploadRef" :action="uploadFileUrl" :headers="headers" :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList" :show-file-list="true"
            :limit="1" class="upload-demo">
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">请上传模板文档</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否默认">
          <el-radio-group v-model="documentForm.isDefault">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="documentForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="documentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDocument">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看短语对话框 -->
    <el-dialog v-model="phraseDialogVisible" title="新增内容" width="50%" destroy-on-close>
      <div class="phrase-content">
        <div class="phrase-form">
          <div class="form-item">
            <span class="label">内容：</span>
            <el-input v-model="phraseForm.content" type="textarea" :rows="8" placeholder="请输入内容"></el-input>
          </div>
          <div class="form-item">
            <span class="label">使用范围：</span>
            <div class="radio-group">
              <el-radio v-model="phraseForm.scope" label="personal">个人</el-radio>
              <el-radio v-model="phraseForm.scope" label="public">全站</el-radio>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSavePhrase">确定</el-button>
          <el-button @click="phraseDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { ComponentInternalInstance } from 'vue'
  import * as api from '@/api/gongzheng/mb/mbJcxx'
  import { MbJcxxQuery, MbJcxxForm, MbJcxxVO } from '@/api/gongzheng/mb/mbJcxx/types'
  import * as mbWdApi from '@/api/gongzheng/mb/mbWd'
  import { MbWdForm, MbWdVO, MbWdQuery } from '@/api/gongzheng/mb/mbWd/types'
  import { listTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO, GzsxQuery, GzsxForm } from '@/api/gongzheng/basicdata/gzsx/types';
  import { getToken } from '@/utils/auth'

  import { docOpenEdit, docOpenShow } from '@/views/gongzheng/doc/DocEditor'
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb, gz_mb_wdlb, gz_mb_mblx, gz_mb_classify } = toRefs<any>(proxy?.useDict('gz_gzlb', 'gz_mb_wdlb', 'gz_mb_mblx', 'gz_mb_classify'));
  // const gzsTreeRef = ref<InstanceType<typeof Tree> | null>(null);
  const gzsxList = ref<GzsxVO[]>([]);
  const gzwdlb = ref([{
    value: 1,
    label: "文书"
  },
  {
    value: 2,
    label: "笔录"
  }, {
    value: 3,
    label: "公证书"
  }, {
    value: 5,
    label: "公证书译文"
  }
  ])
  // 移除不需要的defaultChecke变量
  const gzsxLoading = ref(false)
  const gzsxTableRef = ref(null)
  const defaultProps = ref({
    children: 'children',
    label: 'title'
  })
  const gzsxId = ref(null);
  const gzsxCode = ref(null);
  const gzlbValue = ref('1');
  const gzmc = ref('')
  const selectId = ref([]);
  const defaultCheckedKeys = ref([])
  const loading = ref(true);

  // 上传相关配置
  const uploadRef = ref()
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload')
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  })
  const fileList = ref([])

  // 文档上传相关
  const documentDialogVisible = ref(false)
  const currentTemplateId = ref('')
  const isEditingDocument = ref(false)
  const editingDocumentId = ref('')
  const documentForm = reactive<MbWdForm>({
    mbId: '',
    wdMc: '',
    wdLb: 1,
    wdDz: '',
    ywId: '',
    isDefault: 0,
    remark: ''
  })
  const documentList = ref<MbWdVO[]>([])

  //添加模板文档
  const handleAddDocument = () => {
    // 重置表单
    isEditingDocument.value = false
    editingDocumentId.value = ''
    Object.assign(documentForm, {
      mbId: currentTemplateId.value,
      wdMc: '',
      wdLb: 1,
      wdDz: '',
      ywId: gzsxId.value,
      isDefault: 0,
      remark: ''
    })
    fileList.value = []
    documentDialogVisible.value = true
  }

  // 编辑文档
  const handleEditDocument = async (row : MbWdVO) => {
    try {
      const res = await mbWdApi.getMbWd(row.id)
      const documentData = res.data

      isEditingDocument.value = true
      editingDocumentId.value = documentData.id as string

      // 填充编辑表单数据
      Object.assign(documentForm, {
        id: documentData.id,
        mbId: documentData.mbId,
        wdMc: documentData.wdMc,
        wdLb: documentData.wdLb,
        wdDz: documentData.wdDz,
        ywId: documentData.ywId,
        isDefault: documentData.isDefault,
        remark: documentData.remark
      })

      // 如果有文件地址，显示现有文件
      if (documentData.wdDz) {
        fileList.value = [{
          name: documentData.wdMc,
          url: documentData.wdDz
        }]
      } else {
        fileList.value = []
      }

      documentDialogVisible.value = true
    } catch (error) {
      console.error('获取文档详情失败', error)
      ElMessage.error('获取文档详情失败，请重试')
    }
  }

  // 删除文档
  const handleDeleteDocument = async (row : MbWdVO) => {
    try {
      await proxy?.$modal.confirm(`确认删除文档"${row.wdMc}"？`)
      await mbWdApi.delMbWd(row.id)
      ElMessage.success('删除文档成功')
      await fetchDocumentList(currentTemplateId.value)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除文档失败', error)
        ElMessage.error('删除文档失败，请重试')
      }
    }
  }

  // 保存文档
  const handleSaveDocument = async () => {
    try {
      if (!documentForm.wdMc) {
        ElMessage.warning('请输入文档名称')
        return
      }
      if (!documentForm.wdDz) {
        ElMessage.warning('请上传文件')
        return
      }

      if (isEditingDocument.value) {
        // 编辑模式
        await mbWdApi.updateMbWd(documentForm)
        ElMessage.success('编辑文档成功')
      } else {
        // 新增模式
        await mbWdApi.addMbWd(documentForm)
        ElMessage.success('添加文档成功')
      }

      documentDialogVisible.value = false
      await fetchDocumentList(currentTemplateId.value, null)
    } catch (error) {
      const action = isEditingDocument.value ? '编辑' : '添加'
      console.error(`${action}文档失败`, error)
      ElMessage.error(`${action}文档失败，请重试`)
    }
  }

  // 获取文档列表
  const fetchDocumentList = async (mbId : string, wdLb : number) => {
    try {
      const params : MbWdQuery = {
        mbId: mbId,
        wdLb: wdLb
      }
      const res = await mbWdApi.listMbWd(params)
      documentList.value = res.rows || []
    } catch (error) {
      console.error('获取文档列表失败', error)
    }
  }

  // 文件上传前的处理
  const handleBeforeUpload = (file : any) => {
    const isValidSize = file.size / 1024 / 1024 < 10
    if (!isValidSize) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      return false
    }
    return true
  }

  // 文件上传成功的处理
  const handleUploadSuccess = (res : any, file : any) => {
    if (res.code === 200) {
      documentForm.wdDz = res.data.path
      documentForm.wdMc = documentForm.wdMc || res.data.fileName || file.name
      ElMessage.success('文件上传成功')
    } else {
      ElMessage.error('文件上传失败')
    }
  }

  // 文件上传失败的处理
  const handleUploadError = () => {
    ElMessage.error('文件上传失败')
  }
  //新增模板
  const handleSave = async () => {
    try {
      templateForm.ywId = gzsxId.value
      const res = await api.addMbJcxx(templateForm)
      currentTemplateId.value = res.data.id || res.data
      ElMessage.success('新增模板成功')
      // addDialogVisible.value = false
      await fetchList(gzsxId.value)
    } catch (error) {
      console.error('新增模板失败', error)
    }
  }

  // 处理更新模板
  const handleUpdate = async () => {
    try {
      const res = await api.updateMbJcxx(editForm)
      ElMessage.success('编辑模板成功')
      editDialogVisible.value = false
      await fetchList(gzsxId.value)
    } catch (error) {
      console.error('编辑模板失败', error)
    }
  }
  const handleClickTree = (data) => {
    console.log("handleClickTree 选中：", data)
    if (data) {
      nextTick(() => {
        gzsxId.value = data.id;
        gzsxCode.value = data.code;
        gzlbValue.value = data.gzlbValue;
        fetchList(gzsxId.value);
      })
    }
  }

  const getGzsxList = async () => {
    gzsxLoading.value = true;
    gzsxList.value = [];
    let params = {}
    if (gzmc.value) {
      params = {
        title: gzmc.value
      }
    }
    const res = await listTree(params);
    const data = proxy?.handleTreeCode<GzsxVO>(res.data, 'code');
    if (data) {
      gzsxList.value = data;
      // 等待树组件渲染完成后设置默认选中
      await nextTick()
      if (data[0]?.children?.[0]?.children?.[0]) {
        const defaultNode = data[0].children[0].children[0];
        // 1. 更新默认选中的ID集合（用于初始化）
        defaultCheckedKeys.value = [defaultNode.id];
        // 2. 主动调用树组件的选中方法（确保选中状态生效）
        gzsxTableRef.value?.setCurrentKey(defaultNode.id); // 高亮当前节点
        gzsxTableRef.value?.setCheckedKeys([defaultNode.id]); // 勾选节点（如果需要复选框选中）
        // 3. 触发后续逻辑（如加载右侧列表）
        handleClickTree(defaultNode);
      }
    }
    gzsxLoading.value = false;
  }

  /**
   * 将树结构扁平化为一维数组
   */
  function flattenTree(nodes : GzsxVO[]) : GzsxVO[] {
    return nodes.flatMap(node => [
      node,
      ...(node.children ? flattenTree(node.children) : [])
    ]);
  }
  const handleChangeCategory = (_value) => {
    gzmc.value = ''
    //刷新 公证事项树
    gzmc.value = _value;
    getGzsxList();

  }
  const handleNodeClick = (data, node) => {
    // 这里可以添加其他处理逻辑
    console.log('点击的节点数据:', data.id);
    nextTick(() => {
      gzsxId.value = data.id;
      gzsxCode.value = data.code;
      gzlbValue.value = data.gzlbValue;
      gzsxName.value = data.title
      fetchList(gzsxId.value);
    })
  }


  const searchForm = reactive({
    title: '',
    templateType: '',
    documentType: '',
    wdLb: null
  })

  // 模板列表相关
  const templateList = ref([])
  const total = ref(2)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const selectedTemplates = ref([])

  // 新增模板相关
  const gzsxName = ref('')
  const addDialogVisible = ref(false)
  const templateForm = reactive({
    title: '',
    templateType: 0,
    ywId: null,
    classify: 1,
    defaultStatus: 0,
    mbId: null
  })
  const templateDocuments = ref([])
  const documentPage = ref(1)
  const documentPageSize = ref(10)

  // 编辑模板相关
  const editDialogVisible = ref(false)
  const editForm = reactive<MbJcxxForm>({
    id: '',
    title: '',
    templateType: 1,
    ywId: '',
    remark: '',
    classify: 1
  })

  // 查看模板相关
  const viewDialogVisible = ref(false)
  const viewForm = reactive<MbJcxxVO>({
    id: '',
    title: '',
    templateType: 1,
    ywId: '',
    remark: '',
    classify: 1
  })

  // 变量相关
  const activeTab = ref('system')
  const variableName = ref('')
  const systemVariables = ref([
    { type: '系统变量', name: '公证事项' },
    { type: '系统变量', name: '受理日期' },
    { type: '系统变量', name: '公证编号' },
    { type: '系统变量', name: '出生日期' },
    { type: '系统变量', name: '受理地点' },
    { type: '系统变量', name: '公证员' }
  ])

  // 客户变量相关
  const clientForm = reactive({
    role: '申请人',
    type: '个人',
    dateFormat: '无格式'
  })
  const clientFields = reactive({
    all: false,
    name: false,
    gender: false,
    nationality: false,
    birthdate: false,
    birthdateWithoutYear: false,
    birthplace: false,
    idType: false,
    idNumber: false,
    address: false,
    contactAddress: false,
    phone: false,
    workplace: false,
    title: false
  })

  // 短语对话框相关
  const phraseDialogVisible = ref(false)
  const phraseForm = reactive({
    content: '',
    scope: 'personal'
  })


  // 处理搜索
  const handleSearch = () => {
    // 实际搜索逻辑
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.title = ''
    searchForm.templateType = ''
    searchForm.documentType = ''
    searchForm.wdLb = ''
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection : any[]) => {
    selectedTemplates.value = selection
  }

  // 处理分页变化
  const handleCurrentChange = async (val : number) => {
    currentPage.value = val
    // 加载对应页的数据
    await fetchList(gzsxId.value)
  }

  // 处理新增模板
  const handleAddTemplate = () => {
    templateForm.title = '';
    templateForm.templateType = 0;
    currentTemplateId.value = null;
    addDialogVisible.value = true
  }

  // 处理编辑模板
  const handleEdit = async (row : any) => {
    try {
      const res = await api.getMbJcxx(row.id)
      const templateData = res.data

      // 填充编辑表单数据
      editForm.id = templateData.id
      editForm.title = templateData.title
      editForm.templateType = templateData.templateType
      editForm.ywId = templateData.ywId
      editForm.remark = templateData.remark
      editForm.classify = templateData.classify

      // 设置当前模板ID并加载文档列表
      currentTemplateId.value = templateData.id
      await fetchDocumentList(templateData.id)

      editDialogVisible.value = true
    } catch (error) {
      console.error('获取模板详情失败', error)
      ElMessage.error('获取模板详情失败，请重试')
    }
  }
  const handleEditWord = (row) => {
    if (row.wdDz) {
      docOpenEdit(row.wdDz)
    }
  }
  const handleViewWord = (row) => {
    if (row.wdDz) {
      docOpenShow(row.wdDz)
    }
  }
  // 处理查看模板
  const handleView = async (row : any) => {
    try {
      const res = await api.getMbJcxx(row.id)
      const templateData = res.data

      // 填充查看表单数据
      viewForm.id = templateData.id
      viewForm.title = templateData.title
      viewForm.templateType = templateData.templateType
      viewForm.ywId = templateData.ywId
      viewForm.remark = templateData.remark
      viewForm.classify = templateData.classify

      // 设置当前模板ID并加载文档列表
      currentTemplateId.value = templateData.id
      await fetchDocumentList(templateData.id)

      viewDialogVisible.value = true
    } catch (error) {
      console.error('获取模板详情失败', error)
      ElMessage.error('获取模板详情失败，请重试')
    }
  }

  // 处理下载模板
  const handleDownload = async (row : any) => {
    if (row.wdOssId == null) {
      ElMessage.error('文件不存在')
    } else {
      proxy?.$download.oss(row.wdOssId);
    }

  }
  // 复制
  const handleCopy = (row : any) => {
    ElMessage.info(`开发中`)
  }
  // 处理删除模板
  const handleDelete = async () => {
    if (selectedTemplates.value.length === 0) {
      ElMessage.warning('请选择要删除的模板')
      return
    }
    console.log(selectedTemplates.value)
    const idArray = selectedTemplates.value.map(item => item.id);
    await proxy?.$modal.confirm('确认删除选中的模板？').finally(() => loading.value = false);
    await api.delMbJcxx(idArray)
    proxy?.$modal.msgSuccess("删除成功");
    await fetchList(gzsxId.value);
  }
  //模板列表查询
  const fetchList = async (ywId) => {
    loading.value = true;
    templateList.value = [];
    const params : MbJcxxQuery = {
      title: searchForm.title,
      ywId: ywId,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      classify: 1
    }
    const res = await api.listMbJcxx(params)
    templateList.value = res.rows
    total.value = res.total
    loading.value = false;
  }
  const wdQueryForm = reactive<MbWdQuery>({
    wdLb: null
  })
  const handleWdQuery = async () => {
    await fetchDocumentList(currentTemplateId.value, wdQueryForm.wdLb)
  }
  // 组件挂载时
  onMounted(() => {
    //公证事项树 加载
    getGzsxList()

  })
</script>

<style scoped>
  .template-container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .search-container {
    display: flex;
    height: 100%;
  }

  .left-container {
    width: 300px;
    border-right: 1px solid #dcdfe6;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #fff;
  }

  .tree-header {
    padding: 10px;
    font-weight: bold;
    border-bottom: 1px solid #dcdfe6;
  }

  .tree-search {
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
  }

  .tree-content {
    flex: 1;
    overflow: auto;
    padding: 10px;
  }

  .right-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #fff;
  }

  .search-bar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px;
    /* background-color: #f5f7fa; */
    border-radius: 4px;
    margin-bottom: 10px;
  }

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .label {
    margin-right: 5px;
    white-space: nowrap;
  }

  .search-buttons {
    margin-left: auto;
  }

  .template-list {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .title {
    font-weight: bold;
    font-size: 16px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-content {
    display: flex;
    flex-direction: column;
  }

  .form-header {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 10px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .template-content {
    display: flex;
    flex-direction: column;
    height: 500px;
  }

  .template-documents {
    height: 200px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
  }

  .documents-header {
    padding: 10px;
    font-weight: bold;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .documents-content {
    flex: 1;
    overflow: auto;
  }

  .pagination-mini {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-top: 1px solid #dcdfe6;
  }

  .tab-container {
    flex: 1;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .variable-container {
    height: 250px;
    padding: 10px;
  }

  .variable-list {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .variable-name {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .variable-name .el-input {
    margin-right: 10px;
  }

  .variable-table {
    flex: 1;
    overflow: auto;
  }

  .client-search {
    display: flex;
    flex-direction: column;
  }

  .client-form {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }

  .client-checkboxes {
    margin-bottom: 20px;
  }

  .checkbox-row {
    display: flex;
    margin-bottom: 10px;
  }

  .checkbox-row .el-checkbox {
    margin-right: 30px;
  }

  .client-actions {
    display: flex;
    justify-content: flex-end;
  }

  .phrase-content {
    padding: 20px;
  }

  .phrase-form {
    display: flex;
    flex-direction: column;
  }

  .radio-group {
    display: flex;
  }

  .radio-group .el-radio {
    margin-right: 20px;
  }

  .edit-form .el-form-item {
    margin-bottom: 20px;
  }

  .view-form .el-form-item {
    margin-bottom: 20px;
  }

  .view-form .el-input {
    background-color: #f5f7fa;
  }

  .view-form .el-textarea .el-textarea__inner {
    background-color: #f5f7fa;
  }
</style>
