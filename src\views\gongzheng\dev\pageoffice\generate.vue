<template>
  <div class="pageoffice-demo">
    <!-- 统一文档处理 DEMO 区域 -->
    <el-card style="margin-bottom: 30px;">
      <template #header>
        <span>统一文档处理 DEMO</span>
      </template>
      <el-form :model="processForm" class="process-form" @submit.prevent>
        <el-form-item label="必填：文档类型">
          <el-input v-model="processForm.type" placeholder="请输入文档类型" />
        </el-form-item>
        <el-form-item label="必填：业务ID">
          <el-input v-model="processForm.bizId" placeholder="业务ID" />
        </el-form-item>
        <el-form-item label="必填：附件类别">
          <el-input v-model="processForm.fjlb" placeholder="请输入附件类别" />
          <span style="font-weight: bold;color:red;">说明：（文档拟定1 证据材料2 笔录3 代书(文书)4 其他 99）</span>
        </el-form-item>
        <el-form-item label="动作">
          <el-input v-model="processForm.action" placeholder="请输入动作" />
          <span style="font-weight: bold;color:red;">说明： 默认编辑 edit 可选 view</span>
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 10px;">
        <span style="font-size: 13px;">自定义参数：</span>
        <el-input type="textarea" v-model="processForm.extraParams"></el-input>
        <!-- <el-button size="small" type="primary" @click="addCustomParam">添加参数</el-button> -->
      </div>
      <!--  <div v-for="(item, idx) in processForm.extraParams" :key="idx"
        style="display: flex; align-items: center; margin-bottom: 6px;">
        <el-input v-model="item.key" placeholder="参数名" style="width: 100px; margin-right: 6px;" />
        <el-input v-model="item.value" placeholder="参数值" style="width: 500px; margin-right: 6px;" />
        <el-button size="small" type="danger" @click="removeCustomParam(idx)">删除</el-button>
      </div> -->
      <el-button type="success" @click="ConvertFile" :disabled="buttonDisabled">调用统一文档处理接口</el-button>
      <el-button type="warning" @click="resetTask" :disabled="!buttonDisabled" style="margin-left: 10px;">重置任务</el-button>
      <el-button type="info" @click="testConnection" style="margin-left: 10px;">测试连接</el-button>
      <el-alert v-if="processResultMsg" :title="processResultMsg" type="info" show-icon style="margin: 10px 0;" />
      <el-descriptions v-if="processResultObj" :column="1" border>
        <el-descriptions-item v-for="(val, key) in processResultObj" :key="key"
          :label="key">{{ val }}</el-descriptions-item>
      </el-descriptions>

    </el-card>
    <div id="progressBarContainer">
      <div id="progressBar" ref="progressBar"></div>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { filemakerctrl, POBrowser } from "js-pageoffice";
  import {
    processDocument2
  } from '@/api/gongzheng/dev/pageoffice';
  import type { UnifiedDocumentVO } from '@/api/gongzheng/dev/pageoffice/types';
  const extraParams = {
    'dsrId': '13',
    'dsrLx': '1'
  };

  // 统一文档处理 DEMO 相关
  const processForm = reactive({
    extraParams: JSON.stringify(extraParams),
    bizId: '1946873914006556673',
    type: 'DSR_INFO',
    fjlb: '3',
    action: 'generate'
  });
  const buttonDisabled = ref(false);
  const progressBar = ref(null);
  const processLoading = ref(false);
  const processResultMsg = ref('');
  const processResultObj = ref<Record<string, any> | null>(null);
  import { getToken } from '@/utils/auth';
  function generateTaskId(randomLength = 8) {
    // 获取当前时间戳（毫秒级）
    const timestamp = Date.now();

    // 定义随机字符串的字符集
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    // 生成指定长度的随机字符串
    for (let i = 0; i < randomLength; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      randomString += chars.charAt(randomIndex);
    }

    // 组合并返回结果
    return `${timestamp}_${randomString}`;
  }
  function ConvertFile() {
    // 重置错误消息
    processResultMsg.value = '';

    const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;
    console.log('PageOffice Base URL:', baseUrl);

    // 配置PageOffice连接参数
    try {
      POBrowser.setProxyBaseAPI(baseUrl);
      POBrowser.setHeader("Authorization", "Bearer " + getToken());
      POBrowser.setStorage("Admin-Token", getToken());
      POBrowser.setHeader("clientid", import.meta.env.VITE_APP_CLIENT_ID);

      // 设置连接超时时间（可选）
      if (POBrowser.setConnectTimeout) {
        POBrowser.setConnectTimeout(30000); // 30秒超时
      }

      console.log('PageOffice configuration completed');
    } catch (configError) {
      console.error('PageOffice configuration error:', configError);
      processResultMsg.value = `配置错误: ${configError.message}`;
      return;
    }

    buttonDisabled.value = true;

    // 生成唯一任务ID
    const taskId = generateTaskId();

    // 构造符合application/x-www-form-urlencoded格式的参数
    const formParams = new URLSearchParams();
    formParams.append('type', processForm.type);
    formParams.append('bizId', processForm.bizId);
    formParams.append('fjlb', processForm.fjlb);
    formParams.append('action', processForm.action);
    formParams.append('taskId', taskId);

    console.log('生成参数', { ...processForm, taskId })

    // 处理extraParams - 将JSON字符串解析为对象，然后扁平化传递
    try {
      const extraParamsObj = processForm.extraParams ? JSON.parse(processForm.extraParams) : {};
      // 将extraParams中的每个键值对作为独立参数传递
      Object.entries(extraParamsObj).forEach(([key, value]) => {
        formParams.append(`extraParams[${key}]`, String(value));
      });
    } catch (error) {
      console.error('解析extraParams失败:', error);
      // 如果解析失败，可以传递空的extraParams
    }

    console.log('Request params:', formParams.toString());
    
    // 为保存接口也设置taskId
    const saveParams = new URLSearchParams();
    saveParams.append('taskId', taskId);
    console.log('Save params:', saveParams.toString());

    // 设置用于保存文件的服务器端controller地址,该地址需从"/"开始，指向服务器端根目录
    filemakerctrl.SaveFilePage = `/wordgenerate/document/generateSave?${saveParams.toString()}`;

    filemakerctrl.CallFileMaker({
      // url：指向服务器端FileMakerCtrl打开文件的controller地址，该地址需从"/"开始，指向服务器端根目录
      url: `/wordgenerate/document/generateBanked?${formParams.toString()}`,
      success: (res) => {//res：获取服务器端fs.setCustomSaveResult设置的保存结果
        console.log(`Task ${taskId} completed successfully:`, res);
        setProgress(100);
        buttonDisabled.value = false;
        processResultMsg.value = `任务 ${taskId} 执行成功,返回 ：${res} `;
      },
      progress: (pos) => {
        console.log(`Task ${taskId} running ${pos}%`);
        setProgress(pos);
      },
      error: (msg) => {
        console.log(`Task ${taskId} error occurred:`, msg);
        buttonDisabled.value = false;

        // 根据错误类型提供具体的解决建议
        let errorMessage = `任务 ${taskId} 执行失败: ${msg}`;
        let suggestion = '';

        if (msg.includes('Connection occurred an unexpected error') || msg.includes('0x00010')) {
          suggestion = `
            连接错误解决建议：
            1. 检查网络连接是否正常
            2. 确认PageOffice服务器地址配置正确: ${import.meta.env.VITE_PAGE_OFFICE_BASE_API}
            3. 如在企业网络环境，请联系IT部门检查防火墙和代理设置
            4. 尝试点击"重置任务"按钮后重试
            5. 如问题持续，请尝试重启浏览器或重新安装PageOffice客户端
          `;
        } else if (msg.includes('The same task is running')) {
          suggestion = `
            任务冲突解决建议：
            1. 请稍等片刻，等待上一个任务完成
            2. 点击"重置任务"按钮清理状态
            3. 确保没有其他页面在同时执行类似操作
          `;
        } else if (msg.includes('服务器端FileMaker页面可能未正确创建FileMaker对象')) {
          suggestion = `
            服务器端响应错误解决建议：
            1. 后端需要创建并返回 FileMakerCtrl 对象的HTML内容
            2. 检查后端是否正确实现了以下逻辑：
               - 创建 FileMakerCtrl 实例
               - 创建 WordDocumentWriter 实例
               - 设置数据区域和填充数据
               - 调用 fillDocument() 方法
               - 返回 fmCtrl.getHtml() 的内容
            3. 确认后端响应的 Content-Type 为 text/html
            4. 检查后端日志确认是否有异常
          `;
        }

        processResultMsg.value = errorMessage + (suggestion ? '\n\n' + suggestion : '');
      },
    });
  }
  function setProgress(percent) {
    progressBar.value.style.width = percent + "%";
    progressBar.value.innerText = percent + "%";
  }

  // 重置任务状态
  function resetTask() {
    buttonDisabled.value = false;
    processResultMsg.value = '';
    processResultObj.value = null;
    setProgress(0);
    console.log('Task status reset');
  }

  // 测试连接
  function testConnection() {
    processResultMsg.value = '正在测试连接...';
    const baseUrl = import.meta.env.VITE_PAGE_OFFICE_BASE_API;

    // 测试基本配置
    console.log('Testing PageOffice connection...');
    console.log('Base URL:', baseUrl);
    console.log('Token:', getToken() ? 'Present' : 'Missing');
    console.log('Client ID:', import.meta.env.VITE_APP_CLIENT_ID);

    processResultMsg.value = `连接测试完成
    - PageOffice服务器: ${baseUrl}
    - 认证Token: ${getToken() ? '已配置' : '未配置'}
    - 客户端ID: ${import.meta.env.VITE_APP_CLIENT_ID || '未配置'}
    - 浏览器: ${navigator.userAgent}

    如果仍有问题，请检查：
    1. PageOffice客户端是否已安装
    2. 浏览器是否允许插件运行
    3. 网络连接是否正常`;
  }
</script>

<style scoped>
  #progressBarContainer {
    width: 500px;
    background-color: #e0e0e0;
    border-radius: 5px;
    padding: 3px;
    margin: 10px auto;
  }

  #progressBar {
    height: 20px;
    width: 0%;
    background-color: #76b900;
    border-radius: 5px;
    text-align: center;
    line-height: 20px;
    color: white;
  }

  .pageoffice-demo {
    max-width: 800px;
    margin: 40px auto;
  }

  .toolbar {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .result-area {
    margin-top: 20px;
  }

  .process-form {
    margin-bottom: 10px;
  }
</style>
