<template>
  <gz-dialog v-model="visible" :title="title" @closed="closed" append-to-body>
    <BhLogList :gzjz-id="gzjzId" />

    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BhLogList from './BhLogList.vue';

interface Props {
  modelValue: boolean;
  title?: string;
  gzjzId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '驳回日志'
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const close = () => {
  visible.value = false
}

const closed = () => {}

</script>
