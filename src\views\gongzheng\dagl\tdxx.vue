<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
      <el-form-item label="受理人：" prop="handler">
        <el-input v-model="queryParams.handler" placeholder="请输入受理人" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="调档操作人：" prop="operator">
        <el-input v-model="queryParams.operator" placeholder="请输入调档操作人" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="申请时间：" prop="applyDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
        至
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="handleAdvancedSearch">高级查询</el-button>
      </el-form-item>
    </el-form>

    <!-- 调档信息列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">调档信息</div>
      </div>

      <el-table
        v-loading="loading"
        :data="archiveList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="受理人" align="center" prop="handler" />
        <el-table-column label="申请日期" align="center" prop="applyDate" width="180" />
        <el-table-column label="公证书号" align="center" prop="certNumber" />
        <el-table-column label="审批人" align="center" prop="approver" />
        <el-table-column label="归还日期" align="center" prop="returnDate" width="180" />
        <el-table-column label="档案流程" align="center" prop="archiveProcess" />
        <el-table-column label="操作档案移交人员" align="center" prop="transferPerson" />
        <el-table-column label="操作档案归还人员" align="center" prop="returnPerson" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>首页</span>
          <span>上一页</span>
          <el-input v-model="currentPage" class="page-input" />
          <span>共 {{ totalPages }} 页</span>
          <span>下一页</span>
          <span>尾页</span>
          <el-select v-model="pageSize" class="page-size-select">
            <el-option :value="10" label="10" />
          </el-select>
        </div>
        <div class="pagination-count">
          {{ startIndex }} - {{ endIndex }} 共 {{ total }} 条
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 加载状态
const loading = ref(false)

// 查询参数
const queryParams = reactive({
  handler: '',
  operator: '',
  startDate: '',
  endDate: '',
  pageNum: 1,
  pageSize: 10
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value + 1)
const endIndex = computed(() => Math.min(currentPage.value * pageSize.value, total.value))

// 档案列表数据
const archiveList = ref([
  {
    handler: '黄锡光',
    applyDate: '2018年11月09日',
    certNumber: '(2018) 桂东博证字第8306号',
    approver: '',
    returnDate: '',
    archiveProcess: '受理',
    transferPerson: '',
    returnPerson: ''
  },
  {
    handler: '廖梅发',
    applyDate: '2018年11月08日',
    certNumber: '(2018) 桂东博证字第7941号',
    approver: '刘北辛',
    returnDate: '',
    archiveProcess: '完成',
    transferPerson: '廖梅发',
    returnPerson: ''
  }
])

// 查询档案列表
const getList = () => {
  loading.value = true
  // 这里应该是实际的API调用
  setTimeout(() => {
    // 模拟API返回数据
    archiveList.value = [
      {
        handler: '黄锡光',
        applyDate: '2018年11月09日',
        certNumber: '(2018) 桂东博证字第8306号',
        approver: '',
        returnDate: '',
        archiveProcess: '受理',
        transferPerson: '',
        returnPerson: ''
      },
      {
        handler: '廖梅发',
        applyDate: '2018年11月08日',
        certNumber: '(2018) 桂东博证字第7941号',
        approver: '刘北辛',
        returnDate: '',
        archiveProcess: '完成',
        transferPerson: '廖梅发',
        returnPerson: ''
      }
    ]
    total.value = 2
    loading.value = false
  }, 300)
}

// 搜索按钮操作
const handleQuery = () => {
  currentPage.value = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.handler = ''
  queryParams.operator = ''
  queryParams.startDate = ''
  queryParams.endDate = ''
  handleQuery()
}

// 高级查询
const handleAdvancedSearch = () => {
  ElMessage.info('高级查询')
  // 实际高级查询逻辑
}

// 查看详情
const handleDetail = (row: any) => {
  ElMessage.success(`查看详情: ${row.certNumber}`)
  // 实际查看详情逻辑
}

// 组件挂载时
onMounted(() => {
  // 初始加载数据
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 15px;
}

.search-form {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-input {
  width: 50px;
}

.page-size-select {
  width: 80px;
  margin-left: 10px;
}

.pagination-count {
  font-size: 14px;
  color: #606266;
}
</style> 