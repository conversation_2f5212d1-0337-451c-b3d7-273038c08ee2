<template>
  <el-dialog v-model="props.visible" title="新增证据" width="500px" @close="reset">
    <div>
      <el-tree-select v-model="checkedTypes" :data="gzsxTreeOptions" placeholder="请选择证据材料" filterable clearable
        show-checkbox check-strictly multiple :render-after-expand="false" node-key="id" :props="treeProps"
        style="width: 100%" />
    </div>
    <div class="custom-add">
      <el-input v-model="customTypeName" placeholder="自定义证据名称" style="width: 70%" />
      <el-button type="primary" icon="el-icon-plus" @click="addCustomType" :disabled="!customTypeName">+</el-button>
    </div>
    <div class="dialog-footer" style="margin-top: 16px;">
      <el-button @click="reset">重置</el-button>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { addEvidenceType, getEvidenceMaterialTree } from '@/api/gongzheng/gongzheng/zjcl/index';

  const props = defineProps<{
    visible : boolean;
    allTypes : any[];
    gzjzId : number;
  }>();
  const emits = defineEmits(['update:visible', 'confirm']);
  const checkedTypes = ref<string[]>([]);
  const customTypeName = ref('');
  const gzsxTreeOptions = ref<any[]>([]);
  const treeProps = {
    value: 'id',
    label: 'title',
    children: 'children',
    disabled: 'disabled'
  };

  watch(() => props.visible, async (val) => {
    if (val) {
      reset();
      await fetchTree();
    }
  });

  function buildTree(list : any[]) {
    const map : Record<string, any> = {};
    list.forEach(item => map[item.treeCode] = { ...item, children: [] });
    const tree : any[] = [];
    list.forEach(item => {
      if (item.parentCode && map[item.parentCode]) {
        map[item.parentCode].children.push(map[item.treeCode]);
      } else {
        tree.push(map[item.treeCode]);
      }
    });
    return tree;
  }

  const fetchTree = async () => {
     console.log(props.gzjzId)
    const res = await getEvidenceMaterialTree();
    gzsxTreeOptions.value = buildTree(res.data || []);
  };

  const addCustomType = async () => {
    if (!customTypeName.value) return;
    console.log(props.gzjzId)
    await addEvidenceType({ zmmc: customTypeName.value, gzjzId: props.gzjzId });
    customTypeName.value = '';
    emits('confirm'); // 触发刷新
  };
  const confirm = async () => {
    // 批量添加选中的证据材料
    if (checkedTypes.value.length) {
      for (const id of checkedTypes.value) {
        await addEvidenceType({ zmmc: getLabelById(id), gzjzId: props.gzjzId  });
      }
    }
    emits('confirm', checkedTypes.value);
    emits('update:visible', false);
  };
  const getLabelById = (id : string) => {
    // 遍历树找到label
    const find = (nodes : any[]) : string => {
      for (const node of nodes) {
        if (node.id === id) return node.title;
        if (node.children) {
          const res = find(node.children);
          if (res) return res;
        }
      }
      return '';
    };
    return find(gzsxTreeOptions.value);
  };
  const reset = () => {
    checkedTypes.value = [];
    customTypeName.value = '';
  };
  const close = () => {
    emits('update:visible', false);
  };

  onMounted(fetchTree);
</script>

<style scoped>
  .custom-add {
    display: flex;
    align-items: center;
    margin: 16px 0;
  }
</style>
