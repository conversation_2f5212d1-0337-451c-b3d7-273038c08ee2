<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="公证类别" prop="status">
              <el-select v-model="queryParams.notarizationCategory" placeholder="请选择公证类别">
                <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:gzlbpz:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzlbpzList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="公证类别" align="center" prop="notarizationCategory">
          <template #default="scope">
            <dict-tag :options="gz_gzlb" :value="scope.row.notarizationCategory" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="事项配置" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzlbpz:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzlbpz:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公证类别配置对话框 -->
    <el-drawer :title="dialog.title" v-model="dialog.visible" size="80%" append-to-body>
      <el-form ref="gzlbpzFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公证类别" prop="notarizationCategory">
          <el-select v-model="form.notarizationCategory" placeholder="请选择公证类别" style="width: 200px;">
            <el-option v-for="dict in gz_gzlb" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公证事项" prop="selectId">
          <GzsxTree v-if="dialog.visible" ref="gzsTreeRef" :selectId="form.selectId" @onSave="handleSave"></GzsxTree>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="Gzlbpz" lang="ts">
  import { listGzlbpz, getGzlbpz, delGzlbpz, addGzlbpz, updateGzlbpz } from '@/api/gongzheng/basicdata/gzlbpz';
  import { GzlbpzVO, GzlbpzQuery, GzlbpzForm } from '@/api/gongzheng/basicdata/gzlbpz/types';
  import GzsxTree from '@/views/gongzheng/basicdata/gzsx/components/gzsx_tree.vue';
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_gzlb } = toRefs<any>(proxy?.useDict('gz_gzlb'));
  const gzlbpzList = ref<GzlbpzVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const gzlbpzFormRef = ref<ElFormInstance>();
  const gzsTreeRef = ref(null);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzlbpzForm = {
    notarizationCategory: undefined,
    selectId: undefined,
    id: undefined
  }
  const data = reactive<PageData<GzlbpzForm, GzlbpzQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      notarizationCategory: null,
      pageSize: 10,
      params: {
      }
    },
    rules: {
      notarizationCategory: [
        { required: true, message: "公证类别不能为空", trigger: "blur" }
      ],
      selectId: [
        { required: true, message: "请选择公证事项", trigger: "change" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公证类别配置列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listGzlbpz(queryParams.value);
    gzlbpzList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzlbpzFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzlbpzVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加公证类别配置";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzlbpzVO) => {
    reset();
    const _notarizedMattersId = row?.id || ids.value[0]
    const res = await getGzlbpz(_notarizedMattersId);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改公证类别配置";
  }

  /** 提交按钮 */
  const submitForm = () => {
    gzlbpzFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzlbpz(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzlbpz(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzlbpzVO) => {
    const _notarizedMattersIds = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除公证类别配置编号为"' + _notarizedMattersIds + '"的数据项？').finally(() => loading.value = false);
    await delGzlbpz(_notarizedMattersIds);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzlbpz/export', {
      ...queryParams.value
    }, `gzlbpz_${new Date().getTime()}.xlsx`)
  }

  const handleSave = (data) => {
    form.value.selectId = data;
  }
  onMounted(() => {
    getList();
  });
</script>
