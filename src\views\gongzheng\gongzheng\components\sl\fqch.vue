<template>
  <div class="fqch-container">
    <div class="page-header">
      <h2>待核查人员</h2>
      <div class="header-tip">请公证员对下列内容进行确认</div>
    </div>

    <!-- 人员信息表格 -->
    <div class="table-container">
      <el-table :data="personData" border style="width: 100%" v-loading="loading">
        <el-table-column type="index" width="50" align="center" />
        <el-table-column prop="name" label="名称" align="center">
          <template #default="{ row }">
            {{ formatPersonName(row) }}
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" align="center" width="100">
          <template #default="{ row }">
            {{ formatGender(row) }}
          </template>
        </el-table-column>
        <el-table-column prop="idType" label="证件类型" align="center" width="180">
          <template #default="{ row }">
            <dict-tag v-if="row.dsrLx==='1'" :options="gz_gr_zjlx" :value="row.certificateType || row.idType" />
            <dict-tag v-else-if="row.dsrLx==='2'" :options="gz_jg_zjlx" :value="row.certificateType || row.idType" />
            <span v-else>{{ row.certificateType || row.idType }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="idNumber" label="证件号码" align="center" width="220">
          <template #default="{ row }">
            {{ formatIdNumber(row) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空白区域 -->
    <div class="blank-area"></div>

    <!-- 确认选项 -->
    <div class="confirm-section">
      <div class="confirm-title">在公证机构申请中，应当包括对下列内容的确认：</div>

      <div class="confirm-options">
        <div class="option-item">
          <el-checkbox v-model="confirmOptions.all" @change="handleCheckAll">全选</el-checkbox>
        </div>

        <div class="option-item">
          <el-checkbox v-model="confirmOptions.option1">（一）经人脸识别设备或者其他身份验证系统确定人证同一；</el-checkbox>
        </div>

        <div class="option-item">
          <el-checkbox v-model="confirmOptions.option2">（二）申请人与申请公证的事项有利害关系；</el-checkbox>
        </div>

        <div class="option-item">
          <el-checkbox v-model="confirmOptions.option3">（三）申请人之间对申请公证的事项无争议；</el-checkbox>
        </div>

        <div class="option-item">
          <el-checkbox v-model="confirmOptions.option4">（四）申请公证的事项符合《公证法》第十一条规定的范围；</el-checkbox>
        </div>

        <div class="option-item">
          <el-checkbox v-model="confirmOptions.option5">（五）申请公证的事项符合《公证法》第二十五条的规定和该公证机构在其执业区域内可以受理公证业务的范围。</el-checkbox>
        </div>
      </div>

      <div class="warning-tip">
        前款确认结果有一项不符合规定的，办证系统应当确定公证申请不予办理。
      </div>
    </div>

    <!-- 操作按钮 -->
   <!-- <div class="action-buttons">
      <el-button type="primary" @click="handleSubmit" :disabled="!isAllChecked">确认</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, inject, onMounted, getCurrentInstance, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ComponentInternalInstance, Ref } from 'vue'
import type { GzjzDsrVO, GzjzDsrQuery } from '@/api/gongzheng/gongzheng/gzjzDsr/types'
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr'
import { initialKernel } from '@/api/gongzheng/gongzheng/gzjzJbxx'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_gr_zjlx, gz_jg_zjlx } = toRefs<any>(proxy?.useDict('gz_jg_zjlx', 'gz_gr_zjlx'));

// 获取当前选中的记录ID (从父组件传入)
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null))

// 人员数据
const personData = ref<GzjzDsrVO[]>([])
const loading = ref(false)

// 确认选项
const confirmOptions = reactive({
  all: true,
  option1: true,
  option2: true,
  option3: true,
  option4: true,
  option5: true
})

// 计算是否全部选中
const isAllChecked = computed(() => {
  return confirmOptions.option1 &&
         confirmOptions.option2 &&
         confirmOptions.option3 &&
         confirmOptions.option4 &&
         confirmOptions.option5
})

// 获取当事人列表
const getPartyPersonList = async () => {
  if (!currentRecordId.value) {
    ElMessage.warning('未获取到卷宗信息')
    return
  }

  try {
    loading.value = true
    const query: GzjzDsrQuery = {
      gzjzId: currentRecordId.value,
      pageNum: 1,
      pageSize: 1000
    }
    const res = await listGzjzDsrByGzjz(query)
    if (res.code === 200) {
      personData.value = res.rows || []
      if (personData.value.length === 0) {
        ElMessage.info('当前卷宗暂无当事人信息')
      }
    } else {
      ElMessage.error('获取当事人信息失败：' + (res.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取当事人列表失败:', error)
    ElMessage.error('获取当事人信息失败')
  } finally {
    loading.value = false
  }
}

// 格式化姓名 - 兼容多种字段
const formatPersonName = (row: GzjzDsrVO) => {
  return row.name || row.xm || '未知'
}

// 格式化性别 - 兼容多种字段
const formatGender = (row: GzjzDsrVO) => {
  const gender = row.gender || row.sex || row.xb
  if (gender === '1' || gender === 1) return '男'
  if (gender === '2' || gender === 2) return '女'
  if (gender === '男' || gender === '女') return gender
  return gender || '未知'
}

// 格式化证件号码 - 兼容多种字段
const formatIdNumber = (row: GzjzDsrVO) => {
  return row.idNumber || row.certificateNo || row.zjhm || ''
}

// 监听全选变化
const handleCheckAll = (val: boolean) => {
  confirmOptions.option1 = val
  confirmOptions.option2 = val
  confirmOptions.option3 = val
  confirmOptions.option4 = val
  confirmOptions.option5 = val
}

// 监听各选项变化，更新全选状态
const updateAllChecked = () => {
  confirmOptions.all = isAllChecked.value
}

// 提交确认 - 暴露给父组件调用
const handleSubmit = async () => {
  if (!isAllChecked.value) {
    ElMessage.warning('请确认所有选项')
    return false
  }

  if (!currentRecordId.value) {
    ElMessage.error('未获取到卷宗信息')
    return false
  }

  try {
    await ElMessageBox.confirm('确认提交初核申请？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用发起初核接口
    const res = await initialKernel({ id: currentRecordId.value })
    if (res.code === 200) {
      ElMessage.success('发起初核成功')
      return true
    } else {
      ElMessage.error('发起初核失败：' + (res.msg || '未知错误'))
      return false
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('发起初核失败:', error)
      ElMessage.error('发起初核失败')
    }
    return false
  }
}

// 监听选项变化
watch(
  () => [
    confirmOptions.option1,
    confirmOptions.option2,
    confirmOptions.option3,
    confirmOptions.option4,
    confirmOptions.option5
  ],
  () => {
    updateAllChecked()
  },
  { deep: true }
)

// 监听当前记录ID变化，重新获取当事人数据
watch(
  () => currentRecordId.value,
  (newId) => {
    if (newId) {
      getPartyPersonList()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  handleSubmit
})

onMounted(() => {
  if (currentRecordId.value) {
    getPartyPersonList()
  }
})
</script>

<style scoped>
.fqch-container {
  /* padding: 20px; */
  /* background: #f5f7fa; */
  min-height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-tip {
  color: #606266;
  font-size: 14px;
}

.table-container {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.blank-area {
  height: 100px;
}

.confirm-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.confirm-title {
  font-weight: bold;
  margin-bottom: 15px;
}

.confirm-options {
  margin-left: 20px;
}

.option-item {
  margin-bottom: 10px;
}

.warning-tip {
  margin-top: 20px;
  color: #606266;
  font-size: 14px;
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
