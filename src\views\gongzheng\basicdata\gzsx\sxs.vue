<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="事项名称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入事项名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:gzsxPz:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="gzsxTableRef" v-loading="loading" :data="gzsxPzList" @selection-change="handleSelectionChange"
        row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column label="事项名称" align="left" prop="title" min-width="300" />
        <el-table-column label="事项编号" align="center" prop="code" />
        <el-table-column label="是否基础公证事项" align="center" prop="jcsx">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.jcsx" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="新增" placement="top">
              <el-button v-hasPermi="['basicdata:gzsxPz:add']" link type="primary" icon="Plus"
                @click="handleAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzsxPz:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzsxPz:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
    </el-card>
    <!-- 添加或修改公证事项基础配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="gzsxPzFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="父级" prop="parentCode">
          <el-tree-select v-model="form.parentCode" :data="deptOptions"
            :props="{ value: 'code', label: 'title', children: 'children' } as any" value-key="parentCode"
            placeholder="选择上级告知树" check-strictly />
        </el-form-item>
        <el-form-item label="事项名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入事项名称" />
        </el-form-item>
        <el-form-item label="事项编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入事项编号" />
        </el-form-item>
        <el-form-item label="是否基础公证事项" prop="jcsx">
          <el-radio-group v-model="form.jcsx">
            <el-radio v-for="dict in gz_yes_or_no" :key="dict.value" :value="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Sxs" lang="ts">
  import { getGzsx, delGzsx, addGzsx, updateGzsx, listTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO, GzsxQuery, GzsxForm } from '@/api/gongzheng/basicdata/gzsx/types';
  interface GzsxOptionsType {
    parentId : number | string;
    id : number | string;
    title : string;
    parentCode : string;
    code : string;
    children : GzsxOptionsType[];
  }
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const deptOptions = ref<GzsxOptionsType[]>([]);
  const gzsxPzList = ref<GzsxVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const isExpandAll = ref(true);
  const queryFormRef = ref<ElFormInstance>();
  const gzsxPzFormRef = ref<ElFormInstance>();
  const gzsxTableRef = ref(null)
  const { gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yes_or_no'));
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxForm = {
    id: undefined,
    title: undefined,
    parentId: undefined,
    code: undefined,
    level: undefined,
    remark: undefined,
    jcsx: '0',
    parentCode: undefined
  }
  const data = reactive<PageData<GzsxForm, GzsxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      parentId: undefined,
      code: undefined,
      level: undefined,
      jcsx: undefined,
      parentCode: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      title: [
        { required: true, message: "事项名称不能为空", trigger: "blur" }
      ],
      code: [
        { required: true, message: "事项编号不能为空", trigger: "blur" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公证事项基础配置列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTreeCode<GzsxVO>(res.data, 'code');
    if (data) {
      gzsxPzList.value = data;
    }
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsxPzFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsxVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = async (row ?: GzsxVO) => {
    reset();
    await loadTreeData(row, true);
    dialog.visible = true;
    dialog.title = "添加公证事项";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzsxVO) => {
    reset();
    await loadTreeData(row, false);
    const _id = row?.id || ids.value[0]
    const res = await getGzsx(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改公证事项基础配置";
  }

  const loadTreeData = async (row ?: GzsxVO, isAdd) =>{
    const res = await listTree();
    const data = proxy?.handleTreeCode<GzsxOptionsType>(res.data, 'code');
    if (data) {
      deptOptions.value = data;
      if (isAdd && row && row.code) {
        form.value.parentCode = row?.code;
      }
    }
  }

  /** 提交按钮 */
  const submitForm = () => {
    gzsxPzFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzsx(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzsx(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzsxVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除公证事项基础配置编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGzsx(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzsxPz/export', {
      ...queryParams.value
    }, `gzsxPz_${new Date().getTime()}.xlsx`)
  }

  /** 展开/折叠操作 */
  const handleToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleExpandAll(gzsxPzList.value, isExpandAll.value);
  };
  /** 展开/折叠所有 */
  const toggleExpandAll = (data : GzsxVO[], status : boolean) => {
    data.forEach((item) => {
      gzsxTableRef.value?.toggleRowExpansion(item, status);
      if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
    });
  };
  onMounted(() => {
    getList();
  });
</script>
