<template>
  <el-card>
    <template #header>
      <strong class="text-base">收费及开票记录</strong>
    </template>
    <el-table :data="data" style="width: 100%" border>
      <el-table-column type="index" label="#" width="60" align="center" />
      <el-table-column prop="fphm" label="发票代码" width="120" align="center" />
      <el-table-column prop="sfqdh" label="收据编号" width="120" align="center" />
      <el-table-column prop="jfr" label="当事人" width="120" align="center" />
      <el-table-column prop="sfje" label="金额" width="120" align="center" />
      <el-table-column prop="sfsf" label="状态" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.sfsf === '1'" type="primary">已收费</el-tag>
          <el-tag v-else type="danger">未收费</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sfr" label="开票员" align="center" />
      <el-table-column prop="fprq" label="开票时间" width="120" align="center" />
      <!-- <el-table-column prop="dhhm" label="操作" width="120" align="center" /> -->
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
:deep(.el-card__header) {
  background-color: rgba(221, 236, 250, 0.555);
}
</style>
