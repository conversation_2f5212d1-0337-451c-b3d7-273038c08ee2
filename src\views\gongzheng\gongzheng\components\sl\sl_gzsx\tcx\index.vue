<template>
  <gz-dialog v-model="modelState.visible" :title="modelState.title || title" @closed="closed" append-to-body>
    <div v-loading="modelState.loading">
      <TcxInfoForm v-model="tcxInfo" ref="tcxInfoFormRef"></TcxInfoForm>
      <LqrList v-model="tcxLqrList" ref="lqrListRef"></LqrList>
    </div>
    <template #footer>
      <div class="flex justify-end items-center gap-4px">
        <el-button @click="comfirmSave" :loading="modelState.submitting" :disabled="modelState.submitting"
                   type="primary">保存
        </el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </gz-dialog>
</template>

<script lang="ts" setup>
import TcxInfoForm from './TcxInfoForm.vue';
import LqrList from './LqrList.vue';
import { GzjzTcxVO } from '@/api/gongzheng/bzfz/gzjzTcx/types';
import { GzjzTcxLqrVO, GzTcxLqrListVo } from '@/api/gongzheng/bzfz/gzjzTcxLqr/types';
import { addGzjzTcx, listGzjzTcx, updateGzjzTcx } from '@/api/gongzheng/bzfz/gzjzTcx';
import { listGzjzDsrByGzjz } from '@/api/gongzheng/gongzheng/gzjzDsr';
import { GzjzDsrVO } from '@/api/gongzheng/gongzheng/gzjzDsr/types';
import { addGzjzJd, updateGzjzJd } from '@/api/gongzheng/bzfz/gzjzJd';
import { getLqrPageList, listGzjzTcxLqr } from '@/api/gongzheng/bzfz/gzjzTcxLqr';
import { useUserStore } from '@/store/modules/user';
import { provide } from 'vue';
import eventBus from '@/utils/eventBus';


const userStore = useUserStore();

interface Props {
  title?: string;
}

const tcxInfoFormRef = ref<any>(null);
const lqrListRef = ref<any>(null);
const gzjzGzsxInfo = ref<any>({});
const tcxInfo = ref<GzjzTcxVO[]>([]);
const tcxData = ref<GzjzTcxVO[]>([]);
const tcxLqrList = ref<GzTcxLqrListVo[]>([]);
const gzjzDsrList = ref<GzjzDsrVO[]>([]);


const props = withDefaults(defineProps<Props>(), {
  title: '提存项'
});

const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));

const modelState = reactive({
  visible: false,
  title: '提存项',
  gzjzId: undefined,
  loading: false,
  submitting: false
});
//获取案件当事人
const loadDsr = async () => {
  gzjzDsrList.value = [];
  const queryParams = {
    dsrLx: undefined,
    gzjzId: modelState.gzjzId ||  currentRecordId.value,
    js: undefined,
    pageNum: 1,
    pageSize: 100
  };
  const res = await listGzjzDsrByGzjz(queryParams);
  if (res?.rows) {
    res.rows.forEach((item) => {
      //排除代理人
      if (item.js !== '4') {
        gzjzDsrList.value.push(item);
      }
    });
  }
};

//获取领取人信息
const getLqrDataList = async () => {
  tcxLqrList.value=[]
  const tcxId = tcxInfo.value.id;
  if (tcxId !== null && tcxId !== '' && typeof tcxId !== 'undefined') {
    const lqrPatams = {
      tcxId: tcxId,
      pageNum: 1,
      pageSize: 100
    }
    const res = await getLqrPageList(lqrPatams);
    if (res?.rows) {
      tcxLqrList.value = res.rows;
    }
  }
}

//加载数据
const initData = async () => {
  try {
    modelState.loading = true;

    const params = {
      gzjzId: modelState.gzjzId ||  currentRecordId.value,
      gzjzGzsxId: gzjzGzsxInfo.value.id,
      pageNum: 1,
      pageSize: 100
    };
    const res = await listGzjzTcx(params);
    if (res.code === 200 && res.rows.length > 0) {
      tcxData.value = res.rows[0];
      tcxInfo.value = res.rows[0];
    } else {
      tcxInfo.value = {
        gzjzId: modelState.gzjzId || currentRecordId.value,
        gzjzGzsxId: gzjzGzsxInfo.value.id,
        tclb:gzjzGzsxInfo.value.gzsxMc
      };
    }
    await loadDsr();
    await getLqrDataList();
    tcxInfoFormRef.value.ywlbChange()
    tcxInfoFormRef.value.tcbdChange()

  } catch (err: any) {
    console.error('提存项信息初始化失败', err);
  } finally {
    modelState.loading = false;
  }
};


const comfirmSave = async () => {
  try {
    const isValidOk = await tcxInfoFormRef.value?.validate();
    if(!isValidOk){
      return;
    }else {
      dataclear()
      const params = {
        gzjzId: modelState.gzjzId || currentRecordId.value,
        gzjzGzsxId: gzjzGzsxInfo.value.id,
        ...tcxInfo.value,
      }
      let res = null;
      if(params.id) {
        if (tcxInfo.value.hbJe === '0') {
          params.hbLjsj='0'
          params.hbLjlq='0'
          params.hbYe='0'
        }else{
          params.hbLjsj=tcxInfo.value.hbJe
          params.hbLjlq=tcxInfo.value.hbLjlq
          params.hbYe=(tcxInfo.value.hbJe-tcxInfo.value.hbLjlq).toString()
        }
        res=updateGzjzTcx(params)
      } else {
        params.hbLjsj= tcxInfo.value.hbJe
        params.hbLjlq='0'
        params.hbYe=tcxInfo.value.hbJe
        res = addGzjzTcx(params);
      }
      res.then((res)=>{
        if(res.code === 200) {
          ElMessage.success('提存项保存成功')
          close()
        } else {
          ElMessage.error(res.msg || '提存项保存失败')
        }
      })
    }
  } catch (err: any) {
    console.error('保存失败', err);
  } finally {
    modelState.submitting = false;
  }
};


//数据清理
const dataclear =()=>{
  let ywlb = tcxInfo.value.ywlb;
  let tcbd = tcxInfo.value.tcbd;
  if (ywlb === '2') {
    tcxInfo.value.hbZhrmb = '0';
    tcxInfo.value.hbKhh = '';
    tcxInfo.value.hbYhzh = '';
    tcxInfo.value.hbRzsj = null;
    tcxInfo.value.wpMc = '';
    tcxInfo.value.wpSl = '0';
    tcxInfo.value.wpBz = '';
  }
  if (tcbd === '1') {
    tcxInfo.value.wpMc = '';
    tcxInfo.value.wpSl = '0';
    tcxInfo.value.wpBz = '';
  }else{
    tcxInfo.value.hbLx = '';
    tcxInfo.value.hbJe = '0';
    tcxInfo.value.hbZhrmb = '0';
    tcxInfo.value.hbKhh = '';
    tcxInfo.value.hbRzsj = null;
  }
}

const close = () => {
  modelState.visible = false;
  modelState.gzjzId = undefined;
};

const closed = () => {
  modelState.gzjzId = undefined;
};

const open = (data?: any) => {
  modelState.visible = true;
  modelState.gzjzId = data?.gzjzId || undefined;
  modelState.title = data?.title || '';
  gzjzGzsxInfo.value = data?.gzjzGzsxInfo || {};
  initData();
};

eventBus.on('tcx:lqrList:reloadInfo', getLqrDataList)

/**
 * 对外暴露子组件方法
 */
defineExpose({
  open
});

// 提供给子组件的数据和方法
provide('gzjzDsrList', gzjzDsrList)
provide('currentTcxInfo', tcxInfo)

</script>
