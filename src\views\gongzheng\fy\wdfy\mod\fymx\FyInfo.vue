<template>
  <div class="fy-info-view h-full shadow-[0_0_10px_rgba(0,0,0,0.2)] rounded overflow-hidden">
    <div class="fyi-info-vh h-32px flex items-center px-10px">
      <strong>翻译信息</strong>
    </div>
    <div class="h-[calc(100%-32px)] p-20px flex">
      <el-form label-width="120" label-suffix=":" class="w-full">
        <el-form-item label="卷宗号">{{ curGzjz.jzbh }}</el-form-item>
        <el-form-item label="公证事项">{{ info.gzsx || '' }}</el-form-item>
        <el-form-item label="公证书编号">{{ info.gzsbh || '' }}</el-form-item>
        <el-form-item label="文档类型">
          <el-tag>{{ info.lx == '3' ? '公证书' : '文书' }}</el-tag>
        </el-form-item>
        <el-form-item label="文档名称">
          <el-button v-if="info.wbmc" @click="openDoc()" type="primary" link plain>{{ info.wbmc }}</el-button>
        </el-form-item>
        <el-form-item label="译文名称">
          <el-button v-if="info.ywmc" @click="openDoc(true)" type="primary" link plain>{{ info.ywmc }}</el-button>
        </el-form-item>
        <el-form-item label="公证员">{{ curGzjz.gzyxm || '' }}</el-form-item>
        <el-form-item label="译文">
          <dict-tag :options="gz_yw_wz" :value="curGzjz.ywwz" />
        </el-form-item>
        <el-form-item label="译文翻译要求">
          <div class="srounded px-6px">
            {{ curGzjz.gzjzYqtx?.txFyyq || '' }}
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getGzjzJbxx } from '@/api/gongzheng/gongzheng/gzjzJbxx';
import { GzjzJbxxVO } from '@/api/gongzheng/gongzheng/gzjzJbxx/types';
import { getGzjzWjccxx } from '@/api/gongzheng/gongzheng/gzjzWjccxx';
import { docOpenEdit } from '@/views/gongzheng/doc/DocEditor';

const curGzjz = inject<Ref<GzjzJbxxVO>>('currentRecord', ref(null));
const currentRecordId = inject<Ref<string | number | null>>('currentRecordId', ref(null));

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { gz_yw_wz } = toRefs<any>(proxy?.useDict('gz_yw_wz'));

interface Props {
  info?: any;
}

const props = defineProps<Props>();


const openDoc = (yw: boolean = false) => {
  if(yw) {
    const ywInfo = JSON.parse(props.info?.ywlj || '{}');
    if(ywInfo.path) {
      docOpenEdit(ywInfo.path)
    }
  } else {
    const wbInfo = JSON.parse(props.info?.wblj || '{}');
    if(wbInfo.path) {
      docOpenEdit(wbInfo.path)
    }
  }
}

const init = () => {

}

const getJzInfo = async () => {
  try {
    const res = await getGzjzJbxx(currentRecordId.value);
  } catch (err: any) {

  }
}

const getDocInfo = async () => {
  try {

    const res = await getGzjzWjccxx(props.docInfoId);

  } catch (err: any) {

  }
}


onMounted(() => {
  console.log('fyyyyyyy', curGzjz.value)
})
</script>

<style scoped>
.fy-info-view {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.fyi-info-vh {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
</style>