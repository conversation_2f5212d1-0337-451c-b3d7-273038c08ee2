<template>
  <gz-dialog v-model="visible" :title="props.title" @closed="handleClosed" fullscreen show-close>
    <div class="word-preview" >
      <div class="A4-page" v-html="props.wordHtml"></div>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleClosed">关闭</el-button>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue?: boolean;
  title?: string;
  wordHtml?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '文档预览',
  wordHtml: '',
});

const emit = defineEmits(['update:modelValue', 'onClosed']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
})

const handleClosed = () => {
  emit('update:modelValue', false);
  emit('onClosed');
}


</script>

<style>
.word-preview {
  width: 100%;
  height: 100%;
  padding: 20px;
  border: none;
  overflow: auto;
  background-color: #ccc;
  box-sizing: border-box;
}
.A4-page {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto;
  padding: 16mm;
  box-sizing: border-box;
  background-color: #fff;
  /* border: 1px solid #ccc; */
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.3);
}
.A4-page table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  border: 1px solid #333;
}
.A4-page table th,
.A4-page table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
  vertical-align: top;
}

.A4-page img {
  max-width: 100%;
  object-fit: contain;
}
</style>
