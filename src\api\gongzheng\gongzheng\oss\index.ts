import request from '@/utils/request';
import { AxiosProgressEvent } from 'axios';


/**
 * OSS上传文件
 * @param formData FormData对象
 * @param onUploadProgress 上传进度事件
 * @returns
 */
export const uploadFile = (formData: FormData, onUploadProgress: (progressEvent: AxiosProgressEvent) => void = () => {}) =>{
  return request({
    url: '/resource/oss/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress,
    data: formData
  })
}

/**
 * 根据ID查询OSS信息
 * @param id 资源ID
 * @return 返回请求结果，包含OSS信息
 */
export const queryOssInfo = (id: string) => {
  return request({
    url: `/resource/oss/listByIds/${id}`, // 请求的URL路径，使用GET方法获取指定ID的OSS信息
    method: 'get' // 请求方法为GET
  })
}
