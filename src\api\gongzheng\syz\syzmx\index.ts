import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SyzmxVO, SyzmxForm, SyzmxQuery } from '@/api/gongzheng/syz/syzmx/types';

/**
 * 查询水印纸管理-水印纸明细列表
 * @param query
 * @returns {*}
 */

export const listSyzmx = (query?: SyzmxQuery): AxiosPromise<SyzmxVO[]> => {
  return request({
    url: '/gongzheng/syzmx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询水印纸管理-水印纸明细详细
 * @param id
 */
export const getSyzmx = (id: string | number): AxiosPromise<SyzmxVO> => {
  return request({
    url: '/gongzheng/syzmx/' + id,
    method: 'get'
  });
};

/**
 * 新增水印纸管理-水印纸明细
 * @param data
 */
export const addSyzmx = (data: SyzmxForm) => {
  return request({
    url: '/gongzheng/syzmx',
    method: 'post',
    data: data
  });
};

/**
 * 修改水印纸管理-水印纸明细
 * @param data
 */
export const updateSyzmx = (data: SyzmxForm) => {
  return request({
    url: '/gongzheng/syzmx',
    method: 'put',
    data: data
  });
};

/**
 * 批量修改水印纸管理-水印纸明细
 * @param data
 */
export const updateSyzmxBatch = (data: SyzmxForm[]) => {
  return request({
    url: '/gongzheng/syzmx/batch',
    method: 'put',
    data: data
  });
};

/**
 * 删除水印纸管理-水印纸明细
 * @param id
 */
export const delSyzmx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/syzmx/' + id,
    method: 'delete'
  });
};

/**
 * 水印纸作废
 * @param id
 */
export const syzInvalidation = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/syzmx/invalidation/' + id,
    method: 'post'
  });
};

/**
 * 水印纸销毁
 * @param id
 */
export const destruction = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/syzmx/destruction/' + id,
    method: 'post'
  });
};

