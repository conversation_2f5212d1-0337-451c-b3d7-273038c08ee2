<template>
  <div style="width:100%">
    <!-- <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="事项名称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入事项名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition> -->

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="gzsxTableRef" v-loading="loading" :data="gzsxPzList" @selection-change="handleSelectionChange"
        row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll" highlight-current-row show-selection-column>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="事项名称" align="center" prop="title" />
        <el-table-column label="事项编号" align="center" prop="code" />
        <el-table-column label="是否基础公证事项" align="center" prop="jcsx">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.jcsx" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

  </div>
</template>

<script setup name="GzsxTree" lang="ts">
  import { listTree } from '@/api/gongzheng/basicdata/gzsx';
  import { GzsxVO, GzsxQuery, GzsxForm } from '@/api/gongzheng/basicdata/gzsx/types';
  interface GzsxOptionsType {
    parentId : number | string;
    id : number | string;
    title : string;
    children : GzsxOptionsType[];
  }
  // 定义 Props 类型
  interface Props {
    selectId : string[];
  }
  // 定义事件类型
  interface Emits {
    (e : 'onSave', data : []) : void;
  }

  const emit = defineEmits<Emits>();
  // 声明 Props
  const props = defineProps<Props>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const deptOptions = ref<GzsxOptionsType[]>([]);
  const gzsxPzList = ref<GzsxVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const isExpandAll = ref(true);
  const gzsxTableRef = ref(null)
  const queryFormRef = ref<ElFormInstance>();
  const gzsxPzFormRef = ref<ElFormInstance>();
  const { gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_yes_or_no'));
  const selectedData = ref<Array<string | number>>([]);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxForm = {
    id: undefined,
    title: undefined,
    parentId: undefined,
    code: undefined,
    level: undefined,
    remark: undefined,
    jcsx: undefined
  }
  const data = reactive<PageData<GzsxForm, GzsxQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      parentId: undefined,
      code: undefined,
      level: undefined,
      jcsx: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      title: [
        { required: true, message: "事项名称不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /**
   * 将树结构扁平化为一维数组
   */
  function flattenTree(nodes : GzsxVO[]) : GzsxVO[] {
    return nodes.flatMap(node => [
      node,
      ...(node.children ? flattenTree(node.children) : [])
    ]);
  }
  /** 查询公证事项基础配置列表 */
  const getList = async () => {
    clearSelection();
    loading.value = true;
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTree<GzsxVO>(res.data, 'id');
    if (data) {
      gzsxPzList.value = data;
      if (props.selectId && props.selectId.length > 0) {
        if (gzsxTableRef.value) {
          const flatNodes = flattenTree(data);
          const targetIds = new Set(props.selectId);
          const filteredNodes = flatNodes.filter(node => targetIds.has(node.id));
          nextTick(() => {
            // 逐个选中节点（关键修改点）
            filteredNodes.forEach(node => {
              gzsxTableRef.value?.toggleRowSelection(node, true);
            });
          });
        }
      }
    }
    loading.value = false;
  }


  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsxPzFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsxVO[]) => {
    ids.value = selection.map(item => item.id);
    emit("onSave", ids.value)
  }
  // 清空选择
  const clearSelection = () => {
    if (gzsxTableRef.value) {
      gzsxTableRef.value?.clearSelection();
    }
  };





  /** 展开/折叠操作 */
  const handleToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleExpandAll(gzsxPzList.value, isExpandAll.value);
  };
  /** 展开/折叠所有 */
  const toggleExpandAll = (data : GzsxVO[], status : boolean) => {
    data.forEach((item) => {
      gzsxTableRef.value?.toggleRowExpansion(item, status);
      if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
    });
  };



  onMounted(() => {

    getList();
  });
</script>

<style>
</style>
