<template>
  <gz-dialog v-model="visible" :title="props.title" @closed="handleClosed" fullscreen show-close>
    <iframe
      :src="props.url"
      class="iframe-preview"
    ></iframe>
    <template #footer>
      <el-button type="primary" @click="handleClosed">关闭</el-button>
    </template>
  </gz-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue?: boolean;
  title?: string;
  url: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '文档预览',
  url: '',
});

const emit = defineEmits(['update:modelValue', 'onClosed']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
  },
})

const handleClosed = () => {
  emit('update:modelValue', false);
  emit('onClosed');
}


</script>

<style scoped>
.iframe-preview {
  width: 100%;
  height: 100%;
  border: none;
  overflow: auto;
  background-color: #ccd;
  border-radius: 8px;
  box-sizing: border-box;
}
</style>
