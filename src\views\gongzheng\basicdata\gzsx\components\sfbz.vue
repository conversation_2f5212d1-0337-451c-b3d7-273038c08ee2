<template>
  <div class="p-2">


    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <span style="line-height: 24px; display: block; width: 160px;">收 费 标 准</span>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="primary" icon="Plus" @click="handleAdd()"
              v-hasPermi="['basicdata:gzsxSfbz:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="danger" icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['basicdata:gzsxSfbz:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button size="small" type="primary" icon="Edit" :disabled="single" @click="handleDefault()"
              v-hasPermi="['basicdata:gzsxSfbz:edit']">设为默认</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gzsxSfbzList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="费用类别" align="center" prop="sflb">
          <template #default="scope">
            <dict-tag :options="gz_sf_lb" :value="scope.row.sflb" />
          </template>
        </el-table-column>
        <el-table-column label="收费方式" align="center" prop="sffs">
          <template #default="scope">
            <dict-tag :options="gz_sf_fs" :value="scope.row.sffs" />
          </template>
        </el-table-column>
        <el-table-column label="参考价" align="center" prop="ckj" />
        <el-table-column label="起始价" align="center" prop="qsj" />
        <el-table-column label="结束价" align="center" prop="jsj" />
        <el-table-column label="费用场景" align="center" prop="fycj" >
          <template #default="scope">
            <dict-tag :options="gz_sfcj" :value="scope.row.fycj" />
          </template>
        </el-table-column>
        <el-table-column label="公式" align="center" prop="gs" />
        <el-table-column label="是否默认" align="center" prop="mrzt">
          <template #default="scope">
            <dict-tag :options="gz_yes_or_no" :value="scope.row.mrzt" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:gzsxSfbz:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:gzsxSfbz:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="loadData" />
    </el-card>
    <!-- 添加或修改收费标准对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="650px" append-to-body>
      <el-form ref="gzsxSfbzFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="收费类别" prop="sflb">
              <el-select v-model="form.sflb" placeholder="请选择" :disabled="isEdit" >
                <el-option v-for="dict in gz_sf_lb" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收费方式" prop="sffs">
              <el-select v-model="form.sffs" placeholder="请选择" :disabled="isEdit">
                <el-option v-for="dict in gz_sf_fs" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用场景" prop="fycj">
              <el-select v-model="form.fycj" placeholder="请选择">
                <el-option v-for="dict in gz_sfcj" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.sffs == '2'">
            <el-form-item label="公式" prop="gs">
              <el-input v-model="form.gs" placeholder="请输入" style="width: 50%;" /><span style="color: red; padding-left: 10px;">示例公式:m*0.5%+100</span>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="参考价" prop="ckj">
              <el-input-number :min="0" :step="1" v-model="form.ckj" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.sffs == '2'">
            <el-form-item label="起始价" prop="qsj">
              <el-input-number :min="0" :step="1" v-model="form.qsj" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.sffs == '2'">
            <el-form-item label="结束价" prop="jsj">
              <el-input-number :min="0" :step="1" v-model="form.jsj" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="是否默认" prop="mrzt">
          <el-select v-model="form.mrzt" placeholder="请选择">
            <el-option v-for="dict in gz_yes_or_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GzsxSfbz" lang="ts">
  import { listGzsxSfbz, getGzsxSfbz, delGzsxSfbz, addGzsxSfbz, updateGzsxSfbz } from '@/api/gongzheng/basicdata/gzsxSfbz';
  import { GzsxSfbzVO, GzsxSfbzQuery, GzsxSfbzForm } from '@/api/gongzheng/basicdata/gzsxSfbz/types';
  interface Props {
    gzsxId : string | number;
    selectId : [];
    gzsxCode: string;
    gzlbValue: string;
  }
  const props = defineProps<Props>();
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { gz_sfcj, gz_sf_lb, gz_sf_fs, gz_yes_or_no } = toRefs<any>(proxy?.useDict('gz_sfcj', 'gz_sf_lb', 'gz_sf_fs', 'gz_yes_or_no'));

  const gzsxSfbzList = ref<GzsxSfbzVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const isEdit = ref(false);

  const queryFormRef = ref<ElFormInstance>();
  const gzsxSfbzFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const initFormData : GzsxSfbzForm = {
    gzsxId: undefined,
    id: undefined,
    sflb: undefined,
    sffs: undefined,
    ckj: undefined,
    qsj: undefined,
    jsj: undefined,
    fycj: undefined,
    gs: undefined,
    mrzt: '0',
    selectId: [],
    gzsxCode: undefined,
    gzlbValue: undefined
  }
  const data = reactive<PageData<GzsxSfbzForm, GzsxSfbzQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      gzsxId: undefined,
      mrzt: undefined,
      gzsxCode: undefined,
      gzlbValue: undefined,
      params: {
      }
    },
    rules: {
      gzsxId: [
        { required: true, message: "公证事项不能为空", trigger: "blur" }
      ],
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询收费标准列表 */
  const getList = async (_gzsxCode, _gzlbValue) => {
    if(_gzsxCode && _gzlbValue){
      loading.value = true;
      queryParams.value.gzsxCode = _gzsxCode;
      queryParams.value.gzlbValue = _gzlbValue;
      const res = await listGzsxSfbz(queryParams.value);
      gzsxSfbzList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    }else{
      gzsxSfbzList.value = [];
      total.value = 0;
    }
  }

  const loadData = async () => {
    getList(props.gzsxCode, props.gzlbValue);
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    gzsxSfbzFormRef.value?.resetFields();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : GzsxSfbzVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    isEdit.value = false;
    dialog.visible = true;
    dialog.title = "添加收费标准";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: GzsxSfbzVO) => {
    reset();
    isEdit.value = true;
    const _id = row?.id || ids.value[0]
    const res = await getGzsxSfbz(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改收费标准";
  }

  /** 提交按钮 */
  const submitForm = () => {
    form.value.gzsxId = props.gzsxId;
    form.value.gzsxCode = props.gzsxCode;
    form.value.gzlbValue = props.gzlbValue;
    gzsxSfbzFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateGzsxSfbz(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addGzsxSfbz(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList(props.gzsxCode, props.gzlbValue);
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: GzsxSfbzVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除收费标准编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delGzsxSfbz(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList(props.gzsxCode, props.gzlbValue);
  }

  const handleDefault = async (row ?: GzsxSfbzVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认默认收费标准编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    // await delGzsxSfbz(_ids);
    proxy?.$modal.msgSuccess("默认开发中");
    // await getList(props.gzsxCode, props.gzlbValue);
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/gzsxSfbz/export', {
      ...queryParams.value
    }, `gzsxSfbz_${new Date().getTime()}.xlsx`)
  }
  const init = (_gzsxCode, _gzlbValue) => {
    console.log("选中公证事项：" + _gzsxCode);
    console.log("选中公证类别：" + _gzlbValue);
    getList(_gzsxCode, _gzlbValue)
  }
  const setGzsxIds = (data) => {
    form.value.selectId = data;
  }
  // 显式暴露方法给父组件
  defineExpose({
    init,
    setGzsxIds
  });

  onMounted(() => {
    getList(props.gzsxCode, props.gzlbValue);
  });
</script>
