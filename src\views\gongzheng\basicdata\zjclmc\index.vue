<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="证据名称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入证据名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['basicdata:zjclmc:add']">新增证据</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAddType"
              v-hasPermi="['basicdata:zjclmc:add']">新增证据类型</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="zjclmlTableRef" v-loading="loading" :data="zjclmcList"
        row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column label="证据名称" align="left" prop="title"  />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="新增" placement="top">
              <el-button v-hasPermi="['basicdata:zjclmc:add']" link type="primary" icon="Plus"
                @click="handleAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['basicdata:zjclmc:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['basicdata:zjclmc:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改证据名称对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="zjclmcFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="父级" prop="parentCode">
          <el-tree-select v-model="form.parentCode" :data="deptOptions"
            :props="{ value: 'treeCode', label: 'title', children: 'children' } as any" value-key="parentCode"
            placeholder="选择父级" check-strictly />
        </el-form-item>
        <el-form-item label="证据名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入证据名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Zjclmc" lang="ts">
  import { listZjclmc, getZjclmc, delZjclmc, addZjclmc, updateZjclmc, listTree, listExcludeChild } from '@/api/gongzheng/basicdata/zjclmc';
  import { ZjclmcVO, ZjclmcQuery, ZjclmcForm } from '@/api/gongzheng/basicdata/zjclmc/types';
  interface ZjclmcOptionsType {
    parentId : number | string;
    id : number | string;
    title : string;
    children : ZjclmcOptionsType[];
  }
  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const zjclmcList = ref<ZjclmcVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const deptOptions = ref<ZjclmcOptionsType[]>([]);
  const queryFormRef = ref<ElFormInstance>();
  const zjclmcFormRef = ref<ElFormInstance>();
  const isExpandAll = ref(true);
  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const dialog2 = reactive<DialogOption>({
    visible: false,
    title: ''
  });
  const initFormData : ZjclmcForm = {
    id: undefined,
    title: undefined,
    parentId: undefined,
    parentCode: undefined,
    treeCode: undefined,
  }
  const data = reactive<PageData<ZjclmcForm, ZjclmcQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      parentId: undefined,
      parentCode: undefined,
      treeCode: undefined,
      params: {
      }
    },
    rules: {
      id: [
        { required: true, message: "序号不能为空", trigger: "blur" }
      ],
      title: [
        { required: true, message: "证据名称不能为空", trigger: "blur" }
      ],
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询证据名称列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listTree(queryParams.value);
    const data = proxy?.handleTreeCode<ZjclmcVO>(res.data, 'treeCode');
    if (data) {
      zjclmcList.value = data;
    }
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    zjclmcFormRef.value?.resetFields();
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : ZjclmcVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = async (row ?: ZjclmcVO) => {
    reset();
    await loadTreeData(row, true);
    dialog.visible = true;
    dialog.title = "添加证据名称";
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: ZjclmcVO) => {
    reset();
    await loadTreeData(row, false);
    const _id = row?.id || ids.value[0]
    const res = await getZjclmc(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改证据名称";
  }

  const loadTreeData = async (row ?: ZjclmcVO, isAdd) =>{
    const res = await listTree();
    const data = proxy?.handleTreeCode<ZjclmcOptionsType>(res.data, 'treeCode');
    if (data) {
      deptOptions.value = data;
      if (isAdd && row && row.treeCode) {
        form.value.parentCode = row?.treeCode;
      }
    }
  }

  /** 提交按钮 */
  const submitForm = () => {
    zjclmcFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.id) {
          await updateZjclmc(form.value).finally(() => buttonLoading.value = false);
        } else {
          await addZjclmc(form.value).finally(() => buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
      }
    });
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: ZjclmcVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除证据名称编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delZjclmc(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('basicdata/zjclmc/export', {
      ...queryParams.value
    }, `zjclmc_${new Date().getTime()}.xlsx`)
  }
  const handleAddType = () => {
    proxy?.$modal.msgError("开发中...");
  }

  /** 展开/折叠操作 */
  const handleToggleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    toggleExpandAll(zjclmcList.value, isExpandAll.value);
  };
  /** 展开/折叠所有 */
  const toggleExpandAll = (data : ZjclmcVO[], status : boolean) => {
    data.forEach((item) => {
      zjclmlTableRef.value?.toggleRowExpansion(item, status);
      if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
    });
  };

  onMounted(() => {
    getList();
  });
</script>
