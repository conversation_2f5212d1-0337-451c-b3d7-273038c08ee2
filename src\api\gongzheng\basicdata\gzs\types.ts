export interface GzsVO {
  /**
   * 序号
   */
  id : string | number;

  children : Gzs<PERSON>[];
  /**
   * 告知名称
   */
  title : string;

  /**
   * 父级
   */
  parentId : string | number;

  /**
   * 备注
   */
  remark : string;

  parentCode: string;

  treeCode: string;

  temptreeCode: string;

}

export interface GzsForm extends BaseEntity {
  /**
   * 序号
   */
  id ?: string | number;

  /**
   * 告知名称
   */
  title ?: string;

  /**
   * 父级
   */
  parentId ?: string | number;

  /**
   * 备注
   */
  remark ?: string;

  parentCode?: string;

  treeCode?: string;

  temptreeCode?: string;

}

export interface GzsQuery extends PageQuery {

  /**
   * 告知名称
   */
  title ?: string;

  /**
   * 父级
   */
  parentId ?: string | number;
  parentCode?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}
