import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GzjzTcxZfjlVO, GzjzTcxZfjlForm, GzjzTcxZfjlQuery } from '@/api/gongzheng/bzfz/gzjzTcxZfjl/types';

/**
 * 查询提存项-支付记录列表
 * @param query
 * @returns {*}
 */

export const listGzjzTcxZfjl = (query?: GzjzTcxZfjlQuery): AxiosPromise<GzjzTcxZfjlVO[]> => {
  return request({
    url: '/gongzheng/gzjzTcxZfjl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询提存项-支付记录详细
 * @param id
 */
export const getGzjzTcxZfjl = (id: string | number): AxiosPromise<GzjzTcxZfjlVO> => {
  return request({
    url: '/gongzheng/gzjzTcxZfjl/' + id,
    method: 'get'
  });
};

/**
 * 新增提存项-支付记录
 * @param data
 */
export const addGzjzTcxZfjl = (data: GzjzTcxZfjlForm) => {
  return request({
    url: '/gongzheng/gzjzTcxZfjl',
    method: 'post',
    data: data
  });
};

/**
 * 修改提存项-支付记录
 * @param data
 */
export const updateGzjzTcxZfjl = (data: GzjzTcxZfjlForm) => {
  return request({
    url: '/gongzheng/gzjzTcxZfjl',
    method: 'put',
    data: data
  });
};

/**
 * 删除提存项-支付记录
 * @param id
 */
export const delGzjzTcxZfjl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzTcxZfjl/' + id,
    method: 'delete'
  });
};
