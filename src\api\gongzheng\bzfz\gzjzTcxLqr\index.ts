import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  GzjzTcxLqrForm,
  GzjzTcxLqrQuery,
  GzjzTcxLqrVO,
  GzTcxLqrListQuery,
  GzTcxLqrListVo
} from '@/api/gongzheng/bzfz/gzjzTcxLqr/types';

/**
 * 查询提存项-领取人列表
 * @param query
 * @returns {*}
 */

export const listGzjzTcxLqr = (query?: GzjzTcxLqrQuery): AxiosPromise<GzjzTcxLqrVO[]> => {
  return request({
    url: '/gongzheng/gzjzTcxLqr/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询提存项-领取人详细
 * @param id
 */
export const getGzjzTcxLqr = (id: string | number): AxiosPromise<GzjzTcxLqrVO> => {
  return request({
    url: '/gongzheng/gzjzTcxLqr/' + id,
    method: 'get'
  });
};

/**
 * 新增提存项-领取人
 * @param data
 */
export const addGzjzTcxLqr = (data: GzjzTcxLqrForm) => {
  return request({
    url: '/gongzheng/gzjzTcxLqr',
    method: 'post',
    data: data
  });
};

/**
 * 修改提存项-领取人
 * @param data
 */
export const updateGzjzTcxLqr = (data: GzjzTcxLqrForm) => {
  return request({
    url: '/gongzheng/gzjzTcxLqr',
    method: 'put',
    data: data
  });
};

/**
 * 删除提存项-领取人
 * @param id
 */
export const delGzjzTcxLqr = (id: string | number | Array<string | number>) => {
  return request({
    url: '/gongzheng/gzjzTcxLqr/' + id,
    method: 'delete'
  });
};


/**
 * 获取提存项领取人列表信息
 * @param query
 * @returns {*}
 */

export const getLqrPageList = (query?: GzTcxLqrListQuery): AxiosPromise<GzTcxLqrListVo[]> => {
  return request({
    url: '/gongzheng/gzjzTcxLqr/getLqrPageList',
    method: 'get',
    params: query
  });
};


/**
 * 提存项-同时新增领取人当事人
 * @param data
 */
export const addTqrDsrxx = (data: GzjzTcxLqrForm) => {
  return request({
    url: '/gongzheng/gzjzTcxLqr/addTqrDsrxx',
    method: 'post',
    data: data
  });
};
